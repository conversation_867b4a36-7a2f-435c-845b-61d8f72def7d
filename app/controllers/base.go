package controllers

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/gorm"
	sofaClient "github.com/qbox/pay-sdk/sofa/client"
	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine"
	"qiniu.io/qbpm/engine/lib/gaea"
	"qiniu.io/qbpm/engine/lib/sofa"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/util/logger"
	"qiniu.io/qbpm/engine/util/snowflake"
)

var (
	// ErrOperatorRequired indicates login user required
	ErrOperatorRequired = errors.New("operator required")
)

// Base Controller base
type Base struct {
	ProcessEngine    engine.ProcessEngine
	GaeaAdminService gaea.GaeaAdminService
	SofaService      sofa.SofaService
	SofaClient       sofaClient.Sofa // pay sdk sofa client
}

// RespErr response error info
func (c *Base) RespErr(ctx *gin.Context, statusCode int, err error, errCodes ...int) {
	if err == gorm.ErrRecordNotFound {
		statusCode = http.StatusNotFound
	}

	var errCode int
	if len(errCodes) > 0 {
		errCode = errCodes[0]
	} else {
		errCode = statusCode
	}

	ctx.JSON(statusCode, map[string]interface{}{
		"code":    errCode,
		"message": err.Error(),
	})
}

// RespOK response error info
func (c *Base) RespOK(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, data)
}

// GetParamID Get param id in url param
func (c *Base) GetParamID(ctx *gin.Context) (id uint64, err error) {
	idStr := ctx.Param("id")
	if idStr == "" {
		err = fmt.Errorf("id required")
		return
	}

	id, err = strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		err = fmt.Errorf("invalid id")
		return
	}

	return
}

const (
	ctxKeyReqLogger = "req_logger"
	ctxKeyReqID     = "X-Reqid"
	// ctxKeyRequestUserEmail is context key of request user email, provided by portal.io using http header
	ctxKeyRequestUserEmail = "X-QINIU-ADMIN-EMAIL"
)

// ReqUser get request user
func (c *Base) ReqUser(ctx *gin.Context, email ...string) string {
	if len(email) > 0 && email[0] != "" {
		return email[0]
	}

	return ctx.Request.Header.Get(ctxKeyRequestUserEmail)
}

// ReqLogger get a request logger with reqid
func (c *Base) ReqLogger(ctx *gin.Context) logrus.FieldLogger {
	return ReqLogger(ctx)
}

// ReqLogger get a request logger with reqid
func ReqLogger(ctx *gin.Context) logrus.FieldLogger {
	if log, ok := ctx.Get(ctxKeyReqLogger); ok {
		return log.(logrus.FieldLogger)
	}

	reqID := ctx.GetHeader(ctxKeyReqID)
	if reqID == "" && snowflake.Generator != nil {
		reqID = fmt.Sprintf("%x", snowflake.Generator.ID())
	}

	log := logrus.WithField(logger.FieldKeyReqid, reqID)
	ctx.Set(ctxKeyReqLogger, log)
	return log
}

// Paging covert (page, pageSize) to (offset, limit)
func Paging(page int, pageSize int) (int, int) {
	if page == 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = mgr.DefaultPageSize
	} else if pageSize > mgr.MaxPageSize {
		pageSize = mgr.MaxPageSize
	}
	return pageSize * (page - 1), pageSize
}
