package runtime_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"qiniu.io/qbpm/app/controllers"
	. "qiniu.io/qbpm/app/controllers/runtime"
	"qiniu.io/qbpm/engine"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("runtime", func() {
	var handler Handler
	var srv = gin.Default()
	var engine engine.ProcessEngine
	srv.PUT("/api/runtime/executions", handler.StartProcess)
	srv.GET("/api/runtime/executions", handler.ListExecution)
	srv.GET("/api/runtime/processes", handler.ListProcess)
	srv.POST("/api/runtime/executions/:id", handler.CompleteExecution)
	srv.PUT("/api/runtime/executions/:id", handler.Assign)
	srv.DELETE("/api/runtime/executions/:id", handler.FailExecution)
	srv.DELETE("/api/runtime/processes/:id", handler.Cancel)

	BeforeEach(func() {
		engine = test.InitEngine()
		base := &controllers.Base{ProcessEngine: engine}
		handler = Handler{Base: base}

		model := &models.ProcessDefinition{
			DeployAt: time.Now(),
			Name:     "testRepo",
			Key:      "testRepo",
			XMLData: []byte(
				`<definitions id="definitions"
				  xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
				  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
				 
					<process id="modify_user_price" name="用户改价流程">
						<startEvent id="theStart"/>
				 
						<sequenceFlow id="toProfileCheck" sourceRef="theStart" targetRef="finance"/>
				 
						<userTask id="finance" name="财务审批"/>

						<sequenceFlow id="financeToEnd" sourceRef="finance" targetRef="theEnd"/>
						 
						<endEvent id="theEnd"/>
					</process>
					 
				</definitions>`),
		}
		err := engine.GetRepositoryService().Create(model)
		Expect(err).To(BeNil())
	})

	It("should finish a process", func() {
		w := httptest.NewRecorder()
		input, _ := json.Marshal(StartProcessInput{Key: "testRepo", Excode: "001", Operator: "<EMAIL>"})
		req, _ := http.NewRequest("PUT", "/api/runtime/executions", bytes.NewBuffer(input))
		req.Header = http.Header{"Content-Type": []string{"application/json"}}
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecution models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecution)

		time.Sleep(time.Second)

		req, _ = http.NewRequest("GET", fmt.Sprintf("/api/runtime/executions?process_instance_id=%d", runExecution.ProcessInstanceID), nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecutions []models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecutions)

		input, _ = json.Marshal(OperateExecutionInput{Operator: "<EMAIL>"})
		req, _ = http.NewRequest("POST", "/api/runtime/executions/"+strconv.FormatInt(int64(runExecutions[0].ID), 10), bytes.NewBuffer(input))
		req.Header = http.Header{"Content-Type": []string{"application/json"}}
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))

		time.Sleep(time.Second)
		engine.GetSchedulerService().Shutdown()

		req, _ = http.NewRequest("GET", fmt.Sprintf("/api/runtime/executions?process_instance_id=%d", runExecution.ProcessInstanceID), nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecutions2 []models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecutions2)
		Expect(runExecutions2).To(HaveLen(0))
	})

	It("should fail a process", func() {
		w := httptest.NewRecorder()
		input, _ := json.Marshal(StartProcessInput{Key: "testRepo", Excode: "001", Operator: "<EMAIL>"})
		req, _ := http.NewRequest("PUT", "/api/runtime/executions", bytes.NewBuffer(input))
		req.Header = http.Header{"Content-Type": []string{"application/json"}}
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecution models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecution)

		time.Sleep(time.Second)

		req, _ = http.NewRequest("GET", fmt.Sprintf("/api/runtime/executions?process_instance_id=%d", runExecution.ProcessInstanceID), nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecutions []models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecutions)

		input, _ = json.Marshal(OperateExecutionInput{Operator: "<EMAIL>"})
		req, _ = http.NewRequest("DELETE", "/api/runtime/executions/"+strconv.FormatInt(int64(runExecutions[0].ID), 10), bytes.NewBuffer(input))
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))

		time.Sleep(time.Second)

		req, _ = http.NewRequest("GET", fmt.Sprintf("/api/runtime/executions?process_instance_id=%d", runExecution.ProcessInstanceID), nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecutions2 []models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecutions2)
		Expect(runExecutions2).To(HaveLen(0))
	})

	It("should reassign a runExecution", func() {
		w := httptest.NewRecorder()
		input, _ := json.Marshal(StartProcessInput{Key: "testRepo", Excode: "001", Operator: "<EMAIL>"})
		req, _ := http.NewRequest("PUT", "/api/runtime/executions", bytes.NewBuffer(input))
		req.Header = http.Header{"Content-Type": []string{"application/json"}}
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecution models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecution)

		time.Sleep(time.Second)

		req, _ = http.NewRequest("GET", fmt.Sprintf("/api/runtime/executions?process_instance_id=%d", runExecution.ProcessInstanceID), nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecutions []models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecutions)

		time.Sleep(time.Second)

		assign := "<EMAIL>"

		input, _ = json.Marshal(AssignInput{Assignee: assign, Operator: "tester"})
		req, _ = http.NewRequest("PUT", "/api/runtime/executions/"+strconv.FormatInt(int64(runExecutions[0].ID), 10), bytes.NewBuffer(input))
		req.Header = http.Header{"Content-Type": []string{"application/json"}}
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))

		time.Sleep(time.Second)

		w.Body.Reset()

		req, _ = http.NewRequest("GET", "/api/runtime/executions?assignees=<EMAIL>", nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecutions2 []models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecutions2)
		Expect(runExecutions2).To(HaveLen(1))

		req, _ = http.NewRequest("GET", "/api/runtime/executions?assignees=test", nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		json.NewDecoder(w.Body).Decode(&runExecutions2)
		Expect(runExecutions2).To(HaveLen(0))

	})

	It("should cancel a process", func() {
		w := httptest.NewRecorder()
		input, _ := json.Marshal(StartProcessInput{Key: "testRepo", Excode: "001", Operator: "<EMAIL>"})
		req, _ := http.NewRequest("PUT", "/api/runtime/executions", bytes.NewBuffer(input))
		req.Header = http.Header{"Content-Type": []string{"application/json"}}
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecution models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecution)

		time.Sleep(time.Second)

		req, _ = http.NewRequest("GET", "/api/runtime/processes", nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecutions []models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecutions)
		Expect(runExecutions).To(HaveLen(1))

		req, _ = http.NewRequest("DELETE", "/api/runtime/processes/"+strconv.FormatInt(int64(runExecution.ProcessInstanceID), 10), nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))

		time.Sleep(time.Second)

		req, _ = http.NewRequest("GET", fmt.Sprintf("/api/runtime/executions?process_instance_id=%d", runExecution.ProcessInstanceID), nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var runExecutions2 []models.RunExecution
		json.NewDecoder(w.Body).Decode(&runExecutions2)
		Expect(runExecutions2).To(HaveLen(0))
	})

})
