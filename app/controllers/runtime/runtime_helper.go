package runtime

import (
	"encoding/json"

	"qiniu.io/qbpm/engine/models/enums"
)

type (
	// StartProcessInput defines request params to start a process
	StartProcessInput struct {
		Key      string `form:"key" json:"key"`
		UID      uint64 `form:"uid" json:"uid"`
		Params   string `form:"params" json:"params"`
		Excode   string `form:"excode" json:"excode"`
		Operator string `form:"operator" json:"operator"`
		DDL      int64  `form:"ddl" json:"ddl"` // 到期自动驳回的时间戳，默认值 0 表示永不到期
	}

	// OperateExecutionInput defines request params to operate executions
	OperateExecutionInput struct {
		Params   string       `form:"params" json:"params"`
		Operator string       `form:"operator" json:"operator"`
		Memo     string       `form:"memo" json:"memo"`
		Excodes  []string     `form:"excodes" json:"excodes"`
		Caller   enums.Caller `form:"caller" json:"caller"` // 区分调用来源
	}

	FailProcessInput struct {
		Excodes  []string `form:"excodes" json:"excodes"`
		Operator string   `form:"operator" json:"operator"`
		Memo     string   `form:"memo" json:"memo"`
	}

	// ListExecutionInput defines request params to list running executions
	ListExecutionInput struct {
		ProcessDefinitionID  *uint64                   `schema:"process_definition_id"`
		ProcessDefinitionKey *string                   `schema:"process_definition_key"`
		ProcessInstanceID    *uint64                   `schema:"process_instance_id"`
		ActivityDefineKey    *string                   `schema:"activity_define_key"`
		Excode               *string                   `schema:"excode"`
		UID                  *uint64                   `schema:"uid"`
		Status               *enums.RunExecutionStatus `schema:"status"`
		OriginalAssignees    []string                  `schema:"original_assignees"`
		Assignees            []string                  `schema:"assignees"`
		Starters             []string                  `schema:"starters"`
		Page                 int                       `schema:"page"`
		PageSize             int                       `schema:"page_size"`
	}

	// ListProcesssInput defines request params to list running processes
	ListProcesssInput struct {
		ProcessDefinitionID  *uint64                   `schema:"process_definition_id"`
		ProcessDefinitionKey *string                   `schema:"process_definition_key"`
		ProcessInstanceID    *uint64                   `schema:"process_instance_id"`
		Excode               *string                   `schema:"excode"`
		UID                  *uint64                   `schema:"uid"`
		Status               *enums.RunExecutionStatus `schema:"status"`
		OriginalAssignees    []string                  `schema:"original_assignees"`
		Assignees            []string                  `schema:"assignees"`
		Starters             []string                  `schema:"starters"`
		Page                 int                       `schema:"page"`
		PageSize             int                       `schema:"page_size"`
	}

	// AssignInput defines request params to reassign tasks
	AssignInput struct {
		Assignee string `form:"assignee" json:"assignee"`
		Operator string `form:"operator" json:"operator"`
		Memo     string `form:"memo" json:"memo"`
	}

	// BatchAssignInput defines request params to batch reassign tasks
	BatchAssignInput struct {
		AssignInput
		ProcessDefinitionID  uint64 `form:"process_definition_id" json:"process_definition_id"`
		ProcessDefinitionKey string `form:"process_definition_key" json:"process_definition_key"`
		ActivityDefineKey    string `form:"activity_define_key" json:"activity_define_key"`
		OriginalAssignee     string `form:"original_assignee" json:"original_assignee"`
	}

	// CancelInput defines request params to cancel a process
	CancelInput struct {
		Operator string `form:"operator" json:"operator"`
		Memo     string `form:"memo" json:"memo"`
	}

	// UrgeInput defines request params to urge a process
	UrgeInput struct {
		Operator string `form:"operator" json:"operator"`
		Memo     string `form:"memo" json:"memo"`
	}
)

func (i *AssignInput) Valid() bool {
	return i.Assignee != "" && i.Operator != ""
}

func (i *BatchAssignInput) Valid() bool {
	return i.AssignInput.Valid() &&
		(i.ProcessDefinitionID != 0 || i.ProcessDefinitionKey != "" || i.OriginalAssignee != "")
}

func (c *Handler) validateParams(params string) (err error) {
	if params != "" {
		var p interface{}
		return json.Unmarshal([]byte(params), &p)
	}

	return
}
