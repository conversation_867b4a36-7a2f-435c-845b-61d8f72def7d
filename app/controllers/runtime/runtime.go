package runtime

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/schema"

	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services/runtime"
	"qiniu.io/qbpm/engine/util/logger"
)

// Handler handles request about runtime executions
type Handler struct {
	*controllers.Base
}

// StartProcess starts a new process
func (c *Handler) StartProcess(ctx *gin.Context) {
	var input StartProcessInput
	err := ctx.Bind(&input)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	input.Operator = c.ReqUser(ctx, input.Operator)
	if input.Operator == "" {
		c.RespErr(ctx, http.StatusBadRequest, controllers.ErrOperatorRequired)
		return
	}

	if err = c.validateParams(input.Params); err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	runExecution, err := c.ProcessEngine.GetRuntimeService().StartProcess(
		input.Key,
		input.Params,
		input.Excode,
		input.Operator,
		input.UID,
		input.DDL,
	)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("params", input).
			WithError(err).
			Error("<RuntimeHandler.StartProcess> RuntimeService.StartProcess")
		return
	}

	c.RespOK(ctx, runExecution)
	return
}

// CompleteExecution complete a task by execution id
func (c *Handler) CompleteExecution(ctx *gin.Context) {
	var input OperateExecutionInput
	err := ctx.Bind(&input)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	input.Operator = c.ReqUser(ctx, input.Operator)
	if input.Operator == "" {
		c.RespErr(ctx, http.StatusBadRequest, controllers.ErrOperatorRequired)
		return
	}

	if err = c.validateParams(input.Params); err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	executionID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
	}

	err = c.ProcessEngine.GetRuntimeService().CompleteExecution(uint64(executionID), input.Params, input.Operator, input.Memo)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("executionID", uint64(executionID)).
			WithField("params", input).
			WithError(err).
			Error("<RuntimeHandler.CompleteExecution> RuntimeService.CompleteExecution")
		return
	}

	c.RespOK(ctx, struct{}{})
	return
}

// CounterSignExecution counter-sign a task by execution id
func (c *Handler) CounterSignExecution(ctx *gin.Context) {
	var input runtime.CounterSignExecutionParam
	err := ctx.Bind(&input)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	input.ExecutionParam.Operator = c.ReqUser(ctx, input.ExecutionParam.Operator)
	if input.ExecutionParam.Operator == "" {
		c.RespErr(ctx, http.StatusBadRequest, controllers.ErrOperatorRequired)
		return
	}

	if err = c.validateParams(input.ExecutionParam.Params); err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	executionID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
	}

	input.ExecutionParam.ExecutionID = uint64(executionID)

	err = c.ProcessEngine.GetRuntimeService().CounterSignExecution(input)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("executionID", executionID).
			WithField("params", input).
			WithError(err).
			Error("<RuntimeHandler.CounterSignExecution> RuntimeService.CounterSignExecution")
		return
	}

	c.RespOK(ctx, struct{}{})
	return
}

// GetCounterSignConfig 判断 execution 是否能加签并返回加签相关信息
func (c *Handler) GetCounterSignConfig(ctx *gin.Context) {
	executionID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}
	counterSign, err := c.ProcessEngine.GetRuntimeService().GetCounterSignConfig(executionID)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}
	c.RespOK(ctx, counterSign)
}

// FailExecution reject a task by execution id
func (c *Handler) FailExecution(ctx *gin.Context) {
	var input OperateExecutionInput
	err := ctx.Bind(&input)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	input.Operator = c.ReqUser(ctx, input.Operator)
	if input.Operator == "" {
		c.RespErr(ctx, http.StatusBadRequest, controllers.ErrOperatorRequired)
		return
	}

	if err = c.validateParams(input.Params); err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	executionID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	// 默认调用方是用户
	if input.Caller == "" {
		input.Caller = enums.CallerUser
	}

	if !input.Caller.Valid() {
		c.RespErr(ctx, http.StatusBadRequest, errors.New("unknown caller"))
		return
	}

	err = c.ProcessEngine.GetRuntimeService().FailExecution(
		executionID,
		input.Params,
		input.Operator,
		input.Memo,
		input.Caller,
	)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("executionID", executionID).
			WithField("params", input).
			WithError(err).
			Error("<RuntimeHandler.FailExecution> RuntimeService.FailExecution")
		return
	}

	c.RespOK(ctx, struct{}{})
	return
}

// FailExecutionsByExcodes reject a task by execution excode
func (c *Handler) FailExecutionsByExcodes(ctx *gin.Context) {
	var input OperateExecutionInput
	err := ctx.Bind(&input)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	input.Operator = c.ReqUser(ctx, input.Operator)
	if input.Operator == "" {
		c.RespErr(ctx, http.StatusBadRequest, controllers.ErrOperatorRequired)
		return
	}

	if err = c.validateParams(input.Params); err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Caller.Valid() {
		c.RespErr(ctx, http.StatusBadRequest, errors.New("unknown caller"))
		return
	}

	err = c.ProcessEngine.GetRuntimeService().FailExecutionsByExcodes(
		input.Excodes,
		input.Operator,
		input.Memo,
		input.Caller,
	)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("params", input).
			WithError(err).
			Error("RuntimeService.FailExecutionsByExcodes")
		return
	}

	c.RespOK(ctx, struct{}{})
}

// FailProcessesByExcodes reject processes by given execution excodes
func (c *Handler) FailProcessesByExcodes(ctx *gin.Context) {
	var input FailProcessInput
	err := ctx.Bind(&input)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	input.Operator = c.ReqUser(ctx, input.Operator)
	if input.Operator == "" {
		c.RespErr(ctx, http.StatusBadRequest, controllers.ErrOperatorRequired)
		return
	}
	err = c.ProcessEngine.GetRuntimeService().FailProcessesByExcodes(input.Excodes, input.Operator, input.Memo)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("params", input).
			WithError(err).
			Error("RuntimeService.FailProcessesByExcodes")
		return
	}

	c.RespOK(ctx, struct{}{})
}

// SuspendProcess 根据 process instance id 挂起指定的审批流
func (c *Handler) SuspendProcess(ctx *gin.Context) {
	processInstanceID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	err = c.ProcessEngine.GetRuntimeService().SuspendOrResumeProcess(processInstanceID, true)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("process instance id", processInstanceID).
			WithError(err).
			Error("RuntimeService.SuspendOrResumeExecutionsByExcodes")
		return
	}

	currentUserEmail := controllers.GetReqUserEmail(ctx)

	if currentUserEmail == "" {
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithError(errors.New("unauthorized"))
		return
	}

	err = c.ProcessEngine.GetHistoryService().CreateProcessActivityLog(&models.ProcessActivityLog{
		ProcessInstanceID: processInstanceID,
		ActivityType:      enums.ActivityLogTypeSuspended,
		Operator:          currentUserEmail,
	})
	if err != nil {
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("process instance id", processInstanceID).
			WithError(err).
			Error("RuntimeService.CreateProcessActivityLog")
	}

	c.RespOK(ctx, struct{}{})
	return
}

// 直接调用qbpm的挂起接口, 逻辑和price-biz的接口一致
func (c *Handler) SuspendProcessV2(ctx *gin.Context) {

	currentUser := controllers.GetReqUser(ctx)

	if currentUser == nil {
		c.RespErr(ctx, http.StatusUnauthorized, errors.New("unauthorized"))
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithError(errors.New("unauthorized"))
		return
	}

	processInstanceID, err := c.GetParamID(ctx)

	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	processInstance, err := c.ProcessEngine.GetHistoryService().GetProcessByID(processInstanceID)

	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("process instance id", processInstanceID).
			WithError(err).
			Error("HistoryService.GetProcessByID")
	}

	if !controllers.IsProcessViewer(currentUser) && processInstance.StartByID != currentUser.Email {
		c.RespErr(ctx, http.StatusBadRequest, errors.New("只有审批的发起人能挂起/恢复流程"))
		return
	}

	err = c.ProcessEngine.GetRuntimeService().SuspendOrResumeProcess(processInstanceID, true)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("process instance id", processInstanceID).
			WithError(err).
			Error("RuntimeService.SuspendOrResumeProcess")
		return
	}

	err = c.ProcessEngine.GetHistoryService().CreateProcessActivityLog(&models.ProcessActivityLog{
		ProcessInstanceID: processInstanceID,
		ActivityType:      enums.ActivityLogTypeSuspended,
		Operator:          currentUser.Email,
	})
	if err != nil {
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("process instance id", processInstanceID).
			WithError(err).
			Error("HistoryService.CreateProcessActivityLog")
	}

	c.RespOK(ctx, struct{}{})
}

// ResumeProcess 根据 process instance id 恢复指定的被挂起的审批流
func (c *Handler) ResumeProcess(ctx *gin.Context) {
	processInstanceID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	err = c.ProcessEngine.GetRuntimeService().SuspendOrResumeProcess(processInstanceID, false)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("process instance id", processInstanceID).
			WithError(err).
			Error("RuntimeService.SuspendOrResumeExecutionsByExcodes")
		return
	}
	c.RespOK(ctx, struct{}{})
	return
}

// ListExecution lists specific running executions
func (c *Handler) ListExecution(ctx *gin.Context) {
	input := new(ListExecutionInput)
	err := schema.NewDecoder().Decode(input, ctx.Request.URL.Query())
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}
	runExecutions, _, err := c.ProcessEngine.GetRuntimeService().SearchExecutions(runtime.RunExecutionQuery{
		ListByProcess:        false,
		ProcessDefinitionID:  input.ProcessDefinitionID,
		ProcessDefinitionKey: input.ProcessDefinitionKey,
		ProcessInstanceID:    input.ProcessInstanceID,
		ActivityDefineKey:    input.ActivityDefineKey,
		Excode:               input.Excode,
		UID:                  input.UID,
		Status:               input.Status,
		OriginalAssignees:    input.OriginalAssignees,
		Assignees:            input.Assignees,
		Starters:             input.Starters,
		Page:                 input.Page,
		PageSize:             input.PageSize,
	})

	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("params", input).
			WithError(err).
			Error("<RuntimeHandler.ListExecution> RuntimeService.SearchExecutions")
		return
	}

	c.RespOK(ctx, runExecutions)
}

// ListProcess lists specific running processes
func (c *Handler) ListProcess(ctx *gin.Context) {
	input := new(ListProcesssInput)
	err := schema.NewDecoder().Decode(input, ctx.Request.URL.Query())
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	runExecutions, _, err := c.ProcessEngine.GetRuntimeService().SearchExecutions(runtime.RunExecutionQuery{
		ListByProcess:        true,
		ProcessDefinitionID:  input.ProcessDefinitionID,
		ProcessDefinitionKey: input.ProcessDefinitionKey,
		ProcessInstanceID:    input.ProcessInstanceID,
		Excode:               input.Excode,
		UID:                  input.UID,
		Status:               input.Status,
		OriginalAssignees:    input.OriginalAssignees,
		Assignees:            input.Assignees,
		Starters:             input.Starters,
		Page:                 input.Page,
		PageSize:             input.PageSize,
	})

	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("params", input).
			WithError(err).
			Error("<RuntimeHandler.ListProcess> RuntimeService.SearchExecutions")
		return
	}

	c.RespOK(ctx, runExecutions)
}

// Assign reassigns a task by execution id
func (c *Handler) Assign(ctx *gin.Context) {
	executionID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	var input AssignInput
	err = ctx.Bind(&input)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		c.RespErr(ctx, http.StatusBadRequest, errors.New("assignee and operator required"))
		return
	}

	err = c.ProcessEngine.GetRuntimeService().Assign(executionID, &runtime.ReassignParam{
		Assignee: input.Assignee,
		Operator: input.Operator,
		Memo:     input.Memo,
	})
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("executionID", executionID).
			WithField("params", input).
			WithError(err).
			Error("<RuntimeHandler.Assign> RuntimeService.Assign")
		return
	}
	c.RespOK(ctx, struct{}{})
}

// BatchAssign reassigns tasks by execution id
func (c *Handler) BatchAssign(ctx *gin.Context) {
	var input BatchAssignInput
	err := ctx.Bind(&input)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		c.RespErr(ctx, http.StatusBadRequest, errors.New("invalid params"))
		return
	}

	executions, _, err := c.ProcessEngine.GetRuntimeService().SearchExecutions(runtime.RunExecutionQuery{
		ProcessDefinitionID:  &input.ProcessDefinitionID,
		ProcessDefinitionKey: &input.ProcessDefinitionKey,
		ActivityDefineKey:    &input.ActivityDefineKey,
		OriginalAssignees:    []string{input.OriginalAssignee},
	})
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}

	for _, execution := range executions {
		err = c.ProcessEngine.GetRuntimeService().Assign(execution.ID, &runtime.ReassignParam{
			Assignee: input.Assignee,
			Operator: input.Operator,
			Memo:     input.Memo,
		})
		if err != nil {
			c.RespErr(ctx, http.StatusInternalServerError, err)
			c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
				WithField("executionID", execution.ID).
				WithField("params", input).
				WithError(err).
				Error("<RuntimeHandler.BatchAssign> RuntimeService.Assign")
			return
		}
	}
	c.RespOK(ctx, struct{}{})
}

// Cancel cancels a running process by processInstanceID
func (c *Handler) Cancel(ctx *gin.Context) {
	processInstanceID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	var input CancelInput
	err = ctx.Bind(&input)

	input.Operator = c.ReqUser(ctx, input.Operator)

	if err != nil || input.Operator == "" {
		if err == nil {
			err = errors.New("invalid operator")
		}

		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	err = c.ProcessEngine.GetRuntimeService().Cancel(processInstanceID, input.Operator, input.Memo)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("processInstanceID", processInstanceID).
			WithError(err).
			Error("<RuntimeHandler.Cancel> RuntimeService.Cancel")
		return
	}
	c.RespOK(ctx, struct{}{})
}

// GetExecution gets a execution by id
func (c *Handler) GetExecution(ctx *gin.Context) {
	executionID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	runExecution, err := c.ProcessEngine.GetRuntimeService().GetExecution(uint64(executionID))
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("executionID", executionID).
			WithError(err).
			Error("<RuntimeHandler.GetExecution> RuntimeService.GetExecution")
		return
	}

	c.RespOK(ctx, runExecution)
	return
}

// Urge urge to approve by execution
func (c *Handler) UrgeByExecution(ctx *gin.Context) {
	executionID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	var input UrgeInput
	err = ctx.Bind(&input)

	input.Operator = c.ReqUser(ctx, input.Operator)

	if err != nil || input.Operator == "" {
		if err == nil {
			err = errors.New("invalid operator")
		}

		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	err = c.ProcessEngine.GetRuntimeService().UrgeByExecution(uint64(executionID), input.Operator, input.Memo)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("executionID", executionID).
			WithError(err).
			Error("<RuntimeHandler.UrgeByExecution> RuntimeService.UrgeByExecution")
		return
	}

	c.RespOK(ctx, struct{}{})
}

// Urge urge to approve by process
func (c *Handler) UrgeByProcess(ctx *gin.Context) {
	processID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	var input UrgeInput
	err = ctx.Bind(&input)

	input.Operator = c.ReqUser(ctx, input.Operator)

	if err != nil || input.Operator == "" {
		if err == nil {
			err = errors.New("invalid operator")
		}

		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	err = c.ProcessEngine.GetRuntimeService().UrgeByProcess(uint64(processID), input.Operator, input.Memo)
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("processID", processID).
			WithError(err).
			Error("<RuntimeHandler.UrgeByProcess> RuntimeService.UrgeByProcess")
		return
	}

	c.RespOK(ctx, struct{}{})
}

// AssignNotify 审批提醒通知
func (c *Handler) AssignNotify(ctx *gin.Context) {
	executionID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	err = c.ProcessEngine.GetRuntimeService().AssignNotify(uint64(executionID))
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("executionID", executionID).
			WithError(err).
			Error("<RuntimeHandler.AssignNotify> RuntimeService.AssignNotify")
		return
	}

	c.RespOK(ctx, struct{}{})
}

// CompleteNotify 审批完成通知
func (c *Handler) CompleteNotify(ctx *gin.Context) {
	processInstanceID, err := c.GetParamID(ctx)
	if err != nil {
		c.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	err = c.ProcessEngine.GetRuntimeService().CompleteNotify(uint64(processInstanceID))
	if err != nil {
		c.RespErr(ctx, http.StatusInternalServerError, err)
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "RuntimeHandler").
			WithField("processInstanceID", processInstanceID).
			WithError(err).
			Error("<RuntimeHandler.CompleteNotify> RuntimeService.CompleteNotify")
		return
	}

	c.RespOK(ctx, struct{}{})
}
