package history

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/schema"
	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/engine/models/enums"
	hisService "qiniu.io/qbpm/engine/services/history"
	"qiniu.io/qbpm/engine/util/logger"
)

// ProcessHandler handler for process instance
type ProcessHandler struct {
	*controllers.Base
}

// Get Get process instance
func (h *ProcessHandler) Get(ctx *gin.Context) {
	id, err := h.GetParamID(ctx)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	model, err := h.ProcessEngine.GetHistoryService().GetProcessByID(id)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				logger.FieldKeyPrefix: "ProcessHandler",
				"process_instance_id": id,
			}).
			WithError(err).
			Error("<ProcessHandler.Get> HistoryService.GetProcessByID")
		return
	}

	h.RespOK(ctx, model)
}

// ProcessListParam param for list process
type ProcessListParam struct {
	ProcessDefinitionID  *uint64                      `schema:"process_definition_id"`
	ProcessDefinitionKey *string                      `schema:"process_definition_key"`
	ProcessInstanceID    *string                      `schema:"process_instance_id"`
	Excode               *string                      `schema:"excode"`
	UID                  *uint64                      `schema:"uid"`
	Assignees            []string                     `schema:"assignees"`
	Status               *enums.ProcessInstanceStatus `schema:"status"`
	Starters             []string                     `schema:"starters"`
	Page                 int                          `schema:"page"`
	PageSize             int                          `schema:"page_size"`
}

// List List process instance with params
func (h *ProcessHandler) List(ctx *gin.Context) {
	input := new(ProcessListParam)
	err := schema.NewDecoder().Decode(input, ctx.Request.URL.Query())
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	models, _, err := h.ProcessEngine.GetHistoryService().ListProcess(hisService.ProcessListParam{
		ProcessDefinitionID:  input.ProcessDefinitionID,
		ProcessDefinitionKey: input.ProcessDefinitionKey,
		ProcessInstanceID:    input.ProcessInstanceID,
		Excode:               input.Excode,
		UID:                  input.UID,
		Assignees:            input.Assignees,
		Status:               input.Status,
		Starters:             input.Starters,
		Page:                 input.Page,
		PageSize:             input.PageSize,
	})
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField("params", input).
			WithField(logger.FieldKeyPrefix, "ProcessHandler").
			WithError(err).
			Error("<ProcessHandler.List> HistoryService.ListProcess")
		return
	}

	h.RespOK(ctx, models)
}
