package history

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/app/env/config"
	"qiniu.io/qbpm/engine/models/enums"
	hisService "qiniu.io/qbpm/engine/services/history"
)

type StatisticsHandler struct {
	*controllers.Base
}

// GetPersonalStatistics 函数用于获取个人统计信息
func (h *StatisticsHandler) GetPersonalStatistics(ctx *gin.Context) {
	userId := controllers.GetReqUserEmail(ctx)

	if userId == "" {
		h.RespErr(ctx, http.StatusUnauthorized, errors.New("unauthorized"))
		return
	}

	viewTypeInt, err := strconv.Atoi(ctx.Query("view_type"))
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("invalid Argument"))
		return
	}

	viewType := enums.ViewType(viewTypeInt)
	if viewType.String() == "" {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("invalid view_type"))
		return
	}

	historyService := h.ProcessEngine.GetHistoryService()

	days := config.Conf.Statistics.DefaultDays
	overtimeDays := config.Conf.Statistics.OvertimeDays

	count, err := historyService.CountProcessInstance(hisService.CountProcessInstanceParam{
		ViewType: viewType,
		Days:     days,
		UserId:   userId,
	})

	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}

	// 平均审批时长
	averageDuration, err := historyService.AverageDuration(hisService.AverageDurationParam{
		ViewType: viewType,
		Days:     days,
		UserId:   userId,
	})

	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}

	// 超时数量
	overTimeCount, err := historyService.CountOverTimeProcessInstance(hisService.CountOverTimeProcessInstanceParam{
		ViewType:     viewType,
		OvertimeDays: overtimeDays,
		UserId:       userId,
	})

	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}

	var urgeCount, rejectCount int

	if viewType == enums.Assignee {
		// 催办数量
		urgeCount, err = historyService.CountUrge(hisService.CountUrgeProcessInstanceParam{
			Days:   days,
			UserId: userId,
		})
	} else {
		// 驳回数量
		rejectCount, err = historyService.CountRejectProcessInstance(hisService.CountRejectProcessInstanceParam{
			Days:   days,
			UserId: userId,
		})
	}

	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}

	h.RespOK(ctx, gin.H{
		"count":            count,
		"over_time_count":  overTimeCount,
		"urge_count":       urgeCount,
		"reject_count":     rejectCount,
		"average_duration": averageDuration,
	})
}

// GetGlobalStatistics 函数用于获取全公司审批统计信息
func (h *StatisticsHandler) GetGlobalStatistics(ctx *gin.Context) {
	viewTypeInt, err := strconv.Atoi(ctx.Query("view_type"))
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("invalid Argument"))
		return
	}
	viewType := enums.ViewType(viewTypeInt)
	if viewType.String() == "" {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("invalid view_type"))
		return
	}

	days := config.Conf.Statistics.DefaultDays
	overtimeDays := config.Conf.Statistics.OvertimeDays
	historyService := h.ProcessEngine.GetHistoryService()

	// 审批数量
	count, err := historyService.GlobalAvgCountProcessInstance(hisService.GlobalAvgCountProcessInstanceParam{
		ViewType: viewType,
		Days:     days,
	})
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}

	// 平均审批时长
	averageDuration, err := historyService.GlobalAvgAverageDuration(hisService.GlobalAvgAverageDurationParam{
		ViewType: viewType,
		Days:     days,
	})
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}

	// 超时数量
	overTimeCount, err := historyService.GlobalAvgCountOverTimeProcessInstance(hisService.GlobalAvgCountOverTimeProcessInstanceParam{
		ViewType:     viewType,
		OvertimeDays: overtimeDays,
	})
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}

	var urgeCount, rejectCount int
	if viewType == enums.Assignee {
		// 催办数量
		urgeCount, err = historyService.GlobalAvgCountUrge(hisService.GlobalAvgCountUrgeParam{
			Days: days,
		})
	} else {
		// 驳回数量
		rejectCount, err = historyService.GlobalAvgCountReject(hisService.GlobalAvgCountRejectParam{
			Days: days,
		})
	}

	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		return
	}

	h.RespOK(ctx, gin.H{
		"global_avg_count":            count,
		"global_avg_average_duration": averageDuration,
		"global_avg_over_time_count":  overTimeCount,
		"global_avg_urge_count":       urgeCount,
		"global_avg_reject_count":     rejectCount,
	})
}
