package history

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/schema"
	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/engine/models/enums"
	hisService "qiniu.io/qbpm/engine/services/history"
	"qiniu.io/qbpm/engine/util/logger"
)

// ActivityHandler handler for activity instance
type ActivityHandler struct {
	*controllers.Base
}

// ActivityListParam defines request params to list activities
type ActivityListParam struct {
	ProcessInstanceID *uint64               `schema:"process_instance_id"`
	Excode            *string               `schema:"excode"`
	Status            *enums.ActivityStatus `schema:"status"`
	Types             []string              `schema:"types"`
	Page              int                   `schema:"page"`
	PageSize          int                   `schema:"page_size"`
}

// List List activity instance with params
func (h *ActivityHandler) List(ctx *gin.Context) {
	input := new(ActivityListParam)
	err := schema.NewDecoder().Decode(input, ctx.Request.URL.Query())
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	models, err := h.ProcessEngine.GetHistoryService().ListActivity(hisService.ActivityListParam{
		ProcessInstanceID: input.ProcessInstanceID,
		Excode:            input.Excode,
		Status:            input.Status,
		Types:             input.Types,
		Page:              input.Page,
		PageSize:          input.PageSize,
	})
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				"params":              input,
				logger.FieldKeyPrefix: "ActivityHandler",
			}).
			WithError(err).
			Error("<ActivityHandler.List> HistoryService.ListActivity")
		return
	}

	h.RespOK(ctx, models)
}

// ListProcessActivity List activity instance for specefic process instance
func (h *ActivityHandler) ListProcessActivity(ctx *gin.Context) {
	id, err := h.GetParamID(ctx)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	param := hisService.ActivityListParam{
		ProcessInstanceID: &id,
	}
	models, err := h.ProcessEngine.GetHistoryService().ListActivity(param)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				"process_instance_id": id,
				logger.FieldKeyPrefix: "ActivityHandler",
			}).
			WithError(err).
			Error("<ActivityHandler.ListProcessActivity> HistoryService.ListActivity")
		return
	}

	h.RespOK(ctx, models)
}
