package history

import (
	"errors"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/schema"
	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/engine/models"
	hisService "qiniu.io/qbpm/engine/services/history"
	"qiniu.io/qbpm/engine/util/logger"
)

// ListenerHandler handler for listener
type ListenerHandler struct {
	*controllers.Base
}

type ListenerItem struct {
	ProcessInstanceID   uint64                `json:"process_instance_id,string"`
	ProcessDefinitionID uint64                `json:"process_definition_id,string"`
	ExecutionID         uint64                `json:"execution_id,string"`
	ListenerKey         string                `json:"listener_key"`
	ActionKey           string                `json:"action_key"`
	NodeKey             string                `json:"node_key"`
	Fire                string                `json:"fire"`
	Status              models.ListenerStatus `json:"status"`
	Errors              string                `json:"errors"`
	CreatedAt           time.Time             `json:"created_at"`
	UpdatedAt           time.Time             `json:"updated_at"`
}

// List list listeners with params
func (h *ListenerHandler) List(ctx *gin.Context) {
	input := new(hisService.ListListenerParam)
	err := schema.NewDecoder().Decode(input, ctx.Request.URL.Query())
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("params required"))
		return
	}

	models, err := h.ProcessEngine.GetHistoryService().ListListeners(input)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField("params", input).
			WithField(logger.FieldKeyPrefix, "ListenerHandler").
			WithError(err).
			Error("<ListenerHandler.List> HistoryService.ListListeners")
		return
	}

	result := make([]ListenerItem, len(models))

	for i, item := range models {
		result[i] = ListenerItem{
			ProcessInstanceID:   item.ProcessInstanceID,
			ProcessDefinitionID: item.ProcessDefinitionID,
			ExecutionID:         item.ExecutionID,
			ListenerKey:         item.ListenerKey,
			ActionKey:           item.ActionKey,
			NodeKey:             item.NodeKey,
			Fire:                item.Fire,
			Status:              item.Status,
			Errors:              item.Errors,
			CreatedAt:           item.CreatedAt,
			UpdatedAt:           item.UpdatedAt,
		}
	}

	h.RespOK(ctx, result)
}

// Run run listener with params
func (h *ListenerHandler) Run(ctx *gin.Context) {
	var input hisService.RunListenerParam
	err := ctx.Bind(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("params required"))
		return
	}

	err = h.ProcessEngine.GetHistoryService().RunListener(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField("params", input).
			WithField(logger.FieldKeyPrefix, "ListenerHandler").
			WithError(err).
			Error("<ListenerHandler.Run> HistoryService.RunListener")
		return
	}

	h.RespOK(ctx, struct{}{})
}

// UpdateListenerStatus update listener status
func (h *ListenerHandler) UpdateListenerStatus(ctx *gin.Context) {
	var input hisService.UpdateListenerStatusParam
	err := ctx.Bind(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("params required"))
		return
	}

	err = h.ProcessEngine.GetHistoryService().UpdateListenerStatus(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField("params", input).
			WithField(logger.FieldKeyPrefix, "ListenerHandler").
			WithError(err).
			Error("<ListenerHandler.UpdateListenerStatus> update listener status failed")
		return
	}

	h.RespOK(ctx, struct{}{})
}

// UpdateListenerStatusByExcode update listener status by excode
func (h *ListenerHandler) UpdateListenerStatusByExcode(ctx *gin.Context) {
	var input hisService.UpdateListenerStatusByExcodeParam
	err := ctx.Bind(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("params required"))
		return
	}

	err = h.ProcessEngine.GetHistoryService().UpdateListenerStatusByExcode(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField("params", input).
			WithField(logger.FieldKeyPrefix, "ListenerHandler").
			WithError(err).
			Error("<ListenerHandler.UpdateListenerStatusByExcode> update listener status by excode failed")
		return
	}

	h.RespOK(ctx, struct{}{})
}
