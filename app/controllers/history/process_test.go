package history_test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"time"

	"github.com/gin-gonic/gin"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/app/controllers"
	. "qiniu.io/qbpm/app/controllers/history"
	"qiniu.io/qbpm/engine"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("Process", func() {
	var handler ProcessHandler
	var srv = gin.Default()
	var engine engine.ProcessEngine
	srv.GET("/api/history/processes", handler.List)
	srv.GET("/api/history/processes/:id", handler.Get)

	BeforeEach(func() {
		engine = test.InitEngine()
		base := &controllers.Base{ProcessEngine: engine}
		handler = ProcessHandler{Base: base}

		model := &models.ProcessDefinition{
			DeployAt: time.Now(),
			Name:     "testRepo",
			Key:      "testRepo",
			XMLData: []byte(
				`<definitions id="definitions"
				  xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
				  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
				 
					<process id="modify_user_price" name="用户改价流程">
						<startEvent id="theStart"/>
				 
						<sequenceFlow id="toProfileCheck" sourceRef="theStart" targetRef="finance"/>
				 
						<userTask id="finance" name="财务审批"/>

						<sequenceFlow id="financeToEnd" sourceRef="finance" targetRef="theEnd"/>
						 
						<endEvent id="theEnd"/>
					</process>
					 
				</definitions>`),
		}
		err := engine.GetRepositoryService().Create(model)
		Expect(err).To(BeNil())

		_, err = engine.GetRuntimeService().StartProcess("testRepo", "", "test", "tester", 0, 0)
		Expect(err).To(BeNil())
	})

	It("should list specific activities", func() {
		time.Sleep(time.Second)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/api/history/processes", nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var processes []models.ProcessInstance
		json.NewDecoder(w.Body).Decode(&processes)

		Expect(len(processes)).To(Equal(1))

		w = httptest.NewRecorder()
		req, _ = http.NewRequest("GET", fmt.Sprintf("/api/history/processes/%d", processes[0].ID), nil)
		srv.ServeHTTP(w, req)
		Expect(w.Code).To(Equal(200))
		var process models.ProcessInstance
		json.NewDecoder(w.Body).Decode(&process)

		Expect(process.Name).To(Equal("testRepo"))

	})
})
