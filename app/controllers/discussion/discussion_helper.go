package discussion

import "time"

type UploadAttachment struct {
	FileName      string `json:"file_name,omitempty"`
	ContentBase64 string `json:"content_base64,omitempty"`
}

type Attachment struct {
	FileName string `json:"file_name,omitempty"`
	URL      string `json:"url,omitempty"`
}

type CreateCommentReq struct {
	ParentCommentID   uint64            `json:"parent_comment_id"`
	Author            string            `json:"author"`
	Content           string            `json:"content"`
	ProcessInstanceID string            `json:"process_instance_id"`
	Attachment        *UploadAttachment `json:"attachment"`
}

type CommentResp struct {
	ID                uint64      `json:"id"`
	ProcessInstanceID string      `json:"process_instance_id"`
	ParentCommentID   uint64      `json:"parent_comment_id"`
	Author            string      `json:"author"`
	Content           string      `json:"content"`
	IsDeleted         bool        `json:"is_deleted"`
	Attachment        *Attachment `json:"attachment"`
	EditBy            string      `json:"edit_by"`
	ReplyTo           *string     `json:"reply_to,omitempty"`
	CreatedAt         time.Time   `json:"created_at"`
	UpdatedAt         time.Time   `json:"updated_at"`
}

type DeleteCommentReq struct {
	Operator string `json:"operator"`
}

type ListCommentReq struct {
	ProcessInstanceID string `form:"process_instance_id"`
	Page              int    `form:"page"`
	PageSize          int    `form:"page_size"`
}

type ListCommentResp struct {
	Comments []CommentResp `json:"comments"`
	Count    int64         `json:"count"`
}
