package discussion

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
)

type Handler struct {
	*controllers.Base
}

func (h *Handler) CreateComment(ctx *gin.Context) {
	var req CreateCommentReq
	ctx.ShouldBindJSON(&req)

	if strings.TrimSpace(req.Author) == "" {
		req.Author = "system"
	}

	processInstanceID, err := strconv.Atoi(req.ProcessInstanceID)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		h.ReqLogger(ctx).WithFields(logrus.Fields{
			"processInstanceID": req.ProcessInstanceID,
			"operator":          req.Author,
		}).
			WithError(err).Error("<DiscussionHandler.CreateComment>invalid process instance id")
		return
	}

	comment := models.Comment{
		ProcessInstanceID: uint64(processInstanceID),
		Content:           req.Content,
		Author:            req.Author,
		ParentCommentID:   req.ParentCommentID,
		EditedBy:          req.Author,
		IsDeleted:         false,
	}

	if req.Attachment != nil {
		comment.AttachmentFilename = req.Attachment.FileName
		comment.AttachmentContentBase64 = req.Attachment.ContentBase64
	}

	err = h.ProcessEngine.GetDiscussionService().CreateComment(comment)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				"comment":  comment,
				"operator": req.Author,
			}).
			WithError(err).
			Error("<DiscussionHandler.CreateComment> DiscussionService.CreateComment")
		return
	}

	mentionedUsers := comment.ParseMentionedUsers()
	adminUsers, err := h.GaeaAdminService.ListUserByNames(mentionedUsers)
	if err != nil {
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				"comment":  comment,
				"operator": req.Author,
			}).
			WithError(err).
			Error("<DiscussionHandler.CreateComment> GaeaAdminService.ListUserByNames")
		return
	}

	mentions := make([]*models.ProcessActivityLogMention, 0)
	for _, user := range adminUsers {
		mentions = append(mentions, &models.ProcessActivityLogMention{
			UserID: user.Email,
		})
	}

	err = h.ProcessEngine.GetHistoryService().CreateProcessActivityLogWithMentions(&models.ProcessActivityLog{
		ProcessInstanceID: uint64(processInstanceID),
		ActivityType:      enums.ActivityLogTypeMention,
		Operator:          req.Author,
		Content:           req.Content,
	}, mentions)

	if err != nil {
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				"comment":  comment,
				"operator": req.Author,
			}).
			WithError(err).
			Error("<DiscussionHandler.CreateComment> DiscussionService.CreateComment")
		return
	}

	h.RespOK(ctx, struct{}{})
}

func (h *Handler) GetCommentByID(ctx *gin.Context) {
	commentID, err := h.GetParamID(ctx)
	if err != nil || commentID == 0 {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	comment, err := h.ProcessEngine.GetDiscussionService().GetCommentByID(commentID)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				"commentID": commentID,
			}).
			WithError(err).
			Error("<DiscussionHandler.GetCommentByCommentID> DiscussionService.GetCommentByID")
		return
	}

	processInstanceID := strconv.FormatUint(comment.ProcessInstanceID, 10)
	var attachment *Attachment
	if comment.AttachmentFilename != "" {
		attachment = &Attachment{
			FileName: comment.AttachmentFilename,
			URL:      comment.AttachmentFileURL,
		}
	}
	resp := CommentResp{
		ID:                comment.ID,
		ProcessInstanceID: processInstanceID,
		ParentCommentID:   comment.ParentCommentID,
		Author:            comment.Author,
		Content:           comment.Content,
		IsDeleted:         comment.IsDeleted,
		Attachment:        attachment,
		EditBy:            comment.EditedBy,
		CreatedAt:         comment.CreatedAt,
		UpdatedAt:         comment.UpdatedAt,
	}

	h.RespOK(ctx, resp)
	return
}

func (h *Handler) DeleteComment(ctx *gin.Context) {
	commentID, err := h.GetParamID(ctx)
	if err != nil || commentID == 0 {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	var req DeleteCommentReq
	ctx.ShouldBindJSON(&req)

	if strings.TrimSpace(req.Operator) == "" {
		req.Operator = "system"
	}

	comment, err := h.ProcessEngine.GetDiscussionService().GetCommentByID(commentID)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				"commentID": commentID,
				"operator":  req.Operator,
			}).
			WithError(err).
			Error("<DiscussionHandler.DeleteComment> DiscussionService.GetCommentByID")
		return
	}

	comment.EditedBy = req.Operator
	err = h.ProcessEngine.GetDiscussionService().DeleteComment(*comment)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				"commentID": commentID,
				"operator":  req.Operator,
			}).
			WithError(err).
			Error("<DiscussionHandler.DeleteComment> DiscussionService.DeleteComment")
		return
	}
}

func (h *Handler) ListComment(ctx *gin.Context) {
	var req ListCommentReq
	ctx.ShouldBindQuery(&req)

	// 默认20条
	if req.PageSize == 0 {
		req.PageSize = 20
	}

	offset, limit := controllers.Paging(req.Page, req.PageSize)

	processInstanceID, err := strconv.Atoi(req.ProcessInstanceID)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		h.ReqLogger(ctx).WithField("processInstanceID", req.ProcessInstanceID).
			WithError(err).Error("<DiscussionHandler.ListComment>invalid process instance id")
		return
	}

	comments, err := h.ProcessEngine.GetDiscussionService().ListComment(uint64(processInstanceID), offset, limit)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithFields(logrus.Fields{
				"processInstanceID": processInstanceID,
				"offset":            offset,
				"limit":             limit,
			}).
			WithError(err).
			Error("<DiscussionHandler.ListComment> DiscussionService.ListComment")
		return
	}
	resp := make([]CommentResp, 0)
	for _, comment := range comments {
		var attachment *Attachment
		if comment.AttachmentFilename != "" {
			attachment = &Attachment{
				FileName: comment.AttachmentFilename,
				URL:      comment.AttachmentFileURL,
			}
		}
		csp := CommentResp{
			ID:                comment.ID,
			ProcessInstanceID: strconv.FormatUint(comment.ProcessInstanceID, 10),
			ParentCommentID:   comment.ParentCommentID,
			Author:            comment.Author,
			Content:           comment.Content,
			EditBy:            comment.EditedBy,
			IsDeleted:         comment.IsDeleted,
			Attachment:        attachment,
			CreatedAt:         comment.CreatedAt,
			UpdatedAt:         comment.UpdatedAt,
		}
		if comment.ParentCommentID != 0 {
			csp.ReplyTo = &comment.ReplyTo
		}
		resp = append(resp, csp)
	}

	count, err := h.ProcessEngine.GetDiscussionService().CountComment(uint64(processInstanceID))
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithField("processInstanceID", processInstanceID).
			WithError(err).
			Error("<DiscussionHandler.ListComment> DiscussionService.CountComment")
		return
	}

	ctx.JSON(http.StatusOK, ListCommentResp{
		Comments: resp,
		Count:    count,
	})
	return
}
