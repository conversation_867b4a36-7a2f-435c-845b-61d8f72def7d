// Code generated by MockGen. DO NOT EDIT.
// Source: auth.go

// Package mock_auth is a generated GoMock package.
package mock_auth

import (
	gomock "github.com/golang/mock/gomock"
	reflect "reflect"
)

// MockAuth is a mock of Auth interface
type MockAuth struct {
	ctrl     *gomock.Controller
	recorder *MockAuthMockRecorder
}

// MockAuthMockRecorder is the mock recorder for MockAuth
type MockAuthMockRecorder struct {
	mock *MockAuth
}

// NewMockAuth creates a new mock instance
func NewMockAuth(ctrl *gomock.Controller) *MockAuth {
	mock := &MockAuth{ctrl: ctrl}
	mock.recorder = &MockAuthMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockAuth) EXPECT() *MockAuthMockRecorder {
	return m.recorder
}

// AuthProc mocks base method
func (m *MockAuth) AuthProc(arg0, arg1 string) error {
	ret := m.ctrl.Call(m, "AuthProc", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AuthProc indicates an expected call of AuthProc
func (mr *MockAuthMockRecorder) AuthProc(arg0, arg1 interface{}) *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthProc", reflect.TypeOf((*MockAuth)(nil).AuthProc), arg0, arg1)
}
