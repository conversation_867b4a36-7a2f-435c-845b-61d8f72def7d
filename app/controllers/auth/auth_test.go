package auth

import (
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/onsi/ginkgo/v2"

	"qiniu.io/qbpm/app/controllers/auth/mock_auth"
)

type GinkgoTestReporter struct{}

func (g GinkgoTestReporter) Errorf(format string, args ...interface{}) {
	ginkgo.Fail(fmt.Sprintf(format, args...))
}

func (g GinkgoTestReporter) Fatalf(format string, args ...interface{}) {
	ginkgo.Fail(fmt.Sprintf(format, args...))
}

var _ = ginkgo.Describe("Auth", func() {
	var (
		a      *Handler
		router *gin.Engine
		t      GinkgoTestReporter
	)

	ginkgo.Describe("unit", func() {
		ginkgo.BeforeEach(func() {
			mockCtl := gomock.NewController(t)
			auth := mock_auth.NewMockAuth(mockCtl)
			auth.EXPECT().AuthProc("http://acc.jfcs.qiniu.io/user/info", "tokenstr").Return(nil)
			a = NewHandler("http://acc.jfcs.qiniu.io", auth)
			router = gin.Default()
			router.Use(a.Handle)
			router.GET("/testing", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"hello": "world"})
			})
		})

		ginkgo.It("auth", func() {
			req := httptest.NewRequest(http.MethodGet, "/testing", nil)
			req.Header.Set("Authorization", "Bearer tokenstr")
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			result := w.Result()
			body, _ := io.ReadAll(result.Body)
			ginkgo.By(string(body))
			if string(body) != `{"hello":"world"}` {
				ginkgo.Fail(string(body))
			}
		})
	})

})
