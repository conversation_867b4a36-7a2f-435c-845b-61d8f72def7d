package auth

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/engine/util/logger"
	"qiniu.io/qbpm/engine/util/rest"
)

const (
	// StatusSucc defines successful status
	StatusSucc = iota
	// StatusFail defines failed status
	StatusFail
)

// Auth defines interface to perform authorization
type Auth interface {
	AuthProc(string, string) error
}

// Processer defines method to perform authorization
type Processer func(string, string) error

// AuthProc implements interface to perform authorization
func (f Processer) AuthProc(userInfoURL, tokenStr string) error {
	return f(userInfoURL, tokenStr)
}

// Handler controller to handle authorization
type Handler struct {
	userInfoURL string
	auth        Auth
	*controllers.Base
}

// NewHandler instances a handler to perform authorization
func NewHandler(accHost string, auths ...Auth) *Handler {
	var auth = Auth(Processer(AccAuth))
	if len(auths) > 0 {
		auth = auths[0]
	}
	return &Handler{userInfoURL: accHost + "/user/info", auth: auth}
}

// <PERSON>le authorizes requests
func (c *Handler) Handle(ctx *gin.Context) {
	tokenStr := ctx.Request.Header.Get("Authorization")

	if strings.HasPrefix(tokenStr, "Bearer") {
		tokenStr = strings.TrimPrefix(tokenStr, "Bearer ")
	} else {
		tokenStr = strings.TrimPrefix(tokenStr, "OAuth ")
	}

	if tokenStr == "" {
		c.RespErr(ctx, http.StatusUnauthorized, errors.New("unauthorized"))
		ctx.Abort()
		return
	}

	if err := c.auth.AuthProc(c.userInfoURL, tokenStr); err != nil {
		c.RespErr(ctx, http.StatusForbidden, err)
		ctx.Abort()
		c.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "Handler").
			WithError(err).Error("auth failed")
		return
	}
}

// AccAuth authorizes using acc service
func AccAuth(userInfoURL, tokenStr string) error {
	header := fmt.Sprintf(`{"Authorization": "Bearer %s"}`, tokenStr)
	req := rest.NewRest(userInfoURL, http.MethodPost, header, "")
	_, err := req.Do()
	if err != nil {
		return err
	}

	return nil
}
