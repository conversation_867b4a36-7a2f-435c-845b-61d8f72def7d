package system

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"qiniu.io/qbpm/app/controllers"
	sysService "qiniu.io/qbpm/engine/services/system"
	"qiniu.io/qbpm/engine/util/logger"
)

// SystemHandler handles system request
type SystemHandler struct {
	*controllers.Base
}

// ListService List predefined services
func (h *SystemHandler) ListService(ctx *gin.Context) {
	models, err := h.ProcessEngine.GetSystemService().ListServices()
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithField(logger.FieldKeyPrefix, "SystemHandler").
			WithError(err).
			Error("<SystemHandler.ListService> SystemService.ListServices")
		return
	}

	result := make([]ServiceItem, len(models))
	for i, item := range models {
		result[i] = ServiceItem{
			ID:        item.ID,
			Code:      item.Code,
			Name:      item.Name,
			Host:      item.Host,
			Auth:      item.Auth,
			CreatedAt: item.CreatedAt,
			UpdatedAt: item.UpdatedAt,
		}
	}

	h.Resp<PERSON>(ctx, result)
}

// CreateService create predefined service
func (h *SystemHandler) CreateService(ctx *gin.Context) {
	var input sysService.CreateServiceParam
	err := ctx.Bind(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("params required"))
		return
	}

	model, err := h.ProcessEngine.GetSystemService().CreateService(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithField(logger.FieldKeyPrefix, "SystemHandler").
			WithError(err).
			Error("<SystemHandler.CreateService> SystemService.CreateService")
		return
	}

	h.RespOK(ctx, ServiceItem{
		ID:        model.ID,
		Code:      model.Code,
		Name:      model.Name,
		Host:      model.Host,
		Auth:      model.Auth,
		CreatedAt: model.CreatedAt,
		UpdatedAt: model.UpdatedAt,
	})
}

// UpdateService update predefined service
func (h *SystemHandler) UpdateService(ctx *gin.Context) {

	executionID, err := h.GetParamID(ctx)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	var input sysService.UpdateServiceParam
	err = ctx.Bind(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("params required"))
		return
	}

	err = h.ProcessEngine.GetSystemService().UpdateService(executionID, &input)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithField(logger.FieldKeyPrefix, "SystemHandler").
			WithError(err).
			Error("<SystemHandler.UpdateService> SystemService.UpdateService")
		return
	}

	h.RespOK(ctx, struct{}{})
}

// ListAction List predefined actions
func (h *SystemHandler) ListAction(ctx *gin.Context) {
	var param *sysService.QueryActionParam
	err := ctx.Bind(&param)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	models, err := h.ProcessEngine.GetSystemService().QueryActions(param)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithField(logger.FieldKeyPrefix, "SystemHandler").
			WithError(err).
			Error("<SystemHandler.ListAction> SystemService.QueryActions")
		return
	}

	result := make([]ActionItem, len(models))
	for i, item := range models {
		result[i] = ActionItem{
			ID:             item.ID,
			Code:           item.Code,
			Name:           item.Name,
			Service:        item.Service,
			UseServiceAuth: item.UseServiceAuth,
			CustomAuth:     item.CustomAuth,
			URL:            item.URL,
			Method:         item.Method,
			Header:         item.Header,
			Body:           item.Body,
			StoreKey:       item.StoreKey,
			ParseKey:       item.ParseKey,
			ParseFirst:     item.ParseFirst,
			MaxRetry:       item.MaxRetry,
			CreatedAt:      item.CreatedAt,
			UpdatedAt:      item.UpdatedAt,
		}
	}

	h.RespOK(ctx, result)
}

// CreateAction create predefined action
func (h *SystemHandler) CreateAction(ctx *gin.Context) {
	var input sysService.CreateActionParam
	err := ctx.Bind(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("params required"))
		return
	}

	model, err := h.ProcessEngine.GetSystemService().CreateAction(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithField(logger.FieldKeyPrefix, "SystemHandler").
			WithError(err).
			Error("<SystemHandler.CreateAction> SystemService.CreateAction")
		return
	}

	h.RespOK(ctx, ActionItem{
		ID:             model.ID,
		Code:           model.Code,
		Name:           model.Name,
		Service:        model.Service,
		UseServiceAuth: model.UseServiceAuth,
		CustomAuth:     model.CustomAuth,
		URL:            model.URL,
		Method:         model.Method,
		Header:         model.Header,
		Body:           model.Body,
		StoreKey:       model.StoreKey,
		ParseKey:       model.ParseKey,
		ParseFirst:     model.ParseFirst,
		MaxRetry:       model.MaxRetry,
		CreatedAt:      model.CreatedAt,
		UpdatedAt:      model.UpdatedAt,
	})
}

// UpdateAction update predefined Action
func (h *SystemHandler) UpdateAction(ctx *gin.Context) {

	executionID, err := h.GetParamID(ctx)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	var input sysService.UpdateActionParam
	err = ctx.Bind(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if !input.Valid() {
		h.RespErr(ctx, http.StatusBadRequest, errors.New("params required"))
		return
	}

	err = h.ProcessEngine.GetSystemService().UpdateAction(executionID, &input)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).
			WithField(logger.FieldKeyPrefix, "SystemHandler").
			WithError(err).
			Error("<SystemHandler.UpdateAction> SystemService.UpdateAction")
		return
	}

	h.RespOK(ctx, struct{}{})
}
