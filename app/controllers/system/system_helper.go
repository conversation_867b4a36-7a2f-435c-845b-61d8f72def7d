package system

import (
	"time"

	"qiniu.io/qbpm/engine/models/enums"
)

type ServiceItem struct {
	ID        uint64         `json:"id,string"`
	Code      string         `json:"code"`
	Name      string         `json:"name"`
	Host      string         `json:"host"`
	Auth      enums.AuthType `json:"auth"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
}

type ActionItem struct {
	ID             uint64         `json:"id,string"`
	Code           string         `json:"code"`
	Name           string         `json:"name"`
	Service        uint64         `json:"service,string"`
	UseServiceAuth bool           `json:"use_service_auth"`
	CustomAuth     enums.AuthType `json:"custom_auth"`
	URL            string         `json:"url"`
	Method         string         `json:"method"`
	Header         string         `json:"header"`
	Body           string         `json:"body"`
	StoreKey       string         `json:"store_key"`
	ParseKey       string         `json:"parse_key"`
	ParseFirst     bool           `json:"parse_first"`
	MaxRetry       int            `json:"max_retry"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
}
