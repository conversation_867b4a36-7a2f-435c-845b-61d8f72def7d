package controllers

import (
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	adminv2Client "github.com/qbox/pay-sdk/adminv2/client"
	"github.com/qbox/pay-sdk/middleware/user"
	sofaClient "github.com/qbox/pay-sdk/sofa/client"

	sf_user "github.com/qbox/pay-sdk/sofa/client/user"

	clientUser "github.com/qbox/pay-sdk/adminv2/client/user"
)

const (
	RequestUserEmailKey = "X-QINIU-ADMIN-EMAIL"
)

func GetReqUserEmail(ctx *gin.Context) string {
	return ctx.GetHeader(RequestUserEmailKey)
}

const UserContextKey = "qiniu_admin_user_info"

func UserMiddleware(adminCli adminv2Client.Adminv2, sofaCli sofaClient.Sofa) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		email := GetReqUserEmail(ctx)

		user := user.User{Email: email}

		userPermissions, err := adminCli.User.ListUserPermissionDetails(&clientUser.ListUserPermissionDetailsParams{
			Email:   email,
			Context: ctx,
		})
		if err != nil {
			logrus.WithField("email", email).
				WithError(err).
				Error("<UserMiddleware> admin get user permission details error")
		} else {
			if userPermissions.Payload != nil {
				if userPermissions.Payload.Code != 200 {
					logrus.WithField("email", email).
						WithError(errors.New(userPermissions.Payload.Message)).
						Error("<UserMiddleware> admin get user permission details error")
				} else {
					if userPermissions.Payload.Data != nil {
						user.Roles = userPermissions.Payload.Data.Roles
					}
				}
			}
		}

		sfUser, err := sofaCli.User.GetUser(&sf_user.GetUserParams{
			Context: ctx,
			Email:   &email,
		})
		if err != nil {
			logrus.WithField("email", email).
				WithError(err).
				Error("<UserMiddleware> sofa get user info error")
		} else {
			if sfUser.Payload != nil {
				if sfUser.Payload.Status != 0 {
					logrus.WithField("email", email).
						WithError(errors.New(sfUser.Payload.Message)).
						Error("<UserMiddleware> sofa get user info error")
				} else {
					if sfUser.Payload.Data != nil {
						user.SFInfo = sfUser.Payload.Data
					}
				}
			}
		}

		ctx.Set(UserContextKey, user)

		ctx.Next()
	}
}

func GetReqUser(ctx *gin.Context) *user.User {
	v, exists := ctx.Get(UserContextKey)
	if !exists {
		return nil
	}
	u := v.(user.User)
	return &u
}

func IsProcessViewer(user *user.User) bool {
	return isRole(user, "process-op", "process-r")
}

func isRole(user *user.User, roles ...string) bool {
	if user == nil {
		return false
	}

	for _, role := range user.Roles {
		for _, target := range roles {
			if target == role.Name {
				return true
			}
		}
	}

	return false
}
