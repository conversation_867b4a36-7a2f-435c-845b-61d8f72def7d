package approval

import (
	"errors"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/schema"
	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/app/env/config"
	"qiniu.io/qbpm/engine/lib/gaea"
	"qiniu.io/qbpm/engine/lib/sofa"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	hisService "qiniu.io/qbpm/engine/services/history"
	runtimeService "qiniu.io/qbpm/engine/services/runtime"
)

type ApprovalHandler struct {
	*controllers.Base
}

type ApprovalListParams struct {
	ViewType             enums.ViewType               `schema:"view_type"`
	ProcessInstanceID    *string                      `schema:"process_instance_id"`
	ProcessDefinitionKey *string                      `schema:"process_definition_key"`
	UID                  *uint64                      `schema:"uid"`
	Assignee             string                       `schema:"assignee"`
	Status               *enums.ProcessInstanceStatus `schema:"status"`
	Starter              string                       `schema:"starter"`
	Page                 int                          `schema:"page"`
	PageSize             int                          `schema:"page_size"`
}

func (p *ApprovalListParams) Valid() error {
	if p.ViewType != enums.Assignee && p.ViewType != enums.Starter {
		return errors.New("invalid view_type")
	}

	return nil
}

type UserInfo struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type ActorItem struct {
	Name      string `json:"name"`
	Email     string `json:"email"`
	StartTime string `json:"start_time"`
}

// PendingActor 当前审批人信息
type PendingActor struct {
	Node   string      `json:"node"`
	Actors []ActorItem `json:"actors"`
}

// ApprovalResponse 审批列表响应
type ApprovalResponse struct {
	ID            string                      `json:"id"`
	InstanceID    string                      `json:"instance_id"`
	ApprovalType  string                      `json:"approval_type"`
	Status        enums.ProcessInstanceStatus `json:"status"`
	UID           int                         `json:"uid"`
	PendingActors []PendingActor              `json:"pending_actors"`
	IsUrged       bool                        `json:"is_urged"`
	AccountName   string                      `json:"account_name"`
	AccountType   string                      `json:"account_type"`
	AccountLevel  string                      `json:"account_level"`
	StartTime     string                      `json:"start_time"`
	EndTime       string                      `json:"end_time"`
	Initiator     UserInfo                    `json:"initiator"`
	Excode        string                      `json:"excode"`
}

// ApprovalListResponse 审批列表响应
type ApprovalListResponse struct {
	Total int                `json:"total"`
	List  []ApprovalResponse `json:"list"`
}

// 审批列表
func (h *ApprovalHandler) ApprovalList(ctx *gin.Context) {
	input := new(ApprovalListParams)
	err := schema.NewDecoder().Decode(input, ctx.Request.URL.Query())
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if err = input.Valid(); err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	currentUser := controllers.GetReqUser(ctx)

	if currentUser == nil {
		h.RespErr(ctx, http.StatusUnauthorized, errors.New("unauthorized"))
		return
	}

	assignees := []string{}
	starters := []string{}

	var isProcessViewer = controllers.IsProcessViewer(currentUser)

	// 非管理者只看自己审批
	if input.ViewType == enums.Assignee && !isProcessViewer {
		assignees = append(assignees, currentUser.Email)
		if input.Starter != "" {
			starters = append(starters, input.Starter)
		}
	}

	// 非管理者只看自己发起
	if input.ViewType == enums.Starter && !isProcessViewer {
		starters = append(starters, currentUser.Email)
	}

	// 管理者可以查看指定审批人，管理员功能，查看全部下的筛选
	if input.ViewType == enums.Assignee && isProcessViewer {
		if input.Starter != "" {
			starters = append(starters, input.Starter)
		}
		if input.Assignee != "" {
			assignees = append(assignees, input.Assignee)
		}
	}

	// 管理员查看指定发起人，管理员功能，查看全部下的筛选
	if input.ViewType == enums.Starter && isProcessViewer {
		if input.Starter != "" {
			starters = append(starters, input.Starter)
		}
	}

	var processList []hisService.ProcessListResult
	var runExecutions []runtimeService.SearchExecutionResult
	var count int

	if input.Status != nil && *input.Status == enums.ProcessStatusPending && !isProcessViewer {
		statusPending := enums.RunExecutionStatusPending
		runExecutions, count, err = h.ProcessEngine.GetRuntimeService().SearchExecutions(runtimeService.RunExecutionQuery{
			ProcessDefinitionKey: input.ProcessDefinitionKey,
			UID:                  input.UID,
			Assignees:            assignees,
			Starters:             starters,
			Status:               &statusPending,
			Page:                 input.Page,
			PageSize:             input.PageSize,
			ListByProcess:        true,
		})

		if err != nil {
			h.RespErr(ctx, http.StatusInternalServerError, err)
			h.ReqLogger(ctx).WithField("params", input).
				WithError(err).
				Error("<ApprovalHandler.ApprovalList> RuntimeService.SearchExecutions")
			return
		}

		// RunExecution 转换为 ProcessInstance 的完整映射
		for _, execution := range runExecutions {
			processList = append(processList, hisService.ProcessListResult{
				ProcessInstance: models.ProcessInstance{
					Base: models.Base{
						ID:        execution.ProcessInstanceID,
						CreatedAt: execution.CreatedAt,
						UpdatedAt: execution.UpdatedAt,
					},
					ProcessDefinitionID:    execution.ProcessDefinitionID,
					SuperProcessInstanceID: execution.ProcessInstanceID,
					Name:                   execution.Name,
					Excode:                 execution.Excode,
					UID:                    execution.UID,
					Params:                 execution.Params,
					StartByID:              execution.StartByID,
					StartAt:                execution.StartAt,
					Status:                 enums.ProcessStatusPending,
				},
				ProcessDefinitionKey: execution.ProcessDefinitionKey,
				HasUrge:              execution.HasUrge,
			})
		}
	} else {
		// 获取工作流列表
		processList, count, err = h.ProcessEngine.GetHistoryService().ListProcess(hisService.ProcessListParam{
			ProcessInstanceID:    input.ProcessInstanceID,
			ProcessDefinitionKey: input.ProcessDefinitionKey,
			UID:                  input.UID,
			Assignees:            assignees,
			Status:               input.Status,
			Starters:             starters,
			Page:                 input.Page,
			PageSize:             input.PageSize,
		})

		if err != nil {
			h.RespErr(ctx, http.StatusInternalServerError, err)
			h.ReqLogger(ctx).WithField("params", input).
				WithError(err).
				Error("<ApprovalHandler.ApprovalList> HistoryService.ListProcess")
			return
		}
	}

	// 如果没有数据，直接返回空结果
	if len(processList) == 0 {
		h.RespOK(ctx, ApprovalListResponse{Total: 0, List: []ApprovalResponse{}})
		return
	}

	result := make([]ApprovalResponse, 0, len(processList))

	// 获取待处理活动
	pendingActivitiesMap := make(map[uint64][]PendingActor)
	var pendingActivitiesLock sync.Mutex

	var wg sync.WaitGroup
	for _, process := range processList {

		if process.Status == enums.ProcessStatusPending {
			// 并发查询每个流程的待处理活动
			wg.Add(1)
			go func(processID uint64) {
				defer wg.Done()

				pendingStatus := enums.ActivityStatusPending
				activities, actErr := h.ProcessEngine.GetHistoryService().ListActivity(hisService.ActivityListParam{
					ProcessInstanceID: &processID,
					Status:            &pendingStatus,
				})

				if actErr != nil {
					h.ReqLogger(ctx).WithError(actErr).Error("获取流程活动失败")
					return
				}

				// 将活动转换为PendingActor
				pendingActors := make([]PendingActor, 0, len(activities))
				actorEmails := make([]string, 0, len(activities))
				for _, activity := range activities {
					actor := PendingActor{
						Node: activity.Name,
						Actors: []ActorItem{{
							Name:      activity.Actor,
							Email:     activity.Actor,
							StartTime: activity.StartAt.Format(time.RFC3339),
						}},
					}
					pendingActors = append(pendingActors, actor)
					actorEmails = append(actorEmails, activity.Actor)
				}

				adminUsers, err := GetAdminUsersMapByEmails(h.SofaClient, actorEmails, ctx)

				if err != nil {
					h.ReqLogger(ctx).WithField("params", actorEmails).
						WithError(err).
						Error("<ApprovalHandler.ApprovalList> GetAdminUsersMapByEmails")
				}

				for i := range pendingActors {
					for j := range pendingActors[i].Actors {
						if user, ok := adminUsers[pendingActors[i].Actors[j].Email]; ok {
							pendingActors[i].Actors[j].Name = user.Name
						}
					}
				}

				pendingActivitiesLock.Lock()
				pendingActivitiesMap[processID] = pendingActors
				pendingActivitiesLock.Unlock()
			}(process.ID)
		}
	}

	// 等待所有活动查询完成
	wg.Wait()

	uids := make([]uint64, 0, len(processList))
	starterEmails := make([]string, 0, len(processList))
	for _, process := range processList {
		uids = append(uids, process.UID)
		starterEmails = append(starterEmails, process.StartByID)
	}

	userLevels := make(map[uint32]string)
	userAccounts := make(map[uint32]sofa.Account)
	emailUsers := make(map[string]gaea.AdminUser)
	var wg2 sync.WaitGroup
	wg2.Add(3)

	// 请求用户级别信息
	go func() {
		defer wg2.Done()
		levels, levelErr := h.GaeaAdminService.GetLevelsByUids(uids)
		if levelErr != nil {
			h.ReqLogger(ctx).WithField("params", uids).
				WithError(levelErr).
				Error("<ApprovalHandler.ApprovalList> GaeaAdminService.GetLevelsByUids")
			return
		}

		for _, level := range levels {
			userLevels[level.Uid] = level.Level
		}
	}()

	// 请求用户账户信息
	go func() {
		defer wg2.Done()
		accounts, accountErr := h.SofaService.GetAccountsByUids(uids)
		if accountErr != nil {
			h.ReqLogger(ctx).WithField("params", uids).
				WithError(accountErr).
				Error("<ApprovalHandler.ApprovalList> SofaService.GetAccountsByUids")
			return
		}

		for _, account := range accounts {
			userAccounts[account.UID] = account
		}
	}()

	// 获取发起人账号信息
	go func() {
		defer wg2.Done()
		users, err := h.GaeaAdminService.ListUserByEmails(starterEmails)
		if err != nil {
			h.ReqLogger(ctx).WithField("params", starterEmails).
				WithError(err).
				Error("<ApprovalHandler.ApprovalList> GetAdminUsersMapByEmails")
		}
		for _, user := range users {
			emailUsers[user.Email] = user
		}
	}()

	// 等待所有goroutine完成
	wg2.Wait()

	// 构建响应结果
	for _, process := range processList {
		approvalResp := ApprovalResponse{
			ID:           fmt.Sprintf("%d", process.ID),
			InstanceID:   fmt.Sprintf("%d", process.ID),
			ApprovalType: process.ProcessDefinitionKey,
			Status:       process.Status,
			UID:          int(process.UID),
			StartTime:    process.StartAt.Format(time.RFC3339),
			Excode:       process.Excode,
			IsUrged:      process.HasUrge,
		}

		// 设置结束时间
		if !process.EndAt.IsZero() {
			approvalResp.EndTime = process.EndAt.Format(time.RFC3339)
		}

		// 设置用户级别
		if level, exists := userLevels[uint32(process.UID)]; exists {
			approvalResp.AccountLevel = level
		}

		// 设置用户账户信息
		if account, exists := userAccounts[uint32(process.UID)]; exists {
			approvalResp.AccountName = account.AccountName
			approvalResp.AccountType = account.AccountRecordTypeId
		}

		// 设置待处理活动
		approvalResp.PendingActors = pendingActivitiesMap[process.ID]

		// 发起人信息
		approvalResp.Initiator = UserInfo{
			Name:  emailUsers[process.StartByID].CnName,
			Email: process.StartByID,
		}

		result = append(result, approvalResp)
	}

	resp := ApprovalListResponse{
		Total: count,
		List:  result,
	}

	h.RespOK(ctx, resp)
}

// 审批类型发起次数排序
func (h *ApprovalHandler) ApprovalTypeSortByCount(ctx *gin.Context) {
	currentUserEmail := controllers.GetReqUserEmail(ctx)
	if currentUserEmail == "" {
		h.RespErr(ctx, http.StatusUnauthorized, errors.New("unauthorized"))
		return
	}

	sortedList, err := h.ProcessEngine.GetHistoryService().SortApprovalTypeByCount(hisService.SortApprovalTypeByCountParam{
		UserId: currentUserEmail,
		Days:   config.Conf.ApprovalTypeOrder.Days,
	})
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField("params", currentUserEmail).
			WithError(err).
			Error("<ApprovalHandler.ApprovalTypeSort> HistoryService.SortApprovalTypeByCount")
		return
	}
	h.RespOK(ctx, sortedList)
}

type ApprovalActivityLogParams struct {
	Page      int    `schema:"page"`
	PageSize  int    `schema:"page_size"`
	StartTime string `schema:"start_time"`
	EndTime   string `schema:"end_time"`
}

func (h *ApprovalHandler) ProcessActivityLogList(ctx *gin.Context) {
	input := new(ApprovalActivityLogParams)
	err := schema.NewDecoder().Decode(input, ctx.Request.URL.Query())

	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	currentUserEmail := controllers.GetReqUserEmail(ctx)
	if currentUserEmail == "" {
		h.RespErr(ctx, http.StatusUnauthorized, errors.New("unauthorized"))
		return
	}

	var startTimePtr, endTimePtr *time.Time
	if input.StartTime != "" {
		if t, err := time.Parse(time.DateOnly, input.StartTime); err == nil {
			startTimePtr = &t
		}
	}
	if input.EndTime != "" {
		if t, err := time.Parse(time.DateOnly, input.EndTime); err == nil {
			endTimePtr = &t
		}
	}

	list, count, err := h.ProcessEngine.GetHistoryService().ListProcessActivityLogByUser(hisService.ListProcessActivityLogByUserParam{
		User:      currentUserEmail,
		Page:      input.Page,
		PageSize:  input.PageSize,
		StartTime: startTimePtr,
		EndTime:   endTimePtr,
	})

	userAccounts := make(map[uint64]sofa.Account)
	uids := make([]uint64, 0, len(list))

	for _, item := range list {
		uids = append(uids, item.Uid)
	}

	if len(uids) > 0 {
		accounts, accountErr := h.SofaService.GetAccountsByUids(uids)
		if accountErr != nil {
			h.RespErr(ctx, http.StatusInternalServerError, accountErr)
			h.ReqLogger(ctx).WithField("params", uids).
				WithError(accountErr).
				Error("<ApprovalHandler.ProcessActivityLogList> SofaService.GetAccountsByUids")
			return
		}

		for _, account := range accounts {
			userAccounts[uint64(account.UID)] = account
		}
	}

	for i, item := range list {
		if account, exists := userAccounts[item.Uid]; exists {
			list[i].AccountName = account.AccountName
		}
	}

	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField("params", input).
			WithError(err).
			Error("<ApprovalHandler.ListProcessActivityLog> HistoryService.ListProcessActivityLog")
		return
	}
	h.RespOK(ctx, map[string]interface{}{
		"list":  list,
		"total": count,
	})
}
