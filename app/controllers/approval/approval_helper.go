package approval

import (
	"context"
	"strings"

	sofaClient "github.com/qbox/pay-sdk/sofa/client"
	sofaClientUser "github.com/qbox/pay-sdk/sofa/client/user"
	sofaModal "github.com/qbox/pay-sdk/sofa/models"
)

type AdminUsersMap struct {
}

func GetAdminUsersMapByEmails(sofaClient sofaClient.Sofa, emails []string, ctx context.Context) (res map[string]sofaModal.UserDetail, err error) {
	emailsStr := strings.Join(emails, ",")
	res = make(map[string]sofaModal.UserDetail)
	ret, err := sofaClient.User.ListUser(&sofaClientUser.ListUserParams{
		Emails:  &emailsStr,
		Context: ctx,
	})
	if err != nil {
		return
	}

	if ret.Payload == nil || ret.Payload.Data == nil {
		return
	}

	for _, userDeail := range ret.Payload.Data {
		res[userDeail.Email] = *userDeail
	}

	return
}
