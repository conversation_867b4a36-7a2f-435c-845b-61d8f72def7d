package repository

import (
	"time"

	"qiniu.io/qbpm/engine/models/enums"
)

type (
	// ProcessDefInput input data for create process
	ProcessDefInput struct {
		Name        string    `json:"name" form:"name"`
		Key         string    `json:"key" form:"key"`
		DeployAt    time.Time `json:"deploy_at" form:"deploy_at"`
		Active      bool      `json:"active" form:"active"`
		Description string    `json:"description" form:"description"`
	}
	// ProcessDefUpdate update param for process definition
	ProcessDefUpdate struct {
		Type             IDType     `json:"type" form:"type"`
		Action           ActionType `json:"action" form:"action"`
		IncludeInstances bool       `json:"include_instances" form:"include_instances"`
	}

	// ProcessDefList List param for process definition
	ProcessDefList struct {
		Key      string                  `schema:"type"`
		Name     string                  `schema:"name"`
		Status   *enums.ProcessDefStatus `schema:"status"`
		Page     int                     `schema:"page"`
		PageSize int                     `schema:"page_size"`
	}

	SearchProcessByName struct {
		Name     string `form:"name"`
		Page     int    `form:"page"`
		PageSize int    `form:"page_size"`
	}

	// ActionType activate or suspend
	ActionType string
	// IDType id is default, other values can be 'key'
	IDType string
)

const (
	// ActionTypeActivate activate
	ActionTypeActivate ActionType = "activate"
	// ActionTypeSuspend suspend
	ActionTypeSuspend ActionType = "suspend"
)

const (
	// IDTypeDefault default id
	IDTypeDefault IDType = "id"
	// IDTypeKey key
	IDTypeKey IDType = "key"
)

// IsValid Return if action type is valid
func (t ActionType) IsValid() bool {
	return t == ActionTypeActivate || t == ActionTypeSuspend
}

// IsValid Return if id type is valid
func (t IDType) IsValid() bool {
	return t == IDTypeDefault || t == IDTypeKey
}
