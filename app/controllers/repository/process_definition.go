package repository

import (
	"bufio"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/schema"
	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	repoService "qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/util/logger"
)

// DeployHandler handler for repository
type DeployHandler struct {
	*controllers.Base
}

// Deploy Deploy process definition
func (h *DeployHandler) Deploy(ctx *gin.Context) {
	file, _, err := ctx.Request.FormFile("data")
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	data, err := io.ReadAll(bufio.NewReader(file))
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	var input ProcessDefInput
	err = ctx.Bind(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if input.Name == "" && input.Key == "" {
		h.RespErr(ctx, http.StatusBadRequest, fmt.Errorf("name or key required"))
		return
	}

	if input.Name == "" {
		input.Name = input.Key
	} else if input.Key == "" {
		input.Key = input.Name
	}

	var model = &models.ProcessDefinition{
		Name:        input.Name,
		Key:         input.Key,
		XMLData:     data,
		DeployAt:    input.DeployAt,
		Description: input.Description,
		Status:      enums.ProcessDefStatusNew,
	}
	if input.Active {
		model.Status = enums.ProcessDefStatusActive

	}

	err = h.ProcessEngine.GetRepositoryService().Create(model)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "DeployHandler").
			WithField("name", input.Name).
			WithField("key", input.Key).
			WithError(err).
			Error("<DeployHandler.Deploy> RepositoryService.Create")
		return
	}

	h.RespOK(ctx, map[string]uint64{"id": model.ID})
}

// List List process definitions
func (h *DeployHandler) List(ctx *gin.Context) {
	input := new(ProcessDefList)
	err := schema.NewDecoder().Decode(input, ctx.Request.URL.Query())
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	processes, err := h.ProcessEngine.GetRepositoryService().ListProcess(repoService.ProcessDefList{
		Key:      input.Key,
		Name:     input.Name,
		Status:   input.Status,
		Page:     input.Page,
		PageSize: input.PageSize,
	})
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "DeployHandler").
			WithField("params", input).
			WithError(err).
			Error("<DeployHandler.List> RepositoryService.ListProcess")
		return
	}

	h.RespOK(ctx, processes)
}

// Get Get process definition
func (h *DeployHandler) Get(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		h.RespErr(ctx, http.StatusBadRequest, fmt.Errorf("id required"))
		return
	}

	var (
		process models.ProcessDefinition
		err     error
		idUint  uint64
	)

	_type := ctx.DefaultQuery("type", "id")
	switch _type {
	case "id", "process_instance_id":
		idUint, err = strconv.ParseUint(id, 10, 64)
		if err != nil {
			h.RespErr(ctx, http.StatusBadRequest, fmt.Errorf("invalid id"))
			return
		}

		if _type == "process_instance_id" {
			processInstance, err := h.ProcessEngine.GetHistoryService().GetProcessByID(idUint)
			if err != nil {
				h.RespErr(ctx, http.StatusInternalServerError, err)
				return
			}

			idUint = processInstance.ProcessDefinitionID
		}

		process, err = h.ProcessEngine.GetRepositoryService().GetProcessByID(idUint)
	case "key":
		process, err = h.ProcessEngine.GetRepositoryService().GetProcessByKey(id)
	default:
		h.RespErr(ctx, http.StatusBadRequest, fmt.Errorf("invalid id type"))
		return
	}

	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "DeployHandler").
			WithField("id", id).
			WithField("type", _type).
			WithError(err).
			Error("<DeployHandler.Get> RepositoryService.Get")
		return
	}

	h.RespOK(ctx, process)
}

// Update Update process definition
func (h *DeployHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	if idStr == "" {
		h.RespErr(ctx, http.StatusBadRequest, fmt.Errorf("id required"))
		return
	}

	var param ProcessDefUpdate
	err := ctx.Bind(&param)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}

	if param.Type == "" {
		param.Type = IDTypeDefault
	}
	if !param.Type.IsValid() {
		h.RespErr(ctx, http.StatusBadRequest, fmt.Errorf("invalid id type"))
		return
	}

	if !param.Action.IsValid() {
		h.RespErr(ctx, http.StatusBadRequest, fmt.Errorf("invalid action type"))
		return
	}

	var id uint64

	if param.Type == IDTypeDefault {
		id, err = strconv.ParseUint(idStr, 10, 64)
		if err != nil {
			h.RespErr(ctx, http.StatusBadRequest, fmt.Errorf("invalid id"))
			return
		}

		if param.Action == ActionTypeActivate {
			err = h.ProcessEngine.GetRepositoryService().ActivateProcessByID(id, param.IncludeInstances)
		} else {
			err = h.ProcessEngine.GetRepositoryService().SuspendProcessByID(id, param.IncludeInstances)
		}
	} else {
		if param.Action == ActionTypeActivate {
			err = h.ProcessEngine.GetRepositoryService().ActivateProcessByKey(idStr, param.IncludeInstances)
		} else {
			err = h.ProcessEngine.GetRepositoryService().SuspendProcessByKey(idStr, param.IncludeInstances)
		}
	}

	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField("id", id).
			WithField("params", param).
			WithField(logger.FieldKeyPrefix, "DeployHandler").
			WithError(err).
			Error("<DeployHandler.Update> RepositoryService.Update")
		return
	}

	h.RespOK(ctx, nil)
}

// SearchProcessByName Search process by process name
func (h *DeployHandler) SearchProcessByName(ctx *gin.Context) {
	var input SearchProcessByName
	err := ctx.BindQuery(&input)
	if err != nil {
		h.RespErr(ctx, http.StatusBadRequest, err)
		return
	}
	offset, limit := controllers.Paging(input.Page, input.PageSize)
	processes, err := h.ProcessEngine.GetRepositoryService().SearchProcessByName(
		repoService.SearchProcessByNameParam{
			Name:   input.Name,
			Limit:  limit,
			Offset: offset,
		})
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "DeployHandler").
			WithField("params", input).
			WithError(err).
			Error("RepositoryService.SearchProcessByName failed")
		return
	}

	count, err := h.ProcessEngine.GetRepositoryService().CountProcessByName(input.Name)
	if err != nil {
		h.RespErr(ctx, http.StatusInternalServerError, err)
		h.ReqLogger(ctx).WithField(logger.FieldKeyPrefix, "DeployHandler").
			WithField("params", input).
			WithError(err).
			Error("RepositoryService.CountProcessByName failed")
		return
	}
	type searchResult struct {
		ProcessDefinitions []models.ProcessDefinition `json:"process_definitions"`
		Count              int64                      `json:"count"`
	}
	h.RespOK(ctx, searchResult{ProcessDefinitions: processes, Count: count})
}
