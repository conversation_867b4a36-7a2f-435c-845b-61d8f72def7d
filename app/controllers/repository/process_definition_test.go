package repository_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"

	"github.com/gin-gonic/gin"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/app/controllers"
	. "qiniu.io/qbpm/app/controllers/repository"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("ProcessDefinition", func() {
	var handler DeployHandler
	var srv = gin.Default()
	srv.POST("/api/repository/process-definitions", handler.Deploy)
	srv.GET("/api/repository/process-definitions", handler.List)
	srv.GET("/api/repository/process-definitions/:id", handler.Get)
	srv.PUT("/api/repository/process-definitions/:id", handler.Update)

	BeforeEach(func() {
		engine := test.InitEngine()
		base := &controllers.Base{ProcessEngine: engine}
		handler = DeployHandler{Base: base}
	})

	Describe("DeployController", func() {

		Context("Deploy", func() {

			It("should deploy 1 model with version 1", func() {
				w := httptest.NewRecorder()
				req, _ := http.NewRequest("POST", "/api/repository/process-definitions", nil)
				srv.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(400))
			})
		})

		Context("List", func() {
			JustBeforeEach(func() {
				model := &models.ProcessDefinition{
					Name: "testRepo",
					Key:  "testRepo",
					XMLData: []byte(
						`<definitions>
						<process>
						</process>
						</definitions>`),
				}
				err := handler.ProcessEngine.GetRepositoryService().Create(model)
				Expect(err).To(BeNil())
			})

			It("should get 1 model", func() {
				w := httptest.NewRecorder()
				req, _ := http.NewRequest("GET", "/api/repository/process-definitions", nil)
				srv.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(200))

				var defs []models.ProcessDefinition
				err := json.Unmarshal([]byte(w.Body.String()), &defs)
				Expect(err).To(BeNil())
				Expect(len(defs)).To(Equal(1))
			})

			It("should get 0 model", func() {
				w := httptest.NewRecorder()
				req, _ := http.NewRequest("GET", "/api/repository/process-definitions?name=test", nil)
				srv.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(200))

				var defs []models.ProcessDefinition
				err := json.Unmarshal([]byte(w.Body.String()), &defs)
				Expect(err).To(BeNil())
				Expect(len(defs)).To(Equal(0))
			})

			It("should get 1 model", func() {
				w := httptest.NewRecorder()
				req, _ := http.NewRequest("GET", "/api/repository/process-definitions?name=testRepo", nil)
				srv.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(200))

				var defs []models.ProcessDefinition
				err := json.Unmarshal([]byte(w.Body.String()), &defs)
				Expect(err).To(BeNil())
				Expect(len(defs)).To(Equal(1))
			})
		})

		Context("Get", func() {
			var model *models.ProcessDefinition
			JustBeforeEach(func() {
				model = &models.ProcessDefinition{
					Name: "testRepo",
					Key:  "testRepo",
					XMLData: []byte(
						`<definitions>
						<process>
						</process>
						</definitions>`),
				}
				err := handler.ProcessEngine.GetRepositoryService().Create(model)
				Expect(err).To(BeNil())
			})

			It("should get 1 model by id", func() {
				w := httptest.NewRecorder()
				req, _ := http.NewRequest("GET", fmt.Sprintf("/api/repository/process-definitions/%d", model.ID), nil)
				srv.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(200))

				var m models.ProcessDefinition
				err := json.Unmarshal([]byte(w.Body.String()), &m)
				Expect(err).To(BeNil())
				Expect(m.ID).To(Equal(model.ID))
			})

			It("should get 1 model by key", func() {
				w := httptest.NewRecorder()
				req, _ := http.NewRequest("GET", fmt.Sprintf("/api/repository/process-definitions/%s?type=key", model.Key), nil)
				srv.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(200))

				var m models.ProcessDefinition
				err := json.Unmarshal([]byte(w.Body.String()), &m)
				Expect(err).To(BeNil())
				Expect(m.ID).To(Equal(model.ID))
			})

			It("should get 0 model", func() {
				w := httptest.NewRecorder()
				req, _ := http.NewRequest("GET", "/api/repository/process-definitions/123", nil)
				srv.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(404))
			})

			It("should return 400", func() {
				w := httptest.NewRecorder()
				req, _ := http.NewRequest("GET", "/api/repository/process-definitions/12a", nil)
				srv.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(400))
			})
		})

		Context("Update", func() {
			var model *models.ProcessDefinition
			JustBeforeEach(func() {
				model = &models.ProcessDefinition{
					Name: "testRepo",
					Key:  "testRepo",
					XMLData: []byte(
						`<definitions>
						<process>
						</process>
						</definitions>`),
				}
				err := handler.ProcessEngine.GetRepositoryService().Create(model)
				Expect(err).To(BeNil())
			})

			It("should activate and then suspend 1 model by id", func() {
				param := ProcessDefUpdate{
					Action: ActionTypeActivate,
				}
				paramBytes, _ := json.Marshal(param)
				w := httptest.NewRecorder()

				req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/repository/process-definitions/%d", model.ID), bytes.NewReader(paramBytes))
				req.Header = http.Header{"Content-Type": []string{"application/json"}}
				srv.ServeHTTP(w, req)
				Expect(w.Code).To(Equal(200))

				m, err := handler.ProcessEngine.GetRepositoryService().GetProcessByKey(model.Key)
				Expect(err).To(BeNil())
				Expect(m.Status).To(Equal(enums.ProcessDefStatusActive))

				param.Action = ActionTypeSuspend
				paramBytes, _ = json.Marshal(param)
				w = httptest.NewRecorder()

				req, _ = http.NewRequest("PUT", fmt.Sprintf("/api/repository/process-definitions/%d", model.ID), bytes.NewReader(paramBytes))
				req.Header = http.Header{"Content-Type": []string{"application/json"}}
				srv.ServeHTTP(w, req)
				Expect(w.Code).To(Equal(200))

				m, err = handler.ProcessEngine.GetRepositoryService().GetProcessByKey(model.Key)
				Expect(err).To(BeNil())
				Expect(m.Status).To(Equal(enums.ProcessDefStatusSuspended))

			})

			It("should activate and then suspend 1 model by key", func() {
				param := ProcessDefUpdate{
					Action: ActionTypeActivate,
					Type:   IDTypeKey,
				}
				paramBytes, _ := json.Marshal(param)
				w := httptest.NewRecorder()

				req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/repository/process-definitions/%s", model.Key), bytes.NewReader(paramBytes))
				req.Header = http.Header{"Content-Type": []string{"application/json"}}
				srv.ServeHTTP(w, req)
				Expect(w.Code).To(Equal(200))

				m, err := handler.ProcessEngine.GetRepositoryService().GetProcessByKey(model.Key)
				Expect(err).To(BeNil())
				Expect(m.Status).To(Equal(enums.ProcessDefStatusActive))

				param.Action = ActionTypeSuspend
				paramBytes, _ = json.Marshal(param)
				w = httptest.NewRecorder()

				req, _ = http.NewRequest("PUT", fmt.Sprintf("/api/repository/process-definitions/%s", model.Key), bytes.NewReader(paramBytes))
				req.Header = http.Header{"Content-Type": []string{"application/json"}}
				srv.ServeHTTP(w, req)
				Expect(w.Code).To(Equal(200))

				m, err = handler.ProcessEngine.GetRepositoryService().GetProcessByKey(model.Key)
				Expect(err).To(BeNil())
				Expect(m.Status).To(Equal(enums.ProcessDefStatusSuspended))
			})
		})
	})
})
