listen: ":6013"
mode: "release"
engine:
  name: "qbpm"
  version:  "0.1"
  debug: true
  is_json_log: false
  db:
    driver: "mysql"
    host: "localhost"
    port: 3306
    username: "root"
    password: ""
    db: "qbpm"
    charset: "utf8mb4"
    collation: "utf8mb4_unicode_ci"
    parse_time: true
    loc: "Local"
    max_open_conn: 100
    max_idle_conn: 100
    max_life_time: 5m
  redis:
    redis_config:
      db: 0 # defaults 0
      failover: false
      # master_name: "" # should enable when failover = true
      addrs:
        - "localhost:6379"
  services:
    morse:
      host: "http://localhost:9015"
    acc:
      host: "http://localhost:9100"
      username: "root"
      password: "root"
      client_id: "xxx"
      client_secret: "xxx"
  job_specs:
  # work_item_starter_wechat_notify: "0 18 * * *"
  # work_item_wechat_notify: "0 18 * * *"
  # work_item_auto_refuse_ddl: "* * * * *"

tracing:
  enable: false
  service_name: qbpm
  sampling_type: const
  sampling_param: 1
  sampling_refresh_interval:
  sampling_server_url:
  reporter_local_agent_endpoint: 127.0.0.1:6831
  reporter_collector_endpoint: http://127.0.0.1:14268/api/traces
  
# 统计相关配置
statistics:
  default_days: 10    # 默认统计天数
  overtime_days: 2    # 超时判定天数

approval_type_order:
  days: 30
