package config

import (
	"os"

	yaml "gopkg.in/yaml.v2"
	"qiniu.io/qbpm/engine/conf"
)

// Conf Config instance
var Conf Config

// Config config for app
type Config struct {
	Listen            string
	Engine            conf.Config            `yaml:"engine"`
	Tracing           conf.TracingConfig     `yaml:"tracing"`
	Mode              conf.RunMode           `yaml:"mode"`
	Statistics        conf.StatisticsConfig  `yaml:"statistics"`
	ApprovalTypeOrder conf.ApprovalTypeOrder `yaml:"approval_type_order"`
}

// ParseConfig Parse config
func ParseConfig(file string) error {
	confData, err := os.ReadFile(file)
	if err != nil {
		return err
	}

	err = yaml.Unmarshal(confData, &Conf)
	if err != nil {
		return err
	}

	if Conf.Mode == "" {
		Conf.Mode = conf.ProdMode
	}

	return err
}
