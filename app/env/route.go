package env

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/qbox/pay-sdk/base/account"

	httpTrace "github.com/qbox/sonic/sdk/trace/http"
	"qiniu.io/qbpm/app/controllers"
	"qiniu.io/qbpm/app/controllers/approval"
	"qiniu.io/qbpm/app/controllers/discussion"
	"qiniu.io/qbpm/app/controllers/history"
	"qiniu.io/qbpm/app/controllers/repository"
	"qiniu.io/qbpm/app/controllers/runtime"
	"qiniu.io/qbpm/app/controllers/system"
	"qiniu.io/qbpm/app/env/config"
	"qiniu.io/qbpm/engine"
	"qiniu.io/qbpm/engine/lib/gaea"
	"qiniu.io/qbpm/engine/lib/sofa"

	"github.com/go-openapi/strfmt"
	"github.com/qbox/bo-base/v3/rpc"
	adminv2Client "github.com/qbox/pay-sdk/adminv2/client"
	sofaClient "github.com/qbox/pay-sdk/sofa/client"
)

// InitRoutes Init gin route engine
func InitRoutes() *gin.Engine {
	gin.SetMode(string(config.Conf.Mode))
	router := gin.New()
	router.Use(Logger())
	router.Use(gin.Recovery())
	// router.Use(auth.NewHandler(config.Conf.Engine.Services.Acc.Host).Handle)

	transport, err := account.NewTransport(&config.Conf.Engine.Services.Acc)

	if err != nil {
		log.Fatalf("failed to init http client transport: %v", err)
	}

	cli := &http.Client{Transport: transport}

	adminTrp, err := rpc.NewSwaggerTransport(config.Conf.Engine.Services.PortalIOHost, cli)

	if err != nil {
		log.Fatalf("failed to init admin swagger transport: %v", err)
	}

	adminCli := adminv2Client.New(adminTrp, strfmt.Default)

	sofaTrp, err := rpc.NewSwaggerTransport(config.Conf.Engine.Services.SofaHost, cli)

	if err != nil {
		log.Fatalf("failed to init sofa swagger transport: %v", err)
	}

	sofaCli := sofaClient.New(sofaTrp, strfmt.Default)

	router.Use(controllers.UserMiddleware(*adminCli, *sofaCli))

	client := &http.Client{
		Transport: httpTrace.NewTransport(
			transport,
			httpTrace.OperationNameFunc(
				func(r *http.Request) string {
					return "send_to_http_service"
				},
			),
		),
	}

	gaeaAdminService := gaea.NewGaeaAdminService(config.Conf.Engine.Services.GaeaAdminHost, client)
	sofaService := sofa.NewSofaService(config.Conf.Engine.Services.SofaHost, client)

	base := controllers.Base{ProcessEngine: engine.GetProcessEngine(), GaeaAdminService: gaeaAdminService, SofaService: sofaService, SofaClient: *sofaCli}
	repositoryHandler := repository.DeployHandler{Base: &base}
	runtimeHandler := runtime.Handler{Base: &base}
	processHandler := history.ProcessHandler{Base: &base}
	activityHandler := history.ActivityHandler{Base: &base}
	listenerHandler := history.ListenerHandler{Base: &base}
	systemHandler := system.SystemHandler{Base: &base}
	discussionHandler := discussion.Handler{Base: &base}
	statisticsHandler := history.StatisticsHandler{Base: &base}
	approvalHandler := approval.ApprovalHandler{Base: &base}

	// api routes
	{
		r := router.Group("/api")

		r.GET("/ping", func(ctx *gin.Context) {
			ctx.JSON(http.StatusOK, gin.H{"message": "pong"})
		})

		repository := r.Group("repository")
		{
			def := repository.Group("process-definitions")
			{
				def.POST("", repositoryHandler.Deploy)
				def.GET("", repositoryHandler.List)
				def.GET(":id/search", repositoryHandler.SearchProcessByName)
				def.GET(":id", repositoryHandler.Get)
				def.PUT(":id", repositoryHandler.Update)
			}
		}

		runtime := r.Group("runtime")
		{
			execute := runtime.Group("executions")
			{
				execute.PUT("", runtimeHandler.StartProcess)
				execute.GET("", runtimeHandler.ListExecution)
				execute.POST("", runtimeHandler.BatchAssign)
				execute.GET(":id", runtimeHandler.GetExecution)
				execute.POST(":id", runtimeHandler.CompleteExecution)
				execute.POST(":id/urges", runtimeHandler.UrgeByExecution)
				execute.POST(":id/notify/assign", runtimeHandler.AssignNotify)
				execute.DELETE("", runtimeHandler.FailExecutionsByExcodes)
				execute.DELETE(":id", runtimeHandler.FailExecution)
				execute.PUT(":id", runtimeHandler.Assign)
				execute.PATCH(":id", runtimeHandler.CounterSignExecution)
				execute.GET(":id/counter-sign/config", runtimeHandler.GetCounterSignConfig)
			}
			process := runtime.Group("processes")
			{
				process.GET("", runtimeHandler.ListProcess)
				process.POST(":id/notify/complete", runtimeHandler.CompleteNotify)
				process.POST(":id/urges", runtimeHandler.UrgeByProcess)
				process.DELETE("", runtimeHandler.FailProcessesByExcodes)
				process.DELETE(":id", runtimeHandler.Cancel)
				process.POST(":id/suspend", runtimeHandler.SuspendProcess)
				process.POST(":id/resume", runtimeHandler.ResumeProcess)
				process.POST(":id/suspend/v2", runtimeHandler.SuspendProcessV2)
			}
		}

		history := r.Group("history")
		{
			process := history.Group("processes")
			{
				process.GET("", processHandler.List)
				process.GET(":id", processHandler.Get)
				process.GET(":id/activities", activityHandler.ListProcessActivity)
			}

			activity := history.Group("activities")
			{
				activity.GET("", activityHandler.List)
			}

			listener := history.Group("listeners")
			{
				listener.GET("", listenerHandler.List)
				listener.POST("", listenerHandler.Run)
				listener.PUT("/status", listenerHandler.UpdateListenerStatus)
				listener.PUT("/status/by/excode", listenerHandler.UpdateListenerStatusByExcode)
			}

			statistics := history.Group("statistics")

			{
				statistics.GET("/personal", statisticsHandler.GetPersonalStatistics)
				statistics.GET("/global", statisticsHandler.GetGlobalStatistics)
			}
		}

		system := r.Group("system")
		{
			system.GET("/services", systemHandler.ListService)
			system.POST("/services", systemHandler.CreateService)
			system.PUT("/services/:id", systemHandler.UpdateService)
			system.GET("/actions", systemHandler.ListAction)
			system.POST("/actions", systemHandler.CreateAction)
			system.PUT("/actions/:id", systemHandler.UpdateAction)
		}

		discussion := r.Group("discussion")
		{
			discussion.GET("/list", discussionHandler.ListComment)
			discussion.GET("/get/:id", discussionHandler.GetCommentByID)
			discussion.POST("/create", discussionHandler.CreateComment)
			discussion.POST("/delete/:id", discussionHandler.DeleteComment)
		}

		approval := r.Group("approval")
		{
			approval.GET("/list", approvalHandler.ApprovalList)
			approval.GET("/apply/order", approvalHandler.ApprovalTypeSortByCount)
			approval.GET("/activity-log/list", approvalHandler.ProcessActivityLogList)
		}
	}

	return router
}
