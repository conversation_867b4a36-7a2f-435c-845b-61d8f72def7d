package env

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"qiniu.io/qbpm/app/controllers"
)

// Logger instance a Logger middleware.
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			startTime = time.Now()
			latency   time.Duration
			status    int
		)

		// Process request
		c.Next()

		latency = time.Now().Sub(startTime)
		status = c.Writer.Status()

		info := fmt.Sprintf("%d %s %s %s %s", status, latency, c.ClientIP(), c.Request.Method, c.Request.URL)
		if status/100 == 2 {
			controllers.ReqLogger(c).Info(info)
		} else {
			controllers.ReqLogger(c).Error(info)
		}
	}
}
