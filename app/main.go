package main

import (
	"os"

	"github.com/gin-gonic/gin"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/qiniu/version/v2"

	"github.com/qbox/sonic/sdk/trace"
	httpTrace "github.com/qbox/sonic/sdk/trace/http"

	"gopkg.in/urfave/cli.v1"
	"qiniu.io/qbpm/app/env"
	"qiniu.io/qbpm/app/env/config"
	"qiniu.io/qbpm/engine"
)

func main() {
	var cfgPath string
	var autoMigrate bool

	cli.VersionPrinter = func(c *cli.Context) {
		version.Print()
	}

	app := cli.NewApp()
	app.Flags = []cli.Flag{
		cli.StringFlag{
			Name:        "config,c",
			Value:       "env/config/config.yml",
			Usage:       "Load config file",
			Destination: &cfgPath,
		},
		cli.BoolFlag{
			Name:        "automigrate",
			Usage:       "db autoMigrate or not",
			Destination: &autoMigrate,
		},
	}

	app.Before = func(c *cli.Context) error {
		err := config.ParseConfig(cfgPath)
		if err != nil {
			panic(err)
		}
		config.Conf.Engine.Mode = config.Conf.Mode
		engine.Init(&config.Conf.Engine, autoMigrate)

		app.Name = config.Conf.Engine.Name
		app.Version = config.Conf.Engine.Version
		app.HelpName = config.Conf.Engine.Name

		return nil
	}

	app.Commands = []cli.Command{
		{
			Name:  "web",
			Usage: "run qbpmd as a web app",
			Action: func(c *cli.Context) error {
				ginApp := env.InitRoutes()

				tracer := httpTrace.New(trace.TracerConfig{
					Sampler: trace.Sampler{
						Type:                    config.Conf.Tracing.SamplingType,
						Param:                   config.Conf.Tracing.SamplingParam,
						SamplingRefreshInterval: config.Conf.Tracing.SamplingRefreshInterval,
						SamplingServerURL:       config.Conf.Tracing.SamplingServerURL,
					},
					Reporter: trace.Reporter{
						LocalAgentHostPort: config.Conf.Tracing.ReporterLocalAgentEndpoint,
						CollectorEndpoint:  config.Conf.Tracing.ReporterCollectorEndpoint,
					},
					Service: trace.Service{
						ServiceName: config.Conf.Tracing.ServiceName,
					},
				}, ginApp)

				server := gin.New()
				server.Use(gin.WrapH(tracer))
				return server.Run(config.Conf.Listen)
			},
		},
	}

	app.Run(os.Args)
}
