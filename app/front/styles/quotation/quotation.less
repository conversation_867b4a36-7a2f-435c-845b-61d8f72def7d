.center {
    text-align:center;
}

.contract_num {
    position: absolute;
    right: 0;
    bottom: 5px;
    font-size: 13px;
    color: #616161;
}

.header {
    height: 80px;
    padding: 10px 0;
    border-bottom: 1.5px solid #A8A8A8;
    box-sizing: border-box;
    position: relative; 
    margin-bottom:50px;
}

.icon {
    height: 60px;
}

.title {
    font-size: 35px;
    color: #616161;
}

.product {
    margin-top: 60px;
}

.time_border {    
    border-color: #c3c3c3;
    border-width: 1px;
    border-top-style: solid;
    border-bottom-style: solid;
    padding-left: 70px;
    font-size: 13px;
    line-height: 28px;
    padding-right: 70px;
    color: #616161;
    margin-top: 20px;
}

.quotation_title {
    font-size: 17px;
    margin-top: 40px;
}

.info {
    width:690px
}

.info_key {
    text-align: right;
    width: 25%;
    font-size: 14px;
    color: #616161;
    font-weight:400;
}

.info_value {
    text-align: left;
    width: 25%;
    font-size: 14px;
    color: #616161;
}

.quotation_container {
    width: 600px;
    text-align: left;
}

.table_tair {
    width: 550px;
    border-width: 1px;
    border-color: #898989;
    border-collapse: collapse;
    color: #616161;
    margin-left: 40px;
    margin-top: 5px;
}

.table_tair th {
    border-width: 1px;
    text-align: center;
    border-style: solid;
    font-size: 14px;
                
    border-color: #8D8D8D;
    background-color: #cccccc;
    color: #616161;
    width:50%;
    font-weight:400;
}

.info thead {
    line-height: 30px;
}

.info tbody {
    line-height: 30px;
}

.table_tair thead {
    line-height: 35px;
}

.table_tair tbody {
    line-height: 35px;
}

.table_tair td {
    border-width: 1px;
    text-align: center;
    font-size: 14px;
    border-style: solid;
    border-color: #8D8D8D;
    color: #616161;
    width:50%;
}

div.tip {
    padding-left: 20px;
    padding-right: 20px;
    margin-top: 70px;
}

.tip_info {
    font-size: 14px; 
    line-height:25px
}

.print-btn {
    position: absolute;
    right: 0;
    bottom:45px;
    visibility:visible
}

@media print {
    .print-btn {
        visibility: hidden;
    }
    @page { 
        margin: 0cm; 
        size: 8.27in 11.69in; /* A4 width and height */
    }
    .table_tair th {
        -webkit-print-color-adjust: exact; 
        background-color: #cccccc !important; /* https://stackoverflow.com/questions/14987496/background-color-not-showing-in-print-preview */
    }
}
