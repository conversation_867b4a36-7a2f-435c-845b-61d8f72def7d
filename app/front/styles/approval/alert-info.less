.alert-info-mask {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.3);
  animation: mask-enter .3s;
  @keyframes mask-enter {
    from {
      background-color: rgba(0, 0, 0, 0);
    }
    to {
      background-color: rgba(0, 0, 0, 0.3);
    }
  }
  .alert-info-modal {
    display: flex;
    position: fixed;
    top: 40%;
    left: 50%;
    width: 400px;
    min-height: 120px;
    padding: 20px 10px;
    border-radius: 2px;
    box-shadow: 0 0 4px 2px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    z-index: 999999;
    transform: translate(-50%,-50%);
    flex-direction: column;
    animation: enter 0.3s;
    @keyframes enter {
      from {
        transform: scale(0.1) translate(-50%, -50%);
      }
      to {
        transform: scale(1) translate(-50%, -50%);
      }
    }
    .title {
      font-size: 16px;
      font-weight: bold;
    }
    .content {
      margin: 10px 0;
    }
    .close-btn {
      margin: 10px 0;
      text-align: right;
    }
  }
}
