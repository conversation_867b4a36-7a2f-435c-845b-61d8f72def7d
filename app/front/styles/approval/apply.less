.approval-apply.main-wrapper {
  display: flex;
  padding: 40px 0px;
  min-height: 400px;
  ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  .left-menu {
    position: relative;
    width: 200px;
    border-right: 1px solid #e1e1e1;
    .menu-item {
      position: relative;
      padding: 0 10px;
      height: 50px;
      line-height: 50px;
      font-size: 14px;
      font-weight: 500;
      text-align: left;
      cursor: pointer;
      &.active {
        font-weight: bold;
        background: linear-gradient(90deg, rgba(131, 58, 180, 1) 0%, rgba(253, 29, 29, 1) 33.3%, rgba(252, 176, 69, 1) 66.6%, rgba(131, 58, 180, 1) 100%);
        -webkit-background-clip: text;
        background-size: 300% 100%;
        color: transparent;
        animation: text 4s infinite linear;
        &:after {
          content: '';
          position: absolute;
          display: block;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(36, 170, 227, 0.1 );
          border-right: 3px solid #24aae3;
        }
      }
      @keyframes text{
        0%  { background-position: 0 0;}
        100% { background-position: -150% 0;}
      }
    }
  }
  .right-content {
    &.show {
      animation: content-fade-in 1s;
    }
    @keyframes content-fade-in {
      from { opacity: 0;}
      to { opacity: 1; }
    }
    flex: 1;
    padding: 0 40px;
    h2 {
      margin: 0
    }
    .feature-list {
      padding-left: 2px;
      li {
        display: flex;
        padding: 10px 0;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #eee;
        .content {
          display: flex;
          flex-direction: column;
          .feature-name {
            margin: 5px 0;
            font-size: 14px;
            font-weight: bold;
          }
          .feature-desc {
            margin: 5px 0;
            font-size: 14px;
            font-weight: 400;
          }
        }
        .operation {
          a {
            font-size: 14px;
            color: #24aae3;
          }
        }
      }
    }
  }
}

.respack-tip {
  display: flex;
  gap: 16px;
  border: 1px solid #f8efb6;
  background: #fffbe8;
  color: inherit;

  .warning-icon {
    font-size: 20px;
    color: #f2b751;
  }

  h4 {
    font-weight: bolder;
  }

  .color-red {
    color: red;
  }
}
