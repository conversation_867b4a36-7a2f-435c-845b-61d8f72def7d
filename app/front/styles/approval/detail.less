.text-red {
  color: #f00;
  * {
    color: #f00;
  }
}

.text-blue {
  color: blue;
  * {
    color: blue;
  }
}

.activity-table {
  width: 100%;
  min-width: 700px;
  td, th {
    vertical-align: middle !important;
  }
}

.contract-table {
  width: auto;
  min-width: 700px;
  td, th {
    vertical-align: middle !important;
  }
}

.approval-detail-credit {
  .margin-b-20 {
    margin-bottom: 20px;
  }
  .font-size-16 {
    font-size: 16px;
  }
  .font-size-14 {
    font-size: 14px;
  }
}

.approval-detail-coupon {
  .font-size-16 {
    font-size: 16px;
  }
  .control-label-left {
    text-align: left;
    line-height: 34px;
  }
  .label-width {
    width: 150px;
  }
}

.approval-detail-reduce {
  .product-item-container {
    margin: 20px 0;
    padding: 10px 20px;
    border: 1px solid #ccc;
    .product-title {
      font-size: 18px;
    }
    table {
      margin-top: 10px;
    }
  }
}

.approval-detail-offer {
  .font-size-16 {
    font-size: 16px;
  }
  .font-size-23 {
    font-size: 23px;
  }
}

.main-content #main-panel main[ui-view] .qn-standard-page section {
  overflow-y: scroll;
}

.mobile_history {
  display: none;
}

.operations_item {
  margin-right: 30px;
}

.async-task {
  position: relative;
  .async-task-list {
    position: absolute;
    left: 50%;
    max-height: 0;
    line-height: initial;
    transform: translateX(-50%);
    border-radius: 5px;
    box-shadow: 2px 2px 4px #cccccc;
    color: #555555;
    font-family: 'Glyphicons Halflings';
    overflow-y: hidden;
    transition: max-height 0.5s;
    z-index: 10;
    .async-task-item {
      display: grid;
      padding: 10px;
      grid-template-columns: 200px max-content;
      border-radius: 5px;
      grid-row-gap: 5px;
      border: solid 1px #cccccc;
      background: #f7f7f9;
      align-items: center;
      text-align: left;
      word-break: break-word;
      white-space: break-spaces;
      .listener_error {
        grid-column-start: 1;
        grid-column-end: 3;
      }

      .loader {
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 2px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #3498db;
        border-radius: 50%;
        animation: spin 2s linear infinite;
        margin: 0 auto;
        vertical-align: middle;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .btn-sm {
        width: fit-content;
        cursor: pointer;
      }
      
    }
    .nodata {
      width: 200px;
      padding: 20px;
      background: #f7f7f9;
      text-align: center;
    }
    .label, .btn-sm {
      line-height: normal;
      margin-left: 10px;
    }
  }
  .async-task-list-down {
    max-height: 300px;
    overflow-y: scroll;
    transition: max-height 0.5s;
  }
}

.text-nowrap {
  white-space: nowrap;
}

@media screen and (max-width:480px) {
  .d-flex-wrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    > * {
      margin-bottom: 10px;
      margin-right: 3px;
    }
  }
  .d-flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .approval-detail-page {
    main {
      .col-sm-6 {
        > .row {
          display: flex;
          flex-wrap: wrap;
          > * {
            margin-bottom: 10px;
          }
          > .col-sm-8 {
            max-width: 240px;
          }
        }
      }
    }
  }
  .mobile_history {
    display: block;
    .history_item {
      margin: 10px 0;
      padding: 10px;
      border-radius: 2px;
      box-shadow: 0px 20px 40px rgba(0, 0, 0, 0.08);
      border: 1px solid #cccccc;
      .history_item_title {
        font-size: 16px;
        font-weight: 700;
      }
      .hisroty_item_cont {
        padding: 5px 0;
        & + .hisroty_item_cont{
          border-top: 1px solid #ccc;
        }
        > div {
          margin: 5px 0;
        }
      }
    }
  }
}

.risk-approval-detail {
  .preview {
    display: inline-block;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
