.discussion-wrapper {
  .features {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    .feature {
      cursor: pointer;
      &:hover {
        color: #238efa;
      }
    }
  }
  .editor {
    position: relative;
    padding-bottom: 50px;
    .editor-textarea {
      height: 100px;
    }
    .ldap-wrapper {
      display: none;
      position: absolute;
      max-height: 150px;
      min-width: 120px;
      overflow: hidden;
      z-index: 99;
      ul {
        max-height: 150px;
        list-style: none;
        margin: 0;
        padding: 0;
        border: 1px solid #c1c1c1;
        border-radius: 4px;
        background-color: #f8f8f8;
        li {
          padding: 4px;
        }
        .selected {
          background-color: #238efa;
          color: white;
        }
      }
    }
    .rich-textarea {
      position: absolute;
      width: 100%;
      top: 0;
      left: 0;
      height: 100px;
      z-index: -1;
    }
  }
  .messages {
    border: 1px solid #f1f1f1;
    .message-wrapper {
      list-style: none;
      margin: 0;
      padding: 0;
      .message {
        padding: 10px;
        &:nth-child(odd) {
          background-color: #f8f8f8;
        }
        .comment {
          display: flex;
          align-items: flex-start;
          .author {
            display: flex;
            align-items: center;
            line-height: 20px;
            i {
              font-size: 20px;
            }
            .user {
              margin: 0 10px;
            }
            .reply {

            }
          }
          .content {
            margin: 0;
            line-height: 20px;
            width: auto;
          }
        }
        .operation {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
    }
  }
}