angular.module 'app.service'
  .factory 'withdrawApprovalService', ($http, $q, notificationService) ->

    prefix = '/api/proxy/gaea'

    @getRechargeTransactions = (body) ->
      # coffeelint: disable=max_line_length
      href = "#{prefix}/api/finance/withdraw/assets/list?uid=#{body.uid}&need_simulation_deduct=false&need_nb=#{body.needNB}&withdraw_type=#{body.withdrawType}&need_transferaccin=#{body.needTransferaccin}&apply_withdraw_id=#{body.withdrawID}"
      $q (resolve, reject) ->
        $http.get href
          .success (res) ->
            if res.code == 200
              resolve(res.data)
            else
              reject(res)
          .error (res) ->
            reject(res)
    
    @getAvailableAmount = (uid) ->
      href = "#{prefix}/api/finance/withdraw/available-amount?uid=#{uid}&need_distinguish_withdraw_type=true&filter_type=recharge_refund"
      $q (resolve, reject) ->
        $http.get href
          .success (res) ->
            if res.code == 200
              resolve(res.data)
            else
              reject(res)
          .error (res) ->
            reject(res)

    @apply = (body) ->
      href = "#{prefix}/api/finance/withdraw/apply"
      $q (resolve, reject) ->
        $http.post href, body
          .success (res) ->
            if res.code == 200
              resolve(res.data)
            else
              reject(res)
          .error (res) ->
            reject(res)

    @getDetail = (uid, apply_withdraw_id) ->
      href = "#{prefix}/api/finance/withdraw/details?uid=#{uid}&apply_withdraw_id=#{apply_withdraw_id}"
      $q (resolve, reject) ->
        $http.get href
          .success (res) ->
            if res.code == 200
              resolve(res.data)
            else
              reject(res)
          .error (res) ->
            reject(res)

    @modifyApplication = (body) ->
      href = "#{prefix}/api/finance/withdraw/modify"
      $q (resolve, reject) ->
        $http.post href, body
          .success (res) ->
            if res.code == 200
              resolve(res.data)
            else
              reject(res)
          .error (res) ->
            reject(res)

    return @
