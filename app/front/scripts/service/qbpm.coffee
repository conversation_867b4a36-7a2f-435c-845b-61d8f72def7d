angular.module 'app.service'
  .factory 'qbpmService', ($http, $q, notificationService) ->

    priceBizPrefix = '/api/proxy/price-biz'
    sofaPrefix = '/api/proxy/sofa'
    qbpmPrefix = '/api/proxy/qbpm'
    gaeaPrefix = '/api/proxy/gaea'

    isErr = (data) -> _.isEqual(_.sortBy(_.keys(data)), _.sortBy(['error', 'code', 'details']))

    @GetQuoteSheetBySn = (sn) ->
      $q (resolve, reject) ->
        $http.get priceBizPrefix + "/price/quote-sheet/get?sn=#{sn}"
          .success (res) ->
            if isErr res
              notificationService.error res.error, '获取quote-sheet出错'
              reject res
            else
              resolve res
        .error (res) ->
          notificationService.error res.code, '网络请求失败'
          reject res

    @GetMeasurement = (id) ->
      $q (resolve, reject) ->
        $http.get priceBizPrefix + "/price/measurement/price-user-map/#{id}"
          .success (res) ->
            if isErr res
              notificationService.error res.error, '获取测算结果失败'
              reject res
            else
              resolve res
          .error (res) ->
            notificationService.error res.code, '网络请求失败'
            reject res

    @GetPriceDiffWithPriceUserMapID = (id) ->
      $q (resolve, reject) ->
        $http.get priceBizPrefix + "/price/diff/curr-price/#{id}"
          .success (res) ->
            if isErr res
              notificationService.error res.error, '获取价格表信息失败'
              reject res
            else
              resolve res
          .error (res) ->
            notificationService.error res.code, '网络请求失败'
            reject res

    @GetActivityList = ({process_instance_id, excode}) ->
      $q (resolve, reject) ->
        $http.get priceBizPrefix + '/qbpm/activities',
            params: {process_instance_id, excode}
          .success (res) ->
            if isErr res
              notificationService.error res.error, '获取审批历史信息失败'
              reject res
            else
              resolve res
          .error (res) ->
            notificationService.error res.code, '网络请求失败'
            reject res

    # QBPM 业务上允许审批流程中修改 param，如果发生 param 变更，会记录在变更后的 activity 中
    # 所以这里取最新的 activity 的 param 是最准确的
    # 截止到 2021-04-07，线上还没有出现过审批中途修改 param 的情况
    # TODO: 维军大佬说 QBPM 应该实现获取 param 的接口，实现后这里改成接口直接获取 param
    #
    # XXX 直接这么做的话会出现 BO-17202 的问题，因为不同审批事件的 param 形状不一定相同。
    # 由于上面提到的原因（线上至今没有出现过中途修改 param 的情况），先改成取最早一个事件，
    # 这样至少可以保证取到的 param 形状一定是发起审批那个事件的形状。
    #
    # 后端是按创建时间逆序返回的，这里取最后一个 activity，就是最早的 activity
    # 因同一个 excode 可能被多次修改发起多次审批如 BO-17872，这里取最近一次发起审批的最早的 activity，即当前流程提交审批的节点
    @GetParamByExcode = (excode, process_instance_id) ->
      @GetActivityList({process_instance_id: process_instance_id, excode: excode})
        .then (activityList) ->
          # 先取得当前审批实例的 Id，即最近的 activity 关联的 process_instance_id
          currentProcessId = activityList.activities[0].process_instance_id
          # 剔除非当前审批实例的审批节点
          currentProcessActivities = _.filter activityList.activities, (act) ->
            return act.process_instance_id == currentProcessId
          # 取当前审批实例最早的 activity，即提交审批的节点
          earliestActivity = currentProcessActivities[currentProcessActivities.length - 1]
          # 解析表单数据
          param = JSON.parse(earliestActivity.param).data
          return param
        .catch (err) ->
          notificationService.error err
          return null

    @getProcessListInfo = (param) ->
      $q (resolve, reject) ->
        $http.get priceBizPrefix + '/qbpm/processes',
          params: param
        .success (res) ->
          if isErr res
            notificationService.error res.error, '获取审批列表失败'
            reject res
          else
            resolve res
        .error (res) ->
          notificationService.error res.code, '网络请求失败'
          reject res

    @listProcessInstances = (param) ->
      $q (resolve, reject) ->
        $http.get qbpmPrefix + '/api/history/processes',
          params: param
        .success (res) ->
          if isErr res
            notificationService.error res.error, '获取审批实例列表失败'
            reject res
          else
            resolve res
        .error (res) ->
          reject res

    @getProcessInstanceById = (id) ->
      $q (resolve, reject) ->
        $http.get qbpmPrefix + "/api/history/processes/#{id}"
        .success (res) ->
          if isErr res
            notificationService.error res.error, '获取审批实例失败'
            reject res
          else
            resolve res
        .error (res) ->
          reject res

    @getProcessDefByProcessInstanceId = (id) ->
      $q (resolve, reject) ->
        $http.get qbpmPrefix + "/api/repository/process-definitions/#{id}?type=process_instance_id"
        .success (res) ->
          if isErr res
            notificationService.error res.error, '获取审批流程失败'
            reject res
          else
            resolve res
        .error (res) ->
          reject res

    @handleExecution = (id, action_type, memo ) ->
      $q (resolve, reject) ->
        $http.post priceBizPrefix + "/qbpm/executions/#{id}",
          {action_type, memo}
        .success (res) ->
          if isErr res
            notificationService.error res.error, '请求失败'
            reject res
          else
            resolve res
        .error (res) ->
          notificationService.error res.code, '网络请求失败'
          reject res

    @assignExecution = (id, assignee, operator, memo) ->
      $q (resolve, reject) ->
        $http.put qbpmPrefix + "/api/runtime/executions/#{id}",
          {assignee, operator, memo}
        .success (res) ->
          if isErr res
            notificationService.error res.error, '请求失败'
            reject res
          else
            resolve res
        .error (res) ->
          reject res

    @getExecutionCounterSign = (id) ->
      $q (resolve, reject) ->
        $http.get qbpmPrefix + "/api/runtime/executions/#{id}/counter-sign/config"
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @urgeProcess = (id, operator, memo) ->
      $q (resolve, reject) ->
        $http.post qbpmPrefix + "/api/runtime/processes/#{id}/urges",
          {operator, memo}
        .success (res) ->
          if isErr res
            notificationService.error res.error, '请求失败'
            reject res
          else
            resolve res
        .error (res) ->
          reject res

    @counterSignExecution = (id, params) ->
      $q (resolve, reject) ->
        $http.patch qbpmPrefix + "/api/runtime/executions/#{id}",
          params
        .success (res) ->
          if isErr res
            notificationService.error res.error, '请求失败'
            reject res
          else
            resolve res
        .error (res) ->
          reject res

    @getUserPriceWithTimeRange = (uid, start_time,  end_time) ->
      $q (resolve, reject) ->
        $http.get priceBizPrefix + '/price/time-range',
          params: {'uid': uid, 'start_time': start_time, 'end_time': end_time}
        .success (res) ->
          resolve res
        .error (res) ->
          notificationService.error res.code, '网络请求失败'
          reject res

    @cancelApply = (excode, email) ->
      $q (resolve, reject) ->
        $http.delete sofaPrefix + "/api/v1/processes/excode/#{excode}",
          data: {operator: email}
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @applyUnifyApproval = (data) ->
      $q (resolve, reject) ->
        $http.post "/api/proxy/gaea/api/trade/unify/approval", data
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @checkApproval = (data) ->
      $q (resolve, reject) ->
        $http.post "/api/proxy/trade/unify/approval/check", data
        .success (res) ->
          if res.accepted
            resolve res
          else
            reject res.tips
        .error (res) ->
          reject res

    @getListeners = (processInstanceId) ->
      $q (resolve, reject) ->
        $http.get qbpmPrefix + "/api/history/listeners?process_instance_id=#{processInstanceId}"
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @restartListeners = (param) ->
      $q (resolve, reject) ->
        $http.post qbpmPrefix + "/api/history/listeners", param
        .success (res) ->
          resolve res
        .error (res) ->
          reject res
    @updateListenerStatus = (param) ->
      $q (resolve, reject) ->
        $http.put qbpmPrefix + "/api/history/listeners/status",
          param
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @getActions = () ->
      $q (resolve, reject) ->
        $http.get qbpmPrefix + "/api/system/actions"
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @suspendApprove = (processId, actionType) ->
      body =
        action_type: actionType
      $q (resolve, reject) ->
        $http.post priceBizPrefix + "/qbpm/process/#{processId}", body
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @suspendApproveV2 = (processId) ->
      $q (resolve, reject) ->
        $http.post qbpmPrefix + "/api/runtime/processes/#{processId}/suspend/v2"
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @getInvoiceApplicationDetail = (id) ->

    @getInvoiceApplication = (id) ->
      $q (resolve, reject) ->
        $http.get "/api/proxy/gaea/api/finance/invoice/setting/audit-details?id=#{id}"
        .success (res) ->
          if res.code == 200
            resolve res.data
          else
            reject res.message
        .error (res) ->
          reject res

    @getUnfreezeApplicationDetail = (id) ->
      url = gaeaPrefix + "/api/user/v2/apply-unfreeze?id=#{id}"
      $q (resolve, reject) ->
        $http.get url
        .success (res) ->
          if res.code == 200
            resolve res.data
          else
            reject res
        .error (err) ->
          reject err

    @getExtendBufferApplyDetail = (id) ->
      url = gaeaPrefix + "/api/user/v2/apply-extend-buffer?id=#{id}"
      $q (resolve, reject) ->
        $http.get url
        .success (res) ->
          if res.code == 200
            resolve res.data
          else
            reject res
        .error (err) ->
          reject err

    @applyNB = (data) ->
      $q (resolve, reject) ->
        $http.post "#{gaeaPrefix}/api/finance/nb/apply", data
        .success (res) ->
          if res.code == 200
            resolve res.data
          else
            reject res
        .error (res) ->
          reject res

    @applyCustombillRegen = (data) ->
      $q (resolve, reject) ->
        $http.post "#{gaeaPrefix}/api/bills/apply/regen-custombill", data
        .success (res) ->
          if res.code == 200
            resolve res.data
          else
            reject res
        .error (res) ->
          reject res

    @applyBalanceTransfer = (data) ->
      $q (resolve, reject) ->
        $http.post "#{gaeaPrefix}/api/finance/balance/transfer/apply", data
        .success (res) ->
          if res.code == 200
            resolve res.data
          else
            reject res
        .error (res) ->
          reject res

    @getBalanceTransferDetail = (id) ->
      $q (resolve, reject) ->
        $http.get "#{gaeaPrefix}/api/finance/balance/transfer/apply-get?id=#{id}"
        .success (res) ->
          if res.code == 200
            resolve res.data
          else
            reject res
        .error (res) ->
          reject res

    @createMessage = (instance_id, content, attachment, parentID) ->
      data =
        process_instance_id: instance_id
        content: content
      if attachment
        data.attachment = attachment
      
      if parentID
        data.parent_comment_id = parentID

      $q (resolve, reject) ->
        $http.post "#{priceBizPrefix}/qbpm/discussion/create", data
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @getMessages = (instance_id, page, page_size) ->
      $q (resolve, reject) ->
        $http.get "#{priceBizPrefix}/qbpm/discussion/list?process_instance_id=#{instance_id}&page=#{page}&page_size=#{page_size}"
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @deleteMessage = (id) ->
      $q (resolve, reject) ->
        $http.post "#{priceBizPrefix}/qbpm/discussion/delete/#{id}"
        .success (res) ->
          resolve res
        .error (res) ->
          reject res

    @getLdapUsers = (keyword) ->
      $q (resolve, reject) ->
        $http.get "#{gaeaPrefix}/api/admin/users?name=#{keyword}"
        .success (res) ->
          if res.code == 200
            resolve res.data
          else
            reject res
        .error (res) ->
          reject res

    @getBucketFileUrls = (files) ->
      data =
        files: files
      $q (resolve, reject) ->
        $http.post "#{sofaPrefix}/api/v1/bucket/files/urls", data
        .success (res) ->
          resolve res.data
        .error (res) ->
          reject res

    @getUserAvailableProductsByCodes = (productCodes) ->
      data =
        codes: productCodes
      $q (resolve, reject) ->
        $http.post "#{priceBizPrefix}/filter/user/available-products-by-codes", data
        .success (res) ->
          resolve res
        .error (res) ->
            reject res

    return @
