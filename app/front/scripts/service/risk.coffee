angular.module 'app.service'
.factory 'riskApplyService', ($http, $q) ->

  ucBizPrefix = '/api/proxy/ucbiz'
  gaeaPrefix = '/api/proxy/gaea'

  @getDummyProducts = (uid) ->
    url = gaeaPrefix + "/api/user/#{uid}/bills/dummy-products"
    $q (resolve, reject) ->
      $http.get url
      .success (res) ->
        if res.code == 200
          resolve res.data
        else
          reject res
      .error (err) ->
        reject err

  @getUserInfo = (uid) ->
    $q (resolve, reject) ->
      $http.get ucBizPrefix + "/user/overview?uid=#{uid}"
      .success (res) ->
        resolve res
      .error (err) ->
        reject err

  @applyUnfreeze = (uid, body) ->
    url = gaeaPrefix + "/api/user/#{uid}/v2/apply-unfreeze"
    $q (resolve, reject) ->
      $http.post url, body
      .success (res) ->
        if res.code == 200
          resolve res
        else
          reject res
      .error (err) ->
        reject err

  @applyExtendBuffer = (uid, body) ->
    url = gaeaPrefix + "/api/user/#{uid}/v2/apply-extend-buffer"
    $q (resolve, reject) ->
      $http.post url, body
      .success (res) ->
        if res.code == 200
          resolve res
        else
          reject res
      .error (err) ->
        reject err
  
  return @
