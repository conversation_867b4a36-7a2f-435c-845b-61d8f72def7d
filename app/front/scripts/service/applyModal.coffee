angular.module 'app.service'
.factory 'applyModalHelper', ($modal) ->

  @applyChangeOrderPrice = (order_hash) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/change-order-price.html'
      controller: 'changeOrderPriceCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        order_hash: ->
          return order_hash

  @applyPostPay = (order_hash) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/post-pay.html'
      controller: 'OrderApprovalPostPayCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        order_hash: ->
          return order_hash

  @applyChangePriceAndPostPay = (order_hash) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/change-price-and-post-pay.html'
      controller: 'ChangeOrderPriceAndPostPayCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        order_hash: ->
          return order_hash

  @applyRefund = (order_hash) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/refund.html'
      controller: 'OrderApprovalRefundCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        order_hash: ->
          return order_hash

  @applyModifyRespackStartTime = (order_hash) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/respack-start-time.html'
      controller: 'OrderApprovalModifyRespackStartTimeCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        order_hash: ->
          return order_hash

  @applyExtendRespackDuration = (order_hash) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/respack-duration.html'
      controller: 'OrderApprovalExtendRespackDurationCtrl'
      backdrop: 'static'
      size: 'xlg'
      resolve:
        order_hash: ->
          return order_hash

  @applyWithdraw = ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/withdraw.html'
      controller: 'WithdrawApplyCtrl'
      backdrop: 'static'
      size: 'lg'

  @applyRemoveFreeze = (uid) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/unfreeze.html'
      controller: 'removeFreezeApplyCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: ->
          return uid

  @applyExtendBuffer = (uid) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/extend-buffer.html'
      controller: 'extendBufferApplyCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: ->
          return uid

  @applyBillCoupon = (uid) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/bill-coupon.html'
      controller: 'BillCouponApplyCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: ->
          return uid

  @applyOrderCoupon = (uid) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/order-coupon.html'
      controller: 'OrderCouponApplyCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: ->
          return uid

  @applyRechargeReward = (uid) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/recharge-reward/index.html'
      controller: 'RechargeRewardApplyCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: ->
          return uid

  @applySyncedRegenBill = (product, uid) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/synced-custombill-regen.html'
      controller: 'SyncedCustombillRegenApplyCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        product: ->
          return product
        uid: ->
          return uid

  @applyBalanceTransfer = (uid) ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/balance-transfer.html'
      controller: 'BalanceTransferApplyCtrl'
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: ->
          return uid

  return @
