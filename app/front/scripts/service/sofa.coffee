angular.module 'app.service'
  .factory 'sofaService', ($http, $q, notificationService) ->

    prefix = '/api/proxy/sofa'

    @GetUserInfo = (email) ->
      $q (resolve, reject) ->
        $http.get prefix + "/api/v1/admin/user?email=#{email}"
          .success (res) ->
            if res.status != 0
              notificationService.error res.message, '获取用户信息失败'
              reject res
            else
              resolve res.data
          .error (res) ->
            notificationService.error res.status, '网络请求失败'
            reject res

    @FileProxy = (id) ->
      $http.get prefix + '/api/v1/admin/file/body/' + id, {'responseType': 'blob'}
        .error (res) ->
          notificationService.error res.status, '网络请求失败'

    @FileInfo = (id) ->
      $q (resolve, reject) ->
        $http.get prefix + '/api/v1/admin/file/info/' + id
          .success (res) ->
            if res.status != 0
              notificationService.error res.message, '获取文件信息失败'
              reject res
            else
              resolve res.data
          .error (res) ->
            notificationService.error '网络请求失败'

    @AttachmentsInfoByParent = (id) ->
      $q (resolve, reject) ->
        $http.get prefix + '/api/v1/admin/attachments/infos/' + id
          .success (res) ->
            if res.status != 0
              notificationService.error res.message, '获取文件信息失败'
              reject res
            else
              resolve res.data
          .error (res) ->
            notificationService.error '网络请求失败'
            reject res

    @ContractPartialUpdate = (id, body) ->
      $q (resolve, reject) ->
        $http.patch prefix + "/api/v1/contracts/#{id}", body
          .success (res) ->
            resolve res.data
          .error (res) ->
            reject res
            notificationService.error res.status, res.message
    
    @ContractDetail = (id) ->
      $q (resolve, reject) ->
        $http.get prefix + "/api/v1/contracts/#{id}/detail/with-pricing"
          .success (res) ->
            resolve res.data
          .error (res) ->
            reject res
            notificationService.error res.code, res.message

    @ListQuoteSheets = (query) ->
      $q (resolve, reject) ->
        $http.post "/api/proxy/gaea/api/finance/quote-sheet/list", query
          .success (res) ->
            resolve res.data
          .error (res) ->
            reject res
            notificationService.error res.status, res.message

    @GetPricingExtra = (priceID) ->
      $q (resolve, reject) ->
        $http.get prefix + "/api/v1/admin/prices/price_id/#{priceID}/pricing-extra"
          .success (res) ->
            switch res.status
              when 0
                resolve res.data
              else
                notificationService.error res.status, res.message
                reject res
          .error (res) ->
            if res?.status == 1 and res?.message == 'not found'
              # 404 的情况，因为返回码是 404，所以走的是 error 回调
              # 这个接口的 404 是预期的，表示没有新用户额外信息
              resolve null
              return

            reject res
            notificationService.error res.status, '网络请求失败'

    @getFilesURLByKey = (uploadedFileKeys) ->
      $q (resolve, reject) ->
        $http.post prefix + "/api/v1/bucket/files/urls",
          {"files" : uploadedFileKeys}
        .success (res) ->
          switch res.status
              when 0
                resolve res.data
              else
                notificationService.error res.status, res.message
                reject res
        .error (res) ->
          reject res
          notificationService.error res.code, '网络请求失败'

    @getContracts = (uid) ->
      $q (resolve, reject) ->
        $http.get prefix + "/api/v1/contracts?uid=#{uid}"
        .success (res) ->
          resolve res.data
        .error (res) ->
          reject res
          notificationService.error res.code, res.message

    return @
