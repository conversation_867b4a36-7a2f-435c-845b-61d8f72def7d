angular.module('app')
  .config ($stateProvider, envProvider) ->
    $stateProvider.state 'layout.PricingApprovalDetail',
      url: '/approval/change-price/{id:string}?{uid:int}'
      data:
        pageTitle: "改价详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/main.html'
      controller: 'ApprovalPriceChangeCtrl'

    # 改价重出账
    .state 'layout.ApplyRegenDetail',
      url: '/approval/apply-regen/{id:string}?{uid:int}'
      data:
        pageTitle: "改价详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/main.html'
      controller: 'ApprovalPriceChangeCtrl'

    .state 'layout.TestingApprovalDetail',
      url: '/approval/apply-testing/{id:string}?{uid:int}'
      data:
        pageTitle: "测试申请详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/testing.html'
      controller: 'ApprovalTestingApplyCtrl'

    .state 'layout.ContractApprovalDetail',
      url: '/approval/apply-contract/{id:string}?{uid:int}'
      data:
        pageTitle: "合同申请详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/contract.html'
      controller: 'ApprovalContractApplyCtrl'

    .state 'layout.CustomBillApprovalDetail',
      url: '/approval/apply-custom-bill/{id:string}?{uid:int}'
      data:
        pageTitle: "自定义账单申请详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/custombill.html'
      controller: 'ApprovalCustomBillApplyCtrl'

    .state 'layout.CreditApprovalDetail',
      url: '/approval/apply-credit/{id:string}?{uid:int}'
      data:
        pageTitle: "授信审批详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/credit.html'
      controller: 'ApprovalCreditApplyCtrl'

    # coffeelint: disable=max_line_length
    .state 'layout.ApprovalList',
      url: '/approval/list?{view_type}&{biz_type}&{excode}&{uid}&{email}&{status:int}&{page:int}&{page_size:int}'
      templateUrl: 'templates/approval/list/main.html'
      controller: 'ApprovalListCtrl'
    # coffeelint: enable=max_line_length

    .state 'layout.Quotation',
      url: '/quotation?{uid}&{start_time}&{end_time}&{type}'
      data:
        pageTitle: '报价单'
      templateUrl: 'templates/quotation/quotation.html'
      controller: 'QuotationCtl'

    .state 'layout.CouponApprovalDetail',
      url: '/approval/apply-coupon/{id:string}'
      templateUrl: 'templates/approval/detail/coupon.html'
      controller: 'ApprovalCouponDetailController'

    .state 'layout.VoucherApprovalDetail',
      url: '/approval/apply-voucher/{id: string}'
      templateUrl: 'templates/approval/detail/voucher.html'
      controller: 'VoucherApprovalDetailController'

    .state 'layout.IAMApprovalDetail',
      url: '/approval/apply-iam/{id:string}'
      data:
        pageTitle: "IAM 审批详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/iam.html'
      controller: 'ApprovalIAMApplyCtrl'

    .state 'layout.FinanceRelationApprovalDetail',
      # coffeelint: disable=max_line_length
      url: '/approval/apply-finance-relation/{id:string}?{process_instance_id:string}'
      data:
        pageTitle: "财务父子账号绑定审批详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/financeRelation.html'
      controller: 'ApprovalFinanceRelationApplyCtrl'

    .state 'layout.PortalIoRoleDetail',
      url: '/approval/apply-portalio-role/{id:string}'
      data:
        pageTitle: "申请 portal.qiniu.io 权限审批详情"
      templateUrl: 'templates/approval/detail/portalIoRole.html'
      controller: 'ApprovalPortalIoRoleApplyCtrl'

    .state 'layout.ReduceApprovalDetail',
      url: '/approval/apply-reduce/{id:string}'
      data:
        pageTitle: "减免重出账审批详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/reduceApply.html'
      controller: 'ApprovalReduceApplyCtrl'

    .state 'layout.QvmPricingApprovalDetail',
      url: '/approval/qvm-pricing/{id:string}'
      data:
        pageTitle: "QVM报价审批详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/qvmPricingApply.html'
      controller: 'ApprovalQvmPricingApplyCtrl'

    .state 'layout.SalesGuaranteeDetail',
      url: '/approval/apply-sales-guarantee/{id:string}'
      data:
        pageTitle: "大客户认证审批详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/salesGuarantee.html'
      controller: 'ApplySalesGuaranteeDetailCtrl'

    .state 'layout.IdentityChangeDetail',
      url: '/approval/apply-identity-change/{id:string}?{uid:int}'
      data:
        pageTitle: "认证变更审批详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/identityChange.html'
      controller: 'ApplyIdentityChangeDetailCtrl'

    .state 'layout.OrderApprovalDetail',
      url: '/approval/approve-order/{id:string}?{uid:int}'
      data:
        pageTitle: "预下单审批详情#{envProvider.env.title_suffix}"
      templateUrl: 'templates/approval/detail/orderApproval.html'
      controller: 'OrderApprovalDetailCtrl'

    .state 'layout.ApprovalApply',
      url: '/approval/apply?{feat:string}&{action:string}&{param:string}'
      templateUrl: 'templates/approval/apply/index.html'
      controller: 'ApprovalApprovalApplyCtrl'

    .state 'layout.OrderChangePriceApprovalDetail',
      url: '/approval/approve-change-price/{id:string}?{uid:int}&{process_instance_id:string}'
      templateUrl: 'templates/approval/detail/change-order-price.html'
      controller: 'OrderChangePriceDetailCtrl'

    .state 'layout.OrderPostPayApprovalDetail',
      url: '/approval/approve-post-pay/{id:string}?{uid:int}&{process_instance_id:string}'
      templateUrl: 'templates/approval/detail/order-post-pay.html'
      controller: 'OrderPostPayDetailCtrl'

    .state 'layout.OrderChangePriceAndPostPayApprovalDetail',
      url: '/approval/approve-change-price-and-post-pay/{id:string}?{uid:int}&{process_instance_id:string}'
      templateUrl: 'templates/approval/detail/change-price-and-post-pay.html'
      controller: 'OrderChangePriceAndPostPayDetailCtrl'

    .state 'layout.OrderChangeEffectTimeApprovalDetail',
      url: '/approval/approve-change-effect-time/{id:string}?{uid:int}&{process_instance_id:string}'
      templateUrl: 'templates/approval/detail/respack-start-time.html'
      controller: 'RespackStartTimeApprovalDetailCtrl'

    .state 'layout.OrderChangeEndTimeApprovalDetail',
      url: '/approval/approve-change-end-time/{id:string}?{uid:int}&{process_instance_id:string}'
      templateUrl: 'templates/approval/detail/respack-extend-duration.html'
      controller: 'RespackExtendDurationApprovalDetailCtrl'

    .state 'layout.OrderRefundApprovalDetail',
      url: '/approval/approve-refund/{id:string}?{uid:int}&{process_instance_id:string}'
      templateUrl: 'templates/approval/detail/order-refund.html'
      controller: 'OrderRefundApprovalDetailCtrl'
    
    .state 'layout.ApplyWithdrawDetail',
      url: '/approval/apply-withdraw/{id:string}?{uid:int}'
      templateUrl: 'templates/approval/detail/withdraw.html'
      controller: 'ApplyWithdrawDetailCtrl'

    .state 'layout.InvoiceApplyDetail',
      url: '/approval/vat-invoice-setting/{id:string}?{uid:int}'
      templateUrl: 'templates/approval/detail/invoice-apply.html'
      controller: 'InvoiceApplyDetailCtrl'

    .state 'layout.UnfreezeDetail',
      url: '/approval/apply-unfreeze/{id:string}?{uid:int}'
      templateUrl: 'templates/approval/detail/unfreeze.html'
      controller: 'UnfreezeApplyDetailCtrl'

    .state 'layout.ExtendBufferDetail',
      url: '/approval/apply-extend-buffer/{id:string}?{uid:int}'
      templateUrl: 'templates/approval/detail/extend-buffer.html'
      controller: 'ExtendBufferApplyDetailCtrl'

    .state 'layout.NbApplyDetail',
      url: '/approval/apply-nb/{id:string}?{uid:int}'
      templateUrl: 'templates/approval/detail/recharge-reward.html'
      controller: 'RechargeRewardApplyDetailCtrl'

    .state 'layout.RegenSyncedCustombillDetail',
      url: '/approval/apply-regen-synced-custombill/{id:string}'
      templateUrl: 'templates/approval/detail/regen-synced-custombill.html'
      controller: 'RegenSyncedCustombillDetailCtrl'
    # 兼容老的 url 地址
    .state 'layout.RegenSyncedCustombillDetailLegacy',
      url: '/approval/apply-regen-lcdn-custombill/{id:string}'
      templateUrl: 'templates/approval/detail/regen-synced-custombill.html'
      controller: 'RegenSyncedCustombillDetailCtrl'

    # 余额转移申请详情
    .state 'layout.BalanceTransferDetail',
      url: '/approval/apply-balance-transfer/{id:string}?{uid:int}'
      templateUrl: 'templates/approval/detail/balance-transfer.html'
      controller: 'BalanceTransferDetailCtrl'

    # 添加组织成员账号申请详情
    .state 'layout.OrgMemberDetail',
      url: '/approval/apply-org-member/{id:string}?{uid:int}'
      templateUrl: 'templates/approval/detail/orgMember.html'
      controller: 'OrgMemberDetailCtrl'
