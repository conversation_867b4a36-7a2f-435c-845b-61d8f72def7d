angular.module 'app.directive'

.directive 'measurementV2021Results', (
  $q,
  MEASUREMENT_DIMENSION_SYMBOL_MAP,
  MEASUREMENT_PERIOD,
  MEASUREMENT_PERIOD_SYMBOL_MAP,
  MEASUREMENT_PERIOD_MAP,
) ->
  restrict: 'E'
  scope:
    'uid': '='
    'measurements': '='
    'haveCost': '='
  templateUrl: 'scripts/directive/measurementV2021Results/index.html'

  link: (scope, element, attrs) ->
    now = new Date()

    # 测算时段
    initAvailablePeriods = (nowDate) ->
      thisMonthDate = moment(nowDate).utcOffset(8).startOf('month').toDate()
      thisYearDate = moment(nowDate).utcOffset(8).startOf('year').toDate()

      # 2021/03
      thisMonthStr = moment(thisMonthDate).utcOffset(8).format('YYYY/MM')
      # 2021/01
      thisYearStr = moment(thisYearDate).utcOffset(8).format('YYYY/MM')
      # 2021/12
      thisYearDecemberStr = moment(thisYearDate).utcOffset(8).add(11, 'month').format('YYYY/MM')
      # 2021/02
      prevMonthStr = moment(thisMonthDate).utcOffset(8).add(-1, 'month').format('YYYY/MM')
      # 2020/12
      prevThreeMonthStr = moment(thisMonthDate).utcOffset(8).add(-3, 'month').format('YYYY/MM')
      # 2020/09
      prevSixMonthStr = moment(thisMonthDate).utcOffset(8).add(-6, 'month').format('YYYY/MM')

      result = {}

      strDesc = (key, desc) -> "#{MEASUREMENT_PERIOD_MAP[key]}（#{desc}）"
      fill = (key, desc) ->
        result[key] = strDesc(key, desc)

      fill MEASUREMENT_PERIOD.CURRENT_MONTH_EST, thisMonthStr
      fill MEASUREMENT_PERIOD.CURRENT_YEAR_EST, "#{thisYearStr}~#{thisYearDecemberStr}"
      fill MEASUREMENT_PERIOD.LAST_MONTH, prevMonthStr
      fill MEASUREMENT_PERIOD.LAST_THREE_MONTH_AVG, "#{prevThreeMonthStr}~#{prevMonthStr}"
      fill MEASUREMENT_PERIOD.LAST_SIX_MONTH_AVG, "#{prevSixMonthStr}~#{prevMonthStr}"

      return result

    scope.availablePeriods = initAvailablePeriods now
    scope.currentPeriod = MEASUREMENT_PERIOD.LAST_THREE_MONTH_AVG

    # measurements 响应体处理
    scope.processedDisplayRows = []

    transformMeasurementRow = (row) ->
      {
        id: +row.id,
        uid: +row.uid,
        price_id: +row.price_id,
        measurement_period: MEASUREMENT_PERIOD_SYMBOL_MAP[row.measurement_period],
        dimension: MEASUREMENT_DIMENSION_SYMBOL_MAP[row.dimension],
        fnc_product: row.fnc_product,
        sales: row.sales,
        sales_area: row.sales_area,
        before_income: +row.before_income,
        after_income: +row.after_income,
        cost: +row.cost,
        before_gross_profit: +row.before_gross_profit,
        after_gross_profit: +row.after_gross_profit,
        # created_at updated_at 目前没用到，所以暂不处理
      }

    transformMeasurementDisplayRow = (displayRow) ->
      {
        row: transformMeasurementRow(displayRow.row),
        row_visible: displayRow.row_visible,
        cost_visible: displayRow.cost_visible,
      }

    scope.$watch 'measurements', (newVal) ->
      scope.processedDisplayRows = _.map newVal, transformMeasurementDisplayRow
      scope.refreshPeriodSelection()

    # 按测算时段的分类展示
    scope.selectedDisplayRows = []

    selectPeriodFromDisplayRows = (processedDisplayRows, selectedPeriod) ->
      _.filter processedDisplayRows, (x) -> x.row.measurement_period == selectedPeriod

    scope.refreshPeriodSelection = () ->
      scope.selectedDisplayRows = selectPeriodFromDisplayRows scope.processedDisplayRows, scope.currentPeriod

    scope.$watch 'currentPeriod', () ->
      scope.refreshPeriodSelection()
