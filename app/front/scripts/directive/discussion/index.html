<div class="discussion-wrapper">
  <h4>
    <strong>沟通留言</strong>
  </h4>
  <div class="empty" ng-show="messages.length == 0 && !showEditor">
    <p class="text-center">空空如也</p>
    <p class="text-center">审批中如遇到暂时无法判断的情况，</p>
    <p class="text-center">可在此处留言沟通并@相关同事讨论。</p>
  </div>
  <div class="messages">
    <ul class="message-wrapper">
      <li class="message" ng-repeat="message in messages">
        <div class="comment">
          <div class="author">
            <i class="fa fa-user"></i>
            <span class="user">{{message.author}}</span>
            <span ng-show="message.reply_to">回复&nbsp;&nbsp;</span>
            <a href="javascript:void(0)" class="reply" ng-show="message.reply_to">@{{message.reply_to}}</a>
            <span>：</span>
          </div>
          <p class="content" ng-bind-html="message.content"></p>
        </div>
        <div class="operation margin-top-10">
          <span class="date">{{message.updated_at | financialISODateFormatter:'YYYY-MM-DD HH:mm:ss'}}</span>
          <span class="right">
            <a href="javascript:void(0)" ng-click="deleteMessage(message.id, message.content)" style="margin-right: 20px;" ng-show="message.can_be_deleted">删除</a>
            <a href="javascript:void(0)" ng-click="openEditor(message.author, message.id)">回复</a>
          </span>
        </div>
        <div class="files margin-top-10" ng-show="message.attachment">
          已上传附件：<a href="{{message.attachment.url}}" target="_blank">{{message.attachment.file_name}}</a>，点击可下载
        </div>
      </li>
    </ul>
  </div>
  <div class="text-right" style="width: 100%;">
    <ul class="pagination text-right">
      <li ng-class="{disabled: page == 1}">
        <a href="javascript:void(0)" ng-click="prevPage()">上一页</a>
      </li>
      <li ng-repeat="pageNumber in pageOptions" ng-class="{active: pageNumber == page}">
        <a href="javascript:void(0)" ng-click="setPage(pageNumber)">{{pageNumber}}</a>
      </li>
      <li ng-class="{disabled: page == totalPage}">
        <a href="javascript:void(0)" ng-click="nextPage()">下一页</a>
      </li>
    </ul>
  </div>
  <div class="text-center" ng-show="!showEditor">
    <button class="btn btn-primary" ng-click="openEditor()">我来说几句</button>
  </div>
  <div class="editor margin-top-10" ng-show="showEditor">
    <textarea
      class="form-control editor-textarea"
      rows="4"
      placeholder="可直接@相关同事沟通，系统会发送企微通知。因系统限制@暂时只支持字母拼音请勿@中文，@后请勿删除自动补齐的空格否则@无效"
      ng-model="content"
      name="discussionEditor"
      id="discussionEditor"></textarea>
    <!-- <div class="rich-textarea form-control" contenteditable="true" ng-model="content">
      {{content}}
    </div> -->
    <div class="ldap-wrapper">
      <ul>
        <li class="ldap-item" ng-repeat="user in ldapUsers track by $index" ng-click="selectUser(user)">{{user}}</li>
      </ul>
    </div>
    <div class="features">
      <!-- <span class="feature">@ 同事</span> -->
      <!-- <span class="feature"><i class="fa fa-file"></i> 附件</span> -->
      <qn-single-file-upload file="attachment" size="20 * 1024 *1024"></qn-single-file-upload>
      <div class="btns">
        <button class="btn btn-primary" ng-click="submit()">留言</button>
        <button class="btn btn-default" type="button" ng-click="close()">取消</button>
      </div>
    </div>
  </div>
</div>