angular.module 'app.directive'
.directive 'discussion', (
  $rootScope, qbpmService, notificationService, modalService
) ->
  restrict: 'E'
  scope: {
    excode: '='
  }
  templateUrl: 'scripts/directive/discussion/index.html'
  link: (scope, element, attrs) ->
    scope.messages = []
    scope.content = ''
    scope.attachment = null
    scope.page = 1
    scope.pageSize = 5
    scope.total = 0
    scope.totalPage = 0
    scope.showEditor = false
    scope.startMatchLdap = false
    scope.currentCursorIndex = 0
    scope.parentID = ''

    timer = null

    scope.$watchGroup ['page', 'totalPage'], () ->
      scope.pageOptions = []
      if scope.totalPage < 5
        i = 1
        while i <= scope.totalPage
          scope.pageOptions.push i
          i++
      else
        if scope.page < 2
          scope.pageOptions = [1, 2, 3, 4]
        else if scope.page > scope.totalPage - 3
          scope.pageOptions = [scope.totalPage - 3, scope.totalPage - 2, scope.totalPage - 1, scope.totalPage]
        else
          scope.pageOptions = ['首页', scope.page - 1, scope.page, scope.page + 1, scope.page + 2, '尾页']

    scope.$watch 'content', (newVal, oldVal) ->
      cursorIndex = 0
      # 找出光标位置
      if document.getElementById('discussionEditor') != null
        cursorIndex = document.getElementById('discussionEditor').selectionStart
      # 如果光标前面有@符号，就显示用户列表
      if newVal[cursorIndex - 1] == '@'
        scope.currentCursorIndex = cursorIndex
        scope.startMatchLdap = true
      
      if scope.startMatchLdap
        if newVal[scope.currentCursorIndex - 1] != '@'
          scope.startMatchLdap = false
          $('.ldap-wrapper').hide()
          return
        # 获取@符号之后的内容
        scope.searchUserKeyword = newVal.slice(scope.currentCursorIndex, cursorIndex)
        if timer
          clearTimeout(timer)
        timer = setTimeout () ->
          getLdapUsers()
        , 200
        # 获取光标的绝对定位
        cursorPosition = $('#discussionEditor').caret('position')
        $('.ldap-wrapper').show()
        $('.ldap-wrapper').css
          top: cursorPosition.top + 20
          left: cursorPosition.left

    $('#discussionEditor').on 'keydown', (e) ->
      if (e.keyCode == 38 || e.keyCode == 40 || e.keyCode == 13 && scope.startMatchLdap)
        e.preventDefault()

    $('#discussionEditor').on 'keyup', (e) ->
      # 监听键盘事件，如果是空格或者是esc，就隐藏用户列表
      if e.keyCode == 32 or e.keyCode == 27
        scope.startMatchLdap = false
        $('.ldap-wrapper').hide()
        scope.ldapUsers = []

      # 如果是上下键，就移动用户列表的选中项
      if e.keyCode == 38 # 上
        e.preventDefault()
        # 当前类名为selected的元素，取消类名，上一个元素添加类名
        selected = $('.ldap-wrapper .ldap-item.selected')
        if selected.prev().length > 0
          selected.prev().addClass('selected')
          selected.removeClass('selected')
      if e.keyCode == 40 # 下
        e.preventDefault()
        selected = $('.ldap-wrapper .ldap-item.selected')
        if selected.next().length > 0
          selected.next().addClass('selected')
          selected.removeClass('selected')
      # 如果是回车键，就把当前选中的内容填入输入框
      if e.keyCode == 13
        e.preventDefault()
        selected = $('.ldap-wrapper .ldap-item.selected')
        if selected.length > 0 && scope.startMatchLdap
          preContent = scope.content.slice(0, scope.currentCursorIndex - 1) # 截到@符号
          cursorIndex = document.getElementById('discussionEditor').selectionStart
          inputContent = scope.content.slice(scope.currentCursorIndex, cursorIndex) # 截取@符号之后的内容
          tailContent = scope.content.slice(cursorIndex, scope.content.length) # 截取光标之后的内容
          scope.content = preContent + '@' + selected.text().trim() + ' ' + tailContent
          scope.$apply()
          scope.startMatchLdap = false
          $('.ldap-wrapper').hide()

    scope.selectUser = (user) ->
      preContent = scope.content.slice(0, scope.currentCursorIndex - 1) # 截到@符号
      cursorIndex = document.getElementById('discussionEditor').selectionStart
      inputContent = scope.content.slice(scope.currentCursorIndex, cursorIndex) # 截取@符号之后的内容
      tailContent = scope.content.slice(cursorIndex, scope.content.length) # 截取光标之后的内容
      scope.content = preContent + '@' + user + ' ' + tailContent
      scope.startMatchLdap = false
      $('.ldap-wrapper').hide()
      $('#discussionEditor').focus()

    getMessages = () ->
      if !scope.processInstanceID
        return
      qbpmService.getMessages(scope.processInstanceID, scope.page, scope.pageSize).then (res) ->
        scope.messages = res.comments
        scope.messages.forEach (item) ->
          # 匹配 item.content 中的@与空格之间的内容，然后替换成链接
          item.content = item.content.replace(/@(\S+)\s/g, '<a href="javascript:void(0)">@$1</a> ')
        scope.total = res.count
        scope.totalPage = Math.ceil(scope.total / scope.pageSize)

    scope.$watch 'page', () ->
      getMessages()

    scope.setPage = (page) ->
      if page == '首页'
        scope.page = 1
      else if page == '尾页'
        scope.page = scope.totalPage
      else
        scope.page = page

    scope.prevPage = () ->
      if scope.page > 0
        scope.page--

    scope.nextPage = () ->
      if scope.page < scope.totalPage - 1
        scope.page++

    getLdapUsers = () ->
      qbpmService.getLdapUsers(scope.searchUserKeyword).then (res) ->
        userNames = res?.map (item) -> item.name || []
        if scope.searchUserKeyword
          scope.ldapUsers = userNames.slice(0, 6)
        else
          # 最多4个常用用户
          combineCommonUser = scope.commonUsers.slice(0, 4)
          scope.ldapUsers = _.uniq((combineCommonUser.concat userNames).slice(0, 6))
        setTimeout () ->
          $('.ldap-item').removeClass('selected')
          $('.ldap-item').first().addClass('selected')
        , 200

    getProcessInstanceID = () ->
      qbpmService.GetActivityList({excode: scope.excode}).then (res) ->
        # 设置常用的用户
        users = (res.activities.map (item) -> item.actor.split('@')[0]).filter (item) -> item
        # 去重
        scope.commonUsers = _.uniq(users).reverse()
        scope.processInstanceID = res.activities[0].process_instance_id
        getMessages()
        getLdapUsers()

    scope.openEditor = (author, parentID) ->
      scope.showEditor = true
      scope.content = ''
      scope.parentID = parentID
      if author
        scope.content = "@#{author} "
      textarea = document.getElementById('discussionEditor')
      setTimeout () ->
        textarea.focus()
        textarea.setSelectionRange(-1, -1)
      , 200

    getProcessInstanceID()

    scope.deleteMessage = (messageID, messageContent) ->
      modalService.confirm(
        "<p class='common-confirm-content'>
          <i class='fa fa-exclamation-triangle icon-warning'></i>
          &nbsp;&nbsp; 确认是否删除以下留言?
        </p>
        <p class='common-confirm-content text-left'>#{messageContent}</p>
        "
      ).then () ->
        qbpmService.deleteMessage(messageID).then () ->
          getMessages()
        .catch (err) ->
          notificationService.error "Failed to delete message, #{err.message}"

    scope.close = () ->
      scope.showEditor = false
    scope.submit = () ->
      if scope.content.trim() == ''
        notificationService.error 'Message content cannot be empty'
        return
      qbpmService.createMessage(scope.processInstanceID, scope.content + ' ', scope.attachment, scope.parentID).then () ->
        scope.content = ''
        scope.attachment = null
        scope.showEditor = false
        scope.page = 1
        scope.parentID = ''
        getMessages()
        notificationService.success '留言已发送'
      .catch (err) ->
        notificationService.error "Failed to send message, #{err.message}"