angular.module 'app.directive'

.directive 'testingApplicationRow', (
  $q,
  qbpmService,
) ->
  restrict: 'A' # use like <tr testing-application-row ...></tr>
  scope:
    'row': '='
    'uid': '='
  templateUrl: 'scripts/directive/testingApplicationRow/index.html'

  link: (scope, element, attrs) ->
    scope.products = ''
    scope.starterName = ''
    scope.createdAt = new Date()

    getProductsStrFromActivityDataItems = (items) ->
      _.uniq(_.map(items, (item) -> item.product_name)).join('、')

    scope.refreshRow = (newVal) ->
      scope.createdAt = moment(scope.row.created_at).utcOffset(8).toDate()

      params = JSON.parse newVal.params

      scope.products = getProductsStrFromActivityDataItems params.data.items
      scope.starterName = params.data.starter

    scope.$watch 'row', scope.refreshRow
