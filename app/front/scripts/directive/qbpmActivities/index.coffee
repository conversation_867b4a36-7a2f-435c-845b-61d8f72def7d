angular.module 'app.directive'

.directive 'qbpmActivities', ($rootScope, $modal, $state, $interval,
  userService, qbpmService, notificationService, modalService,
  ACTIVITY_STATUS, ACTIVITY_STATUS_MAP, PROCESS_STATUS, PROCESS_STATUS_MAP,
  HANDLE_EXECUTION_ACTION_TYPE, HANDLE_EXECUTION_ACTION_TYPE_MAP, PROCESS_SUSPEND
  counterSignType, COUNTER_SIGN_TYPE_LIST, LISTENER_STATUS_MAP, LISTENER_STATUS
) ->
  restrict: 'E'
  scope:
    'excode': '@',
    'processInstanceId': '@',
    'limitNodes': '=', # 限制显示节点
    'approveTips': '@', # 批准操作提示
    'rejectTips': '@', # 驳回操作提示
    'approveCountdown': '@', # 批准操作确认按钮可用倒计时（秒）
    'rejectCountdown': '@', # 驳回操作确认按钮可用倒计时（秒）
  templateUrl: 'scripts/directive/qbpmActivities/index.html'

  link: (scope, element, attrs, ngModelCtrl) ->

    groupActivities = (activities) ->
      # 按流程分组
      groupByProcess = _.groupBy activities, (act) ->
        return act.process_instance_id

      groupedActivities = []
      _.forOwn groupByProcess, (processActs, process_id) ->
        # 按节点分组
        groupByProcessNode = _.groupBy processActs, (act) ->
          return act.name
        _.forOwn groupByProcessNode, (nodeActs, name) ->
          groupedActivity = {}
          groupedActivity.name = name
          # 过滤掉状态为「无响应」的历史项
          groupedActivity.items = _.filter nodeActs, (nodeAct) ->
            return nodeAct.status != "#{ACTIVITY_STATUS.NORESPONSE}"
          if groupedActivity.items.length > 0
            groupedActivities.push groupedActivity

      return groupedActivities

    scope.isDataLoading = true
    scope.ACTIVITY_STATUS_MAP = ACTIVITY_STATUS_MAP
    scope.ACTIVITY_STATUS = ACTIVITY_STATUS
    scope.PROCESS_STATUS_MAP = PROCESS_STATUS_MAP
    scope.PROCESS_STATUS = PROCESS_STATUS
    scope.HANDLE_EXECUTION_ACTION_TYPE = HANDLE_EXECUTION_ACTION_TYPE
    scope.LISTENER_STATUS_MAP = LISTENER_STATUS_MAP
    scope.LISTENER_STATUS = LISTENER_STATUS
    scope.isListenerFaild = false
    scope.isProcessOp = false
    scope.asyncTaskShow = false
    scope.actionsMap = {}

    getExecutionActivities = (activities) ->
      # hasPendingSuggest = hasPendingSuggestNode(activities)

      # 优先找操作人是当前用户的节点
      # 可能存在当前用户既是审批流管理员又是某个节点的操作人
      # 应该优先以节点审批人身份审批
      # 详见：https://jira.qiniu.io/browse/BO-24013
      for activity in activities
        isPending = checkIsPendingNode(activity)
        # isSuggest = checkIsSuggestNode(activity)
        isAssignee = activity.actor == $rootScope.userInfo?.email
        # 可审批的节点，需满足以下条件：
        # 1. 节点状态是待审批
        # 2. 当前用户是节点分配的操作人
        # 3. 节点是建议节点 或 没有待审批的建议节点
        #    有待审批建议节点时，只有建议节点可以审批
        #    正常审批节点不能操作审批，需等所有待审批节点执行完成
        if isPending && isAssignee  # && (isSuggest || !hasPendingSuggest)
          return activity

      # 如果没有找到操作人是当前用户的节点，并且当前用户是审批流管理员
      # 找审批流管理员可操作的第一个节点
      if scope.isProcessOp
        for activity in activities
          isPending = checkIsPendingNode(activity)
          isSuggest = checkIsSuggestNode(activity)
          if isPending # && (isSuggest || !hasPendingSuggest)
            return activity

      return

    # 初始化函数，依赖 userInfo 的逻辑
    initializeActivities = ->
      return if !$rootScope.userInfo # 如果 userInfo 不存在，则不执行
      
      qbpmService.GetActivityList {process_instance_id: scope.processInstanceId, excode: scope.excode}
        .then (activityList) ->
          scope.isProcessOp = activityList.is_process_op
          scope.curProcessInstanceId = activityList.activities[0].process_instance_id

          curActivities = (
            activity for activity in activityList.activities when activity.process_instance_id == scope.curProcessInstanceId
          )

          if !scope.execution
            activity = getExecutionActivities(curActivities)
            scope.execution = activity
            scope.node = activity?.activity_define_key

          if scope.execution
            qbpmService.getExecutionCounterSign scope.execution.execution_id
            .then (res) ->
              scope.counterSignInfo = res

          qbpmService.getProcessDefByProcessInstanceId scope.curProcessInstanceId
            .then (process) ->
              scope.process = parseProcessDefinition process
              processEndEventId = process.process_model.process.end_events[0].id
              return qbpmService.getListeners scope.curProcessInstanceId
                .then (listeners) ->
                  scope.endListeners = listeners.filter (listener) ->
                    listener.node_key == processEndEventId
                  scope.isListenerFaild = scope.endListeners.some (endListener) ->
                    endListener.status == LISTENER_STATUS.FAILED
            .catch (error) ->
              notificationService.error '请求出错:' + error.message

          scope.groupedActivities = groupActivities curActivities
          getProcessInstance curActivities
          scope.isDataLoading = false

    # 如果已经有 userInfo，直接初始化
    if $rootScope.userInfo
      initializeActivities()
    else
      # 监听 userInfo 变化
      unwatch = scope.$watch(
        -> $rootScope.userInfo,
        (newVal, oldVal) ->
          if newVal
            initializeActivities()
            unwatch() # 取消监听，只需要执行一次
      )

    qbpmService.getActions()
      .then (actions) ->
        actions.forEach (action) ->
          scope.actionsMap[action.code] = action.name
      .catch (error) ->
        notificationService.error '请求出错:' + error.message

    getProcessInstance = (activities) ->
      if !activities || activities.length == 0
        return
      qbpmService.getProcessInstanceById activities[0].process_instance_id
        .then (process) ->
          scope.processInstance = process
          scope.canRevokeProcess = getCanRevokeProcess(process, activities, scope.isProcessOp, $rootScope.userInfo)
          scope.canUrge = scope.canRevokeProcess
          scope.canSuspend = !!scope.execution && process.status != PROCESS_STATUS.SUSPEND
          scope.canReassign = scope.execution && scope.isProcessOp

    getCanRevokeProcess = (processInstance, activities, isProcessOp, userInfo) ->
      # 当前流程审批中，且当前用户为审批发起人或管理员可撤回审批
      startNode = _.find activities, (act) ->
        act.type == 'StartEvent'
      return (processInstance.status == PROCESS_STATUS.PENDING || processInstance.status == PROCESS_STATUS.SUSPEND) &&
        (isProcessOp || startNode.actor == userInfo?.email)

    parseProcessDefinition = (processDef) ->
      if !processDef.process_model || !processDef.process_model.process
        return undefined

      process =
        start_key: ''
        definition: {}

      for nodeType, nodes of processDef.process_model.process
        if Object.prototype.toString.call(nodes) != '[object Array]'
          continue
        if nodeType == 'start_events' && nodes.length > 0 && nodes[0].id
          process.start_key = nodes[0].id
        for node in nodes
          process.definition[node.id] = node
          process.definition[node.id].type = nodeType

      return process

    checkIsSuggestNode = (activity) ->
      return activity?.type == 'SuggestTask'

    checkIsPendingNode = (activity) ->
      return activity?.status == "#{ACTIVITY_STATUS.PENDING}"

    scope.checkIsSuggestNode = checkIsSuggestNode

    hasPendingSuggestNode = (activities) ->
      for activity in activities
        if checkIsSuggestNode(activity) && checkIsPendingNode(activity)
          return true
      return false

    # 获取节点加签信息，依赖 scope.node
    getCounterSignInfo = () ->
      counterSignInfo =
        allow_counter_sign: false
      if scope.node && scope.process?.definition[scope.node]?.allow_counter_sign
        counterSignInfo.allow_counter_sign = true
        node = scope.process.definition[scope.node]
        counterSignInfo.counter_sign_types = node.counter_sign_types
        counterSignInfo.counter_signs = node.counter_signs
      return counterSignInfo

    scope.getActivityStatus = (act) ->
      if act.type == 'SuggestTask'
        if act.status == "#{ACTIVITY_STATUS.COMPLETE}"
          return '审批意见：通过'
        else if act.status == "#{ACTIVITY_STATUS.FAILED}"
          return '审批意见：驳回'
      return ACTIVITY_STATUS_MAP[act.status]

    scope.clickApprove = (actionType, execution) ->
      isApprove = actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_PASS ||
        actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_COUNTER_SIGN
      if scope.$parent.approveCallback
        scope.$parent.approveCallback(actionType, execution)
          .then () ->
            scope.approve(actionType, isApprove)
      else
        scope.approve(actionType, isApprove)

    scope.approve = (actionType, isApprove) ->
      executionId = scope.execution?.execution_id
      modalInstance = $modal.open
        backdrop: 'static'
        templateUrl: 'scripts/directive/qbpmActivities/confirm.html'
        controller: ($scope) ->
          $scope.HANDLE_EXECUTION_ACTION_TYPE = HANDLE_EXECUTION_ACTION_TYPE
          $scope.HANDLE_EXECUTION_ACTION_TYPE_MAP = HANDLE_EXECUTION_ACTION_TYPE_MAP
          $scope.actionType = actionType
          $scope.tips = if isApprove then scope.approveTips else scope.rejectTips
          # coffeelint: disable=max_line_length
          $scope.tips = '' if scope.limitNodes && scope.limitNodes.length > 0 && _.indexOf(scope.limitNodes, scope.node) == -1
          $scope.countdown = if isApprove then parseInt(scope.approveCountdown) || 0 else parseInt(scope.rejectCountdown) || 0
          # coffeelint: enable=max_line_length
          # 如果未设置提示，将倒计时置为 0
          $scope.countdown = 0 if !$scope.tips
          # 根据
          $scope.isAck = if $scope.countdown > 0 then false else true
          # 加签候选人
          $scope.counterSignInfo = scope.counterSignInfo
          # 选择的待加签人
          $scope.selectedSigns = []
          $scope.counterSignType = counterSignType
          $scope.isSuggestNode = checkIsSuggestNode(scope.execution)

          # 获取可选加签类型
          if $scope.counterSignInfo.allow_counter_sign
            if $scope.counterSignInfo.counter_sign_types?.length > 0
              $scope.counterSignTypes = _.filter COUNTER_SIGN_TYPE_LIST, (typeItem) ->
                for signType in $scope.counterSignInfo.counter_sign_types
                  if signType == typeItem.value
                    return true
                return false
            else
              $scope.counterSignTypes = COUNTER_SIGN_TYPE_LIST
            # 设置默认值
            $scope.counterSignInfo.counterSignType = $scope.counterSignTypes[0]?.value

          $scope.save = () ->
            $scope.forbidSubmit = true
            if actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_COUNTER_SIGN
              params =
                execution_param:
                  memo: $scope.memo
                counter_sign_type: $scope.counterSignInfo.counterSignType
                counter_sign_nodes: $scope.selectedSigns
              promise = qbpmService.counterSignExecution executionId, params
            else
              promise = qbpmService.handleExecution executionId, actionType, $scope.memo
            promise.then (data) ->
              modalInstance.close data
              $state.reload()

          $scope.cancel = () ->
            modalInstance.dismiss 'cancel'

          $scope.onSubmit = () ->
            if !$scope.isAck
              notificationService.warning '请先确认提示内容再操作审批'

          counter = $interval ->
            $scope.countdown-- if $scope.countdown > 0
          , 1000

          $scope.$on '$destroy', -> $interval.cancel counter
    scope.cancelApprove = () ->
      modalService.confirm(
        "<p class='common-confirm-content'>
          <i class='fa fa-exclamation-triangle icon-warning'></i>
          &nbsp;&nbsp;您确定要撤回当前审批？
        </p>
        <p class='common-confirm-content'>撤回后，如需要申请，请重新填写申请。</p>
        "
      ).then () ->
        qbpmService.cancelApply scope.excode, $rootScope.userInfo?.email
        .then (res) ->
          notificationService.success '撤销成功！'
          $state.reload()
        .catch (res) ->
          notificationService.warning "#{res.message}"

    scope.urge = () ->
      # scope.processInstance 中的 id 会有精度损失，这里取 activity 中拿到的 processInstanceId
      if !scope.curProcessInstanceId then return
      modalService.confirm(
        "<p class='common-confirm-content'>
          <i class='fa fa-exclamation-triangle icon-warning'></i>
          &nbsp;&nbsp;您确定要催办当前审批？
        </p>
        <p class='common-confirm-content'>为避免频繁请求，当前限制一定时间内只能催办 1 次。</p>
        "
      ).then () ->
        qbpmService.urgeProcess scope.curProcessInstanceId, $rootScope.userInfo?.email, ''
        .then (res) ->
          notificationService.success '催办成功！'
          $state.reload()
        .catch (res) ->
          notificationService.warning "#{res.message}"

    scope.markComplete = (e, listener) ->
      modalInstance = $modal.open
        backdrop: 'static'
        templateUrl: 'scripts/directive/qbpmActivities/markComplete.html'
        controller: ($scope) ->
          $scope.submit = () ->
            if !$scope.remark
              notificationService.warning "备注必填"
              return
            $scope.forbidSubmit = true
            qbpmService.updateListenerStatus {
              action_key: listener.action_key,
              listener_key: listener.listener_key,
              execution_id: listener.execution_id,
              errors: $scope.remark,
              status: 1
            }
            .then ->
              modalInstance.close()
              $state.reload()
            .catch (res) ->
              notificationService.warning "#{res.message}"
            .finally ->
              $scope.forbidSubmit = false
          $scope.cancel = () ->
            modalInstance.dismiss 'cancel'

    scope.reassign = () ->
      executionId = scope.execution?.execution_id
      orignalActor = scope.execution?.original_actor
      modalInstance = $modal.open
        backdrop: 'static'
        templateUrl: 'scripts/directive/qbpmActivities/reassign.html'
        controller: ($scope) ->

          $scope.submit = () ->
            if !$scope.assignee
              notificationService.warning "新分配审批人必填"
              return
            if $scope.assignee == orignalActor
              notificationService.warning "新分配审批人不能与当前审批人相同"
              return
            $scope.forbidSubmit = true
            executionId = scope.execution?.execution_id
            qbpmService.assignExecution executionId, $scope.assignee, $rootScope.userInfo?.email, $scope.memo
            .then ->
              modalInstance.close()
              $state.reload()
            .catch (res) ->
              notificationService.warning "#{res.message}"
            .finally ->
              $scope.forbidSubmit = false

          $scope.cancel = () ->
            modalInstance.dismiss 'cancel'
    scope.asyncTaskListDropDown = (e) ->
      scope.asyncTaskShow = !scope.asyncTaskShow
    scope.retry = (e, listener) ->
      if scope.retrying
        return
      e.stopPropagation()
      scope.retrying = true
      param = {
        execution_id: listener.execution_id,
        listener_key: listener.listener_key,
        action_key: listener.action_key
      }
      qbpmService.restartListeners(param)
        .then () ->
          notificationService.success '重试成功'
        .catch (error) ->
          notificationService.error "#{error.message}"
        .finally () ->
          scope.retrying = false
    scope.suspendApprove = () ->
      qbpmService.suspendApproveV2 scope.curProcessInstanceId
      .then () ->
        notificationService.success '成功挂起'
        $state.reload()
      .catch (err) ->
        notificationService.error "挂起失败，#{err.message}"

    scope.recoveryApprove = () ->
      actionType = PROCESS_SUSPEND.CONTINUE
      qbpmService.suspendApprove scope.curProcessInstanceId, actionType
      .then () ->
        notificationService.success '已恢复审批'
        $state.reload()
      .catch (err) ->
        notificationService.error "恢复审批失败，#{err.message}"
