<div class="modal-header ">
  <h3 class="modal-title">请确认是否要{{ HANDLE_EXECUTION_ACTION_TYPE_MAP[actionType]}}申请</h3>
</div>
<div class="text-center" style="margin: 10px;" ng-if="actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_PASS">
  <div ng-if="isSuggestNode">通过后将审批意见将反馈到下个审批节点，此操作无法撤回，请谨慎操作</div>
  <div ng-if="!isSuggestNode">通过后此申请将进入下个审批节点，此操作无法撤回，请谨慎操作</div>
</div>
<div class="text-center" style="margin: 10px;" ng-if="actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_REFUSE">
  <div ng-if="isSuggestNode">驳回后此申请将回到上个审批节点，此操作无法撤回，请谨慎操作</div>
  <div ng-if="!isSuggestNode">驳回后将审批意见将反馈到下个审批节点，此操作无法撤回，请谨慎操作</div>
</div>
<div class="text-left" style="margin: 10px;padding-left:30px;" ng-if="actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_COUNTER_SIGN">
  <span ng-repeat="t in counterSignTypes">{{counterSignType.hint(t.value)}}<br/></span>
</div>
<form name="form-horizontal">
  <div class="form-group" ng-if="actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_COUNTER_SIGN" style="margin-top: 20px">
    <label class="col-sm-2 control-label text-right">加签类型</label>
    <div class="col-sm-9">
      <label class = "radio-inline" data-ng-repeat="t in counterSignTypes">
        <input type="radio" name="optSignType" data-ng-model="counterSignInfo.counterSignType" data-ng-value="{{t.value}}">{{t.text}}
      </label>
    </div>
  </div>
  <div class="form-group" ng-if="actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_COUNTER_SIGN" style="margin-top: 20px">
    <label class="col-sm-2 control-label text-right">加签候选人</label>
    <div class="col-sm-10" ng-class="{ 'has-error': form-horizontal.assignee.$dirty && form-horizontal.assignee.$invalid }">
      <div ng-repeat="sign in counterSignInfo.counter_signs" class="checkbox-inline" style="margin-left: 10px;">
        <input type="checkbox" name="assignee" checklist-model="selectedSigns" checklist-value="sign" required>
        {{sign.display_name || sign.assignee}}
      </div>
      <span class="help-block" ng-show="selectedSigns.length == 0">
        <span>请至少选择一个加签候选人进行审批</span>
      </span>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label text-right">{{ actionType != HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_REFUSE ? "备注" : "驳回原因" }}<br>(选填)</label>
    <div class="col-sm-9">
      <textarea name="memo" ng-model="memo" class="form-control" cols="10" rows="5"></textarea>
    </div>
  </div>
  <div class="form-check form-check-inline text-center" ng-show="tips">
    <input class="form-check-input" type="checkbox" id="ackCheck" ng-model="isAck" value="true" ng-disabled="countdown > 0">
    <label class="form-check-label text-danger" for="ackCheck" style="width: auto; cursor: pointer;">
        {{tips}}
    </label>
  </div>
  <div class="modal-footer text-center">
    <span ng-click="onSubmit()">
      <button
        class="btn btn-primary"
        type="button"
        ng-disabled="forbidSubmit || !isAck || (actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_COUNTER_SIGN && selectedSigns.length == 0)"
        ng-click="save()"
      >
        确认
        <span ng-if="countdown > 0">（{{countdown}}）</span>
      </button>
    </span>
    <button class="btn btn-warning" type="button" ng-click="cancel()">取消</button>
  </div>
</form>