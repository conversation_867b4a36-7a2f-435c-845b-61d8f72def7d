<div class="history clearfix" style="margin-bottom: 20px;" ng-show="!isDataLoading">
  <div class="row">
    <h3 class="col-sm-12" style="margin-bottom: 20px;"><strong>审批历史</strong></h3>
  </div>

  <div class="row hidden-xs">
    <div class="col-sm-12">
      <table class="table table-bordered text-center activity-table">
        <thead>
          <tr>
            <th>标题</th>
            <th>被指派人</th>
            <th>操作人</th>
            <th>操作时间</th>
            <th class="text-nowrap">状态&nbsp;
              <span class="label {{processInstance.status | processStatusStyleFilter}}" ng-if="processInstance">
                {{PROCESS_STATUS_MAP[processInstance.status]}}
              </span>
              <span
                class="label {{processInstance.status | processStatusStyleFilter}} async-task"
                content-permission="qbpm-activity-listener"
                ng-class="{'label-danger': isListenerFaild}"
              >
                <span ng-click="asyncTaskListDropDown($event)">
                  <span ng-show="isListenerFaild">服务调用失败</span>
                  <span class="glyphicon glyphicon-collapse-down"></span>
                </span>
                <div class="async-task-list" ng-class="{'async-task-list-down': asyncTaskShow}">
                  <div class="panel panel-default" ng-repeat="endListener in endListeners" style="margin: 0;border: none;text-align: left;">
                    <div class="panel-heading">{{ actionsMap[endListener.action_key] }}
                      <span class="label {{ endListener.status | listenerStatusStyleFilter }}">{{ LISTENER_STATUS_MAP[endListener.status] }}</span>
                    <button
                            class="btn btn-sm btn-primary"
                            ng-show="endListener.status == LISTENER_STATUS.FAILED"
                            ng-click="retry($event, endListener)"
                            ng-disabled="retrying"
                            content-permission="qbpm-activity-listener-retry"
                    ><span class="glyphicon glyphicon-repeat"></span> 重试</button>
                    <button
                            class="btn btn-sm btn-success"
                            ng-show="endListener.status == LISTENER_STATUS.FAILED"
                            ng-click="markComplete($event, endListener)"
                            ng-disabled="retrying"
                            content-permission="qbpm-activity-listener-mark-complete"
                    ><span class="glyphicon glyphicon-comment"></span> 标记完成</button>
                    </div>
                    <div class="panel-body listener_error" style="max-width: 500px;text-wrap: auto" ng-if="endListener.errors">{{ endListener.errors }}</div>
                  </div>
                  <div ng-show="!endListeners.length" class="nodata">没有数据！</div>
                </div>
              </span>
            </th>
            <th style="width: 30%">备注</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat-start="node in groupedActivities">
            <td rowspan="{{node.items.length}}">{{ node.name }}</td>
            <td>{{ node.items[0].original_actor_name }}</td>
            <td>{{ node.items[0].actor_name }}</td>
            <td>{{ node.items[0].updated_at | formatTimeFilter }}</td>
            <td>
              <span class="text-danger"
                ng-if="isProcessOp && node.items[0].execution_id == execution.execution_id"
              >
                <i class="fa fa-dot-circle-o"></i>
              </span>
              {{ getActivityStatus(node.items[0]) }}
            </td>
            <td>{{ node.items[0].memo }}</td>
          </tr>
          <tr ng-repeat-end ng-repeat="activity in node.items" ng-hide="$first">
            <td>{{ activity.original_actor_name }}</td>
            <td>{{ activity.actor_name }}</td>
            <td>{{ activity.updated_at | formatTimeFilter }}</td>
            <td>
              <span class="text-danger"
                ng-if="isProcessOp && activity.execution_id == execution.execution_id"
              >
                <i class="fa fa-dot-circle-o"></i>
              </span>
              {{ getActivityStatus(activity) }}
            </td>
            <td>{{ activity.memo }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="mobile_history">
    当前状态：
    <span class="label {{processInstance.status | processStatusStyleFilter}}" ng-if="processInstance">
      {{PROCESS_STATUS_MAP[processInstance.status]}}
    </span>
    <span
      class="label {{processInstance.status | processStatusStyleFilter}} async-task"
      content-permission="qbpm-activity-listener"
      ng-class="{'label-danger': isListenerFaild}"
    >
      <span ng-click="asyncTaskListDropDown($event)">
        <span ng-show="isListenerFaild">服务调用失败</span>
        <span class="glyphicon glyphicon-collapse-down"></span>
      </span>
      <div class="async-task-list" ng-class="{'async-task-list-down': asyncTaskShow}">
        <div class="async-task-item" ng-repeat="endListener in endListeners">
          <div>{{ actionsMap[endListener.action_key] }}<span class="label {{ endListener.status | listenerStatusStyleFilter }}">{{ LISTENER_STATUS_MAP[endListener.status] }}</span></div>
          <div
            class="btn-sm btn-primary"
            ng-show="endListener.status == LISTENER_STATUS.FAILED"
            ng-click="retry($event, endListener)"
            content-permission="qbpm-activity-listener-retry"
          >重试</div>
          <div class="listener_error" style="max-width: 500px;">{{ endListener.errors }}</div>
        </div>
        <div ng-show="!endListeners.length" class="nodata">没有数据！</div>
      </div>
    </span>
    <div class="history_item" ng-repeat="node in groupedActivities">
      <div class="history_item_title">{{ node.name }}</div>
      <div class="hisroty_item_cont" ng-repeat="activity in node.items">
        <div>
          被指派人：{{ activity.original_actor_name }}
        </div>
        <div>操作人：{{ activity.actor_name }}</div>
        <div class="d-flex-between">
          <div class="label {{activity.status | processStatusStyleFilter}}">
            {{ getActivityStatus(activity) }}
          </div>
          <div>{{ activity.updated_at | formatTimeFilter }}</div>
        </div>
        <div>备注：{{ activity.memo }}</div>
      </div>
    </div>
  </div>

  <div class="operations clearfix" style="margin-bottom: 20px;">
    <div class="row" style="margin-bottom: 10px; text-align: center;">
      <div class="col-sm-12 d-flex-wrap">
        <button ng-if="execution && processInstance.status != PROCESS_STATUS.SUSPEND" class="btn btn-lg btn-primary operations_item" ng-click="clickApprove(HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_PASS, execution)">
          {{ checkIsSuggestNode(execution) ? '审批意见：通过' : '通过'}}
        </button>
        <button ng-if="execution" class="btn btn-lg btn-primary operations_item" ng-show="counterSignInfo.allow_counter_sign" ng-click="clickApprove(HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_COUNTER_SIGN, execution)">加签</button>
        <button ng-if="execution && processInstance.status != PROCESS_STATUS.SUSPEND" class="btn btn-lg btn-danger operations_item" ng-click="clickApprove(HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_REFUSE, execution)">
          {{ checkIsSuggestNode(execution) ? '审批意见：驳回' : '驳回'}}
        </button>
        <button ng-if="canReassign" class="btn btn-lg btn-primary operations_item" ng-click="reassign()">重新分配</button>
        <button ng-if="canUrge" class="btn btn-lg btn-primary operations_item" ng-click="urge()">催办</button>
        <button ng-if="canRevokeProcess" class="btn btn-lg btn-primary operations_item" ng-click="cancelApprove()">撤回审批</button>
        <button ng-if="canSuspend" class="btn btn-lg btn-primary operations_item" ng-click="suspendApprove()">挂起审批</button>
        <button ng-if="processInstance.status == PROCESS_STATUS.SUSPEND" class="btn btn-lg btn-primary operations_item" ng-click="recoveryApprove()">恢复审批</button>
        <a href="javascript:history.go(-1)"><button class="btn btn-lg btn-warning">返回</button></a>
      </div>
    </div>
  </div>

</div>
