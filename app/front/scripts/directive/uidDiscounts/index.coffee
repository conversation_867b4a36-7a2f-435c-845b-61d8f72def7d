angular.module 'app.directive'

.directive 'uidDiscounts', ($rootScope, $compile, $timeout, $http, $q, $document) ->
  restrict: 'E'
  scope:
    'uid': '@'
  templateUrl: 'scripts/directive/uidDiscounts/index.html'

  link: (scope, element, attrs, ngModelCtrl) ->
    scope.isV1DataLoading = false
    scope.isV2021DataLoading = false
    scope.v1Discounts = null
    scope.v2021Discount = null

    getV1Discounts = ->
      scope.v1Discounts = null
      scope.isV1DataLoading = true

      $http.post '/api/proxy/wallet/v3/finance/history/overview',
        uids: [parseInt(scope.uid)]
      .success (data) ->
        scope.v1Discounts = data
      .finally ->
        scope.isV1DataLoading = false

    aggregateV2021Discounts = (resp) ->
      initial =
        total_freenb: 0
        available_freenb: 0
        total_coupon: 0
        available_coupon: 0

      fn = (result, value) ->
        result.total_freenb += value.total_freenb
        result.available_freenb += value.available_freenb
        result.total_coupon += value.total_coupon
        result.available_coupon += value.available_coupon
        result

      _.reduce resp, fn, initial

    getV2021Discount = ->
      scope.v2021Discount = null
      scope.isV2021DataLoading = true

      $http.post '/api/proxy/wallet/v3/finance/history/overview/v2021',
        uids: [parseInt(scope.uid)]
      .success (data) ->
        scope.v2021Discount = aggregateV2021Discounts(data)
      .finally ->
        scope.isV2021DataLoading = false

    # 初始化，获取优惠信息
    getV1Discounts()
    getV2021Discount()

    scope.shouldShowDirective = () -> scope.shouldShowV1Data() || scope.shouldShowV2021Data()

    scope.shouldShowV1Data = ->
      if !scope.v1Discounts
        return false
      
      for discount in scope.v1Discounts
        if scope.shouldShowV1MonthsData(discount)
          return true
      return false

    scope.shouldShowV1MonthsData = (discount) ->
      return discount.freenb + discount.coupon + discount.cash > 0

    scope.shouldShowV2021Data = () -> scope.v2021Discount != null
