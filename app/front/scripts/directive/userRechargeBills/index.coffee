angular.module 'app.directive'

.directive 'userRechargeBills', (
  $q,
  walletService, gaeaService, notificationService,
  USER_RECHARGE_BILLS_SHOW_ORDER_TYPE_LIST
) ->
  restrict: 'E'
  scope:
    'uid': '@'
    'currencyType': '='
    'monthsRange': '@'
    'showTotalRow': '@'
    'showAverageRow': '@'
  templateUrl: 'scripts/directive/userRechargeBills/index.html'

  link: (scope, ele, attr) ->
    scope.hideheader = if attr.hideheader != undefined then true else false
    scope.userID = if scope.uid then scope.uid * 1 else 0
    scope.showMonths = if scope.monthsRange then scope.monthsRange * 1 else 6
    scope.isShowTotal = if scope.showTotalRow == 'true' then true else false
    scope.isShowAverage = if scope.showAverageRow == 'true' then true else false

    scope.isRechargeBillsLoading = true
    scope.rechargeBillsInfo = null

    currentTime = moment().utcOffset(8).valueOf()
    reqMonthLayout = 'YYYYMM'
    showMonthLayout = 'YYYY-MM'
    monthSplitList = []

    userRechargeList = []
    userBillsList = []
    userOrdersList = []
    userBillsAndOrdersList = []

    getUserRechargeTransactions = () ->
      startTime = moment(currentTime).subtract(scope.showMonths, 'months').startOf('month').valueOf() * 10000
      endTime = moment(currentTime).startOf('month').valueOf() * 10000

      params =
        start: startTime
        end: endTime

      $q (resolve, reject) ->
        walletService.listRechargeTransactions scope.userID, params
          .then (resp) ->
            userRechargeList = transformRechargeRespData resp || []
            resolve()
          .catch (err) ->
            reject err

    getUserTransactions = () ->
      startTime = moment(currentTime).subtract(scope.showMonths, 'months').startOf('month').valueOf() * 10000
      endTime = moment(currentTime).startOf('month').valueOf() * 10000

      params =
        uid: scope.userID
        prefix: 'DEDUCT'
        starttime: startTime
        endtime: endTime

      $q (resolve, reject) ->
        walletService.listTransactions params
          .then (resp) ->
            userOrdersList = transformTransactionsRespData resp || []
            resolve()
          .catch (err) ->
            reject err

    getUserBills = () ->
      startTime = moment(currentTime).subtract(scope.showMonths, 'months').format(reqMonthLayout)
      endTime = moment(currentTime).format(reqMonthLayout)

      params =
        start: startTime
        end: endTime

      $q (resolve, reject) ->
        gaeaService.billsListByUid scope.userID, params
          .then (resp) ->
            userBillsList = transformBillsRespData resp.bills || []
            resolve()
          .catch (err) ->
            reject err

    getUserBillsAndOrders = () ->
      promiseList = [getUserBills(), getUserTransactions()]

      $q (resolve, reject) ->
        $q.all promiseList
          .then (resp) ->
            userBillsAndOrdersList = userBillsList.concat userOrdersList
            scope.rechargeBillsInfo = getRechargeBillsInfo()
            resolve()
          .catch (err) ->
            reject err

    transformRechargeRespData = (list) ->
      resultRechargeList = []

      _.forEach list, (item) ->
        data =
          money: item.money || 0
          month: moment(item.time / 10000).utcOffset(8).format(showMonthLayout)

        resultRechargeList.push data

      return resultRechargeList

    transformBillsRespData = (list) ->
      resultBillsList = []

      _.forEach list, (item) ->
        data =
          money: item.money || 0
          month: moment(item.month).utcOffset(8).format(showMonthLayout)

        resultBillsList.push data

      return resultBillsList

    transformTransactionsRespData = (list) ->
      resultOrdersList = []

      _.forEach list, (item) ->
        type = item.type || ''

        if USER_RECHARGE_BILLS_SHOW_ORDER_TYPE_LIST.indexOf(type) > -1
          data =
            money: Math.abs(item.money) || 0
            month: moment(item.time / 10000).utcOffset(8).format(showMonthLayout)

          resultOrdersList.push data

      return resultOrdersList

    getMonthSplitList = (time) ->
      resultMonthSplitList = []

      for i in [0...scope.showMonths]
        month = moment(time).subtract(i + 1, 'months').format(showMonthLayout)

        resultMonthSplitList.push month

      return resultMonthSplitList

    getRechargeBillsInfo = () ->
      list = []
      sumBillMoney = 0
      sumRechargeMoney = 0

      for month in monthSplitList
        data =
          month: month
          bill_money: 0
          recharge_money: 0

        for bill in userBillsAndOrdersList
          if bill.month == month
            data.bill_money += bill.money
            sumBillMoney += bill.money

        for recharge in userRechargeList
          if recharge.month == month
            data.recharge_money += recharge.money
            sumRechargeMoney += recharge.money

        list.push data

      resultRechargeBillsInfo =
        list: list
        sum_bill_money: sumBillMoney
        sum_recharge_money: sumRechargeMoney

      return resultRechargeBillsInfo


    getRechargeAndBillsData = () ->
      promiseList = [getUserRechargeTransactions(), getUserBillsAndOrders()]

      $q.all promiseList
        .then (resp) ->
          scope.rechargeBillsInfo = getRechargeBillsInfo()
        .catch (err) ->
          notificationService.error err.message, '获取消费和充值数据失败'
        .finally () ->
          scope.isRechargeBillsLoading = false

    # start
    monthSplitList = getMonthSplitList currentTime
    getRechargeAndBillsData()
