<div class="brief clearfix user-recharge-bills">
  <div class="row margin-b-20" ng-show="!hideheader">
    <h3 class="col-sm-12"><strong>近&nbsp;{{ showMonths }}&nbsp;个月消费和充值</strong></h3>
  </div>
  <div class="text-center">
    <button
      type="button"
      ng-show="isRechargeBillsLoading"
      class="btn btn-success btn-inline"
      name="button">
      <i ng-show="isRechargeBillsLoading" class="fa fa-spinner fa-spin"></i>&nbsp;消费和充值数据加载中...
    </button>
    <button
      type="button"
      disabled="true"
      ng-show="!isRechargeBillsLoading && !rechargeBillsInfo"
      class="btn btn-danger btn-inline" name="button">
      消费和充值数据获取失败
    </button>
  </div>
  <div class="row margin-b-20" ng-show="!isRechargeBillsLoading && rechargeBillsInfo">
    <div class="col-sm-12">
      <table class="table table-bordered text-center recharge-bills-table">
        <thead>
          <tr>
            <th>月份</th>
            <th>消费金额</th>
            <th>充值金额</th>
          </tr>
        </thead>

        <tbody>
          <tr ng-if="rechargeBillsInfo && rechargeBillsInfo.list.length > 0" ng-repeat="record in rechargeBillsInfo.list">
            <td>{{ record.month }}</td>
            <td>{{ record.bill_money | moneyWithCurrencyFilter:'':currencyType }}</td>
            <td>{{ record.recharge_money | moneyWithCurrencyFilter:'':currencyType }}</td>
          </tr>
          <tr ng-if="isShowTotal && rechargeBillsInfo && rechargeBillsInfo.list.length > 0">
            <td>总计</td>
            <td>{{ rechargeBillsInfo.sum_bill_money | moneyWithCurrencyFilter:'':currencyType }}</td>
            <td>{{ rechargeBillsInfo.sum_recharge_money | moneyWithCurrencyFilter:'':currencyType }}</td>
          </tr>
          <tr ng-if="isShowAverage && rechargeBillsInfo && rechargeBillsInfo.list.length > 0">
            <td>平均</td>
            <td>
              {{ (rechargeBillsInfo.sum_bill_money / rechargeBillsInfo.list.length) | moneyWithCurrencyFilter:'':currencyType }}
              /&nbsp;月
            </td>
            <td>
              {{ (rechargeBillsInfo.sum_recharge_money / rechargeBillsInfo.list.length) | moneyWithCurrencyFilter:'':currencyType }}
              /&nbsp;月
            </td>
          </tr>
          <tr ng-if="!rechargeBillsInfo || rechargeBillsInfo.list.length == 0">
            <td colspan="3">暂无数据</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
