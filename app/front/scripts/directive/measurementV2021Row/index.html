<td>{{ rowDesc }}</td>

<td ng-if="rowVisible">{{ renderRow.before_income | moneyFormatter:2:10000:false }} 元</td>
<td ng-if="!rowVisible">******</td>

<td ng-if="rowVisible">{{ renderRow.after_income | moneyFormatter:2:10000:false }} 元 <span ng-class="{ 'text-red': renderRow.income_change_ratio &lt; 0 }">({{ renderRow.income_change_ratio > 0 ? '+' : '' }}{{ renderRow.income_change_ratio * 100 | number: 1 }}%)</span></td>
<td ng-if="!rowVisible">******</td>

<td ng-if="costVisible">{{ renderRow.cost | moneyFormatter:2:10000:false }} 元</td>
<td ng-if="!costVisible">******</td>

<td ng-if="costVisible">{{ renderRow.before_gp | moneyFormatter:2:10000:false }} 元</td>
<td ng-if="!costVisible">******</td>

<td ng-if="costVisible">{{ renderRow.after_gp | moneyFormatter:2:10000:false }} 元 <span ng-class="{ 'text-red': renderRow.gp_change_ratio &lt; 0 }">({{ renderRow.gp_change_ratio > 0 ? '+' : '' }}{{ renderRow.gp_change_ratio * 100 | number: 1 }}%)</span></td>
<td ng-if="!costVisible">******</td>

<td ng-if="costVisible">{{ renderRow.before_gpm * 100 | number: 1 }}%</td>
<td ng-if="!costVisible">******</td>

<td ng-if="costVisible">{{ renderRow.after_gpm * 100 | number: 1 }}%</td>
<td ng-if="!costVisible">******</td>
