angular.module 'app.directive'

.directive 'measurementV2021Row', (
  $q,
  MEASUREMENT_DIMENSION,
) ->
  restrict: 'A' # use like <tr measurement-v2021-row ...></tr>
  scope:
    'uid': '='
    'displayRow': '='
  templateUrl: 'scripts/directive/measurementV2021Row/index.html'

  link: (scope, element, attrs) ->
    scope.rowVisible = false
    scope.costVisible = false

    getRowDesc = (row, uid) ->
      switch row.dimension
        when MEASUREMENT_DIMENSION.USER_PRODUCT
          "当前用户单产品 - #{row.fnc_product}"
        when MEASUREMENT_DIMENSION.USER
          "当前用户整体（#{uid}）"
        when MEASUREMENT_DIMENSION.SALES
          # TODO: LDAP -> 姓名
          "当前销售（#{row.sales}）"
        when MEASUREMENT_DIMENSION.SALES_AREA
          "当前销售大区（#{row.sales_area}）"
        when MEASUREMENT_DIMENSION.PRODUCT
          "当前产品线整体 - #{row.fnc_product}"
        when MEASUREMENT_DIMENSION.QINIU
          '七牛全公司整体'
        else
          "未知维度（#{row.dimension}）"

    refreshRowDesc = () ->
      scope.rowDesc = getRowDesc scope.row, scope.uid

    # 除数为 0 时返回 0 的除法操作
    guardedRatio = (numer, denom) ->
      if denom == 0
        0.0
      else
        numer / denom

    transformRow = (row) ->
      return {
        before_income: row.before_income,
        after_income: row.after_income,
        income_change_ratio: guardedRatio(row.after_income - row.before_income, row.before_income),
        cost: row.cost,
        before_gp: row.before_gross_profit,
        after_gp: row.after_gross_profit,
        gp_change_ratio: guardedRatio(row.after_gross_profit - row.before_gross_profit, row.before_gross_profit),
        before_gpm: guardedRatio(row.before_gross_profit, row.before_income),
        after_gpm: guardedRatio(row.after_gross_profit, row.after_income),
      }

    refreshDisplayRow = () ->
      scope.row = scope.displayRow.row
      scope.rowVisible = scope.displayRow.row_visible
      scope.costVisible = scope.displayRow.cost_visible

      refreshRowDesc()
      scope.renderRow = transformRow(scope.row)

    scope.$watch 'uid', refreshRowDesc
    scope.$watch 'displayRow', refreshDisplayRow
