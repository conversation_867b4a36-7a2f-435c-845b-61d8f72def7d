angular.module 'app.controller'
  .controller 'ApprovalCreditApplyCtrl', (
    $scope, $stateParams, $q,
    qbpmService, developerMgrService, userService, sofaService, notificationService, ContractingBodyTextMap
  ) ->
    $scope.excode = $stateParams.id || ''

    $scope.isInfoLoading = true
    $scope.isAttachmentLoading = true

    $scope.creditInfo = null
    $scope.attachmentInfoList = []
    $scope.ContractingBodyTextMap = ContractingBodyTextMap

    getAttachmentInfoByExcode = () ->
      sofaService.AttachmentsInfoByParent $scope.excode
        .then (resp) ->
          attachmentInfoList = resp || []
          $scope.attachmentInfoList = attachmentInfoList
        .finally () ->
          $scope.isAttachmentLoading = false

    qbpmService.GetParamByExcode($scope.excode)
      .then (param) ->
        $scope.creditInfo = param
        $scope.isInfoLoading = false

    getAttachmentInfoByExcode()
