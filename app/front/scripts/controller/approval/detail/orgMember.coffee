angular.module 'app.controller'
.controller 'OrgMemberDetailCtrl', (
  $scope, $stateParams, qbpmService
  FinancialTypeDisplay
) ->
  $scope.FinancialTypeDisplay = FinancialTypeDisplay
  $scope.excode = $stateParams.id
  $scope.detail = null
  # 获取审批详情
  getDetail = () ->
    qbpmService.GetParamByExcode($scope.excode).then (res) ->
      $scope.detail = res
      qbpmService.getBucketFileUrls($scope.detail.attachments).then (files) ->
        $scope.attachments = files

  getDetail()
