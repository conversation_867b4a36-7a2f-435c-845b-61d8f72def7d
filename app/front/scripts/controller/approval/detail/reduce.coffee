angular.module 'app.controller'
.controller 'ApprovalReduceApplyCtrl', (
  $scope, $stateParams, $q
  qbpmService, billService
) ->
  $scope.excode = $stateParams.id
  $scope.reduceData = {}
  FORMAT = 'YYYY-MM-DD'
  BillType = 1
  qbpmService.GetParamByExcode($scope.excode).then (res) ->
    $scope.reduceData = res
    $scope.uid = res.uid
    endDate = moment(res.endDate, FORMAT)
    products = Object.keys res.productMap
    promises = products.map (product) ->
      productPromise = []
      now = moment(res.startDate, FORMAT)
      start = now.startOf('month').format()
      end = moment(now).add(1, 'month').startOf('month').format()
      productPromise.push billService.getBillFee($scope.uid, start, end, product, BillType)
      while now.year() != endDate.year() || now.month() != endDate.month()
        now.add(1, 'month')
        start = now.startOf('month').format()
        end = moment(now).add(1, 'month').startOf('month').format()
        productPromise.push billService.getBillFee($scope.uid, start, end, product, BillType)
      return productPromise
    promises.forEach (p, productName) ->
      $q.all(p).then (data) ->
        $scope.reduceData.productMap[products[productName]]['monthBills'] = {}
        total_reduce = $scope.reduceData.productMap[products[productName]].feeItemList
        .map (reduceItem) -> reduceItem.reduceAmount
        .reduce (a, b) -> a + b
        data.forEach (bill, month) ->
          m = moment($scope.reduceData.startDate, FORMAT).add(month, 'month').format('YYYY-MM')
          billData =
            fee: 0
            reduce: 0
            simulation: 0
            rate: '-'
          if bill.data
            billData.fee = (bill.data.total_fee / 10000).toFixed(2)
            billData.reduce = -(total_reduce).toFixed(2)
            if bill.data.total_fee > total_reduce * 10000
              billData.reduce = -(total_reduce).toFixed(2)
            else
              billData.reduce = -(bill.data.total_fee / 10000).toFixed(2)
              billData.rate = '-100%'
            if bill.data.total_fee == 0
              billData.rate = '-'
            else
              billData.rate =
              ((billData.reduce * 10000 / bill.data.total_fee) * 100).toFixed(2) + '%'
            billData.simulation = (Number(billData.fee) + Number(billData.reduce)).toFixed(2)
          $scope.reduceData.productMap[products[productName]]['monthBills'][m] = billData
        
