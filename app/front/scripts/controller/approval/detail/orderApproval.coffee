angular.module 'app.controller'
.controller 'OrderApprovalDetailCtrl', (
  $scope, $stateParams, $q, CurrencyType
  qbpmService, pronoeService, orderProductService, developerMgrService, tradeService
) ->
  $scope.excode = $stateParams.id
  $scope.totalPrice = 0
  qbpmService.GetParamByExcode($scope.excode).then (res) ->
    $scope.approveInfo = res
    developerMgrService.getDeveloperInfo {uid: res.order_param.uid}
    .then (userInfo) ->
      $scope.userInfo = userInfo
      developerMgrService.getSalesInfo(userInfo.sfSalesId)
      .then (salesInfo) ->
        $scope.salesInfo = salesInfo

  $scope.$watch 'currencyType', (newVal) ->
    if !newVal
      return
    products_param = $scope.approveInfo.order_param.goods.map (g) -> {
      duration: g.duration
      quantity: g.quantity
      goods_type: g.goods_type
      goods_id: g.goods_id
    }
    productInfoPromises = products_param.map (p) ->
      if p.goods_type == 2
      then tradeService.listPackages({package_id: p.goods_id})
      else orderProductService.getProductByIDOrModel {id: p.goods_id}

    promotion_promises = products_param.map (p) ->
      body =
        uid: $scope.approveInfo.order_param.uid
        currency_type: $scope.currencyType
        goods: [p]
      pronoeService.clacPromotionFee(body)
    $q.all promotion_promises
    .then (data) ->
      $scope.totalPrice = 0
      data.forEach (d, index) ->
        target = $scope.approveInfo.order_param.goods[index]
        target.totalPrice = Number(d.goods[0].promotion_fee)
        $scope.totalPrice += target.totalPrice
    $q.all productInfoPromises
    .then (data) ->
      data.forEach (d, index) ->
        if d instanceof Array
          d = d[0]
        target = $scope.approveInfo.order_param.goods[index]
        target.name = d.name
  , true
