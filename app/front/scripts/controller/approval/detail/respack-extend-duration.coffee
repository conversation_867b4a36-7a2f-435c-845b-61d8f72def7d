angular.module 'app.controller'
.controller 'RespackExtendDurationApprovalDetailCtrl', (
  $scope, $stateParams,
  PRODUCT_UNIT_KEYS_MAP_TEXT, PRODUCT_UNIT_TO_CHAR
  qbpmService, tradeService, notificationService, developerMgrService
) ->
  $scope.PRODUCT_UNIT_MAP = PRODUCT_UNIT_KEYS_MAP_TEXT
  $scope.excode = $stateParams.id
  $scope.uid = $stateParams.uid
  $scope.process_instance_id = $stateParams.process_instance_id || ''

  $scope.transformDetail = ->
    $scope.orderDetail.product_orders.find (po) ->
      if po.id == $scope.approvalInfo.req_product_order_extend_duration.product_order_id
        # coffeelint: disable=max_line_length
        extend_duration = $scope.approvalInfo.req_product_order_extend_duration.extend_duration
        unit = po.product.unit
        po.new_end_time = moment(po.end_time).add(extend_duration, PRODUCT_UNIT_TO_CHAR[unit]).format('YYYY-MM-DD HH:mm:ss')

  getUserInfo = ->
    developerMgrService.getDeveloperInfo {uid: $scope.uid}
    .then (userInfo) ->
      $scope.userInfo = userInfo
      developerMgrService.getSalesInfo(userInfo.sfSalesId)
      .then (salesInfo) ->
        $scope.salesInfo = salesInfo
    .catch ->
      notificationService.warning '客户信息获取不完整'

  # 获取审批详情
  getDetail = () ->
    qbpmService.GetParamByExcode($scope.excode, $scope.process_instance_id)
    .then (res) ->
      $scope.approvalInfo = res
      $scope.orderHash = res.order_hash
      $scope.orderDetail = $scope.approvalInfo.extra_param.order_snapshot
      $scope.transformDetail()
    .catch ->
      notificationService.error '获取详情失败'

  getDetail()
  getUserInfo()
