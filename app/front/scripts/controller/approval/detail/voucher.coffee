angular.module 'app.controller'
.controller 'VoucherApprovalDetailController', (
  $scope, $stateParams
  responsibleBizType
  qbpmService, developerViewService, notificationService
) ->
  $scope.excode = $stateParams.id
  $scope.BIZ_TYPE = _.omit(responsibleBizType, (v) -> v.value < 100 || v.value > 103) # filter which is not coupon
  qbpmService.GetParamByExcode($scope.excode)
  .then (res) ->
    $scope.applyInfo = res.create_coupon_batch_params
    $scope.coupon = res.create_coupon_batch_params.coupons[0]
    developerViewService.getOverview({uid: $scope.coupon.uid})
    .then (developer) ->
      $scope.developerInfo = developer
    .catch () ->
      $scope.developerInfo = null
      notificationService.error '获取用户信息失败'