angular.module 'app.controller'
.controller 'ApplyWithdrawDetailCtrl', (
  $scope, $stateParams, developerMgrService, developerViewService, $state
  qbpmService, notificationService, withdrawApprovalService, modalService
  $modal, HumanizeWithdrawType, WithdrawReasonList, $q, freezeStrategyService
  FREEZE_STRATEGY, FREEZE_STRATEGY_TEXT_MAP, FREEZE_TYPE_TEXT, WITHDRAWTYPE
  PROPOSER_TYPE, WithdrawMethod, ContractingBodyTextMap
) ->
  $scope.HumanizeWithdrawType = HumanizeWithdrawType
  $scope.WithdrawMethod = WithdrawMethod
  $scope.WITHDRAWTYPE = WITHDRAWTYPE
  $scope.excode = $stateParams.id
  $scope.uid = $stateParams.uid
  $scope.enableEdit = false
  $scope.attachments = []
  $scope.FREEZE_STRATEGY = FREEZE_STRATEGY
  $scope.FREEZE_STRATEGY_TEXT_MAP = FREEZE_STRATEGY_TEXT_MAP
  $scope.FREEZE_TYPE_TEXT = FREEZE_TYPE_TEXT
  $scope.PROPOSER_TYPE = PROPOSER_TYPE
  $scope.ContractingBodyTextMap = ContractingBodyTextMap
  
  getUserInfo = ->
    developerMgrService.getDeveloperInfo {uid: $scope.uid}
    .then (userInfo) ->
      $scope.userInfo = userInfo
      developerMgrService.getSalesInfo(userInfo.sfSalesId)
      .then (salesInfo) ->
        $scope.salesInfo = salesInfo
    .catch ->
      notificationService.warning '客户信息获取不完整'

  # 获取审批详情
  getDetail = () ->
    qbpmService.GetParamByExcode($scope.excode)
    .then (res) ->
      $scope.approvalInfo = res
      $scope.applyWithdrawID = $scope.approvalInfo.withdraw_apply_id
      withdrawApprovalService.getDetail($scope.uid, $scope.applyWithdrawID)
      .then (detail) ->
        $scope.detail = detail
        $scope.detail.withdrawReasonType = (WithdrawReasonList.find (item) ->
          item.value == $scope.detail.withdraw_reason_type
        ).display
    .catch ->
      notificationService.error '获取详情失败'

  getStrategy = () ->
    freezeStrategyService.list {uid: $scope.uid}
    .then (res) ->
      $scope.strategy = res

  $scope.modifyStrategy = () ->
    modalService.confirm(
      "<p class='common-confirm-content'>
        <i class='fa fa-exclamation-triangle icon-warning'></i>
        &nbsp;&nbsp;确定修改冻结策略为 FC ？
      </p>"
    ).then () ->
      freezeStrategyService.update($scope.uid, FREEZE_STRATEGY.C)
      .then () ->
        $state.reload()

  getDetail()
  getUserInfo()
  getStrategy()

  developerViewService.getOverview {uid: $scope.uid}
    .then (res) ->
      $scope.currency = res.currency_type
      $scope.contractingBody = res.contracting_body

  $scope.showRechargeTransactions = () ->
    modal = $modal.open
      templateUrl: 'templates/approval/detail/rechargeTransactions.html'
      controller: 'WithdrawDetailRechargeTransactionsModalCtrl',
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: -> $scope.uid
        currency: -> $scope.currency
        withdrawType: -> $scope.detail.withdraw_type
        withdrawID: -> $scope.applyWithdrawID
    
    modal.result.then (result) ->
      $scope.withdraws = result
      $scope.withdraws.forEach (item) ->
        item.money = Math.round(item.money * 10000)
        item.nb_offsetting_cash = Math.round(item.nb_offsetting_cash * 10000)
      requestBody =
        uid: $scope.uid
        apply_withdraw_id: $scope.applyWithdrawID
        audit_comment: $scope.detail.financial_info.audit_comment
        withdraws: $scope.withdraws
        attachments: []
        delete_attachments: []
      withdrawApprovalService.modifyApplication requestBody
      .then () ->
        getDetail()
      .catch (err) ->
        notificationService.error err

  $scope.editComment = (type) ->
    modal = $modal.open
      templateUrl: 'templates/approval/detail/editComment.html'
      controller: 'EditCommentModalCtrl',
      backdrop: 'static'
      resolve:
        auditComment: -> $scope.detail.financial_info.audit_comment
        financialComment: -> $scope.detail.financial_info.financial_comment
        commentType: -> type

    modal.result.then (result) ->
      if type == 'audit'
        $scope.detail.financial_info.audit_comment = result
      else
        $scope.detail.financial_info.financial_comment = result
      $scope.saveComment()
  
  $scope.saveComment = () ->
    requestBody =
      uid: $scope.uid
      apply_withdraw_id: $scope.applyWithdrawID
      audit_comment: $scope.detail.financial_info.audit_comment
      financial_comment: $scope.detail.financial_info.financial_comment
      withdraws: $scope.detail.withdraws
      attachments: []
      delete_attachments: []
    withdrawApprovalService.modifyApplication requestBody
    .then () ->
      $scope.enableEdit = false
      getDetail()

  $scope.cancelEditComment = () ->
    $scope.enableEdit = false
    getDetail()
  
  blobToBase64 = (file) ->
    return $q (res) ->
      reader = new FileReader()
      reader.readAsDataURL(file.file)
      reader.addEventListener('load', ->
          res(reader.result)
      )

  $('#withdrawFileUpload').on('change', (e) ->
    files = e.target.files
    if files.length == 0 then $scope.attachments = []
    else
      i = 0
      while i < files.length
        $scope.attachments.push {
          file: files[i]
          file_name: files[i].name
          content_base64: null
        }
        i++
      promises = $scope.attachments.map (file) -> blobToBase64(file)
      $q.all promises
      .then (res) ->
        res.forEach (item, index) ->
          $scope.attachments[index].content_base = item
        requestBody =
          uid: $scope.uid
          apply_withdraw_id: $scope.applyWithdrawID
          audit_comment: $scope.detail.financial_info.audit_comment
          withdraws: $scope.detail.withdraws
          attachments: $scope.attachments
          delete_attachments: []
        withdrawApprovalService.modifyApplication requestBody
        .then () ->
          $state.reload()
  )

angular.module 'app.controller'
.controller 'WithdrawDetailRechargeTransactionsModalCtrl', (
  $scope, withdrawApprovalService, uid, $modalInstance, currency, withdrawType, withdrawID
  humanizeDepositType
) ->
  $scope.humanizeDepositType = humanizeDepositType
  $scope.currency = currency
  withdrawApprovalService.getRechargeTransactions({
    uid: uid,
    withdrawType: withdrawType,
    withdrawID, withdrawID,
    needNB: true,
    needTransferaccin: true
  })
  .then (res) ->
    $scope.rechargeTransactions = res.map (item) ->
      item.money = item.money / 10000
      item.nb_offsetting_cash = item.nb_offsetting_cash / 10000
      item.selected = !!item.details_id
      return item
  $scope.close = ->
    $modalInstance.dismiss 'close'
  
  $scope.selectTransaction = (item) ->
    item.selected = true

  $scope.$watch 'rechargeTransactions', (newVal) ->
    selected = newVal.find (item) -> item.selected
    $scope.noSelected = !selected
    $scope.invalid = !!(newVal.find (item) -> item.selected && (
      Math.round((item.money + item.nb_offsetting_cash) * 10000) > item.withdrawable_money
    ))
  , true

  $scope.submit = () ->
    selected = $scope.rechargeTransactions.filter (item) -> item.selected
    $modalInstance.close selected

angular.module 'app.controller'
.controller 'EditCommentModalCtrl', (
  $scope, auditComment, financialComment, $modalInstance, commentType
) ->
  $scope.auditComment = auditComment
  $scope.financialComment = financialComment
  $scope.comment = if commentType == 'audit' then $scope.auditComment else $scope.financialComment
  $scope.ok = () ->
    $modalInstance.close $scope.comment
  $scope.cancel = () ->
    $modalInstance.dismiss 'cancel'

      