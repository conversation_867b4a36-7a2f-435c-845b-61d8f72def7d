angular.module 'app.controller'
.controller 'RegenSyncedCustombillDetailCtrl', (
  $scope, $stateParams, qiniuProduct
  FREEZE_TYPE_TEXT_MAP, FREEZE_TYPE, FREEZE_FREEZE_TYPE
  qbpmService, developerMgrService, notificationService
  walletBizService,
) ->
  $scope.excode = $stateParams.id
  $scope.isLoading = false

  getUserInfo = (uid) ->
    $scope.isLoading = true
    $scope.uid = uid
    developerMgrService.getDeveloperInfo {uid: $scope.uid}
    .then (userInfo) ->
      $scope.userInfo = userInfo
    .catch ->
      notificationService.warning '客户信息获取不完整'
    .finally () ->
      $scope.isLoading = false

  getDetail = () ->
    qbpmService.GetParamByExcode($scope.excode)
    .then (res) ->
      $scope.approvalInfo = res
      $scope.approvalInfo.month_start = moment.unix(res.start).format('YYYY-MM')
      $scope.approvalInfo.month_end = moment.unix(res.end).subtract(1, 'month').format('YYYY-MM')
      console.log $scope.approvalInfo
      getUserInfo($scope.approvalInfo.uid)
      splitAndSyncBills(
        $scope.approvalInfo.month_start,
        $scope.approvalInfo.month_end,
        $scope.approvalInfo.uid,
        $scope.approvalInfo.product,
      )
    .catch ->
      notificationService.error '获取详情失败'

  $scope.getUniqueItems = (month) ->
    return [] unless month?.newBills? or month?.oldBills?
    items = new Set()
    if month.newBills?
      for bill in month.newBills
        items.add(bill.item_str) if bill.item_str?
    if month.oldBills?
      for bill in month.oldBills
        items.add(bill.item_str) if bill.item_str?
    Array.from(items).sort()

  $scope.getBillByItemStr = (bills, item_str) ->
    return null unless bills? and item_str?
    bill = bills.find((b) -> b?.item_str == item_str)
    return null unless bill?
    parseFloat(bill?.money) || 0

  $scope.getDiff = (newVal, oldVal) ->
    newValue = parseFloat(newVal) || 0
    oldValue = parseFloat(oldVal) || 0
    newValue - oldValue

  $scope.compareBillData = ->
    return unless $scope.custombills? and $scope.approvalInfo?.month_summary?

    comparedData = {}

    # Process old bills first
    for monthData in $scope.approvalInfo.month_summary
      month = monthData.month
      comparedData[month] = {
        month: moment(month, 'YYYYMM').format('YYYYMM')  # 确保月份格式正确
        newBills: []
        oldBills: monthData.bills || []
        totalNew: 0
        totalOld: parseFloat(monthData.money) || 0
      }

    # Process new bills and merge
    for monthData in $scope.custombills
      month = monthData.month
      if comparedData[month]?
        comparedData[month].newBills = monthData.bills || []
        comparedData[month].totalNew = parseFloat(monthData.money) || 0
      else
        comparedData[month] = {
          month: moment(month, 'YYYYMM').format('YYYYMM')  # 确保月份格式正确
          newBills: monthData.bills || []
          oldBills: []
          totalNew: parseFloat(monthData.money) || 0
          totalOld: 0
        }

    # Convert to array and sort by month
    $scope.comparedBills = Object.values(comparedData)
    $scope.comparedBills.sort (a, b) ->
      moment(a.month, 'YYYYMM').diff(moment(b.month, 'YYYYMM'))

  splitAndSyncBills = (start, end, uid, product) ->
    $scope.isLoading = true
    startDate = moment(start, 'YYYY-MM')
    endDate = moment(end, 'YYYY-MM')
    promises = []

    currentDate = startDate.clone()
    while currentDate.isBefore(endDate) or currentDate.isSame(endDate, 'month')
      monthStart = currentDate.unix()
      monthEnd = currentDate.clone().add(1, 'month').unix()

      promise = walletBizService.syncCustomBill
        uid: uid
        product: product
        start: monthStart
        end: monthEnd
        is_dummy: true
        debug: false
        ignore_measure_user: true

      promises.push(promise)
      currentDate.add(1, 'month')

    Promise.all(promises)
    .then (results) ->
      # Transform and merge results into the required structure
      mergedBills = results.reduce((acc, curr) ->
        return acc unless curr?
        for bill in curr
          try
            detail = JSON.parse(bill.detail)
            month = moment(detail.month, 'YYYYMM').format('YYYYMM')  # 确保月份格式正确
            detailMoney = parseFloat(detail.money) || 0

            existingMonth = acc.find((item) -> item.month == month)

            if existingMonth
              existingMonth.bills.push(detail)
              existingMonth.money = (existingMonth.money || 0) + detailMoney
            else
              acc.push({
                month: month
                money: detailMoney
                bills: [detail]
              })
          catch e
            console.error('Error processing bill:', e)
        return acc
      , [])

      # Sort bills within each month by item_str
      for monthData in mergedBills
        monthData.bills.sort (a, b) ->
          (a?.item_str || '').localeCompare(b?.item_str || '')

      # Sort months chronologically
      mergedBills.sort (a, b) ->
        moment(a.month, 'YYYYMM').diff(moment(b.month, 'YYYYMM'))

      $scope.custombills = mergedBills
      $scope.compareBillData()
      $scope.$apply() if !$scope.$$phase
    .catch (err) ->
      console.error('Error in splitAndSyncBills:', err)
      notificationService.error '获取即时账单数据失败'
    .finally ->
      $scope.isLoading = false

  getDetail()
