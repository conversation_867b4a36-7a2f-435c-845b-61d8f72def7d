angular.module 'app.controller'

.controller 'ApplySalesGuaranteeDetailCtrl', (
  $scope, $stateParams
  notificationService
  qbpmService, identityService
) ->

  $scope.excode = $stateParams.id || ''
  $scope.currentApply = null
  $scope.historySalesGuarantee = null
  $scope.isPageLoading = true

  qbpmService.GetParamByExcode($scope.excode)
  .then (param) ->
    promises = [identityService.getSalesGuaranteeByID(param.current.id)]
    if param.history && param.history.id
      promises.push identityService.getSalesGuaranteeByID(param.history.id)
    getApprovalData promises
  .finally ->
    $scope.isPageLoading = false

  getApprovalData = (promises) ->
    $scope.isPageLoading = true
    Promise.all promises
    .then (results) ->
      if results?.length == 0
        notificationService.error '未获取大客户认证信息'
        return
      $scope.currentApply = results[0]
      if results.length == 2
        $scope.historySalesGuarantee = results[1]
      $scope.isPageLoading = false
    .catch (err) ->
      notificationService.error '获取大客户认证信息失败'
      $scope.isPageLoading = false
