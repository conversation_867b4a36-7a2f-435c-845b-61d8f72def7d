angular.module 'app.controller'
.controller 'ApprovalCouponDetailController', (
  $scope, $state, $stateParams, $q,
  newCouponService, notificationService, developerMgrService, tradeService,
  newCouponMap, qbpmService, sellerService
) ->
  $scope.excode = $stateParams.id
  $scope.BIZ_TYPE = newCouponMap.reasonType
  $scope.coupon = {}

  transform = (coupon) ->
    fmtLayout = 'YYYY-MM-DD HH:mm:ss'
    couponEffectTimeStr = moment(coupon.coupon_effect_time).utcOffset(8).format(fmtLayout)
    couponDeadTimeStr = moment(coupon.coupon_dead_time).utcOffset(8).format(fmtLayout)

    sellersStr = ''
    if coupon.is_unlimited_scope
      sellersStr = '全部产品线'
    else
      sellersStr = (coupon.scope.seller_ids_include.map (item) ->
        sellerTarget = $scope.sellerList.find (seller) -> seller.id == item
        if sellerTarget then (sellerTarget.id + '-' + sellerTarget.title) else '未知'
      ).join('、')

    c = angular.copy(coupon)
    c.coupon_effect_time = couponEffectTimeStr
    c.coupon_dead_time = couponDeadTimeStr
    c.sellers = sellersStr
    return c

  sellerRq = sellerService.getSellerList({status: 1, page_size: 200})
  couponInfoRq = qbpmService.GetParamByExcode($scope.excode)
  $q.all([sellerRq, couponInfoRq])
  .then (data) ->
    sellers = data[0]
    couponData = data[1]
    $scope.sellerList = sellers.map (item) ->
      id: item.id
      title: item.title
    $scope.coupon = transform(couponData.create_coupon_batch_params)
    $scope.coupon.uid = couponData.uid

  $scope.$watch 'coupon.uid', (newVal) ->
    if newVal
      developerMgrService.getDeveloperInfo({uid: newVal})
      .then (res) ->
        $scope.coupon.fullname = res.fullName
      .catch (err) ->
        $scope.coupon.fullname = ''
        notificationService.error '获取用户信息失败'
