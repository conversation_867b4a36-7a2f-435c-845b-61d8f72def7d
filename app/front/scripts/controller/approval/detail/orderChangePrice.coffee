angular.module 'app.controller'
.controller 'OrderChangePriceDetailCtrl', (
  $scope, $stateParams,
  PRODUCT_UNIT_KEYS_MAP_TEXT, compareUtil
  qbpmService, tradeService, notificationService, developerMgrService, gaeaService
) ->
  $scope.PRODUCT_UNIT_MAP = PRODUCT_UNIT_KEYS_MAP_TEXT
  $scope.excode = $stateParams.id
  $scope.uid = $stateParams.uid
  $scope.process_instance_id = $stateParams.process_instance_id || ''
  $scope.lowPriceUserText = ''

  getUserInfo = ->
    developerMgrService.getDeveloperInfo {uid: $scope.uid}
    .then (userInfo) ->
      $scope.userInfo = userInfo
      developerMgrService.getSalesInfo(userInfo.sfSalesId)
      .then (salesInfo) ->
        $scope.salesInfo = salesInfo
    .catch ->
      notificationService.warning '客户信息获取不完整'

  # 获取低价用户状态
  getLowPriceUserText = (uid) ->
    gaeaService.getLowPriceProductsByUID(uid)
    .then (res) ->
      currentLowPrice = res.low_price_products?.length > 0
      approvalLowPrice = $scope.approvalInfo.low_price_products?.length > 0

      if approvalLowPrice
        $scope.lowPriceUserText = '该客户是 SMB 低价线路客户，请产品线审核时注意 '
        $scope.isLowPriceChanged = true
      if currentLowPrice && !approvalLowPrice
        $scope.lowPriceUserText = '该客户原本是 SMB 低价线路客户，本次申请取消低价线路状态，请产品线审核时注意'
        $scope.isLowPriceChanged = true
    .catch (err) ->
      notificationService.error '获取低价用户状态失败:', err

  # 获取审批详情
  getDetail = () ->
    qbpmService.GetParamByExcode($scope.excode, $scope.process_instance_id)
    .then (res) ->
      $scope.approvalInfo = res
      $scope.orderHash = res.order_hash
      $scope.orderDetail = $scope.approvalInfo.extra_param.order_snapshot
      $scope.poUnitPrices = {}
      if res.extra_param && res.extra_param.unit_prices
        for p in res.extra_param.unit_prices
          if p.last_approved
            p.delta = compareUtil.comparePercentage p.unit_price, p.last_approved
            p.delta_abs = Math.abs(p.delta)
          $scope.poUnitPrices[p.id] = p

      if res.req_update_price.po_actually_fee && res.req_update_price.po_actually_fee.length > 0
        sum = 0
        res.req_update_price.po_actually_fee.map (po) ->
          sum += po.actually_fee
        $scope.approvalInfo.req_update_price.po_actually_fee_sum = sum
      getLowPriceUserText($scope.uid)
    .catch ->
      notificationService.error '获取详情失败'

  getDetail()
  getUserInfo()
