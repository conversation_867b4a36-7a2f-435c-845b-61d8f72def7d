angular.module 'app.controller'
.controller 'RechargeRewardApplyDetailCtrl', (
   $scope, $stateParams, qiniuProduct
   developerViewService, qbpmService, notificationService
   riskApplyService, TransactionTypeTranslation
) ->
  $scope.TransactionTypeTranslation = TransactionTypeTranslation
  $scope.excode = $stateParams.id
  $scope.uid = $stateParams.uid
  $scope.qiniuProduct = qiniuProduct

  # 获取审批详情
  getDetail = () ->
    qbpmService.GetParamByExcode($scope.excode)
    .then (res) ->
      $scope.approvalInfo = res

  getUserInfo = ->
    developerViewService.getOverview {uid: $scope.uid}
    .then (res) ->
      $scope.developerInfo = res
    .catch () ->
      notificationService.warning '客户信息获取不完整'

  getDummyProducts = () ->
    riskApplyService.getDummyProducts $scope.uid
    .then (res) ->
      $scope.dummyProducts = res.products

  getDetail()
  getUserInfo()
  getDummyProducts()
  