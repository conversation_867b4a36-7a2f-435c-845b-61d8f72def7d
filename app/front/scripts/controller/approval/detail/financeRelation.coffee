angular.module 'app.controller'

.controller 'ApprovalFinanceRelationApplyCtrl', (
  $scope, $stateParams, qbpmService
) ->

  $scope.excode = $stateParams.id || ''
  $scope.process_instance_id = $stateParams.process_instance_id || ''
  $scope.financeRelationInfo = null
  $scope.isPageLoading = true

  qbpmService.GetParamByExcode($scope.excode, $scope.process_instance_id)
    .then (param) ->
      $scope.financeRelationInfo = param
      $scope.isPageLoading = false
