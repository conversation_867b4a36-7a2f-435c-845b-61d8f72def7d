angular.module 'app.controller'
  .controller 'ApprovalTestingApplyCtrl', (
    $rootScope,
    $scope,
    $q,
    $modal,
    $state,
    $stateParams,
    qbpmService,
    developerMgrService,
    userService,
    dictv4Service
  ) ->
    $scope.excode = $stateParams.id
    $scope.isLoading = true

    # 部分计费项的 product name 最后一段是区域 code，展示的时候要去掉
    getProductName = (name) ->
      index = name.lastIndexOf('-')
      if index > 0
        return name.substr(0, index)
      return name

    itemsToItemGroup = (items) ->
      return {
        product: getProductName(items[0].product_name),
        items: items
      }

    mapProductCode = (productCode) ->
      if productCode == 'storage'
        return 'kodo'
      if productCode == 'fusionov'
        return 'fusion'
      return productCode

    loadFormData = (userInfo) ->
      qbpmService.GetParamByExcode($scope.excode)
      .then (res) ->
        param = res
        allProductCodes = _.map(param.items, (i)->mapProductCode(i.product_code))
        qbpmService.getUserAvailableProductsByCodes(allProductCodes)
        .then (resp) ->
          visibleItems = _.filter(param.items, (item)-> _.includes(resp.codes, mapProductCode(item.product_code)))
          itemGroups = _.chain(visibleItems)
          .groupBy('product_code')
          .mapValues(itemsToItemGroup)
          .value()
          $scope.data = _.merge(param, {itemGroups: itemGroups})
        .finally ->
            $scope.isLoading = false

      .finally ->
        $scope.isLoading = false
    
    $rootScope.$watch 'userInfo', (userInfo) ->
      if !userInfo then return
      loadFormData userInfo
