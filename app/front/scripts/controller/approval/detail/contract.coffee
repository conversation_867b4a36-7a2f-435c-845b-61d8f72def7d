angular.module 'app.controller'
  .controller 'ApprovalContractApplyCtrl', (
    $scope,
    $modal,
    $state,
    $stateParams,
    $q,
    sofaService,
    identityService,
    notificationService,
    ContractingBodyTextMap,
    HANDLE_EXECUTION_ACTION_TYPE
  ) ->
    $scope.isLoading = true
    $scope.showFinanceHeader = false
    $scope.showFinanceEdit = false
    $scope.charge_type_map = {
      'PublicCustom': '公有云非标计费',
      'Other': '其他',
      'Consult': '咨询服务',
      'TSService': '技术支持服务',
      'PrivateProject': '私有云实施',
      'SoftwareLicence': '软件授权',
      'MServer': '机器托管'
    }
    $scope.product_map = {
      'kodo': 'Kodo'
      'kodo_ov': 'Kodo海外'
      'kodo_origin': 'Kodo外网流出',
      'kodo_origin_ov': 'Kodo外网流出海外',
      'kodo_origin_cdn': 'Kodo_CDN回源',
      'kodo_private': 'Kodo私有云',
      'fusion': 'QCDN',
      'fusion_ov': 'QCDN海外',
      '302cdn': '302CDN',
      'lcdn': 'LCDN',
      'lcdn_ov': 'LCDN海外',
      'pcdnsdk': 'PCDNSDK',
      'ssl_certificate': 'SSL证书',
      'dora': '多媒体处理',
      'dora_private': '多媒体处理私有云',
      'lemon': '内容审核',
      'lemon_private': '内容审核私有云',
      'market': '生态拓展',
      'pili': '直播云',
      'pili_ov': '直播云海外',
      'pili_live': '企业直播',
      'pili_live_private': '企业直播私有云',
      'pili_sdk': '短视频SDK',
      'mikustream': 'Miku流媒体',
      'qrtc': 'QRTC',
      'qvs': 'QVS',
      'qvs_private': 'QVS私有云',
      'ums': 'UMS',
      'sms': '云短信',
      'sms_ov': '云短信海外',
      'kirk': '容器云',
      'pandora': 'Pandora',
      'ataraxia': '机器学习',
      'mserver': '机器托管',
      'qvm': 'QVM云主机',
      'qvm_ov': 'QVM云主机海外',
      'GPUpool': '推理智算',
      'luckin': '算力集群',
      'gpu_leasing': 'GPU租赁',
      'las': 'LAS',
      'las_ov': 'LAS海外',
      'aitoken': 'AI推理',
      'idc_hosting': '机器托管',
      'idc_hosting_ov': '机器托管海外',
      'vrman': '数字人',
      'others': '其他',
      'others_private': '其他',
      'qiniu': '公司级',
    }
    $scope.contract_type_map = {
      'StandardContract': '标准合同',
      'CustomContract': '非标合同'
    }

    $scope.quote_sheet_status_map = {
      1: '草稿',
      2: '审批中',
      3: '已批准',
      4: '已拒绝'
    }

    $scope.finance_map = {
      'PublicCustom': [],
      'Other': ['human_cost', 'total_cost', 'gross_profit'],
      'Consult': ['human_cost', 'total_cost', 'gross_profit'],
      'SoftwareLicence': ['human_cost', 'total_cost', 'gross_profit'],
      'TSService': ['human_cost', 'total_cost', 'gross_profit'],
      'PrivateProject': ['human_cost', 'total_cost', 'gross_profit', 'hardware_purchase_agent_cost'],
      'MServer': ['machine_trustee_cost', 'human_cost', 'total_cost', 'gross_profit', 'bandwidth_cost'],
    }
    $scope.ContractingBodyTextMap = ContractingBodyTextMap

    $scope.host = window.location.protocol + '//' + window.location.host

    # 更新saleforce的model
    $scope.financeParams = {}
    $scope.contractInfo = {}
    $scope.excode = $stateParams.id

    validateFinanceParams = () ->
      if $scope.isStandardContract
        return true
      keys = $scope.finance_map[$scope.data.custom_pricing.record_type.developer_name] || []
      for key in keys
        if $scope.financeParams[key] == undefined
          return false
      return true

    $scope.checkTwoDecimals = (event) ->
      key = event.target.getAttribute('ng-model').split('.')[1]
      if event.target.value < 0
        event.target.value = ''
        delete $scope.finance[key]
      
      strs = event.target.value.split('.')
      if strs.length == 2 && strs[1].length > 2
        event.target.value = event.target.value.substring(0, event.target.value.length - strs[1].length + 2)
     
    $scope.checkProfit = (event) ->
      strs = event.target.value.split('.')
      if strs.length == 2 && strs[1].length > 2
        event.target.value = event.target.value.substring(0, event.target.value.length - strs[1].length + 2)

    $scope.approveCallback = (actionType, execution) ->
      return $q (resolve, reject) ->
        isApprove = actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_PASS ||
        actionType == HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_COUNTER_SIGN
        if document.getElementById('contract_number_edit') && isApprove && execution?.name == '销售运营审批'
          if !$scope.contractInfo.contract_no
            notificationService.error '合同编号字段必填'
            reject()
          else
             sofaService.ContractPartialUpdate $scope.excode, $scope.contractInfo
              .then (res) ->
                resolve()
              , (res) ->
                reject()
        else if document.getElementById('profit') && isApprove && isApprove && execution?.name == '财务经理审批'
          if !validateFinanceParams()
            notificationService.error '表单字段必填'
            reject()
          else
            sofaService.ContractPartialUpdate $scope.excode, $scope.financeParams
              .then (res) ->
                resolve()
              , (res) ->
                reject()
        else
          resolve()
    
    $scope.getContractDetail = (contractId) ->
      $scope.isLoading = true
      sofaService.ContractDetail contractId
        .then (result) ->
          $scope.uid = result.contract_info.uid
          $scope.data = result
          $scope.isStandardContract = result.contract_info.record_type.developer_name == 'StandardContract'
          if !$scope.isStandardContract
            $scope.isPublicCustomPricing = result.custom_pricing.record_type.developer_name == 'PublicCustom'
          $scope.currencyType = result.contract_info.currency_type
          $scope.financeCheck(result)

          return sofaService.ListQuoteSheets({
            uid: $scope.uid
            status: 3
            biz_types: [1, 2, 3]
            page_size: 100
            page: 1
          }).then (result) ->
            if result.quote_sheets
              $scope.quoteSheets = result.quote_sheets.filter (quoteSheet) ->
                return quoteSheet.contract_id == contractId
        .finally ->
          $scope.isLoading = false

    $scope.getIdentityInfo = (uid) ->
      identityService.getIdentityDetail(uid)
        .then (identityInfo) ->
          $scope.identityInfo = identityInfo

    $scope.financeCheck = (data) ->
      if $scope.isStandardContract || $scope.isPublicCustomPricing
        return
      $scope.showFinanceHeader = true
      if !data.contract_info.contract_no
        return
      count = 0
      keys = $scope.finance_map[data.custom_pricing.record_type.developer_name] || []
      for key in keys
        if data.custom_pricing[key] == null
          count = count + 1
          break
      if count != 0
        $scope.showFinanceEdit = true

    $scope.getContractDetail($scope.excode)
    $scope.getIdentityInfo($stateParams.uid)
