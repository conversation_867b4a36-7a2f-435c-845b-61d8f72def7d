angular.module 'app.controller'
.controller 'UnfreezeApplyDetailCtrl', (
  $scope, $stateParams, qiniuProduct
  FREEZE_TYPE_TEXT_MAP, FREEZE_TYPE, FREEZE_FREEZE_TYPE
  qbpmService, developerMgrService, developerViewService
  freezeListService, riskApplyService
) ->
  $scope.excode = $stateParams.id
  $scope.qiniuProduct = qiniuProduct
  $scope.uid = $stateParams.uid

  $scope.FREEZE_TYPE = FREEZE_TYPE
  $scope.FREEZE_TYPE_TEXT_MAP = FREEZE_TYPE_TEXT_MAP
  $scope.FREEZE_FREEZE_TYPE = FREEZE_FREEZE_TYPE

  getUserInfo = ->
    developerViewService.getOverview {uid: $scope.uid}
    .then (userInfo) ->
      $scope.userInfo = userInfo
    .catch ->
      notificationService.warning '客户信息获取不完整'

  getDetail = () ->
    qbpmService.getUnfreezeApplicationDetail($scope.excode)
    .then (res) ->
      $scope.detail = res

  freezeListService.getFreezeListInfo({uid: $scope.uid, page_size: 10, order: 'desc'})
    .then (data) ->
      $scope.showFreezeList = data || []

  getDummyProducts = () ->
    riskApplyService.getDummyProducts $scope.uid
    .then (res) ->
      $scope.dummyProducts = res.products

  getDetail()
  getUserInfo()
  getDummyProducts()
