angular.module 'app.controller'
  .controller 'ApprovalPriceChangeCtrl', (
    $scope,
    $modal,
    $state,
    $stateParams,
    qbpmService,
    sofaService,
    developerViewService,
    userService,
    gaeaService,
    STAIR_PRICE_TYPE,
    ACTIVITY_STATUS,
    ACTIVITY_STATUS_MAP,
    RESPONSIBLE_OWNER_MAP,
    HANDLE_EXECUTION_ACTION_TYPE,
  ) ->

    $scope.STAIR_PRICE_TYPE = STAIR_PRICE_TYPE
    $scope.ACTIVITY_STATUS_MAP = ACTIVITY_STATUS_MAP
    $scope.RESPONSIBLE_OWNER_MAP = RESPONSIBLE_OWNER_MAP
    $scope.excode = $stateParams.id
    $scope.lowPriceUserText = ''

    # 是否需要标红
    # types 由 因价格相同被归并的阶梯的type组成
    $scope.isDiff = (types) ->
      for type in types
        if type != 'EQ'
          return true
      return false

    getLowPriceUserText = (uid) ->
      gaeaService.getLowPriceProductsByUID(uid)
      .then (res) ->
        currentLowPrice = res.low_price_products?.length > 0
        approvalLowPrice = $scope.currentLowPrice?.length > 0

        if approvalLowPrice
          $scope.lowPriceUserText = '该客户是 SMB 低价线路客户，请产品线审核时注意 '
          $scope.isLowPriceChanged = true
        if currentLowPrice && !approvalLowPrice
          $scope.lowPriceUserText = '该客户原本是 SMB 低价线路客户，本次申请取消低价线路状态，请产品线审核时注意'
          $scope.isLowPriceChanged = true
      .catch (err) ->
        notificationService.error '获取低价用户状态失败:', err

    parseStairs = (prices) ->
      # 通过order对stairs排序
      p = _.sortBy prices, [(o) -> o.stair.price_item_stair.order ]
      # 把上一个的end作为下一个的start存到next中
      next = null
      # 存 归并后的阶梯
      newP = []
      for price, i in p
        # 如果当前阶梯价和上个阶梯价相同，则归并
        # 特殊地，
        #     1: 最后一个阶梯被拆成多个价格相同的阶梯，如 0 - 10，10 - 20，20 以上，其中后两个阶梯价格一样；
        #     2: 同样是最后一个阶梯被拆分，区别的是从 0 开始被拆成多个价格相同的阶梯，如 0 - 10，10 以上，均为相同价格；
        if newP.length > 0 &&
          newP[newP.length - 1].end.price_item_stair.price == price.stair.price_item_stair.price
            if i < p.length - 1
              next = newP[newP.length - 1].end = price.stair
            else if newP[newP.length - 1].start
              # coffeelint: disable=max_line_length
              newP[newP.length - 1].end.price_item_stair.quantity = newP[newP.length - 1].start.price_item_stair.quantity
              # coffeelint: enable=max_line_length
              newP[newP.length - 1].end.price_item_stair.unit_id = newP[newP.length - 1].start.price_item_stair.unit_id
            else
              newP[newP.length - 1].end.price_item_stair.quantity = '0'

            # 把type放到归并后的types中
            newP[newP.length - 1].types.push price.type
            if price.cost_diff == 'LT'
              newP[newP.length - 1].cost_diff = price.cost_diff
            continue

        # 如果有next，则作为当前的start
        if next
          price.start = next

        # 把上一个的end作为下一个的start存到next中
        next = price.end = price.stair
        delete price.stair

        # 把type放到归并后的types中
        price.types = [price.type]
        delete price.type

        # 存 归并后的阶梯
        newP.push price

      return newP

    getKey = (price) ->
      # 用 itemid 和 zoneid 作为键
      price.price_item.item.id + '_' + price.price_item.zone.id

    groupByProduct = {}
    $scope.isLoading = true
    $scope.havePricingExtra = false
    $scope.testingApplications = []
    qbpmService.GetParamByExcode($scope.excode)
    .then (param) ->
      $scope.remark = param.remark
      $scope.responsibleOwner = param.responsible_owner
      $scope.currentLowPrice = param.low_price_products
      $scope.isRegen = param.need_regen_bills
      $scope.customer_introduction = param.customer_introduction
      $scope.fetchPricingExtra()
      if param.uploaded_file_keys
        sofaService.getFilesURLByKey(param.uploaded_file_keys)
        .then (response) ->
          $scope.uploadedFiles = response
          $scope.showFiles = response.length > 0 ? true : false
      else
        $scope.showFiles = false

      qbpmService.GetPriceDiffWithPriceUserMapID $stateParams.id
      .then (response) ->

        if response.uid
          $scope.uid = response.uid
          developerViewService.getOverview {uid: response.uid}
            .then (response) ->
              $scope.developer = response
              getLowPriceUserText($scope.uid)

        # 把 before 按产品解析到 groupByProduct 中
        _.forOwn response.before.item_prices, (p) ->
          for price, _ in p.price_items
            productCode = price.price_item.product.code
            if groupByProduct[productCode] == undefined
              groupByProduct[productCode] = {product: price.price_item.product, list: {}}
            key = getKey price
            price.stairs = parseStairs price.stairs
            if groupByProduct[productCode].list[key] == undefined
              groupByProduct[productCode].list[key] = {}
            groupByProduct[productCode].list[key].before = price

        # 把 after 按产品解析到 groupByProduct 中
        _.forOwn response.after.item_prices, (p) ->
          for price, _ in p.price_items
            if !$scope.effectTime
              $scope.effectTime = price.price_item.price_item.effect_time
              $scope.deadTime = price.price_item.price_item.dead_time
            productCode = price.price_item.product.code
            if groupByProduct[productCode] == undefined
              groupByProduct[productCode] = {product: price.price_item.product, list: {}}
            key = getKey price
            price.stairs = parseStairs price.stairs
            if groupByProduct[productCode].list[key] == undefined
              groupByProduct[productCode].list[key] = {}
            groupByProduct[productCode].list[key].after = price

        # 对 算法 做diff
        _.forOwn groupByProduct, (product, code) ->
          # 根据有没有 before 判断用户对于该产品线是否为新用户
          groupByProduct[code].isNew = ( _.findKey product.list, (item) -> item.before != undefined ) == undefined
          _.forOwn product.list, (item, key) ->
            if item.before != undefined && item.before.price_item.algorithm.id != item.after.price_item.algorithm.id
              product.list[key].after.price_item.algorithm.diff = true
              groupByProduct[code] = product
          groupByProduct[code].list = _.values groupByProduct[code].list

        #测算
        qbpmService.GetMeasurement $stateParams.id
          .then (response) ->
            $scope.measurementsVersion = switch response.version
              when 'MEASUREMENT_RESP_VERSION_1'
                1
              when 'MEASUREMENT_RESP_VERSION_2021'
                2021
              # 让 default 分支代表 v1 是为了兼容完整 v2021 逻辑上线前的后端
              else
                1

            $scope.onMeasurementV1Response response
            if $scope.measurementsVersion == 2021
              $scope.onMeasurementV2021Response response
        for product of groupByProduct
          list = groupByProduct[product].list
          sortFunc = (a,b) ->
            aHasDiff = _.some(a.after.stairs, (e)->_.some(e.types, (i)->i!='EQ'))
            bHasDiff = _.some(b.after.stairs, (e)->_.some(e.types, (i)->i!='EQ'))
            if aHasDiff && !bHasDiff
              return -1
            if !aHasDiff && bHasDiff
              return 1
            if a.after.price_item.algorithm.diff && !b.after.price_item.algorithm.diff
              return -1
            if !a.after.price_item.algorithm.diff && b.after.price_item.algorithm.diff
              return 1
            return 0
          list.sort(sortFunc)
          groupByProduct[product].list = list
        $scope.groupByProduct = groupByProduct
      .finally ->
        $scope.isLoading = false

    $scope.onMeasurementV1Response = (response) ->
      $scope.columns = response.columns
      $scope.isOverflow = isMeasureOverflow(response.measurements)
      for item in response.measurements
        if _.get(groupByProduct, item.product + '.measurements') == undefined
          if !groupByProduct[item.product]
            continue
          groupByProduct[item.product]['measurements'] = []
        groupByProduct[item.product]['measurements'].push(item)
      for p, i in groupByProduct
        groupByProduct[i].measurements = _.orderBy(p.measurements, ['month'], ['desc'])

    $scope.onMeasurementV2021Response = (response) ->
      $scope.measurementsV2021 = response.measurementsV2021
      $scope.haveV2021Cost = response.have_v2021_cost

    # 拉取新用户介绍和测试费用相关信息
    $scope.fetchPricingExtra = ()->
      if $scope.customer_introduction
        $scope.havePricingExtra = true
        # 拉取该用户的测试费用申请记录一并展示
        $scope.fetchTestingApplications $scope.uid

    # 查询某个用户的测试费用申请记录
    $scope.fetchTestingApplications = (uid) ->
      params = {
        process_definition_key: 'apply-testing',
        uid: uid,
        page: 0,
        page_size: 20,  # 不会太多，先只查一页
      }
      qbpmService.listProcessInstances params
        .then (response) ->
          $scope.testingApplications = response

    $scope.showColumn = (column) =>
      show = _.get this.cache, column
      if show == undefined
        show = _.includes $scope.columns, column
        _.set this.cache, column, show
      return show

    $scope.showProductMeasurement = (measurements) ->
      if !measurements || measurements.length == 0
        return false
      filtered = _.filter measurements, (m) ->
        return m.bill_money > 0 || m.measure_money > 0
      return filtered.length > 0

    $scope.isFirstStair = (stair) ->
      return !stair.start

    $scope.isLastStair = (stair) ->
      if $scope.isFirstStair stair
        return stair.end.price_item_stair.quantity == '0'
      return stair.start.price_item_stair.quantity == stair.end.price_item_stair.quantity &&
        stair.start.price_item_stair.unit_id == stair.end.price_item_stair.unit_id

    $scope.isDiffRatioOverflow = (measurement) -> measurement.diff_ratio * 100 <= -10

    $scope.showPriceUnit = (priceType, p) -> (!_.includes ['FIRST_BUYOUT', 'EACH_BUYOUT',
      'BUYOUT'], priceType) || (priceType == 'FIRST_BUYOUT' && p.start != undefined)

    isMeasureOverflow = (measurements) ->
      measures = _.filter measurements, (measure) ->
        return measure.product == '' && measure.month == ''
      return measures &&
        measures.length == 1 &&
        isItemStairLess(measures[0].diff_current_price) &&
        isItemStairLess(measures[0].price_diff)

    # 阶梯价格比对结果是否低于比较值
    isItemStairLess = (diffType) ->
      return diffType == 'LT' || diffType == 'INCOMPARABLE'
