angular.module 'app.controller'
.controller 'ApprovalQvmPricingApplyCtrl', (
  $scope, $stateParams,
  qbpmService, developerMgrService
) ->
  FORMAT = 'YYYY-MM-DD'
  $scope.excode = $stateParams.id
  $scope.DiscountMap = {
    '0': '公开报价',
    '1': '一级折扣',
    '2': '二级折扣',
    '3': '三级折扣',
    '4': '四级折扣',
    '99': '亏损'
  }
  qbpmService.GetParamByExcode($scope.excode).then (res) ->
    $scope.qvmPricingData = res
    $scope.start_time = moment($scope.qvmPricingData.start_time).utcOffset(8).format(FORMAT)
    $scope.end_time = moment($scope.qvmPricingData.end_time).utcOffset(8).format(FORMAT)
    developerMgrService.getDeveloperInfo({uid: res.uid}).then (developer) ->
      $scope.fullName = developer.fullName
