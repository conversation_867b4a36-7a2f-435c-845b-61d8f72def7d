angular.module 'app.controller'

.controller 'ApplyIdentityChangeDetailCtrl', (
  $scope, $modal, $stateParams, identityService
) ->

  $scope.excode = $stateParams.id || ''
  $scope.isLoading = true
  $scope.identity = null
  uid = $stateParams.uid || 0

  if uid != 0
    identityService.getIdentityDetail(uid)
      .then (identity) ->
        $scope.identity = identity
        $scope.isLoading = false

  $scope.openImage = (imageUrl) ->
    modal = $modal.open
      template: """
        <div>
          <img ng-src="{{imageUrl}}" style="width:100%;">
        </div>
      """
      controller: ($scope) ->
        $scope.imageUrl = imageUrl
  
