angular.module 'app.controller'
.controller 'OrderPostPayDetailCtrl', (
  $scope, $stateParams,
  PRODUCT_UNIT_KEYS_MAP_TEXT
  qbpmService, tradeService, notificationService, developerMgrService
) ->
  $scope.PRODUCT_UNIT_MAP = PRODUCT_UNIT_KEYS_MAP_TEXT
  $scope.excode = $stateParams.id
  $scope.uid = $stateParams.uid
  $scope.process_instance_id = $stateParams.process_instance_id || ''

  getUserInfo = ->
    developerMgrService.getDeveloperInfo {uid: $scope.uid}
    .then (userInfo) ->
      $scope.userInfo = userInfo
      developerMgrService.getSalesInfo(userInfo.sfSalesId)
      .then (salesInfo) ->
        $scope.salesInfo = salesInfo
    .catch ->
      notificationService.warning '客户信息获取不完整'

  # 获取审批详情
  getDetail = () ->
    qbpmService.GetParamByExcode($scope.excode, $scope.process_instance_id)
    .then (res) ->
      $scope.approvalInfo = res
      $scope.orderHash = res.order_hash
      $scope.orderDetail = $scope.approvalInfo.extra_param.order_snapshot
    .catch ->
      notificationService.error '获取详情失败'

  getDetail()
  getUserInfo()
