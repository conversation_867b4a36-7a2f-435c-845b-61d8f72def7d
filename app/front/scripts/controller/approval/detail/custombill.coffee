angular.module 'app.controller'
.controller 'ApprovalCustomBillApplyCtrl', (
  $scope, $stateParams, qiniuProduct
  FREEZE_TYPE_TEXT_MAP, FREEZE_TYPE, FREEZE_FREEZE_TYPE
  qbpmService, developerMgrService, notificationService
) ->
  $scope.excode = $stateParams.id
  $scope.uid = $stateParams.uid
  $scope.moment = moment
  $scope.qiniuProduct = qiniuProduct

  getUserInfo = (uid) ->
    $scope.uid = uid
    developerMgrService.getDeveloperInfo {uid: $scope.uid}
    .then (userInfo) ->
      $scope.userInfo = userInfo
    .catch ->
      notificationService.warning '客户信息获取不完整'

  getDetail = () ->
    qbpmService.GetParamByExcode($scope.excode)
    .then (res) ->
      $scope.approvalInfo = res
      console.log $scope.approvalInfo
      getUserInfo($scope.approvalInfo.quote_sheet?.uid)
      # 新建、修改、撤销
      # 目前item_bills只会有一个元素
      $scope.approvalInfo.quote_sheet?.custom_sheets?.item_bills.forEach (bill) ->
        if bill.writeoff
          $scope.op_type = '撤销自定义账单'
        else
          $scope.original_sn = bill.original_sn
          if !$scope.original_sn
            $scope.op_type = '新建自定义账单'
          else
            $scope.op_type = '修改自定义账单'
            # 查询原账单
            qbpmService.GetQuoteSheetBySn($scope.original_sn)
            .then (res) ->
              $scope.original_quote_sheet = res
              console.log $scope.original_quote_sheet

    .catch ->
      notificationService.error '获取详情失败'

  getDetail()