angular.module 'app.controller'
.controller 'OrderRefundApprovalDetailCtrl', (
  $scope, $stateParams,
  PRODUCT_UNIT_KEYS_MAP_TEXT, CarryOverPolicyConst, CarryOverPolicyMap
  qbpmService, tradeService, notificationService, developerMgrService, respackMgrService
) ->
  $scope.PRODUCT_UNIT_MAP = PRODUCT_UNIT_KEYS_MAP_TEXT
  $scope.excode = $stateParams.id
  $scope.uid = $stateParams.uid
  $scope.process_instance_id = $stateParams.process_instance_id || ''
  $scope.CarryOverPolicyConst = CarryOverPolicyConst
  $scope.CarryOverPolicyMap = CarryOverPolicyMap
  $scope.moment = moment

  $scope.transformDetail = () ->
    if $scope.isAllRefund
      $scope.orderDetail.product_orders.forEach (po) ->
        po.refundMoney = po.c_fee
        po.isAllRefund = true
    else
      $scope.orderDetail.product_orders.find (po) ->
        if po.id == $scope.approvalInfo.req_product_order_refund_cash.product_order_id
          # coffeelint: disable=max_line_length
          po.refundMoney = $scope.approvalInfo.req_product_order_refund_cash.expected_money
          po.isAllRefund = $scope.approvalInfo.req_product_order_refund_cash.expected_money == po.c_fee

  getUserInfo = ->
    developerMgrService.getDeveloperInfo {uid: $scope.uid}
    .then (userInfo) ->
      $scope.userInfo = userInfo
      developerMgrService.getSalesInfo(userInfo.sfSalesId)
      .then (salesInfo) ->
        $scope.salesInfo = salesInfo
    .catch ->
      notificationService.warning '客户信息获取不完整'

  # 获取资源包用量
  getRespackInfo = () ->
    $scope.orderDetail.product_orders.forEach((po) ->
      if po.product.category_id != 3
        return
      # 查询资源包用量
      return tradeService.respackUsageDetail({ uid: po.buyer_id, po_id: po.id }).then((res) ->
        po.respackUsageDetail = res.data

        # 查询到用量请求包收益
        return respackMgrService.getRevenueRecognition(po.id).then((res) ->
          po.respackRevenueRecognition = res
        )
      ).catch((err) ->
        # 404错误不报错， 5是grpc code
        if err.code == 404 || err.code == 5
          return
        notificationService.error "#{err}"
      )
    )

  # 获取审批详情
  getDetail = () ->
    qbpmService.GetParamByExcode($scope.excode, $scope.process_instance_id)
    .then (res) ->
      $scope.approvalInfo = res
      $scope.isAllRefund = if res.req_order_refund_cash.order_hash then true else false
      $scope.orderHash = res.order_hash
      $scope.orderDetail = $scope.approvalInfo.extra_param.order_snapshot
      getRespackInfo()
      $scope.transformDetail()
    .catch ->
      notificationService.error '获取详情失败'

  $scope.isRespackUsed = (respackUsageDetail) ->
    # 提交时未用，则详情也应该按照未用来处理，否则过段时间再重进详情时，审批详情的已用情况可能会发生变化
    if 'used_already' of $scope.approvalInfo.extra_param and !$scope.approvalInfo.extra_param.used_already
      return false

    if !respackUsageDetail
      return false

    # 一次分配直接看用量
    if respackUsageDetail.carryover_policy == CarryOverPolicyConst.Lifetime
      return respackUsageDetail.real_used > 0

    console.log moment(respackUsageDetail.start_time).isBefore(moment())

    # 按月分配看时间
    return moment(respackUsageDetail.start_time).startOf('month').isBefore(moment().startOf('month'))

  $scope.amountTranform = (amount) ->
    return (amount / 10000).toFixed(2)

  $scope.respackMaxRefundAmount = (po) ->
    if !po.respackUsageDetail || !po.respackRevenueRecognition
      return po.c_fee

    return $scope.amountTranform(po.respackRevenueRecognition.c_fee - po.respackRevenueRecognition.total_recognized_revenue)

  getDetail()
  getUserInfo()
