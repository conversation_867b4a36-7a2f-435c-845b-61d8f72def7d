angular.module 'app.controller'
  .controller 'ApprovalListCtrl', (
  $scope, $modal, $state, $stateParams,
  PROCESS_BIZ_TYPE, PROCESS_VIEW_TYPE,
  PROCESS_STATUS, PROCESS_STATUS_MAP,
  qbpmService, contentPermissionService, developerMgrService
) ->

  # 展示的审批列表
  $scope.processList = []
  $scope.bizTypes = PROCESS_BIZ_TYPE
  $scope.viewTypes = PROCESS_VIEW_TYPE
  $scope.PROCESS_STATUS_MAP = PROCESS_STATUS_MAP
  $scope.REVIEW = 1
  $scope.COMPLETE = 3
  $scope.FAIL = 4

  # 当前过滤的审批状态码
  $scope.statusCode = if $stateParams.status == undefined then PROCESS_STATUS.PENDING else $stateParams.status

  # query用于和前端过滤条件绑定
  $scope.query =
    view_type: if $stateParams.view_type == undefined then 0 else $stateParams.view_type
    biz_type: if $stateParams.biz_type == undefined then '' else $stateParams.biz_type
    uid: $stateParams.uid
    email: if $stateParams.email == undefined then '' else $stateParams.email
    status: '' + $scope.statusCode
    page: if $stateParams.page == undefined then 0 else $stateParams.page
    page_size: if $stateParams.page_size == undefined then 20 else $stateParams.page_size

  if $scope.query.uid == 0
    $scope.query.uid = undefined

  $scope.nextPage = () ->
    goto($scope.query.page + 1, $scope.query.page_size)

  $scope.previouspage = () ->
    goto($scope.query.page - 1, $scope.query.page_size)

  $scope.BuildParams = (page) ->
    params = {
      view_type: $scope.query.view_type,
      process_definition_key: $scope.query.biz_type,
      uid: $scope.query.uid,
      initiator_ids: if $scope.query.email then [$scope.query.email] else null,
      status: $scope.query.status,
      page: page,
      page_size: $scope.query.page_size,
    }
    return params

  $scope.filterReq = () ->
    goto(0, $scope.query.page_size)

  $scope.onTabSel = (t) ->
    if t == PROCESS_VIEW_TYPE.PROCESS_APPLY.value
      $state.go 'layout.ApprovalApply'
      return
    $scope.query =
      view_type: t
      biz_type: ''
      initiator_ids: []
      process_instance_id: ''
      status: PROCESS_STATUS.PENDING
      page: 0
      page_size: 20
    $scope.filterReq()

  goto = (page, page_size) ->
    $state.go 'layout.ApprovalList',
      view_type: $scope.query.view_type
      biz_type: $scope.query.biz_type
      uid: $scope.query.uid
      email: $scope.query.email
      status: $scope.query.status
      page: page
      page_size: page_size

  formatPendingActors = (process) ->
    if 'pending_actors' of process
      actors = []
      for node in process.pending_actors
        nodeActors = (actor.name for actor in node.actors).join('、')
        if node.actors.length > 2
          nodeActors = node.actors[0].name + '等'
        else if node.actors.length > 1
          nodeActors = '(' + nodeActors + ')'
        actors.push nodeActors
      process.assignees_str = actors.join('，')
    else
      process.assignees_str = ''

  $scope.isLoading = true
  requestPageData = (page) ->
    params = $scope.BuildParams(page)
    qbpmService.getProcessListInfo params
      .then (response) ->
        $scope.query.page = page
        if 'data' of response
          $scope.processList = response.data
          for process in response.data
            process.assignees_str = formatPendingActors process
          getDevelopers(response.data)
        else
          $scope.processList = []
      .finally ->
        $scope.isLoading = false

  # 获取七牛用户信息
  getDevelopers = (processes) ->
    uids = []

    for process in processes
      if process.uid && process.uid > 0
        uids.push(_.parseInt(process.uid))

    if uids.length > 0
      uids = _.uniq(uids)

      developerMgrService.listDeveloperByUIDs(uids)
      .then (usersInfo) ->
        if !usersInfo || usersInfo.length == 0
          return

        $scope.uidInfoMap = {}
        for u in usersInfo
          $scope.uidInfoMap[u.uid] = u

  # 展示客户名称
  $scope.displayAccount = (uid) ->
    if !$scope.uidInfoMap || !$scope.uidInfoMap[uid]
      return ''
    
    developer = $scope.uidInfoMap[uid]
    return if developer.CompanyName then developer.CompanyName else developer.fullname

  # 请求数据
  requestPageData($scope.query.page)

  $scope.stopProp = ($event) ->
    $event.stopPropagation()
