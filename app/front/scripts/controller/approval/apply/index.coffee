angular.module 'app.controller'
.controller 'ApprovalApprovalApplyCtrl', (
  $scope, $state, $modal, TRADE_APPROVAL_TYPE, $stateParams
  PROCESS_VIEW_TYPE, approvalFeatureList, applyModalHelper, featureTriggerList
) ->
  $scope.featureList = approvalFeatureList
  $scope.viewTypes = PROCESS_VIEW_TYPE
  $scope.current = $stateParams.feat || $scope.featureList.ORDER.value
  $scope.action = $stateParams.action || ''
  $scope.param = $stateParams.param || ''
  $scope.refund_content = '
    此处申请仅支持以下产品及条件申请：
    </br/>
    1.资源包退款：需注意区分已用和未用，已用包理论上不建议退款，如遇极端情况可发起申请；
    </br>
    2.普通商品退款：除云主机外的商品退款，需保证商品【未过期】且【已完成】，建议先和产线沟通情况确认是否可退后再申请。
    </br>
    其他退款申请：
    </br>
    1.云主机退款：<a target="_blank" href="https://cf.qiniu.io/pages/viewpage.action?pageId=29732993">https://cf.qiniu.io/pages/viewpage.action?pageId=29732993</a>
    </br>
    2.未完成证书退款：客户在证书控制台操作退款或者联系产线处理。
  '
  triggerFeature = featureTriggerList[$scope.current]
  if triggerFeature
    action = triggerFeature[$scope.action]
    if action
      applyModalHelper[action]($scope.param)
  
  $scope.onTabSel = (t) ->
    if t == PROCESS_VIEW_TYPE.PROCESS_APPLY.value
      $state.go 'layout.ApprovalApply'
      return
    $state.go 'layout.ApprovalList',
      view_type: t
      page: 0
      page_size: 20

  $scope.switchFeat =  (t) ->
    $state.go 'layout.ApprovalApply', {feat: t, action: '', param: ''}

  $scope.applyChangeOrderPrice = (order_hash) ->
    applyModalHelper.applyChangeOrderPrice(order_hash)

  $scope.applyPostPay = (order_hash) ->
    applyModalHelper.applyPostPay(order_hash)

  $scope.applyChangePriceAndPostPay = (order_hash) ->
    applyModalHelper.applyChangePriceAndPostPay(order_hash)

  $scope.applyRefund = (order_hash) ->
    applyModalHelper.applyRefund(order_hash)

  $scope.applyModifyRespackStartTime = (order_hash) ->
    applyModalHelper.applyModifyRespackStartTime(order_hash)

  $scope.applyExtendRespackDuration = (order_hash) ->
    applyModalHelper.applyExtendRespackDuration(order_hash)

  $scope.applyWithdraw = ->
    applyModalHelper.applyWithdraw()
  
  $scope.applyRemoveFreeze = ->
    applyModalHelper.applyRemoveFreeze()

  $scope.applyExtendBuffer = ->
    applyModalHelper.applyExtendBuffer()

  $scope.applyBillCoupon = (uid) ->
    applyModalHelper.applyBillCoupon(uid)

  $scope.applyOrderCoupon = (uid) ->
    applyModalHelper.applyOrderCoupon(uid)

  $scope.applyRechargeReward = (uid) ->
    applyModalHelper.applyRechargeReward(uid)

  $scope.applySyncedRegenBill = (product, uid) ->
    applyModalHelper.applySyncedRegenBill(product, uid)

  $scope.applyBalanceTransfer = (uid) ->
    applyModalHelper.applyBalanceTransfer(uid)
