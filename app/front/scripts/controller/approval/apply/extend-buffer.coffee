angular.module 'app.controller'
.controller 'extendBufferApplyCtrl', (
  $scope, $modalInstance, notificationService
  developerViewService, riskApplyService, freezeStrategyService
  uid, FREEZE_CLOCK, FREEZE_TYPE_TEXT
) ->
  $scope.uid = uid
  init = () ->
    $scope.promisePayFiles = []
    $scope.creditsFiles = []
    $scope.financeFiles = []
    $scope.userInfo = null
    $scope.reason = ''
    $scope.days_to_extend = ''
    freezeStrategyService.list {uid: $scope.uid}
    .then (res) ->
      $scope.freezeStrategy = res

  init()

  $scope.query = () ->
    developerViewService.getOverview {uid: $scope.uid}
    .then (res) ->
      $scope.userInfo = res

  $scope.$watch 'uid', () ->
    init()

  $scope.$watch 'userInfo', (info) ->
    if info && !info.is_freezed
      $scope.invalid = false
      return
    $scope.invalid = true
  , true

  $scope.$watch 'days_to_extend', (day) ->
    if day > 0
      actuallyHours = day * 24 - $scope.freezeStrategy.freeze_buffered_temp_hours
      if $scope.freezeStrategy.freeze_type == FREEZE_TYPE_TEXT.FREEZEBUFFERED
        estimateDate = moment().add(
          $scope.freezeStrategy.remaining_hours - FREEZE_CLOCK + actuallyHours, 'hour'
        )
        if estimateDate.isBefore(moment().endOf('day'))
          $scope.invalidTempHours = true
        else
          $scope.invalidTempHours = false
        $scope.estimatedBufferDate = estimateDate.format('YYYY-MM-DD')

  $scope.submit = () ->
    if !$scope.reason
      notificationService.error '申请理由必填'
      return
    if !$scope.days_to_extend
      notificationService.error '临时保护期必须大于 1 天'
      return
    if $scope.invalidTempHours
      notificationService.error '生效后预计冻结时间小于等于当前时间'
      return
    if $scope.promisePayFiles.length == 0
      notificationService.error '付款承诺书必须上传至少一份。'
      return
    body =
      attachments:
        written_undertakings: $scope.promisePayFiles
        credit_reports: $scope.creditsFiles
        financial_statements: $scope.financeFiles
      reason: $scope.reason
      days_to_extend: $scope.days_to_extend

    $scope.isSubmitting = true
    riskApplyService.applyExtendBuffer $scope.uid, body
    .then () ->
      notificationService.success '申请成功'
      $modalInstance.close()
    .catch (err) ->
      notificationService.error "申请失败，#{err.message}"
    .finally () ->
      $scope.isSubmitting = false

  $scope.close = () ->
    $modalInstance.close()
