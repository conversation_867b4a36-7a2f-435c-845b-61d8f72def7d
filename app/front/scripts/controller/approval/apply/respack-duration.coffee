angular.module 'app.controller'
.controller 'OrderApprovalExtendRespackDurationCtrl', (
  $scope, $modalInstance, $modal, $q, $state, order_hash
  CurrencyType, TRADE_ORDER_STATUS, PRODUCT_TYPE, PRODUCT_UNIT_KEYS_MAP_TEXT
  TRADE_APPROVAL_TYPE, PRODUCT_UNIT_TO_CHAR
  tradeService, notificationService, respackMgrService, qbpmService
) ->
  $scope.isLoading = false
  $scope.PRODUCT_UNIT_MAP = PRODUCT_UNIT_KEYS_MAP_TEXT
  $scope.TRADE_ORDER_STATUS = TRADE_ORDER_STATUS
  $scope.CurrencyType = CurrencyType
  $scope.orderHash = order_hash
  $scope.changedPrice = NaN
  $scope.hasChange = false
  $scope.reason = ''
  $scope.edit = true

  $scope.close = ->
    $modalInstance.dismiss 'close'

  $scope.cancelAlter = (po) ->
    po.extend_duration = undefined
    po.new_end_time = undefined

  $scope.$watchGroup ['orderHash', 'reason'], (newVal) ->
    if !newVal[0]
      $scope.disableSubmit = true
      return
    if !newVal[1]
      $scope.disableSubmit = true
      return
    $scope.disableSubmit = false

  $scope.$watch 'orderDetail', (newVal) ->
    if !newVal
      $scope.hasChange = false
    else if (newVal.product_orders.find (item) -> item.extend_duration && !item.approvalAccepted)
      $scope.hasChange = true
    else
      $scope.hasChange = false
  , true
  
  $scope.queryOrder = ->
    if $scope.isLoading
      return
    if $scope.orderHash.length != 32
      notificationService.error '请输入正确的订单号'
      return
    $scope.isLoading = true
    $scope.changedPrice = NaN
    $scope.reason = ''
    tradeService.getOrder({
      order_hash: $scope.orderHash
      with_detail: true
    })
    .then (res) ->
      $scope.edit = false
      promises = res.product_orders.map (po) ->
        tradeService.respackUsageDetail({uid: po.buyer_id, po_id: po.id})
      promiseAll(promises).then (resps) ->
        res.product_orders.forEach (po, index) ->
          po.respackUsageDetail = resps[index].data
        $scope.orderDetail = res
    .catch (err) ->
      notificationService.error "订单查询失败,#{err}"
      $scope.orderDetail = undefined
    .finally ->
      $scope.isLoading = false

  if $scope.orderHash
    $scope.queryOrder()

  $scope.editOrderHash = ->
    $scope.edit = true

  promiseAll = (promises) ->
    allPromies = promises.map (item) ->
      item.catch () -> item
    return $q.all(allPromies)

  $scope.submit = ->
    if $scope.isLoading
      return
    $scope.isLoading = true
    po_params = $scope.orderDetail.product_orders.map (po) ->
      if po.new_end_time && !po.approvalAccepted then {
        order_hash: $scope.orderHash
        reason: $scope.reason
        approval_type: TRADE_APPROVAL_TYPE.CHANGE_END_TIME
        req_product_order_extend_duration:
          order_hash: $scope.orderHash
          product_order_id: po.id
          extend_duration: po.extend_duration
          capacity: po.respackUsageDetail.capacity
          used: po.respackUsageDetail.used
          respack_unit: po.respackUsageDetail.respack_unit
      } else null
    params = po_params.filter (item) -> item
    promises = params.map (param) ->
      qbpmService.applyUnifyApproval param

    promiseAll(promises).then (resps) ->
      result = resps.map (resp) -> if resp.code == 200 && resp.data.accepted then true else false
      if (result.filter (res) -> !res).length == 0
        notificationService.success '审批发起成功'
        $modalInstance.close 'ok'
        setTimeout ->
          $state.go 'layout.ApprovalList', {
              view_type: 1,
              status: 1,
              page: 0,
              page_size: 20
            }
        , 100
      else
        params.forEach (po, index) ->
          matchPoId = po.req_product_order_extend_duration.product_order_id
          targetPo = $scope.orderDetail.product_orders.find (p) -> p.id == matchPoId
          if result[index]
            targetPo?.approvalAccepted = true
          else
            notificationService.error "#{targetPo.product_name}申请失败，#{resps[index].data.tips || resps[index].message}"
    .catch ->
      notificationService.error "申请失败"
    .finally ->
      $scope.isLoading = false

  $scope.extendDuration = (po) ->
    if $scope.isLoading
      return
    $scope.isLoading = true
    param =
      order_hash: $scope.orderHash
      approval_type: TRADE_APPROVAL_TYPE.CHANGE_END_TIME
      req_product_order_extend_duration:
        order_hash: $scope.orderHash
        product_order_id: po.id
    qbpmService.checkApproval param
    .then ->
      po.isApproval = true
      po.unit = po.product.unit
      modal = $modal.open
        templateUrl: 'templates/financial/trade/extendDuration.html'
        size: 'md'
        animation: true
        controller: 'extendDurationController'
        resolve:
          po: ->
            return po

      modal.result.then (result) ->
        po.extend_duration = result
        po.new_end_time = moment(po.end_time).add(result, PRODUCT_UNIT_TO_CHAR[po.unit]).format()
      .catch -> null
    .catch (err) ->
      notificationService.error "#{err}"
    .finally ->
      $scope.isLoading = false
