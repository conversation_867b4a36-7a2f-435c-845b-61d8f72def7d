angular.module 'app.controller'
.controller 'changeOrderPriceCtrl', (
  $scope, $modalInstance, order_hash, $state, modalService, compareUtil
  CurrencyType, TRADE_ORDER_STATUS, TRADE_APPROVAL_TYPE, PRODUCT_UNIT_KEYS_MAP_TEXT
  tradeService, notificationService, qbpmService, gaeaService
) ->
  $scope.isLoading = false
  $scope.PRODUCT_UNIT_MAP = PRODUCT_UNIT_KEYS_MAP_TEXT
  $scope.CurrencyType = CurrencyType
  $scope.orderHash = order_hash
  $scope.changedPrice = NaN
  $scope.changedType = '0'
  $scope.reason = ''
  $scope.edit = true
  $scope.isLowPriceUser = false
  $scope.canEditLowPriceUser = false
  $scope.canDisplayLowPriceUser = false

  ALLOWED_SELLER_IDS = [20, 23, 45]

  $scope.close = ->
    $modalInstance.dismiss 'close'

  $scope.$watchGroup ['orderHash', 'changedPrice', 'reason'], (newVal) ->
    if !newVal[0]
      $scope.disableSubmit = true
      return
    if !(newVal[1] || newVal[1] == 0)
      $scope.disableSubmit = true
      return
    if !newVal[2]
      $scope.disableSubmit = true
      return
    $scope.disableSubmit = false

  $scope.$watch 'orderDetail' , (newVal) ->
    if $scope.changedType == '0'
      $scope.changedPrice = NaN
      return
    $scope.changedPrice = 0
    newVal.product_orders.map (po) ->
      $scope.changedPrice += po.changedPrice
  , true

  $scope.$watch 'changedType', (newVal) ->
    $scope.changedPrice = 0
    $scope.orderDetail.product_orders = $scope.orderDetail.product_orders.map (po) ->
      po.changedPrice = po.c_fee
      $scope.changedPrice += po.c_fee
      return po
    if newVal == '0'
      $scope.changedPrice = NaN
      $scope.poUnitPrices = {}

  $scope.$watchGroup ['changedPrice'], (newVal) ->
    if (!$scope.changedPrice && $scope.changedPrice != 0) ||
        $scope.changedPrice == NaN ||
        $scope.changedPrice == $scope.orderDetail.c_fee
      $scope.poUnitPrices = {}
      return
    params = {
      order_hash: $scope.orderHash
      ignore_expired: true
      need_approved_stats: true
    }
    if $scope.changedType == '0'
      params.actually_fee = $scope.changedPrice
    if $scope.changedType == '1'
      po_actually_fee = []
      $scope.orderDetail.product_orders.map (po) ->
        po_actually_fee.push
          po_id: po.id
          actually_fee: po.changedPrice
      params.po_actually_fee = po_actually_fee
    tradeService.orderUnitPrice(params)
    .then (res) ->
      if res.prices
        poUnitPrices = {}
        _.forEach res.prices, (p) ->
          if p.last_approved
            p.delta = compareUtil.comparePercentage p.unit_price, p.last_approved
            p.delta_abs = Math.abs(p.delta)
          poUnitPrices[p.id] = p
        $scope.poUnitPrices = poUnitPrices
    .catch (err) ->
      notificationService.error "#{err}"

  $scope.queryOrder = ->
    if $scope.isLoading
      return
    if $scope.orderHash.length != 32
      notificationService.error '请输入正确的订单号'
      return
    $scope.isLoading = true
    $scope.changedPrice = NaN
    $scope.reason = ''
    param =
      order_hash: $scope.orderHash
      approval_type: TRADE_APPROVAL_TYPE.CHANGE_PRICE
    qbpmService.checkApproval param
    .then () ->
      tradeService.getOrder({
        order_hash: $scope.orderHash
        with_detail: true
      })
      .then (res) ->
        res.product_orders = res.product_orders.map (po) ->
          po.changedPrice = po.c_fee
          return po
        $scope.orderDetail = res
        $scope.edit = false
        # 检查seller_id是否在允许列表中
        $scope.canDisplayLowPriceUser = ALLOWED_SELLER_IDS.includes(res.seller_id)
        # 如果可以显示低价用户选项，则检查用户是否为低价用户
        if $scope.canDisplayLowPriceUser && res.buyer_id
          gaeaService.getLowPriceProductsByUID(res.buyer_id)
          .then (lowPriceRes) ->
            $scope.isLowPriceUser = lowPriceRes.low_price_products?.length > 0
            # 如果是低价用户，则禁用编辑
            $scope.canEditLowPriceUser = !$scope.isLowPriceUser
          .catch (err) ->
            notificationService.error '获取低价线路客户状态失败:', err
    .catch (err) ->
      notificationService.error "#{err}"
      $scope.orderDetail = undefined
    .finally ->
      $scope.isLoading = false

  if $scope.orderHash
    $scope.queryOrder()

  $scope.editOrderHash = ->
    $scope.edit = true

  $scope.submit = ->
    if $scope.isLoading
      return
    if $scope.changedPrice * 10000 == $scope.orderDetail.c_fee * 10000
      notificationService.error '改价金额不能与原始金额一样'
      return
    moneySplit = $scope.changedPrice.toString().split('.')
    if moneySplit[1] && moneySplit[1].length > 2
      notificationService.error '价格最多支持小数点后两位'
      return
    
    if ($scope.changedPrice / $scope.orderDetail.c_fee) < 0.3
      currency = if $scope.orderDetail.currency_type == CurrencyType.CNY then '¥' else '$'
      text = "#{currency} #{$scope.changedPrice}"
      modalService.confirm(
        "<p class='red text-center'>您当前改价后金额为：" + text + " ，请确认无误</p>"
      ).then () ->
        $scope.submitForm()
     else
        $scope.submitForm()

  $scope.submitForm = ->
    $scope.isLoading = true
    params =
      order_hash: $scope.orderHash
      reason: $scope.reason
      approval_type: TRADE_APPROVAL_TYPE.CHANGE_PRICE
      req_update_price:
        order_hash: $scope.orderHash
        ignore_expired: true
      low_price_products: if $scope.isLowPriceUser then ['fusion'] else []

    if $scope.changedType == '0'
      params.req_update_price.actually_fee = $scope.changedPrice
    if $scope.changedType == '1'
      po_actually_fee = []
      $scope.orderDetail.product_orders.map (po) ->
        po_actually_fee.push
          po_id: po.id
          actually_fee: po.changedPrice
      params.req_update_price.po_actually_fee = po_actually_fee
    qbpmService.applyUnifyApproval params
    .then (res) ->
      if res.code == 200
        if res.data.accepted
          notificationService.success '审批发起成功！'
          $modalInstance.close 'ok'
          setTimeout ->
            $state.go 'layout.ApprovalList', {
              view_type: 1,
              status: 1,
              page: 0,
              page_size: 20
            }
          , 100
        else
          notificationService.error "申请失败，#{res.data.tips}"
      else
        notificationService.error "申请失败，#{res.message}"
    .catch (err) ->
      notificationService.error "申请失败，#{err}"
    .finally ->
      $scope.isLoading = false
