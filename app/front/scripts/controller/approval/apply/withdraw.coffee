angular.module 'app.controller'
.controller 'WithdrawApplyCtrl', (
  $scope, $modal, withdrawApprovalService,
  $modalInstance, developerViewService, WithdrawReasonList, notificationService
  FREEZE_FREEZE_TYPE_ENUM
) ->
  $scope.WithdrawReasonList = WithdrawReasonList
  $scope.availableAmount = null
  $scope.identityName = ''
  $scope.applyRequestBody =
    uid: ''
    withdraw_type: 'offline_withdraw'
    withdraw_reason: ''
    withdraw_reason_type: ''
    payee_info:
      type: 'BANK'
      account: ''
      bank: ''
      branch_bank: ''
      name: ''
    withdraws: []
    attachments: []
  $scope.showRechargeTransactions = () ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/rechargeTransactions.html'
      controller: 'rechargeTransactionsModalCtrl',
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: -> $scope.applyRequestBody.uid
        currency: -> $scope.currency

    modal.result.then (result) ->
      $scope.applyRequestBody.withdraws = result

  $scope.$watch 'applyRequestBody.withdraws', (newVal) ->
    $scope.applyMoney = _.sum(newVal.map (item) -> item.apply_money) * 10000
  , true

  $scope.$watch 'applyRequestBody', (newVal) ->
    if !newVal.withdraw_reason || !newVal.withdraw_reason_type || !newVal.payee_info.account ||
    !newVal.payee_info.bank || !newVal.payee_info.branch_bank ||
    !newVal.payee_info.name || $scope.applyMoney <= 0
      $scope.invalid = true
    else
      $scope.invalid = false
  , true

  $scope.query = () ->
    $scope.isQueryLoading = true
    withdrawApprovalService.getAvailableAmount $scope.applyRequestBody.uid
    .then (res) ->
      $scope.availableAmount = res

    developerViewService.getOverview {uid: $scope.applyRequestBody.uid}
    .then (res) ->
      $scope.identityName = res.customer_name
      $scope.currency = res.currency_type
      $scope.isIllegalFreezed = res.is_freezed && res.freeze_type == FREEZE_FREEZE_TYPE_ENUM.ILLEGAL
    .finally ->
      $scope.isQueryLoading = false
  
  $scope.submit = () ->
    requestBody = _.cloneDeep $scope.applyRequestBody
    requestBody.withdraws.forEach (item) -> item.apply_money = Math.round item.apply_money * 10000
    $scope.isLoading = true
    withdrawApprovalService.apply requestBody
    .then () ->
      notificationService.success '申请成功'
      $modalInstance.close 'ok'
    .catch ->
      notificationService.error '申请失败, 请重试'
    .finally ->
      $scope.isLoading = false

  blobToBase64 = (file) ->
    reader = new FileReader()
    reader.addEventListener('load', ->
      file.content_base64 = reader.result
    )
    reader.readAsDataURL(file.file)

  changeHandler = (e) ->
    if e.target.id == 'withdrawFormFile'
      files = e.target.files
      if files.length == 0 then $scope.applyRequestBody.attachments = []
      else
        i = 0
        while i < files.length
          $scope.applyRequestBody.attachments.push {
            file: files[i],
            file_name: files[i].name
            content_base64: null
          }
          i++
        $scope.applyRequestBody.attachments.forEach (file) ->
          blobToBase64(file)

  document.addEventListener('change', changeHandler)

  $scope.close = ->
    document.removeEventListener('change', changeHandler)
    $modalInstance.dismiss 'close'


angular.module 'app.controller'
.controller 'rechargeTransactionsModalCtrl', (
  $scope, withdrawApprovalService, uid, $modalInstance, currency
  humanizeDepositType
) ->
  $scope.humanizeDepositType = humanizeDepositType
  $scope.currency = currency
  withdrawApprovalService.getRechargeTransactions({
    uid: uid,
    withdrawType: 'offline_withdraw',
    needNB: false,
    needTransferaccin: true
  })
  .then (res) ->
    $scope.rechargeTransactions = res.map (item) ->
      item.apply_money = item.withdrawable_money / 10000
      item.selected = false
      return item
  $scope.close = ->
    $modalInstance.dismiss 'close'
  
  $scope.selectTransaction = (item) ->
    item.selected = true

  $scope.$watch 'rechargeTransactions', (newVal) ->
    selected = newVal.find (item) -> item.selected
    $scope.noSelected = !selected
    # coffeelint: disable=max_line_length
    $scope.invalid = !!newVal.find (item) -> item.selected && (!item.apply_money || Math.round(item.apply_money * 10000) > item.withdrawable_money)
  , true

  $scope.submit = () ->
    selected = $scope.rechargeTransactions.filter (item) -> item.selected
    $modalInstance.close selected
