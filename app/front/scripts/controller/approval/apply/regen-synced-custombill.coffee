angular.module 'app.controller'
.controller 'SyncedCustombillRegenApplyCtrl', (
  $scope, $modal, developerMgrService, uid, product,
  $modalInstance, notificationService, qbpmService
) ->

  $scope.uid = uid || ''
  $scope.product = product
  $scope.month_start = moment().subtract(1, 'month')
  $scope.month_end = moment().subtract(1, 'month')
  $scope.regen_reason = ''

  $scope.query = () ->
    developerMgrService.getDeveloperInfo({uid: $scope.uid})
    .then (res) ->
      $scope.fullname = res.fullName

  $scope.close = ->
    $modalInstance.dismiss 'close'

  $scope.monthStartOnSetTime = () ->
    $scope.$broadcast('month-start-changed')

  $scope.monthEndOnSetTime = () ->
    $scope.$broadcast('month-end-changed')

  $scope.monthStartBeforeRender = ($dates) ->
    if !$scope.month_end
      return
    $dates.filter((date) ->
      return date.localDateValue() > moment($scope.month_end).valueOf()
    ).forEach((date) ->
      date.selectable = false
    )
  $scope.monthEndBeforeRender = ($dates) ->
    if !$scope.month_start
      return
    $dates.filter((date)->
      return date.localDateValue() < moment($scope.month_start).valueOf() ||
        date.localDateValue() > moment().subtract(1, 'month').valueOf()
    ).forEach((date)->
      date.selectable = false
    )


  $scope.submit = () ->
    if !$scope.uid
      return
    if !$scope.month_end || !$scope.month_start || !$scope.regen_reason
      notificationService.error '重出月份和重出原因必填'
      return
    param =
      uid: $scope.uid
      # 后端接口是左闭右开，[start,end), 所以这里 end 需要多加一个月
      start: moment($scope.month_start).startOf('month').unix()
      end: moment($scope.month_end).add(1, 'month').startOf('month').unix()
      reason: $scope.regen_reason
      product: product
    $scope.isLoading = true
    qbpmService.applyCustombillRegen param
    .then (res) ->
      notificationService.success '申请成功'
      $modalInstance.close()
    .catch (err) ->
      notificationService.error "申请失败，#{err.message}"
    .finally ->
      $scope.isLoading = false

