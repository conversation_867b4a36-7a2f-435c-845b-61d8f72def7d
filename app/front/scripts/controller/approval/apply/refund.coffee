angular.module 'app.controller'
.controller 'OrderApprovalRefundCtrl', (
  $scope, $modalInstance, $state, order_hash
  CurrencyType, TRADE_ORDER_STATUS, PRODUCT_UNIT_KEYS_MAP_TEXT, TRADE_APPROVAL_TYPE, CarryOverPolicyConst, CarryOverPolicyMap
  tradeService, notificationService, qbpmService, respackMgrService
) ->
  $scope.isLoading = false
  $scope.PRODUCT_UNIT_MAP = PRODUCT_UNIT_KEYS_MAP_TEXT
  $scope.TRADE_ORDER_STATUS = TRADE_ORDER_STATUS
  $scope.CurrencyType = CurrencyType
  $scope.CarryOverPolicyConst = CarryOverPolicyConst
  $scope.CarryOverPolicyMap = CarryOverPolicyMap
  $scope.moment = moment
  $scope.orderHash = order_hash
  $scope.changedPrice = NaN
  $scope.hasSelected = false
  $scope.reason = ''
  $scope.edit = true
  $scope.refund =
    refundType: 'allRefund'

  $scope.close = ->
    $modalInstance.dismiss 'close'

  $scope.selectPoID = (id) ->
    $scope.selectedPoID = id

  $scope.unCheck = (e) ->
    e.preventDefault()
    $scope.refundAllGoods = false

  getSelecteGoodsRespackInfo = () ->
    Promise.all($scope.selectedGoods.map((po) ->
      if po.product.category_id != 3
        return Promise.resolve()
      # 查询资源包用量
      return tradeService.respackUsageDetail({ uid: po.buyer_id, po_id: po.id }).then((res) ->
        po.respackUsageDetail = res.data

        # 查询到用量请求包收益
        return respackMgrService.getRevenueRecognition(po.id).then((res) ->
          po.respackRevenueRecognition = res
        )
      ).catch((err) ->
        # 404错误不报错， 5是grpc code
        if err.code == 404 || err.code == 5
          return
        notificationService.error "#{err}"
      )
    ))

  $scope.selectGoods = () ->
    if $scope.isLoading
      return
    $scope.isLoading = true
    if $scope.refundAllGoods
      param =
        order_hash: $scope.orderHash
        approval_type: TRADE_APPROVAL_TYPE.REFUND
        req_order_refund_cash:
          order_hash: $scope.orderHash
      qbpmService.checkApproval param
      .then ->
        $scope.selectedGoods = $scope.orderDetail.product_orders
        $scope.hasSelected = true

        return getSelecteGoodsRespackInfo()
      .catch (err) ->
        notificationService.error "#{err}"
      .finally ->
        $scope.isLoading = false
    else
      param =
        order_hash: $scope.orderHash
        approval_type: TRADE_APPROVAL_TYPE.REFUND
        req_product_order_refund_cash:
          product_order_id: $scope.selectedPoID
      qbpmService.checkApproval param
      .then ->
        # coffeelint: disable=max_line_length
        $scope.selectedGoods = $scope.orderDetail.product_orders.filter (po) -> po.id == $scope.selectedPoID
        $scope.hasSelected = true

        $scope.selectedGoods.forEach (po) ->
          if po.product.category_id == 3 && moment(po.end_time).isBefore(moment())
            $scope.selectedGoods = []
            $scope.hasSelected = false
            throw '该商品已过期，无法申请退款'

        return getSelecteGoodsRespackInfo()
      .catch (err) ->
        notificationService.error "#{err}"
      .finally ->
        $scope.isLoading = false

  $scope.reSelect = ->
    $scope.hasSelected = false
    $scope.selectedGoods = []

  $scope.amountTranform = (amount) ->
    return (amount / 10000).toFixed(2)

  $scope.isRespackUsed = (respackUsageDetail) ->
    if !respackUsageDetail
      return false

    # 一次分配直接看用量
    if respackUsageDetail.carryover_policy == CarryOverPolicyConst.Lifetime
      return respackUsageDetail.real_used > 0

    # 按月分配看时间
    return moment(respackUsageDetail.start_time).startOf('month').isBefore(moment().startOf('month'))

  $scope.respackMaxRefundAmount = (po) ->
    if !po.respackUsageDetail || !po.respackRevenueRecognition
      return po.c_fee

    return $scope.amountTranform(po.respackRevenueRecognition.c_fee - po.respackRevenueRecognition.total_recognized_revenue)


  $scope.$watchGroup ['orderHash', 'reason'], (newVal) ->
    if !newVal[0]
      $scope.disableSubmit = true
      return
    if !newVal[1]
      $scope.disableSubmit = true
      return
    $scope.disableSubmit = false
  
  $scope.queryOrder = ->
    if $scope.isLoading
      return
    if $scope.orderHash.length != 32
      notificationService.error '请输入正确的订单号'
      return
    $scope.isLoading = true
    $scope.changedPrice = NaN
    $scope.reason = ''
    tradeService.getOrder({
      order_hash: $scope.orderHash
      with_detail: true
    })
    .then (res) ->
      if res.status != TRADE_ORDER_STATUS.PAID && res.status != TRADE_ORDER_STATUS.POSTPAY
        notificationService.error '该订单状态不满足退款条件！'
        return
      $scope.orderDetail = res
      $scope.selectedGoods = []
      $scope.hasSelected = false
      $scope.edit = false
      $scope.refund =
        refundType: 'allRefund'
      if res.status == TRADE_ORDER_STATUS.POSTPAY
        $scope.isLoading = false
        $scope.refundAllGoods = true
        $scope.forceRefundAllGoods = true
        $scope.selectGoods()
       else
        $scope.forceRefundAllGoods = false

    .catch (err) ->
      notificationService.error "订单查询失败,#{err}"
      $scope.orderDetail = undefined
    .finally ->
      $scope.isLoading = false

  if $scope.orderHash
    $scope.queryOrder()

  $scope.editOrderHash = ->
    $scope.edit = true

  $scope.submit = ->
    refundMoney = 0
    if !$scope.refundAllGoods
      if $scope.refund.refundType == 'partRefund'
        if !$scope.selectedGoods[0].refundQuota && $scope.selectedGoods[0].refundQuota != 0
          notificationService.error "退款金额必须大于等于 0 小于等于 #{$scope.selectedGoods[0].c_fee}"
          return
        money = Number($scope.selectedGoods[0].refundQuota)
        moneySplit = money.toString().split('.')
        if moneySplit[1] && moneySplit[1].length > 2
          notificationService.error "退款金额最多支持小数点后两位"
          return
        refundMoney = money
      else
        refundMoney = $scope.selectedGoods[0].c_fee
    else
      refundMoney = $scope.selectedGoods.map((item) -> item.c_fee).reduce((a, b) -> a + b)
    params =
      order_hash: $scope.orderHash
      reason: $scope.reason
      approval_type: TRADE_APPROVAL_TYPE.REFUND
    if $scope.refundAllGoods
      params.req_order_refund_cash =
        order_hash: $scope.orderHash
        expected_money: refundMoney
    else
      params.req_product_order_refund_cash =
        product_order_id: $scope.selectedGoods[0].id
        expected_money: refundMoney
    if $scope.isLoading
      return
    $scope.isLoading = true
    qbpmService.applyUnifyApproval params
    .then (res) ->
      if res.code == 200 && res.data.accepted
        notificationService.success '审批发起成功'
        $modalInstance.close 'ok'
        setTimeout ->
          $state.go 'layout.ApprovalList', {
              view_type: 1,
              status: 1,
              page: 0,
              page_size: 20
            }
        , 100
      else
        notificationService.error "申请失败，#{res.data.tips}"
    .catch (err) ->
      notificationService.error "申请失败，#{err}"
    .finally ->
      $scope.isLoading = false
