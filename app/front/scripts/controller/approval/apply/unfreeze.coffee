angular.module 'app.controller'
.controller 'removeFreezeApplyCtrl', (
  $scope, $modalInstance, notificationService, uid
  developerViewService, riskApplyService
) ->
  $scope.uid = uid
  init = () ->
    $scope.promisePayFiles = []
    $scope.creditsFiles = []
    $scope.financeFiles = []
    $scope.userInfo = null
    $scope.reason = ''

  init()

  $scope.query = () ->
    developerViewService.getOverview {uid: $scope.uid}
    .then (res) ->
      $scope.userInfo = res

  $scope.$watch 'uid', () ->
    init()

  $scope.$watch 'userInfo', (info) ->
    # freeze_type == 5 违规冻结
    if info && (info.is_buffered || info.is_freezed) && info.freeze_type != 5
      $scope.invalid = false
      return
    $scope.invalid = true
  , true

  $scope.submit = () ->
    if !$scope.reason
      notificationService.error '申请理由必填'
      return
    if $scope.promisePayFiles.length == 0
      notificationService.error '付款承诺书必须上传至少一份。'
      return
    body =
      attachments:
        written_undertakings: $scope.promisePayFiles
        credit_reports: $scope.creditsFiles
        financial_statements: $scope.financeFiles
      reason: $scope.reason
    $scope.isSubmitting = true
    riskApplyService.applyUnfreeze $scope.uid, body
    .then () ->
      notificationService.success '申请成功'
      $modalInstance.close()
    .catch (err) ->
      notificationService.error "申请失败，#{err.message}"
    .finally () ->
      $scope.isSubmitting = false

  $scope.close = () ->
    $modalInstance.close()
