angular.module 'app.controller'
.controller 'OrderCouponApplyCtrl', (
  $scope, $state, $stateParams, $modalInstance
  newCouponService, sellerService, developerViewService, uid
  notificationService, newCouponMap, $q, tradeService, $rootScope
  CurrencyType
) ->
  $scope.CurrencyType = CurrencyType
  $scope.translationTexts =
    checkAll: '全选'
    uncheckAll: '取消全选'
    buttonDefaultText: '请选择产品线（可多选）'
  $scope.extraSettings =
    smartButtonMaxItems: 10
    smartButtonTextConverter: (itemtext) -> itemtext
    scrollable: true
  $scope.interval = null
  $scope.uid = uid || ''
  # 产品线下拉框
  sellerListParams =
    status: 1
    page_size: 200
  $scope.sellerMapList = []
  sellerService.getSellerList(sellerListParams).then (res) ->
    $scope.sellerMapList = res.map (item) ->
      id: item.id
      label: item.id + '-' + item.title
  $scope.selectedSellers = []
  $scope.BIZ_TYPE = _.omit(newCouponMap.reasonType, ['Activity']) # 过滤掉活动
  # 商家selected model
  $scope.sellers = ''
  # 已选的商家
  $scope.sellersList = []
  $scope.discountType = '0' # 满减类型

  $scope.coupon =
    applicant: 29 # 固定为销售
    name: ''
    description: ''
    reason: ''
    reason_desc: ''
    bind_method: newCouponMap.couponBindMethod.COUPON_BIND_METHOD_PREBOUND
    time_period_type: newCouponMap.couponTimePeriodType.COUPON_TIME_PERIOD_TYPE_ABSOLUTE
    effect_days: 0
    threshold_money: null
    is_multiple_use: true
    coupon_money: null
    coupon_effect_time: moment().toDate()
    coupon_dead_time: ''
    is_unlimited_scope: false
    user_scope: newCouponMap.couponUserScope.COUPON_USER_SCOPE_UNLIMITED
    pay_mode_scope: newCouponMap.couponPayModeScope.COUPON_PAY_MODE_SCOPE_PREPAID
    scope:
      seller_ids_include: []
      seller_ids_exclude: []
      product_ids_include: []
      product_ids_exclude: []
    coupon_scope_desc: ''

  $scope.$watch 'discountType', (newVal) ->
    if newVal == '0'
      $scope.coupon.threshold_money = null

  $scope.$watch 'coupon.scope', (newVal) ->
    clearTimeout($scope.interval)
    $scope.interval = setTimeout(
      () ->
        if !$scope.coupon.is_unlimited_scope
          tanslateScope(newVal)
      , 500
    )
  , true

  $scope.$watch 'coupon.is_unlimited_scope', (newVal) ->
    if newVal
      $scope.coupon.coupon_scope_desc = '预付费通用'
    else
      $scope.sellers = ''
      setTimeout(
        () ->
          tanslateScope($scope.coupon.scope)
        , 100
      )

  $scope.$watchGroup ['coupon.coupon_money', 'sellers', 'coupon.reason'], (newVal) ->
    if newVal[0] > 1000 && newVal[1] && newVal[1].id == 54 && newVal[2] == 101
      $scope.qvmMoneyInvalid = true
    else
      $scope.qvmMoneyInvalid = false

  $scope.$watch 'sellers', (newVal) ->
    $scope.coupon.is_unlimited_scope = false
    $scope.coupon.scope.seller_ids_include = [newVal.id]
  , true

  $scope.query = () ->
    developerViewService.getOverview({uid: $scope.uid})
    .then (res) ->
      if res.currency_type == CurrencyType.USD
        $scope.isUSDUser = true
        $scope.developerInfo = null
      else
        $scope.isUSDUser = false
        $scope.developerInfo = res
    .catch () ->
      $scope.developerInfo = null
      notificationService.error '获取用户信息失败'

  tanslateScope = (couponScope) ->
    if !$scope.sellers
      return
    $scope.sellersList = []
    selectedSellers = couponScope.seller_ids_include
    sellerPromises = []
    sellerPromises = selectedSellers.map((item) -> getSeller(item))
    $q.all(sellerPromises)
    .then () ->
      text = []
      text = text.concat $scope.sellersList.map((item) -> item.title)
      $scope.coupon.coupon_scope_desc = text.join('、')

  getSeller = (id) ->
    tradeService.getSeller({id: id})
    .then (res) ->
      $scope.sellersList.push res
    .catch (err) ->
      notificationService.error "获取商品id:#{id} 失败, #{err}"

  $scope.submit = () ->
    formData = _.cloneDeep($scope.coupon)
    formData.coupon_money = Math.round(formData.coupon_money * 10000)
    formData.threshold_money = Math.round(formData.threshold_money * 10000)
    if formData.is_unlimited_scope
      formData.scope =
        seller_ids_include: []
        seller_ids_exclude: []
        product_ids_include: []
        product_ids_exclude: []
    if !formData.is_unlimited_scope and formData.scope.seller_ids_include.length == 0
      alert('未选择商家！')
      return
    if moment(formData.coupon_effect_time).isBefore(moment().startOf('day'))
      alert('生效开始日期不可早于当前日期')
      return
    if moment(formData.coupon_effect_time).isAfter(moment(formData.coupon_dead_time))
      alert('生效截止日期不可早于生效开始日期')
      return
    formData.coupon_effect_time = moment(formData.coupon_effect_time).startOf('day').format()
    formData.coupon_dead_time = moment(formData.coupon_dead_time).startOf('day').format()
    formData.start_time = moment().format()
    formData.end_time = moment().format()
    formData.batch_size = 1
    formData.max_activation_times_per_user = 1
    bodyParams =
      uid: $scope.uid
      applicant_email: $rootScope.userInfo.email
      create_coupon_batch_params: formData
    $scope.isSubmitting = true
    newCouponService.applyCoupon(bodyParams)
    .then (res) ->
      notificationService.success '申请成功，请到 "工作台 -> 审批任务" 中查看'
      $scope.close()
    .catch (err) ->
      notificationService.error "申请失败#{err.error}"
    .finally ->
      $scope.isSubmitting = false

  $scope.close = ->
    $modalInstance.dismiss 'close'