angular.module 'app.controller'
.controller 'RechargeRewardApplyCtrl', (
  $scope, $modalInstance, uid, $modal
  developerViewService, notificationService
  TransactionTypeTranslation, qbpmService
) ->
  $scope.TransactionTypeTranslation = TransactionTypeTranslation
  $scope.uid = uid || ''
  $scope.hasContract = true # 默认有合同
  $scope.amount = null
  $scope.reason = ''
  $scope.transaction = null
  $scope.relationContract = null

  $scope.query = () ->
    developerViewService.getOverview({uid: $scope.uid})
    .then (res) ->
      $scope.developerInfo = res
    .catch ->
      $scope.developerInfo = null
      notificationService.error '获取客户信息失败'

  $scope.$watch 'uid', () ->
    if $scope.uid
      $scope.amount = null
      $scope.reason = ''
      $scope.transaction = null
      $scope.relationContract = null
      $scope.query($scope.uid)

  $scope.$watch 'hasContract', (newVal) ->
    $scope.relationContract = null

  $scope.$watchGroup [
    'uid', 'transaction',
    'relationContract', 'hasContract'
  ], (newVal) ->
    if !$scope.uid || !$scope.transaction
      $scope.invalidForm = true
      return
    if $scope.hasContract && !$scope.relationContract
      $scope.invalidForm = true
      return
    $scope.invalidForm = false
  , true

  $scope.showRechargeTransactions = () ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/recharge-reward/transactions.html'
      controller: 'RechargeRewardSelectRechargeCtrl',
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: -> $scope.uid
        currency: -> $scope.developerInfo.currency_type

    modal.result.then (result) ->
      $scope.transaction = result

  $scope.showContracts = () ->
    modal = $modal.open
      templateUrl: 'templates/approval/apply/recharge-reward/contracts.html'
      controller: 'RechargeRewardContractCtrl',
      backdrop: 'static'
      size: 'lg'
      resolve:
        uid: -> $scope.uid

    modal.result.then (result) ->
      $scope.relationContract = result

  $scope.close = ->
    $modalInstance.dismiss 'close'

  $scope.handleAmountChange = ->
    return unless $scope.amount?
    
    amountStr = $scope.amount.toString()
    if amountStr.includes('.')
      [integer, decimal] = amountStr.split('.')
      if decimal.length > 2
        decimal = decimal.slice(0, 2)
        $scope.amount = parseFloat("#{integer}.#{decimal}")

  $scope.submit = () ->
    data =
      uid: $scope.uid
      amount: Math.round(($scope.amount || 0) * 10000)
      reason: $scope.reason
      transaction: $scope.transaction
    if $scope.hasContract
      data.contract = $scope.relationContract

    $scope.isSubmitting = true
    qbpmService.applyNB data
    .then () ->
      notificationService.success '申请成功，请到 "工作台 -> 审批任务" 中查看'
      $scope.close()
    .catch (err) ->
      notificationService.error err
    .finally ->
      $scope.isSubmitting = false

angular.module 'app.controller'
.controller 'RechargeRewardSelectRechargeCtrl', (
  $scope, $modalInstance, uid, currency
  walletService, TransactionTypeTranslation
) ->
  $scope.TransactionTypeTranslation = TransactionTypeTranslation
  $scope.currency = currency
  $scope.transaction =
    value: null

  filterTypes = [
    'paypal_card', 'recharge_renewal', 'BANK', 'alipay', 'wxpay', 'ebank'
  ]
  walletService.listRechargeTransactionsWithSnapshot uid
  .then (res) ->
    $scope.rechargeTransactions = res.filter (item) ->
      return filterTypes.includes item.transaction.type

  $scope.selectTransaction = (t) ->
    $scope.transaction.value = t

  $scope.close = ->
    $modalInstance.dismiss 'close'

  $scope.submit = () ->
    $modalInstance.close $scope.transaction.value


angular.module 'app.controller'
.controller 'RechargeRewardContractCtrl', (
  $scope, $modalInstance, uid, sofaService, notificationService
) ->
  $scope.uid = uid
  $scope.contract =
    value: null

  $scope.query = () ->
    sofaService.getContracts($scope.uid)
    .then (res) ->
      $scope.contracts = res.items

  $scope.query()

  $scope.selectContract = (c) ->
    $scope.contract.value = c

  $scope.submit = () ->
    $modalInstance.close $scope.contract.value

  $scope.close = ->
    $modalInstance.dismiss 'close'
