angular.module 'app.controller'
.controller 'BillCouponApplyCtrl', (
  $scope, $stateParams, $rootScope, $state, $modalInstance, $filter
  responsibleOwnerType, qiniuPriceProduct, responsibleBizType, qiniuProduct
  COUPON_TYPE, uid, CurrencyType, dictv4Service,
  developerViewService, developerQBPMService, notificationService, couponService
) ->
  $scope.applyUid = uid || ''
  $scope.shouldForbidSubmit = false

  $scope.query = () ->
    developerViewService.getOverview({uid: $scope.applyUid})
    .then (res) ->
      $scope.developerInfo = res
      $scope.TestQuota = if res.currency_type == CurrencyType.USD then 1000 else 5000
      $scope.QvmTestQuota = if res.currency_type == CurrencyType.USD then 200 else 1000
    .catch () ->
      $scope.developerInfo = null
      notificationService.error '获取用户信息失败'

  # 抵用券事由分类
  # coffeelint: disable=max_line_length
  $scope.BIZ_TYPE = _.omit(responsibleBizType, (v) -> v.value < 100 || v.value > 103 || v.value == 102) # filter which is not coupon
  # 责任承担方代码
  $scope.OWNER_CODE = _.assign
    bo: '商业运营'
    marketing: '市场部'
    ts: '技术支持'
    sales: '销售部'
  , _.mapValues _.omit(qiniuPriceProduct, (v) -> v.sortIndex > 100), (v) -> v.display # filter '未知产品线', '其他产品线'

  # 申请信息
  $scope.reason = ''

  $scope.coupon =
    quota: NaN
    day: 0
    deadtime: ''
    type: COUPON_TYPE.RECHARGE
    desc: ''
    title: ''
    all: false
    products: ''
    items: []
    effecttime: ''
    expiredtime: ''
    arrearCanUse: true # 欠费可用
    max_activation_times_in_batch: 0 #不限制激活
    uid: $scope.applyUid
    owner_type: responsibleOwnerType.Team.value
    owner_code: 'sales' # 销售
    biz_type: 0

  # 产品多选框配置
  $scope.productsList = []
  dictv4Service.listAllProducts(true)
   .then (res) ->
    $scope.productsList = _.map res.products, (product, name) ->
        id: product.code
        label: product.name
   .catch (err) ->
    notificationService.error '获取产品失败: ' + err.message

  $scope.beforeRender = ($view, $dates) ->
    timezoneOffset = 0
    now = moment().local().startOf('month')
    # @if isSufy
    nowDate = new Date()
    timezoneOffset = nowDate.getTimezoneOffset()
    # @endif
    $dates.filter((date) ->
      # @if isSufy
      date.utcDateValue = date.utcDateValue - timezoneOffset * 60000
      # @endif
      return date.localDateValue() < now.valueOf()
    ).forEach((date) ->
      date.selectable = false
    )
  $scope.$watch 'coupon.biz_type', (newVal) ->
    if newVal == responsibleBizType.CRKeep.value
      $scope.productsList = [{id: 'all', label: '全部产品线'}].concat($scope.productsList.filter (item) -> item.id != 'all')
    else
      $scope.productsList = $scope.productsList.filter (item) -> item.id != 'all'
      if $scope.coupon.products == 'all'
        $scope.coupon.products = ''
  , true

  limitedQuotaFilter = (quota) ->
    return $filter('moneyWithCurrencyFilter')(quota, 1, $scope.developerInfo.currency_type)

  $scope.$watchGroup ['coupon.products', 'coupon.biz_type'], (newVal) ->
    $scope.shouldForbidSubmit = false
    if newVal[0] && newVal[1] == responsibleBizType.Test.value
      body =
        uid: $scope.applyUid
        product_codes: if newVal[0] == 'qvm' then [newVal[0]] else []
        is_all_products: true
        reason: $scope.coupon.biz_type
      couponService.getTotalCouponQuota body
      .then (res) ->
        product = res.find (item) -> item.product_code == 'all'
        $scope.overQuotaLabel = ''
        $scope.limitedLabel = ''
        $scope.qvmLimitedLabel = ''
        $scope.quotaIsLimited = true
        $scope.qvmLimitedBalanceQuota = NaN
        $scope.quotaLimited = product?.quota
        if $scope.coupon.products == 'qvm'
          $scope.isQvmLimited = true
          qvmProdcutUsedQuota = (res.find (item) -> item.product_code == 'qvm')?.quota
          qvmLimitedQuota = $scope.QvmTestQuota * 10000 - qvmProdcutUsedQuota
          if $scope.quotaLimited >= $scope.TestQuota * 10000
            $scope.overQuotaLabel = "测试类按量计费抵用券总金额不得大于 #{limitedQuotaFilter($scope.TestQuota)}，已无法再申请 。"
            notificationService.error $scope.overQuotaLabel
            $scope.shouldForbidSubmit = true
          else if qvmLimitedQuota <= 0
            $scope.overQuotaLabel = "QVM 测试类按量计费抵用券总金额不得大于 #{limitedQuotaFilter($scope.QvmTestQuota)}，已无法再申请 "
            notificationService.error $scope.overQuotaLabel
            $scope.shouldForbidSubmit = true
          else
            quota = if (qvmLimitedQuota + $scope.quotaLimited) > $scope.TestQuota * 10000 then ($scope.TestQuota * 10000 - $scope.quotaLimited) else qvmLimitedQuota
            $scope.qvmLimitedBalanceQuota = quota / 10000
            $scope.qvmLimitedLabel = "QVM 测试类按量计费抵用券总金额不得大于 #{limitedQuotaFilter($scope.QvmTestQuota)}，#{if $scope.qvmLimitedBalanceQuota then '本次最多申请' +  limitedQuotaFilter($scope.qvmLimitedBalanceQuota) else '本次无法申请'} 。"
            $scope.shouldForbidSubmit = false
        else
          if $scope.quotaLimited >= $scope.TestQuota * 10000
            $scope.overQuotaLabel = "测试类按量计费抵用券总金额不得大于 #{limitedQuotaFilter($scope.TestQuota)}，已无法再申请 。"
            notificationService.error $scope.overQuotaLabel
            $scope.shouldForbidSubmit = true
          else
            $scope.limitedBalanceQuota = if ($scope.TestQuota * 10000 - $scope.quotaLimited) > 0 then ($scope.TestQuota * 10000 - $scope.quotaLimited) / 10000 else 0
            $scope.limitedLabel = "测试类按量计费抵用券总金额不得大于 #{limitedQuotaFilter($scope.TestQuota)}，#{if $scope.limitedBalanceQuota then '本次最多申请' +  limitedQuotaFilter($scope.limitedBalanceQuota) else '本次无法申请'} 。"
            $scope.shouldForbidSubmit = false
          $scope.isQvmLimited = false
      .catch (err) ->
        notificationService.error(err)
    else
      $scope.quotaIsLimited = false
  , true

  $scope.submit = () ->
    if $scope.shouldForbidSubmit
      return
    $scope.shouldForbidSubmit = true
    copyCoupon = angular.copy $scope.coupon
    copyCoupon.uid = $scope.applyUid
    copyCoupon.all = if copyCoupon.products == 'all' then true else false
    copyCoupon.products = if copyCoupon.products == 'all' then [] else [copyCoupon.products]
    copyCoupon.quota = Math.round copyCoupon.quota * 10000 # 取整的方式在这里应该不会有副作用(解决浮点数问题)
    copyCoupon.effecttime = moment(copyCoupon.effecttime).startOf('month').valueOf() * 10000
    copyCoupon.expiredtime = moment(copyCoupon.expiredtime).endOf('month').valueOf() * 10000
    copyCoupon.deadtime = copyCoupon.expiredtime
    data =
      name: $scope.coupon.title
      desc: $scope.reason
      coupons: [copyCoupon]
    option =
      applicant_uid: $scope.applyUid
      create_coupon_batch_params: data
    couponService.applyCoupon option
    .then (res) ->
      notificationService.success '申请成功，请到 "工作台 -> 审批任务" 中查看'
      $scope.close()
    .catch (err) ->
      notificationService.error "申请失败：#{err.message}"
    .finally ->
      $scope.shouldForbidSubmit = false

  $scope.close = ->
    $modalInstance.dismiss 'close'
