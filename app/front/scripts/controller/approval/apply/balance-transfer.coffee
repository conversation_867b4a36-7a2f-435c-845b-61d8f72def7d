angular.module 'app.controller'
.controller 'BalanceTransferApplyCtrl', (
  $scope, uid, $modalInstance, notificationService
  developerViewService, qbpmService, walletService
) ->

  $scope.formData =
    from_uid: uid
    to_uid: ''
    cash_amount: 0
    nb_amount: 0
    reason: ''
    attachments: []

  $scope.$watch 'formData.from_uid', (uid) ->
    if uid
      developerViewService.getOverview {uid: uid}
      .then (res) ->
        $scope.fromUserInfo = res
      .catch ->
        $scope.fromUserInfo = null
      walletService.getWalletOverview uid
      .then (res) ->
        $scope.fromUserInfo.walletOverview = res
    else
      $scope.fromUserInfo = null
  , true

  $scope.$watch 'formData.to_uid', (uid) ->
    if uid
      developerViewService.getOverview {uid: uid}
      .then (res) ->
        $scope.toUserInfo = res
      .catch ->
        $scope.toUserInfo = null
    else
      $scope.toUserInfo = null
  , true

  $scope.checkFormData = ->
    if !$scope.formData.from_uid
      notificationService.error '请选择转账发起方'
      return false
    if !$scope.formData.to_uid
      notificationService.error '请选择转账接收方'
      return false
    if $scope.formData.attachments.length == 0
      notificationService.error '请上传附件'
      return false
    if !$scope.formData.cash_amount && !$scope.formData.nb_amount
      notificationService.error '现金和赠送金不能同时为 0'
      return false
    if $scope.formData.from_uid == $scope.formData.to_uid
      notificationService.error '转入 uid 和转出 uid 不能是同一个'
      return false
    if $scope.fromUserInfo.currency_type != $scope.toUserInfo.currency_type
      notificationService.error '转入 uid和转出 uid 币种不一致'
      return false
    if $scope.toUserInfo.parent_uid > 0
      notificationService.error '转入 uid 不能是 oem 子账号'
      return false
    return {
      from_uid: $scope.formData.from_uid
      to_uid: $scope.formData.to_uid
      cash_amount: Math.round $scope.formData.cash_amount * 10000
      nb_amount: Math.round $scope.formData.nb_amount * 10000
      reason: $scope.formData.reason
      attachments: $scope.formData.attachments
    }

  $scope.submit = () ->
    data = $scope.checkFormData()
    if !data
      return
    if $scope.isSubmitting
      return
    $scope.isSubmitting = true
    qbpmService.applyBalanceTransfer data
    .then () ->
      notificationService.success '申请成功，请到 "工作台 -> 审批任务" 中查看'
      $scope.close()
    .catch (err) ->
      notificationService.error "申请失败，#{err}"
    .finally () ->
      $scope.isSubmitting = false

  $scope.close = () ->
    $modalInstance.dismiss 'close'
