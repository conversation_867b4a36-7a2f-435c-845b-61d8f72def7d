angular.module 'app.controller'
.controller 'OrderApprovalPostPayCtrl', (
  $scope, $modalInstance, order_hash, $state
  CurrencyType, TRADE_ORDER_STATUS, PRODUCT_UNIT_KEYS_MAP_TEXT, TRADE_APPROVAL_TYPE
  tradeService, notificationService, qbpmService
) ->
  $scope.isLoading = false
  $scope.PRODUCT_UNIT_MAP = PRODUCT_UNIT_KEYS_MAP_TEXT
  $scope.CurrencyType = CurrencyType
  $scope.orderHash = order_hash
  $scope.reason = ''
  $scope.edit = true
  $scope.close = ->
    $modalInstance.dismiss 'close'

  $scope.$watchGroup ['orderHash', 'reason'], (newVal) ->
    if !newVal[0]
      $scope.disableSubmit = true
      return
    if !newVal[1]
      $scope.disableSubmit = true
      return
    $scope.disableSubmit = false
  
  $scope.queryOrder = ->
    if $scope.isLoading
      return
    if $scope.orderHash.length != 32
      notificationService.error '请输入正确的订单号'
      return
    $scope.isLoading = true
    $scope.reason = ''
    param =
      order_hash: $scope.orderHash
      approval_type: TRADE_APPROVAL_TYPE.POST_PAY
    qbpmService.checkApproval param
    .then () ->
      tradeService.getOrder({
        order_hash: $scope.orderHash
        with_detail: true
      })
      .then (res) ->
        $scope.orderDetail = res
        $scope.edit = false
    .catch (err) ->
      notificationService.error "#{err}"
      $scope.orderDetail = undefined
    .finally ->
      $scope.isLoading = false

  if $scope.orderHash
    $scope.queryOrder()

  $scope.editOrderHash = ->
    $scope.edit = true

  $scope.submit = ->
    if $scope.isLoading
      return
    $scope.isLoading = true
    params =
      order_hash: $scope.orderHash
      reason: $scope.reason
      approval_type: TRADE_APPROVAL_TYPE.POST_PAY
      req_order_pay:
        order_hash: $scope.orderHash
        ignore_expired: true
    qbpmService.applyUnifyApproval params
    .then (res) ->
      if res.code == 200
        if res.data.accepted
          notificationService.success '审批发起成功！'
          $modalInstance.close 'ok'
          setTimeout ->
            $state.go 'layout.ApprovalList', {
              view_type: 1,
              status: 1,
              page: 0,
              page_size: 20
            }
          , 100
        else
          notificationService.error "申请失败，#{res.data.tips}"
      else
        notificationService.error "申请失败，#{res.message}"
    .catch (err) ->
      notificationService.error "申请失败，#{err}"
    .finally ->
      $scope.isLoading = false
