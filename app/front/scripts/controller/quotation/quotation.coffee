angular.module 'app.controller'
  .controller 'QuotationCtl', (
  $scope,
  $modal,
  $state,
  $filter,
  $stateParams,
  qbpmService,
  STAIR_PRICE_TYPE
) ->

  price_type_desc = {
    'bandwidth': '按带宽计费',
    'concurrent': '按并发数计费',
    'time': '按时长计费'
  }

  # 由于某些内容显示是可选的, 为了前端展示, 实现会别扭些
  $scope.info_key_desc = {
    'stair_type': '阶梯规则：',
    'unit_rate': '进制单位：',
    'price_type': '计费规则：',
    'algorithm_name': '计 费 点 ：',
    'time_range': '有效时间：'
  }

  $scope.type = $stateParams.type
  $scope.time_range_quotation = 'time_range'
  $scope.time_point_quotation = 'time_point'
  $scope.isLoading = true
  $scope.start_time = $stateParams.start_time
  $scope.end_time = $stateParams.end_time

  # qbpmService.getUserPriceWithTimeRange "1810734984", "2018-01-02T15:04:05.0Z", "2019-01-02T15:04:05.0Z"
  qbpmService.getUserPriceWithTimeRange $stateParams.uid, $stateParams.start_time, $stateParams.end_time
    .then (response) ->
      quotation = parseResult(response, $stateParams.type)
      sortByItemTitle(quotation)
      $scope.quotation = quotation
      $scope.isLoading = false

  sortByItemTitle = (quotation) ->
    for product_name, items of quotation
      items.sort((a, b) ->
        if a.title > b.title
          return 1
        if a.title < b.title
          return -1
        return 0
      )

  parseResult = (response, type) ->
    quotation = {}
    for product, items of response['product_prices']
      for item, prices of items['item_prices']
        pricesByZone = {}
        # 计费项聚合的改价，再按机房聚合
        for index, price of prices['price_items']
          if !pricesByZone[price.zone.id]
            pricesByZone[price.zone.id] = [price]
          else
            pricesByZone[price.zone.id].push(price)
            
        for zoneId, prices of pricesByZone
          if type == $scope.time_range_quotation
            price_items = prices
          else
            price_items = prices.slice(prices.length - 1)

          for index, price of price_items
            quotation_item =  parsePriceItems(price, type)
            product_name = price['product']['name']
            if !quotation[product_name]
              quotation[product_name] = [quotation_item]
            else
              quotation[product_name].push(quotation_item)
    return quotation


  parseStair = (quotation_item, price_item_stairs, stair_type, price_unit) ->
    quotation_item['stairs'] = []
    prev_stair_value = 0
    prev_unit = ''
    for index, stair_item of price_item_stairs
      stair_value = stair_item['price_item_stair']['quantity']
      unit = stair_item['unit']['name']
      price_value = $filter('moneyFormatter')(parseInt(stair_item['price_item_stair']['price']), 8, 100000000, true) + ' 元'
      if prev_unit == ''
        prev_unit = unit
      if parseInt(index) + 1 == price_item_stairs.length
        quotation_item['stairs'].push({
          'stair_range': stair_value + ' ' + unit + '以上',
          'price': price_value + stairPriceUnit(stair_type, price_unit, parseInt(index))
        })
      else
        quotation_item['stairs'].push({
          'stair_range': prev_stair_value + ' ' + prev_unit + ' ~ ' + stair_value + ' ' + unit,
          'price': price_value + stairPriceUnit(stair_type, price_unit, parseInt(index))
        })
      prev_stair_value = stair_value
      prev_unit = unit


  stairPriceUnit = (stair_type, unit, index) ->
    switch stair_type
      when 'FIRST_BUYOUT'
        if index == 0
          return ''
        else
          return '/' + unit
      when 'EACH_BUYOUT', 'BUYOUT'
        return ''
      else
        return '/' + unit



  parsePriceItems = (price, quotation_type) ->
    quotation_item = {
      'title': price['item']['name'] + ' - ' + price['zone']['title'],
      'info': [
          {'key': 'stair_type', 'value': STAIR_PRICE_TYPE[price['price_item']['stair_price_type']]}
      ]
    }

    price_type = price['data_type']['type']
    if price_type == 'default'
      if price['data_type']['name'].indexOf('流量') >= 0
        price_type = '按流量计费'
        quotation_item['info'].push({'key': 'price_type', 'value': price_type})
    else
      price_type = price_type_desc[price_type]
      quotation_item['info'].push({'key': 'price_type', 'value': price_type})

    if isContainInfo(quotation_item['info'], 'price_type')
      unit_rate = parseInt(price['price_item']['unit_rate'])
      if unit_rate == 0
        unit_rate = parseInt(price['data_type']['unit_rate'])
      quotation_item['info'].push({'key': 'unit_rate', 'value': unit_rate})

    if price['data_type']['type'] == 'bandwidth'
      algorithm_name = dealAlgorithmName(price['algorithm']['name'])
      quotation_item['info'].push({'key': 'algorithm_name', 'value': algorithm_name})

    if quotation_type == $scope.time_range_quotation
      # coffeelint: disable=max_line_length
      quotation_item['info'].push({'key': 'time_range', 'value': $filter('formatDateFilter')(price['price_item']['effect_time'], '/') + ' ~ ' + $filter('formatDateFilter')(price['price_item']['dead_time'], '/')})
      # coffeelint: enable=max_line_length

    # coffeelint: disable=max_line_length
    parseStair(quotation_item, price['price_item_stairs'], price['price_item']['stair_price_type'], price['price_unit']['name'])
    # coffeelint: enable=max_line_length
    return quotation_item

  dealAlgorithmName = (name) ->
    index = name.indexOf('(')
    if index > 0
      return name.substring(0, index)
    return name

  isContainInfo = (infos, key) ->
    for index, info of infos
      if info.key == key
        return true
    return false

  # 由于portal-io添加了水印，不知啥原因打印不出来，所以选择局部打印的方式，规避空白问题和去除水印
  $scope.myprint = () ->
    childs = window.document.body.childNodes
    for child in childs
      if child.id && child.id.indexOf('watermark') == 0
        window.document.body.removeChild(child)
        break
    window.print()
