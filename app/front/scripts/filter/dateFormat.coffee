angular.module 'app.filter'
.filter 'formatDateFilter', () ->
  return (date, separator) ->
    if separator
      return moment(new Date(date)).format('YYYY' + separator + 'MM' + separator + 'DD')
    else
      return moment(new Date(date)).format('YYYY-MM-DD')

.filter 'YYYYMMDDFormat', () ->
  return (date, format) ->
    return moment(date, 'YYYYMMDD').format(format || 'YYYY-MM-DD HH:mm:ss')

.filter 'dateFormatter', () ->
  return (date, format) ->
    # coffeelint: disable=max_line_length
    return if moment(date).valueOf() < 0 then '-' else moment(date).format(format || 'YYYY-MM-DD HH:mm:ss')