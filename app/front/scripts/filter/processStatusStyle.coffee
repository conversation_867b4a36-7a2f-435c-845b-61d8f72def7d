angular.module 'app.filter'
.filter 'processStatusStyleFilter', (PROCESS_STATUS) ->
  return (status) ->
    switch parseInt(status)
      when PROCESS_STATUS.PENDING then return 'label-info'
      when PROCESS_STATUS.COMPLETE then return 'label-success'
      when PROCESS_STATUS.REMOVED then return 'label-warning'
      when PROCESS_STATUS.FAILED then return 'label-danger'
      else return 'label-default'
