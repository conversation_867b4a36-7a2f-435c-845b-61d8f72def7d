angular.module 'app.filter'
.filter 'byteSizeFilter', () ->
  return (size, decimal = 2) ->
    if !size
      return '0 Bytes'

    decimal = if decimal < 0 then 0 else decimal

    unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    i = Math.floor(Math.log(size) / Math.log(1024))
    if i >= unitArr.length
      i = unitArr.length - 1
    return parseFloat((size / Math.pow(1024, i)).toFixed(decimal)) + ' ' + unitArr[i]
