(->
  # 保持与 https://github.com/qbox/pay-sdk/blob/develop/pricebiz/models.proto Dimension 定义一致
  angular.module 'app.constant'
    .constant 'MEASUREMENT_DIMENSION', {
      USER_PRODUCT: 1
      USER: 2
      SALES: 3
      SALES_AREA: 4
      PRODUCT: 5
      QINIU: 6
    }
    .factory 'MEASUREMENT_DIMENSION_SYMBOL_MAP', (MEASUREMENT_DIMENSION) ->
      @['DIMENSION_USER_PRODUCT'] = MEASUREMENT_DIMENSION.USER_PRODUCT
      @['DIMENSION_USER'] = MEASUREMENT_DIMENSION.USER
      @['DIMENSION_SALES'] = MEASUREMENT_DIMENSION.SALES
      @['DIMENSION_SALES_AREA'] = MEASUREMENT_DIMENSION.SALES_AREA
      @['DIMENSION_PRODUCT'] = MEASUREMENT_DIMENSION.PRODUCT
      @['DIMENSION_QINIU'] = MEASUREMENT_DIMENSION.QINIU
      return @
    .factory 'MEASUREMENT_DIMENSION_MAP', (MEASUREMENT_DIMENSION) ->
      @[MEASUREMENT_DIMENSION.USER_PRODUCT] = '单用户单产品'
      @[MEASUREMENT_DIMENSION.USER] = '当前用户整体'
      @[MEASUREMENT_DIMENSION.SALES] = '当前销售'
      @[MEASUREMENT_DIMENSION.SALES_AREA] = '当前销售大区'
      @[MEASUREMENT_DIMENSION.PRODUCT] = '当前产品线整体'
      @[MEASUREMENT_DIMENSION.QINIU] = '七牛全公司'
      return @
)()
