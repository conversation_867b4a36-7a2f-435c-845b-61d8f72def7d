angular.module 'app.constant'
.constant 'approvalFeatureList', {
  CONTRACT:
    value: 'contract'
    display: '合同审批'
  ORDER:
    value: 'order'
    display: '订单相关审批'
  BILL:
    value: 'bill'
    display: '账单相关审批'
  OFFER:
    value: 'offer'
    display: '报价审批'
  COUPON:
    value: 'coupon'
    display: '券申请审批'
  CAPITAL:
    value: 'capital'
    display: '账户资金相关审批'
  RISK_CONTROL:
    value: 'risk_control'
    display: '风控相关审批'
}

.constant 'featureTriggerList', {
  order:
    'approve-change-price': 'applyChangeOrderPrice'
    'approve-post-pay': 'applyPostPay'
    'approve-change-price-and-post-pay': 'applyChangePriceAndPostPay'
    'approve-refund': 'applyRefund'
    'approve-change-effect-time': 'applyModifyRespackStartTime'
    'approve-change-end-time': 'applyExtendRespackDuration'
  capital:
    'withdraw': 'applyWithdraw'
  risk_control:
    'unfreeze': 'applyRemoveFreeze'
    'extend-buffer': 'applyExtendBuffer'
  coupon:
    'apply-bill-coupon': 'applyBillCoupon',
    'apply-order-coupon': 'applyOrderCoupon'
}
