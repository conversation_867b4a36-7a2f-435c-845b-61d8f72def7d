(->
  # 保持与 https://github.com/qbox/pay-sdk/blob/develop/pricebiz/models.proto MeasurementPeriod 定义一致
  angular.module 'app.constant'
    .constant 'MEASUREMENT_PERIOD', {
      CURRENT_MONTH_EST: 1
      CURRENT_YEAR_EST: 2
      LAST_MONTH: 3
      LAST_THREE_MONTH_AVG: 4
      LAST_SIX_MONTH_AVG: 5
    }
    .factory 'MEASUREMENT_PERIOD_SYMBOL_MAP', (MEASUREMENT_PERIOD) ->
      @['MEASUREMENT_PERIOD_CURRENT_MONTH_EST'] = MEASUREMENT_PERIOD.CURRENT_MONTH_EST
      @['MEASUREMENT_PERIOD_CURRENT_YEAR_EST'] = MEASUREMENT_PERIOD.CURRENT_YEAR_EST
      @['MEASUREMENT_PERIOD_LAST_MONTH'] = MEASUREMENT_PERIOD.LAST_MONTH
      @['MEASUREMENT_PERIOD_LAST_THREE_MONTH_AVG'] = MEASUREMENT_PERIOD.LAST_THREE_MONTH_AVG
      @['MEASUREMENT_PERIOD_LAST_SIX_MONTH_AVG'] = MEASUREMENT_PERIOD.LAST_SIX_MONTH_AVG
      return @
    .factory 'MEASUREMENT_PERIOD_MAP', (MEASUREMENT_PERIOD) ->
      @[MEASUREMENT_PERIOD.CURRENT_MONTH_EST] = '当前月度预估'
      @[MEASUREMENT_PERIOD.CURRENT_YEAR_EST] = '当前年度预估'
      @[MEASUREMENT_PERIOD.LAST_MONTH] = '上月账期'
      @[MEASUREMENT_PERIOD.LAST_THREE_MONTH_AVG] = '近三月月平均'
      @[MEASUREMENT_PERIOD.LAST_SIX_MONTH_AVG] = '近半年月平均'
      return @
)()
