(->
  class CreditPrevTwoYearsRevenue
    constructor: ->
      @PREV_REVENUE_LT_50W = 1
      @PREV_REVENUE_50W_TO_1000W = 2
      @PREV_REVENUE_1000W_TO_1Y = 3
      @PREV_REVENUE_GT_1Y = 4

    humanize: (x) ->
      switch x
        when @PREV_REVENUE_LT_50W then return '小于 50 万元'
        when @PREV_REVENUE_50W_TO_1000W then return '50 - 1000 万元'
        when @PREV_REVENUE_1000W_TO_1Y then return '1000 万 - 1 亿元'
        when @PREV_REVENUE_GT_1Y then return '大于 1 亿元'
        else return "未知(#{x})"

    humanizeUSD: (x) ->
      switch x
          when @PREV_REVENUE_LT_50W then return '小于 $500K'
          when @PREV_REVENUE_50W_TO_1000W then return '$500K - $10M'
          when @PREV_REVENUE_1000W_TO_1Y then return '$10M - $100M'
          when @PREV_REVENUE_GT_1Y then return '大于 $100M'
          else return "未知(#{x})"

    isLT50W: (x) ->
      return @PREV_REVENUE_LT_50W == x

    is50WTO1000W: (x) ->
      return @PREV_REVENUE_50W_TO_1000W == x

    is1000WTO1Y: (x) ->
      return @PREV_REVENUE_1000W_TO_1Y == x

    isGT1Y: (x) ->
      return @PREV_REVENUE_GT_1Y == x

  angular.module 'app.constant'
    .constant 'creditPrevTwoYearsRevenue', new CreditPrevTwoYearsRevenue()
    .constant 'CREDIT_PREV_REVENUE', {
      PREV_REVENUE_LT_50W: 1,
      PREV_REVENUE_50W_TO_1000W: 2,
      PREV_REVENUE_1000W_TO_1Y: 3,
      PREV_REVENUE_GT_1Y: 4
    }
    .factory 'CREDIT_PREV_REVENUE_MAP', (CREDIT_PREV_REVENUE) ->
      @[CREDIT_PREV_REVENUE.PREV_REVENUE_LT_50W] = '小于 50 万元'
      @[CREDIT_PREV_REVENUE.PREV_REVENUE_50W_TO_1000W] = '50 - 1000 万元'
      @[CREDIT_PREV_REVENUE.PREV_REVENUE_1000W_TO_1Y] = '1000 万 - 1 亿元'
      @[CREDIT_PREV_REVENUE.PREV_REVENUE_GT_1Y] = '大于 1 亿元'

      return @
)()
