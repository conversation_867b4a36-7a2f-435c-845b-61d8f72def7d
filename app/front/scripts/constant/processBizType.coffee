# 流程业务类型
class ProcessBizType
  constructor: ->
    @processBizType =
        APPROVAE_ORDER_CHANGE_PRICE:
          value: 'approve-change-price'
          display: '订单改价申请'
          # coffeelint: disable=max_line_length
          link: 'layout.OrderChangePriceApprovalDetail({id: process.excode, uid: process.uid, process_instance_id: process.process_instance_id})'
        APPROVAE_ORDER_POST_PAY:
          value: 'approve-post-pay'
          display: '订单后付费申请'
          # coffeelint: disable=max_line_length
          link: 'layout.OrderPostPayApprovalDetail({id: process.excode, uid: process.uid, process_instance_id: process.process_instance_id})'
        APPROVAE_ORDER_CHANGE_PRICE_AND_POST_PAY:
          value: 'approve-change-price-and-post-pay'
          display: '订单改价并后付费申请'
          # coffeelint: disable=max_line_length
          link: 'layout.OrderChangePriceAndPostPayApprovalDetail({id: process.excode, uid: process.uid, process_instance_id: process.process_instance_id})'
        APPROVAE_ORDER_CHANGE_EFFECT_TIME:
          value: 'approve-change-effect-time'
          display: '资源包改开始时间申请'
          # coffeelint: disable=max_line_length
          link: 'layout.OrderChangeEffectTimeApprovalDetail({id: process.excode, uid: process.uid, process_instance_id: process.process_instance_id})'
        APPROVAE_ORDER_CHANGE_END_TIME:
          value: 'approve-change-end-time'
          display: '资源包延期申请'
          # coffeelint: disable=max_line_length
          link: 'layout.OrderChangeEndTimeApprovalDetail({id: process.excode, uid: process.uid, process_instance_id: process.process_instance_id})'
        APPROVAE_ORDER_REFUND:
          value: 'approve-refund'
          display: '订单退款'
          # coffeelint: disable=max_line_length
          link: 'layout.OrderRefundApprovalDetail({id: process.excode, uid: process.uid, process_instance_id: process.process_instance_id})'
        APPROVE_ORDER:
          value: 'approve-order'
          display: '预下单申请'
          link: 'layout.OrderApprovalDetail({id: process.excode, uid: process.uid})'
        CHANGE_PRICE:
          value: 'change-price'
          display: '改价申请'
          link: 'layout.PricingApprovalDetail({id: process.excode, uid: process.uid})'
        APPLY_REGEN:
          value: 'apply-regen'
          display: '改价重出账申请'
          link: 'layout.ApplyRegenDetail({id: process.excode, uid: process.uid})'
        APPLY_REDUCE:
          value: 'apply-reduce'
          display: '减免重出账申请'
          link: 'layout.ReduceApprovalDetail({id: process.excode, uid: process.uid})'
        APPLY_TESTING:
          value: 'apply-testing'
          display: '测试申请'
          link: 'layout.TestingApprovalDetail({id: process.excode, uid: process.uid})'
        APPLY_CONSTRACT:
          value: 'apply-contract'
          display: '合同申请'
          link: 'layout.ContractApprovalDetail({id: process.excode, uid: process.uid})'
        APPLY_CREDIT:
          value: 'apply-credit'
          display: '授信申请'
          link: 'layout.CreditApprovalDetail({id: process.excode, uid: process.uid})'
        APPLY_COUPON:
          value: 'apply-coupon'
          display: '包年包月（订单）抵用券申请'
          link: 'layout.CouponApprovalDetail({id: process.excode})'
        APPLY_VOUCHER:
          value: 'apply-voucher'
          display: '按量计费抵用券申请'
          link: 'layout.VoucherApprovalDetail({id: process.excode})'
        APPLY_IAM:
          value: 'apply-iam'
          display: 'IAM 申请'
          link: 'layout.IAMApprovalDetail({id: process.excode})'
        APPLY_FINANCE_RELATION:
          value: 'apply-finance-relation'
          display: '财务父子账号绑定申请'
          # coffeelint: disable=max_line_length
          link: 'layout.FinanceRelationApprovalDetail({id: process.excode, process_instance_id: process.process_instance_id})'
        APPLY_PORTALIO_ROLE:
          value: 'apply-portalio-role'
          display: 'PortalIO 权限申请'
          link: 'layout.PortalIoRoleDetail({id: process.excode})'
        QVM_PRICING:
          value: 'qvm-pricing'
          display: 'QVM报价审批'
          link: 'layout.QvmPricingApprovalDetail({id: process.excode})'
        APPLY_SALES_GUARANTEE:
          value: 'apply-sales-guarantee'
          display: '大客户认证申请'
          link: 'layout.SalesGuaranteeDetail({id: process.excode})'
        APPLY_IDENTITY_CHANGE:
          value: 'apply-identity-change'
          display: '认证变更申请'
          link: 'layout.IdentityChangeDetail({id: process.excode, uid: process.uid})'
        APPROVAL_WITHDRAW:
          value: 'apply-withdraw'
          display: '提现申请'
          link: 'layout.ApplyWithdrawDetail({id: process.excode, uid: process.uid})'
        INVOICE_APPLY:
          value: 'vat-invoice-setting'
          display: '开票信息审批'
          link: 'layout.InvoiceApplyDetail({id: process.excode, uid: process.uid})'
        UNFREEZE_APPLY:
          value: 'apply-unfreeze'
          display: '解除欠费保护期/冻结申请'
          link: 'layout.UnfreezeDetail({id: process.excode, uid: process.uid})'
        EXTEND_BUFFER_APPLY:
          value: 'apply-extend-buffer'
          display: '延长临时欠费保护期申请'
          link: 'layout.ExtendBufferDetail({id: process.excode, uid: process.uid})'
        APPLY_NB:
          value: 'apply-nb'
          display: '充值赠送金申请'
          link: 'layout.NbApplyDetail({id: process.excode, uid: process.uid})'
        APPLY_REGEN_CUSTOMBILL:
          value: 'apply-regen-synced-custombill'
          display: 'LCDN/LAS重出账申请'
          link: 'layout.RegenSyncedCustombillDetail({id: process.excode, uid: process.uid})'
        APPLY_BALANCE_TRANSFER:
          value: 'apply-balance-transfer'
          display: '余额转移申请'
          link: 'layout.BalanceTransferDetail({id: process.excode, uid: process.uid})'
        APPLY_ORG_MEMBER:
          value: 'apply-org-member'
          display: '添加组织成员账号申请'
          link: 'layout.OrgMemberDetail({id: process.excode, uid: process.uid})'
        APPLY_CUSTOMBILL:
          value: 'apply-custom-bill'
          display: '自定义账单申请'
          link: 'layout.CustomBillApprovalDetail({id: process.excode, uid: process.uid})'

  _processBizType = {}

  toMap: ->
    if _.keys(_processBizType).length == 0
      _.values(@processBizType).forEach((e) -> _processBizType[e.value] = e)
    _processBizType

  display: (value) ->
    bizType = @toMap()[value]
    if bizType
      return bizType.display
    return '未知业务类型'

  link: (value) ->
    bizType = @toMap()[value]
    if bizType
      return bizType.link
    return 'unknown'


angular.module 'app.constant'
  .constant 'PROCESS_BIZ_TYPE', new ProcessBizType()
