(->
  class CounterSignType
    constructor: ->
      @SIGN_TYPE_UNKNOWN = 0
      @SIGN_TYPE_BEFORE = 1
      @SIGN_TYPE_AFTER = 2
      @SIGN_TYPE_AND = 3

    display: (type) ->
      switch type
        when @SIGN_TYPE_BEFORE then return '前加签'
        when @SIGN_TYPE_AFTER then return '后加签'
        when @SIGN_TYPE_AND then return '并加签'
        else return "未知类型(#{type})"
    
    hint: (type) ->
      switch type
        when @SIGN_TYPE_BEFORE then return '前加签 - 在当前节点之前增加一个额外审批节点，同时挂起当前节点审批，待增加的节点操作完成后将恢复当前审批操作'
        when @SIGN_TYPE_AFTER then return '后加签-请注意后加签默认视作通过当前审批，并且在当前节点后增加一个额外审批节点，审批流程将向后流转至新增节点'
        when @SIGN_TYPE_AND then return '并加签 - 在当前审批节点上增加若干个同级审批人一同参与审批，所有审批人需全部审批通过流程才会继续往下流转，否则退回流程起点'
        else return ''

  angular.module 'app.constant'
    .constant 'counterSignType', new CounterSignType()
    .constant 'COUNTER_SIGN_TYPE_ENUM', {
      SIGN_TYPE_UNKNOWN: 0
      SIGN_TYPE_BEFORE: 1
      SIGN_TYPE_AFTER: 2
      SIGN_TYPE_AND: 3
    }
    .factory 'COUNTER_SIGN_TYPE_LIST', (counterSignType, COUNTER_SIGN_TYPE_ENUM) ->
      group = [{
        text: counterSignType.display(COUNTER_SIGN_TYPE_ENUM.SIGN_TYPE_BEFORE)
        value: COUNTER_SIGN_TYPE_ENUM.SIGN_TYPE_BEFORE
      }, {
        text: counterSignType.display(COUNTER_SIGN_TYPE_ENUM.SIGN_TYPE_AFTER)
        value: COUNTER_SIGN_TYPE_ENUM.SIGN_TYPE_AFTER
      }, {
        text: counterSignType.display(COUNTER_SIGN_TYPE_ENUM.SIGN_TYPE_AND)
        value: COUNTER_SIGN_TYPE_ENUM.SIGN_TYPE_AND
      }]
      return group
)()
