(->
  angular.module 'app.constant'
    .constant 'HANDLE_EXECUTION_ACTION_TYPE', {
      EXECUTION_UNKNOWN: 0
      EXECUTION_REFUSE: 1
      EXECUTION_PASS: 2
      EXECUTION_COUNTER_SIGN: 3
    }
    .factory 'HANDLE_EXECUTION_ACTION_TYPE_MAP', (HANDLE_EXECUTION_ACTION_TYPE) ->
      @[HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_UNKNOWN] = '未知'
      @[HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_REFUSE] = '驳回'
      @[HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_PASS] = '通过'
      @[HANDLE_EXECUTION_ACTION_TYPE.EXECUTION_COUNTER_SIGN] = '加签'
      return @
    .constant 'PROCESS_SUSPEND', {
      CONTINUE: 1
      SUSPEND: 2
    }
)()
