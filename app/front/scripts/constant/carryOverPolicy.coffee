angular.module 'app.constant'
.constant 'monthlyPayType', {
  # coffeelint: disable=max_line_length
  minimum: { name: '按月分配不可结转', tooltip: '所购买的容量规格为有效期内每月下发的量，当月未用完的量月底清零。如：购买资源包A 100GB，1年有效。开始生效时，会每月下发 100GB，共发 12 个月。当月未用完的量，月底清零，次月继续下发 100GB。' },
  carryOver: { name: '按月分配可结转', tooltip: '所购买的容量规格为有效期内每月下发的量，当月未用完的量可结转到次月。如：购买资源包A100GB，1年有效。开始生效时，会每月下发100GB+上个月未用完的量，共发12个月。' },
  lifetime: { name: '一次性分配', tooltip: '所购买的容量规格为有效期内的可用总量。如：购买资源包A100GB，1年有效。开始生效时，会下发100GB，1年内均可使用，过期后未用完的作废。' }
}