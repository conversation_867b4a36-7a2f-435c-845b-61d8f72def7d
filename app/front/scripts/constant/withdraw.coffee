angular.module 'app.constant'
.constant 'WithdrawReasonList', [
  {
    value: 1,
    display: '项目结束'
  }
  {
    value: 2,
    display: '测试结束'
  }
  {
    value: 3,
    display: '充值过多'
  }
  {
    value: 4,
    display: '充错账号'
  }
  {
    value: 5,
    display: '业务调整'
  }
  {
    value: 6,
    display: '产品使用问题'
  }
  {
    value: 7,
    display: '订单退款'
  }
  {
    value: 8,
    display: '迁移至其他厂商'
  }
  {
    value: 9,
    display: '企业倒闭'
  }
  {
    value: 10,
    display: '更换账号'
  }
  {
    value: 99,
    display: '其他'
  }
]
.constant 'HumanizeWithdrawType', {
  'offline_withdraw': '线下提现'
  'original_withdraw': '原路提现'
}

.constant 'WITHDRAWTYPE', {
  offlineWithdraw: 'offline_withdraw',
  originalWithdraw: 'original_withdraw'
}

.constant 'humanizeDepositType', {
  alipay: '支付宝充值',
  wxpay: '微信充值',
  ebank: '网上银行充值',
  BANK: '银行充值',
  recharge_renewal: '银行充值',
  refund_po: '订单退款',
  refund_po_failed: '订单发货失败自动退款',
  revise: '账单退款',
  revise_custombill: '自定义账单退款',
  transferaccin: '转账转入',
  PRESENT: '充值赠送'
}

.constant 'PROPOSER_TYPE', {
  customer: '客户',
  sales: '销售'
}

.constant 'WithdrawMethod', {
  alipay: '支付宝',
  wxpay: '微信',
  BANK: '银行转账',
}
