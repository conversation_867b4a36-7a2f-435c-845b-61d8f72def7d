(->
  class CreditStaffScale
    constructor: ->
      @STAFF_SCALE_LT_10 = 1
      @STAFF_SCALE_10_TO_100 = 2
      @STAFF_SCALE_100_TO_300 = 3
      @STAFF_SCALE_GT_300 = 4

    humanize: (x) ->
      switch x
        when @STAFF_SCALE_LT_10 then return '小于 10 人'
        when @STAFF_SCALE_10_TO_100 then return '10 - 100 人'
        when @STAFF_SCALE_100_TO_300 then return '100 - 300 人'
        when @STAFF_SCALE_GT_300 then return '大于 300 人'
        else return "未知(#{x})"

    isLT10: (x) ->
      return @STAFF_SCALE_LT_10 == x

    is10TO100: (x) ->
      return @STAFF_SCALE_10_TO_100 == x

    is100TO300: (x) ->
      return @STAFF_SCALE_100_TO_300 == x

    isGT300: (x) ->
      return @STAFF_SCALE_GT_300 == x

  angular.module 'app.constant'
    .constant 'creditStaffScale', new CreditStaffScale()
    .constant 'CREDIT_STAFF_SCALE', {
      STAFF_SCALE_LT_10: 1,
      STAFF_SCALE_10_TO_100: 2,
      STAFF_SCALE_100_TO_300: 3,
      STAFF_SCALE_GT_300: 4
    }
    .factory 'CREDIT_STAFF_SCALE_MAP', (CREDIT_STAFF_SCALE) ->
      @[CREDIT_STAFF_SCALE.STAFF_SCALE_LT_10] = '小于 10 人'
      @[CREDIT_STAFF_SCALE.STAFF_SCALE_10_TO_100] = '10 - 100 人'
      @[CREDIT_STAFF_SCALE.STAFF_SCALE_100_TO_300] = '100 - 300 人'
      @[CREDIT_STAFF_SCALE.STAFF_SCALE_GT_300] = '大于 300 人'

      return @
)()
