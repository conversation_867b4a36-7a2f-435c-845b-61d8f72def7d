import {getRestActionsResponse} from 'mock/restActions.js';
import {getUsersResponse} from 'mock/users.js';
import {getProcessDefinitionsResponse, postProcessDefinitionsResponse} from 'mock/processDefinitions.js';
import {getLabelsResponse} from 'mock/labels.js';

export default {
    ...getRestActionsResponse,
    ...getUsersResponse,
    ...getProcessDefinitionsResponse,
    ...postProcessDefinitionsResponse,
    ...getLabelsResponse
}