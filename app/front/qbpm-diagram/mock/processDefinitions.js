import Mock from 'mockjs';

function getXML(name, version) {
    return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:qbpm="https://qiniu.com" id="Definitions_Process_1665655336726" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_${version}" name="${name}" qbpm:version="${version}">
    <bpmn:startEvent id="Event_18p96qk">
      <bpmn:outgoing>Flow_01ivkzk</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_01ivkzk" sourceRef="Event_18p96qk" targetRef="Activity_0u1vzc1" />
    <bpmn:endEvent id="Event_14gsbvr">
      <bpmn:extensionElements>
        <qbpm:listener fire="begin" actionRefs="assignWechatNotify">
          <qbpm:param name="msg" value="看看效果😋" />
        </qbpm:listener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_05yjrnz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_05yjrnz" sourceRef="Activity_0u1vzc1" targetRef="Event_14gsbvr" />
    <bpmn:userTask id="Activity_0u1vzc1" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="static" qbpm:assignee="<EMAIL>" name="这是${name}">
      <bpmn:incoming>Flow_01ivkzk</bpmn:incoming>
      <bpmn:outgoing>Flow_05yjrnz</bpmn:outgoing>
    </bpmn:userTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_${version}">
      <bpmndi:BPMNShape id="Event_18p96qk_di" bpmnElement="Event_18p96qk">
        <dc:Bounds x="192" y="62" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14gsbvr_di" bpmnElement="Event_14gsbvr">
        <dc:Bounds x="192" y="262" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h90rx7_di" bpmnElement="Activity_0u1vzc1">
        <dc:Bounds x="160" y="130" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_01ivkzk_di" bpmnElement="Flow_01ivkzk">
        <di:waypoint x="210" y="98" />
        <di:waypoint x="210" y="130" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05yjrnz_di" bpmnElement="Flow_05yjrnz">
        <di:waypoint x="210" y="210" />
        <di:waypoint x="210" y="262" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`
}

const processes = [
    {
        id: 1029645091486101504,
        created_at: '1919-08-10T00:00:00',
        updated_at: '1919-08-10T00:00:00',
        key: '田所浩二审批流',
        version: 114514,
        name: '账单抵用券审批流',
        status: 1,
        description: '就是啊,九十啊，旧时啊，救市啊',
        deploy_at: '2022-01-01T00:00:00',
        xml_data: getXML('账单抵用券审批流', 114514),
        process_model: {
            process: {}
        }
    },
    {
        id: 1029645091486101505,
        created_at: '1919-08-10T00:00:00',
        updated_at: '1919-08-10T00:00:00',
        key: '田所浩二审批流',
        version: 114515,
        name: '账单抵用券审批流(1)',
        status: 1,
        description: '压力马斯涅',
        deploy_at: '2022-01-02T00:00:00',
        xml_data: getXML('账单抵用券审批流', 114515),
        process_model: {
            process: {}
        }
    },
    {
        id: 1029645091486101506,
        created_at: '1919-08-10T00:00:00',
        updated_at: '1919-08-10T00:00:00',
        key: '订单抵用券审批流',
        version: 114514,
        name: '订单抵用券审批流',
        status: 1,
        description: '想啊，很想啊',
        deploy_at: '2022-01-01T00:00:00',
        xml_data: getXML('订单抵用券审批流', 114514),
        process_model: {
            process: {}
        }
    },
    {
        id: 1029645091486101507,
        created_at: '1919-08-10T00:00:00',
        updated_at: '1919-08-10T00:00:00',
        key: '合同审批流',
        version: 114514,
        name: '合同审批流',
        status: 1,
        description: '三回啊三回',
        deploy_at: '2022-01-01T00:00:00',
        xml_data: getXML('合同审批流', 114514),
        process_model: {
            process: {}
        }
    },
    {
        id: 1029645091486101508,
        created_at: '1919-08-10T00:00:00',
        updated_at: '1919-08-10T00:00:00',
        key: '订单抵用券审批流',
        version: 114516,
        name: '订单抵用券审批流(114514)',
        status: 1,
        description: '戳啦',
        deploy_at: '2022-01-02T00:00:00',
        xml_data: getXML('订单抵用券审批流', 114516),
        process_model: {
            process: {}
        }
    }
]

export const postProcessDefinitionsResponse = Mock.mock('/api/proxy/qbpm/api/repository/process-definitions', 'post', {"code": 200})

export const getProcessDefinitionsResponse = Mock.mock(RegExp('/api/proxy/qbpm/api/repository/process-definitions/name/search.*'), 'get',
    {process_definitions: processes, count: 5})