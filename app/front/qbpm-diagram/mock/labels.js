import Mock from 'mockjs';

const labels = [
    {
        "id": "6351dc36a5802a5f3a2883cc",
        "name": "测试标签",
        "created_at": "0001-01-01T00:00:00Z",
        "updated_at": "2022-10-21T04:06:00.449Z"
    },
    {
        "id": "6351dc36a5802a5f3a2883ca",
        "name": "嘉然标签",
        "created_at": "0001-01-01T00:00:00Z",
        "updated_at": "2022-10-21T04:06:00.449Z"
    },
    {
        "id": "6351dc36a5802a5f3a2883ce",
        "name": "乃琳标签",
        "created_at": "0001-01-01T00:00:00Z",
        "updated_at": "2022-10-21T04:06:00.449Z"
    }
]

export const getLabelsResponse = Mock.mock('/api/proxy/gaea/api/admin/labels',
    'get',
    {
        "code": 200,
        "data": labels
    })