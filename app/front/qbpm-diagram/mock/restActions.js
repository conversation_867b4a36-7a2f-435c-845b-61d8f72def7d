import Mock from 'mockjs';

const restActions = [
    {
        id: 1,
        code: "restToGetStarter",
        name: "获取申请人信息"
    },
    {
        id: 2,
        code: "restToGetStarterTopManager",
        name: "获取申请人头部上司信息"
    },
    {
        id: 3,
        code: "getLastThreeYearRate",
        name: "获取近三年综合优惠率",
        params: ["productName", "zone"]
    },
    {
        id: 4,
        code: "getLastYearRate",
        name: "获取近一年综合优惠率",
        params: ["productName", "zone"]
    },
    {
        id: 5,
        code: "assignWechatNotify",
        name: "企业微信通知",
        params: ["msg"]
    },
    {
        id: 6,
        code: "assignNotify",
        name: "邮件通知",
        params: ["subject", "content"]
    },
    {
        id: 7,
        code: "restToUpdateSofa",
        name: "更新至 sofa"
    },
    {
        id: 8,
        code: "createVoucher",
        name: "创建账单抵用券"
    }
]

export const getRestActionsResponse = Mock.mock(
    '/api/proxy/qbpm/api/system/actions', 'get', restActions
)
