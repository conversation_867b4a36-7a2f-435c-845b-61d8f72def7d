# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ant-design/colors@^6.0.0":
  version "6.0.0"
  resolved "https://registry.npmjs.org/@ant-design/colors/-/colors-6.0.0.tgz"
  integrity sha512-qAZRvPzfdWHtfameEGP2Qvuf838NhergR35o+EuVyB5XvSA98xod5r4utvi4TJ3ywmevm290g9nsCG5MryrdWQ==
  dependencies:
    "@ctrl/tinycolor" "^3.4.0"

"@ant-design/icons-svg@^4.2.1":
  version "4.2.1"
  resolved "https://registry.npmjs.org/@ant-design/icons-svg/-/icons-svg-4.2.1.tgz"
  integrity sha512-EB0iwlKDGpG93hW8f85CTJTs4SvMX7tt5ceupvhALp1IF44SeUFOMhKUOYqpsoYWQKAOuTRDMqn75rEaKDp0Xw==

"@ant-design/icons-vue@^6.1.0":
  version "6.1.0"
  resolved "https://registry.npmjs.org/@ant-design/icons-vue/-/icons-vue-6.1.0.tgz"
  integrity sha512-EX6bYm56V+ZrKN7+3MT/ubDkvJ5rK/O2t380WFRflDcVFgsvl3NLH7Wxeau6R8DbrO5jWR6DSTC3B6gYFp77AA==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.2.1"

"@antfu/install-pkg@^0.1.0", "@antfu/install-pkg@^0.1.1":
  version "0.1.1"
  resolved "https://registry.npmjs.org/@antfu/install-pkg/-/install-pkg-0.1.1.tgz"
  integrity sha512-LyB/8+bSfa0DFGC06zpCEfs89/XoWZwws5ygEa5D+Xsm3OfI+aXQ86VgVG7Acyef+rSZ5HE7J8rrxzrQeM3PjQ==
  dependencies:
    execa "^5.1.1"
    find-up "^5.0.0"

"@antfu/utils@^0.5.2":
  version "0.5.2"
  resolved "https://registry.npmjs.org/@antfu/utils/-/utils-0.5.2.tgz"
  integrity sha512-CQkeV+oJxUazwjlHD0/3ZD08QWKuGQkhnrKo3e6ly5pd48VUpXbb77q0xMU4+vc2CkJnDS02Eq/M9ugyX20XZA==

"@babel/parser@^7.16.4":
  version "7.19.3"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.19.3.tgz"
  integrity sha512-pJ9xOlNWHiy9+FuFP09DEAFbAn4JskgRsVcc169w2xRBC3FRGuQEwjeIMMND9L2zc0iEhO/tGv4Zq+km+hxNpQ==

"@babel/runtime@^7.10.5":
  version "7.19.0"
  resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.19.0.tgz"
  integrity sha512-eR8Lo9hnDS7tqkO7NsV+mKvCmv5boaXFSZ70DnfhcgiEne8hv9oCEd36Klw74EtizEqLsy4YnW8UWwpBVolHZA==
  dependencies:
    regenerator-runtime "^0.13.4"

"@bpmn-io/element-template-chooser@^0.1.0":
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/@bpmn-io/element-template-chooser/-/element-template-chooser-0.1.0.tgz#7a9ac02b2f9746f044d86d709395644d48d07f48"
  integrity sha512-ATiOg8DNku2DY+0ENW7BPf+2CQwT4e6ORriw2/s9qRRitajOhvjBGrudk4jRLyTokpOF3rGkjtsAUTUaTQmZ+A==
  dependencies:
    clsx "^1.2.1"
    htm "^3.1.1"
    min-dash "^4.0.0"
    min-dom "^4.0.2"
    preact "^10.11.0"
    tiny-svg "^3.0.0"

"@bpmn-io/element-templates-validator@^0.11.0":
  version "0.11.0"
  resolved "https://registry.yarnpkg.com/@bpmn-io/element-templates-validator/-/element-templates-validator-0.11.0.tgz#3ca7d9ad2dcc39fd2e51ea5d6b78ad4bea496fff"
  integrity sha512-4eZCPLuWf1N4lL8jIKZjWgwLJ2IUTgkQ4VDnfbDiSvjGJqHaLA4XBcC5smvb8Q/MqsJFxWZumolJJb1h7gt39Q==
  dependencies:
    "@camunda/element-templates-json-schema" "^0.10.1"
    "@camunda/zeebe-element-templates-json-schema" "^0.6.0"
    json-source-map "^0.6.1"
    min-dash "^4.0.0"

"@bpmn-io/extract-process-variables@^0.6.0":
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/@bpmn-io/extract-process-variables/-/extract-process-variables-0.6.0.tgz#58391a4ae42fa7a9e3a50e3c2a4ae665847e1819"
  integrity sha512-vq4jwGXDO11jwQgj9lvpVxVxjnRAz4C4TqPnhromcsllH5iRBrUNtBKgK0c/RWxiEGNTBhTYm19sP+LN1UcLWA==
  dependencies:
    min-dash "^4.0.0"

"@bpmn-io/feel-editor@0.4.1":
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/@bpmn-io/feel-editor/-/feel-editor-0.4.1.tgz#3e3eaaaa45688ca60d6b770e5a4d206f6e5a99d5"
  integrity sha512-+UGpofI09xGxs1Rr/1V3NLeNSfeKrIGcWvwDY5M3xb4tP6nOQfwqmQA1761Wni9fl3RuLzf6gOx7vGWeQ7afIA==
  dependencies:
    "@codemirror/autocomplete" "^6.1.1"
    "@codemirror/commands" "^6.0.0"
    "@codemirror/language" "^6.0.0"
    "@codemirror/lint" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    "@lezer/highlight" "^1.0.0"
    lang-feel "^0.0.3"
    lezer-feel "^0.14.1"
    min-dom "^4.0.1"

"@bpmn-io/properties-panel@^0.23.0":
  version "0.23.0"
  resolved "https://registry.yarnpkg.com/@bpmn-io/properties-panel/-/properties-panel-0.23.0.tgz#6724310ec95b22afc44db6495033f3676c4041d2"
  integrity sha512-K/KHAf/XEhTPEeVmMdj9j6Al8XLz0eVdTBfKbyvAgSCbq4GVrEU/ylJVRyZo4KGRhj4O4AUo1zaal8pyhaAxdg==
  dependencies:
    "@bpmn-io/feel-editor" "0.4.1"
    classnames "^2.3.1"
    min-dash "^4.0.0"
    min-dom "^4.0.3"

"@camunda/element-templates-json-schema@^0.10.1":
  version "0.10.1"
  resolved "https://registry.yarnpkg.com/@camunda/element-templates-json-schema/-/element-templates-json-schema-0.10.1.tgz#2d27f402f170e730c90b98a938c64b87519097d4"
  integrity sha512-sw8RNecjQgY7tX26PMLRJGNX/2QCnlwCvZfxQWh606qlJZsLbpvEbvfgIGCRoYlHYTlsP6PxVcWYx5LPo7yisg==

"@camunda/zeebe-element-templates-json-schema@^0.6.0":
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/@camunda/zeebe-element-templates-json-schema/-/zeebe-element-templates-json-schema-0.6.0.tgz#3eded2b64fff6b40372bfa6aef7f684460ff7cfc"
  integrity sha512-qawIFM52lp1hW2vWrHaX8ywguZsp2olE0DRTHUY+KWH5GwszZwGWECP3tji1KVih2TasQyf28kcQVh8TeQ6dAg==

"@codemirror/autocomplete@^6.0.0", "@codemirror/autocomplete@^6.1.1":
  version "6.3.0"
  resolved "https://registry.yarnpkg.com/@codemirror/autocomplete/-/autocomplete-6.3.0.tgz#217e16bb6ce63374ec7b9d2a01d007ba53ff0aff"
  integrity sha512-4jEvh3AjJZTDKazd10J6ZsCIqaYxDMCeua5ouQxY8hlFIml+nr7le0SgBhT3SIytFBmdzPK3AUhXGuW3T79nVg==
  dependencies:
    "@codemirror/language" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    "@lezer/common" "^1.0.0"

"@codemirror/commands@^6.0.0":
  version "6.1.1"
  resolved "https://registry.yarnpkg.com/@codemirror/commands/-/commands-6.1.1.tgz#f92a343f53f4ecff10fc1f4114d0c9e49e7715b7"
  integrity sha512-ibDohwkk7vyu3VsnZNlQhwk0OETBtlkYV+6AHfn5Zgq0sxa+yGVX+apwtC3M4wh6AH7yU5si/NysoECs5EGS3Q==
  dependencies:
    "@codemirror/language" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    "@lezer/common" "^1.0.0"

"@codemirror/language@^6.0.0":
  version "6.2.1"
  resolved "https://registry.yarnpkg.com/@codemirror/language/-/language-6.2.1.tgz#cb10cd785a76e50ecd2fe2dc59ff66af8a41b87a"
  integrity sha512-MC3svxuvIj0MRpFlGHxLS6vPyIdbTr2KKPEW46kCoCXw2ktb4NTkpkPBI/lSP/FoNXLCBJ0mrnUi1OoZxtpW1Q==
  dependencies:
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    "@lezer/common" "^1.0.0"
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"
    style-mod "^4.0.0"

"@codemirror/lint@^6.0.0":
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/@codemirror/lint/-/lint-6.0.0.tgz#a249b021ac9933b94fe312d994d220f0ef11a157"
  integrity sha512-nUUXcJW1Xp54kNs+a1ToPLK8MadO0rMTnJB8Zk4Z8gBdrN0kqV7uvUraU/T2yqg+grDNR38Vmy/MrhQN/RgwiA==
  dependencies:
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    crelt "^1.0.5"

"@codemirror/state@^6.0.0":
  version "6.1.2"
  resolved "https://registry.yarnpkg.com/@codemirror/state/-/state-6.1.2.tgz#182d46eabcc17c95508984d6add5a5a641dcd517"
  integrity sha512-Mxff85Hp5va+zuj+H748KbubXjrinX/k28lj43H14T2D0+4kuvEFIEIO7hCEcvBT8ubZyIelt9yGOjj2MWOEQA==

"@codemirror/view@^6.0.0":
  version "6.3.0"
  resolved "https://registry.yarnpkg.com/@codemirror/view/-/view-6.3.0.tgz#715c97d64e7e20c9674782e7101ab7aefef8e23d"
  integrity sha512-jMN9OGKmzRPJ+kksfMrB5e/A9heQncirHsz8XNBpgEbYONCk5tWHMKVWKTNwznkUGD5mnigXI1i5YIcWpscSPg==
  dependencies:
    "@codemirror/state" "^6.0.0"
    style-mod "^4.0.0"
    w3c-keyname "^2.2.4"

"@ctrl/tinycolor@^3.4.0":
  version "3.4.1"
  resolved "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.4.1.tgz"
  integrity sha512-ej5oVy6lykXsvieQtqZxCOaLT+xD4+QNarq78cIYISHmZXshCvROLudpQN3lfL8G0NL7plMSSK+zlyvCaIJ4Iw==

"@esbuild/android-arm@0.15.10":
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/@esbuild/android-arm/-/android-arm-0.15.10.tgz#a5f9432eb221afc243c321058ef25fe899886892"
  integrity sha512-FNONeQPy/ox+5NBkcSbYJxoXj9GWu8gVGJTVmUyoOCKQFDTrHVKgNSzChdNt0I8Aj/iKcsDf2r9BFwv+FSNUXg==

"@esbuild/linux-loong64@0.15.10":
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/@esbuild/linux-loong64/-/linux-loong64-0.15.10.tgz#78a42897c2cf8db9fd5f1811f7590393b77774c7"
  integrity sha512-w0Ou3Z83LOYEkwaui2M8VwIp+nLi/NA60lBLMvaJ+vXVMcsARYdEzLNE7RSm4+lSg4zq4d7fAVuzk7PNQ5JFgg==

"@highlightjs/vue-plugin@^2.1.0":
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/@highlightjs/vue-plugin/-/vue-plugin-2.1.0.tgz#b7c41e3597a46975665b10cad57882cbde1d1594"
  integrity sha512-E+bmk4ncca+hBEYRV2a+1aIzIV0VSY/e5ArjpuSN9IO7wBJrzUE2u4ESCwrbQD7sAy+jWQjkV5qCCWgc+pu7CQ==

"@iconify/types@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@iconify/types/-/types-2.0.0.tgz"
  integrity sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==

"@iconify/utils@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@iconify/utils/-/utils-2.0.0.tgz"
  integrity sha512-thvwZ6m3frWJiOYwRdHPSPGC25rmyvDl0vXdQ8ocRJZx31m8Pn4y7V1mTc5UReR6MiAj+hrV2UPZQjx7zI960g==
  dependencies:
    "@antfu/install-pkg" "^0.1.0"
    "@antfu/utils" "^0.5.2"
    "@iconify/types" "^2.0.0"
    debug "^4.3.4"
    kolorist "^1.5.1"
    local-pkg "^0.4.2"

"@lezer/common@^1.0.0":
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/@lezer/common/-/common-1.0.1.tgz#d014fda6d582c24336fadf2715e76f02f73c8908"
  integrity sha512-8TR5++Q/F//tpDsLd5zkrvEX5xxeemafEaek7mUp7Y+bI8cKQXdSqhzTOBaOogETcMOVr0pT3BBPXp13477ciw==

"@lezer/highlight@^1.0.0":
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/@lezer/highlight/-/highlight-1.1.1.tgz#1f4f12f21320d7de102b46a5416eff49fdf3c695"
  integrity sha512-duv9D23O9ghEDnnUDmxu+L8pJy4nYo4AbCOHIudUhscrLSazqeJeK1V50EU6ZufWF1zv0KJwu/frFRyZWXxHBQ==
  dependencies:
    "@lezer/common" "^1.0.0"

"@lezer/lr@^1.0.0", "@lezer/lr@^1.2.3":
  version "1.2.3"
  resolved "https://registry.yarnpkg.com/@lezer/lr/-/lr-1.2.3.tgz#f44ca844f15f6762fde4eab877d110567e34ffa1"
  integrity sha512-qpB7rBzH8f6Mzjv2AVZRahcm+2Cf7nbIH++uXbvVOL1yIRvVWQ3HAM/saeBLCyz/togB7LGo76qdJYL1uKQlqA==
  dependencies:
    "@lezer/common" "^1.0.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@rollup/pluginutils@^4.2.1":
  version "4.2.1"
  resolved "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-4.2.1.tgz"
  integrity sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==
  dependencies:
    estree-walker "^2.0.1"
    picomatch "^2.2.2"

"@simonwep/pickr@~1.8.0":
  version "1.8.2"
  resolved "https://registry.npmjs.org/@simonwep/pickr/-/pickr-1.8.2.tgz"
  integrity sha512-/l5w8BIkrpP6n1xsetx9MWPWlU6OblN5YgZZphxan0Tq4BByTCETL6lyIeY8lagalS2Nbt4F2W034KHLIiunKA==
  dependencies:
    core-js "^3.15.1"
    nanopop "^2.1.0"

"@vitejs/plugin-vue@^3.1.0":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@vitejs/plugin-vue/-/plugin-vue-3.1.0.tgz"
  integrity sha512-fmxtHPjSOEIRg6vHYDaem+97iwCUg/uSIaTzp98lhELt2ISOQuDo2hbkBdXod0g15IhfPMQmAxh4heUks2zvDA==

"@vue/compiler-core@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.2.40.tgz"
  integrity sha512-2Dc3Stk0J/VyQ4OUr2yEC53kU28614lZS+bnrCbFSAIftBJ40g/2yQzf4mPBiFuqguMB7hyHaujdgZAQ67kZYA==
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/shared" "3.2.40"
    estree-walker "^2.0.2"
    source-map "^0.6.1"

"@vue/compiler-dom@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.2.40.tgz"
  integrity sha512-OZCNyYVC2LQJy4H7h0o28rtk+4v+HMQygRTpmibGoG9wZyomQiS5otU7qo3Wlq5UfHDw2RFwxb9BJgKjVpjrQw==
  dependencies:
    "@vue/compiler-core" "3.2.40"
    "@vue/shared" "3.2.40"

"@vue/compiler-sfc@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.2.40.tgz"
  integrity sha512-tzqwniIN1fu1PDHC3CpqY/dPCfN/RN1thpBC+g69kJcrl7mbGiHKNwbA6kJ3XKKy8R6JLKqcpVugqN4HkeBFFg==
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.40"
    "@vue/compiler-dom" "3.2.40"
    "@vue/compiler-ssr" "3.2.40"
    "@vue/reactivity-transform" "3.2.40"
    "@vue/shared" "3.2.40"
    estree-walker "^2.0.2"
    magic-string "^0.25.7"
    postcss "^8.1.10"
    source-map "^0.6.1"

"@vue/compiler-ssr@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.2.40.tgz"
  integrity sha512-80cQcgasKjrPPuKcxwuCx7feq+wC6oFl5YaKSee9pV3DNq+6fmCVwEEC3vvkf/E2aI76rIJSOYHsWSEIxK74oQ==
  dependencies:
    "@vue/compiler-dom" "3.2.40"
    "@vue/shared" "3.2.40"

"@vue/devtools-api@^6.2.1":
  version "6.4.2"
  resolved "https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-6.4.2.tgz"
  integrity sha512-6hNZ23h1M2Llky+SIAmVhL7s6BjLtZBCzjIz9iRSBUsysjE7kC39ulW0dH4o/eZtycmSt4qEr6RDVGTIuWu+ow==

"@vue/reactivity-transform@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/reactivity-transform/-/reactivity-transform-3.2.40.tgz"
  integrity sha512-HQUCVwEaacq6fGEsg2NUuGKIhUveMCjOk8jGHqLXPI2w6zFoPrlQhwWEaINTv5kkZDXKEnCijAp+4gNEHG03yw==
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.40"
    "@vue/shared" "3.2.40"
    estree-walker "^2.0.2"
    magic-string "^0.25.7"

"@vue/reactivity@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.2.40.tgz"
  integrity sha512-N9qgGLlZmtUBMHF9xDT4EkD9RdXde1Xbveb+niWMXuHVWQP5BzgRmE3SFyUBBcyayG4y1lhoz+lphGRRxxK4RA==
  dependencies:
    "@vue/shared" "3.2.40"

"@vue/runtime-core@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.2.40.tgz"
  integrity sha512-U1+rWf0H8xK8aBUZhnrN97yoZfHbjgw/bGUzfgKPJl69/mXDuSg8CbdBYBn6VVQdR947vWneQBFzdhasyzMUKg==
  dependencies:
    "@vue/reactivity" "3.2.40"
    "@vue/shared" "3.2.40"

"@vue/runtime-dom@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.2.40.tgz"
  integrity sha512-AO2HMQ+0s2+MCec8hXAhxMgWhFhOPJ/CyRXnmTJ6XIOnJFLrH5Iq3TNwvVcODGR295jy77I6dWPj+wvFoSYaww==
  dependencies:
    "@vue/runtime-core" "3.2.40"
    "@vue/shared" "3.2.40"
    csstype "^2.6.8"

"@vue/server-renderer@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.2.40.tgz"
  integrity sha512-gtUcpRwrXOJPJ4qyBpU3EyxQa4EkV8I4f8VrDePcGCPe4O/hd0BPS7v9OgjIQob6Ap8VDz9G+mGTKazE45/95w==
  dependencies:
    "@vue/compiler-ssr" "3.2.40"
    "@vue/shared" "3.2.40"

"@vue/shared@3.2.40":
  version "3.2.40"
  resolved "https://registry.npmjs.org/@vue/shared/-/shared-3.2.40.tgz"
  integrity sha512-0PLQ6RUtZM0vO3teRfzGi4ltLUO5aO+kLgwh4Um3THSR03rpQWLTuRCkuO5A41ITzwdWeKdPHtSARuPkoo5pCQ==

acorn@^8.8.0:
  version "8.8.0"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.8.0.tgz"
  integrity sha512-QOxyigPVrpZ2GXT+PFyZTl6TtOFc5egxHIP9IlQ+RbupQuX4RkT/Bee4/kQuC02Xkzg84JcT7oLYtDIQxp+v7w==

ant-design-vue@^3.2.12:
  version "3.2.12"
  resolved "https://registry.npmjs.org/ant-design-vue/-/ant-design-vue-3.2.12.tgz"
  integrity sha512-CPsoWJ3t+sqq/EPINPXb4fC5/9iKkUdYOfK9M9kLKbXlRN3MAoVwWUbaFnUqc+ngtbEpn/d69hTF/Eh7MeWMhQ==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-vue" "^6.1.0"
    "@babel/runtime" "^7.10.5"
    "@ctrl/tinycolor" "^3.4.0"
    "@simonwep/pickr" "~1.8.0"
    array-tree-filter "^2.1.0"
    async-validator "^4.0.0"
    dayjs "^1.10.5"
    dom-align "^1.12.1"
    dom-scroll-into-view "^2.0.0"
    lodash "^4.17.21"
    lodash-es "^4.17.15"
    resize-observer-polyfill "^1.5.1"
    scroll-into-view-if-needed "^2.2.25"
    shallow-equal "^1.0.0"
    vue-types "^3.0.0"
    warning "^4.0.0"

anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz"
  integrity sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

array-move@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/array-move/-/array-move-3.0.1.tgz#179645cc0987b65953a4fc06b6df9045e4ba9618"
  integrity sha512-H3Of6NIn2nNU1gsVDqDnYKY/LCdWvCMMOWifNGhKcVQgiZ6nOek39aESOvro6zmueP07exSl93YLvkN4fZOkSg==

array-tree-filter@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/array-tree-filter/-/array-tree-filter-2.1.0.tgz"
  integrity sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==

async-validator@^4.0.0:
  version "4.2.5"
  resolved "https://registry.npmjs.org/async-validator/-/async-validator-4.2.5.tgz"
  integrity sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

axios@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/axios/-/axios-1.1.2.tgz#8b6f6c540abf44ab98d9904e8daf55351ca4a331"
  integrity sha512-bznQyETwElsXl2RK7HLLwb5GPpOLlycxHCtrpDR/4RqqBzjARaOTo3jz4IgtntWUYee7Ne4S8UHd92VCuzPaWA==
  dependencies:
    follow-redirects "^1.15.0"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
  integrity sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==

bpmn-js-properties-panel@^1.9.0:
  version "1.9.0"
  resolved "https://registry.yarnpkg.com/bpmn-js-properties-panel/-/bpmn-js-properties-panel-1.9.0.tgz#182fe0d7ca666f34d2894e0a24dfaf0e5f0052e9"
  integrity sha512-IIqxV2GqPJnhI4Ex91Mg+j97z8GDWgZYAVzh2/7kMBYAV3rixZ0fF37Jxaz4qjEMr3+EipqTSwu9c7ujVUQf+g==
  dependencies:
    "@bpmn-io/element-templates-validator" "^0.11.0"
    "@bpmn-io/extract-process-variables" "^0.6.0"
    array-move "^3.0.1"
    classnames "^2.3.1"
    ids "^1.0.0"
    min-dash "^4.0.0"
    min-dom "^4.0.3"
    preact-markup "^2.1.1"
    semver-compare "^1.0.0"

bpmn-js@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/bpmn-js/-/bpmn-js-10.1.0.tgz"
  integrity sha512-kQ5ep4VLteFzcWTBm+Vt2/fETBtNSCRmKOWMo0sQkOlGTsqprLxkNfGHOINSNTf81OyrTY7yyffHIZ9QPh1N0Q==
  dependencies:
    bpmn-moddle "^7.1.3"
    css.escape "^1.5.1"
    diagram-js "^9.1.0"
    diagram-js-direct-editing "^2.0.0"
    ids "^1.0.0"
    inherits-browser "^0.1.0"
    min-dash "^4.0.0"
    min-dom "^4.0.2"
    object-refs "^0.3.0"
    tiny-svg "^3.0.0"

bpmn-moddle@^7.1.3:
  version "7.1.3"
  resolved "https://registry.npmjs.org/bpmn-moddle/-/bpmn-moddle-7.1.3.tgz"
  integrity sha512-ZcBfw0NSOdYTSXFKEn7MOXHItz7VfLZTrFYKO8cK6V8ZzGjCcdiLIOiw7Lctw1PJsihhLiZQS8Htj2xKf+NwCg==
  dependencies:
    min-dash "^3.5.2"
    moddle "^5.0.2"
    moddle-xml "^9.0.6"

bpmn-moddle@^8.0.0:
  version "8.0.0"
  resolved "https://registry.yarnpkg.com/bpmn-moddle/-/bpmn-moddle-8.0.0.tgz#bb6da18f78bb4ec5abe1b5b995c950d16548e3e4"
  integrity sha512-mHmtIVzUyZcPMKKl/REq151gYxEtZxvivKnIEp/RtuRm8SOgxAK58uYBkP+jQQBy6XudwObRTH0pwyeKLALWrA==
  dependencies:
    min-dash "^4.0.0"
    moddle "^6.0.0"
    moddle-xml "^10.0.0"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

chokidar@^3.5.3:
  version "3.5.3"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

classnames@^2.3.1:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/classnames/-/classnames-2.3.2.tgz#351d813bf0137fcc6a76a16b88208d2560a0d924"
  integrity sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==

clipboard@^2.0.6:
  version "2.0.11"
  resolved "https://registry.yarnpkg.com/clipboard/-/clipboard-2.0.11.tgz#62180360b97dd668b6b3a84ec226975762a70be5"
  integrity sha512-C+0bbOqkezLIsmWSvlsXS0Q0bmkugu7jcfMIACB+RDEntIzQIkdr148we28AfSloQLRdZlYL/QYyrq05j/3Faw==
  dependencies:
    good-listener "^1.2.2"
    select "^1.1.2"
    tiny-emitter "^2.0.0"

clsx@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/clsx/-/clsx-1.2.1.tgz#0ddc4a20a549b59c93a4116bb26f5294ca17dc12"
  integrity sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==

codemirror-editor-vue3@^2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/codemirror-editor-vue3/-/codemirror-editor-vue3-2.3.0.tgz#e6c97aba62eee8bc1d980ea78e112179777d5a32"
  integrity sha512-e6y/2wBL4Xb4SD10e/jq/lcoIeiWPMw2XnF6KePLlbmOphZ830XGU3kSz52qkoa9RUjaY6zHuSlwd09o13O/oQ==
  dependencies:
    codemirror "^5.64.0"
    diff-match-patch "^1.0.5"

codemirror@^5.6.0, codemirror@^5.64.0:
  version "5.65.12"
  resolved "https://registry.yarnpkg.com/codemirror/-/codemirror-5.65.12.tgz#294fdf097d10ac5b56a9e011a91eff252afc73ae"
  integrity sha512-z2jlHBocElRnPYysN2HAuhXbO3DNB0bcSKmNz3hcWR2Js2Dkhc1bEOxG93Z3DeUrnm+qx56XOY5wQmbP5KY0sw==

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@*:
  version "9.4.1"
  resolved "https://registry.yarnpkg.com/commander/-/commander-9.4.1.tgz#d1dd8f2ce6faf93147295c0df13c7c21141cfbdd"
  integrity sha512-5EEkTNyHNGFPD2H+c/dXXfQZYa/scCKasxWcXJaWnNJ99pnQN9Vnmqow+p+PlFPE63Q6mThaZws1T+HxfpgtPw==

component-event@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/component-event/-/component-event-0.1.4.tgz"
  integrity sha512-GMwOG8MnUHP1l8DZx1ztFO0SJTFnIzZnBDkXAj8RM2ntV2A6ALlDxgbMY1Fvxlg6WPQ+5IM/a6vg4PEYbjg/Rw==

compute-scroll-into-view@^1.0.17:
  version "1.0.17"
  resolved "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-1.0.17.tgz"
  integrity sha512-j4dx+Fb0URmzbwwMUrhqWM2BEWHdFGx+qZ9qqASHRPqvTYdqvWnHg0H1hIbcyLnvgnoNAVMlwkepyqM3DaIFUg==

core-js@^3.15.1:
  version "3.25.3"
  resolved "https://registry.npmjs.org/core-js/-/core-js-3.25.3.tgz"
  integrity sha512-y1hvKXmPHvm5B7w4ln1S4uc9eV/O5+iFExSRUimnvIph11uaizFR8LFMdONN8hG3P2pipUfX4Y/fR8rAEtcHcQ==

crelt@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/crelt/-/crelt-1.0.5.tgz#57c0d52af8c859e354bace1883eb2e1eb182bb94"
  integrity sha512-+BO9wPPi+DWTDcNYhr/W90myha8ptzftZT+LwcmUbbok0rcP/fequmFYCw8NMoH7pkAZQzU78b3kYrlua5a9eA==

cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css.escape@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/css.escape/-/css.escape-1.5.1.tgz"
  integrity sha512-YUifsXXuknHlUsmlgyY0PKzgPOr7/FjCePfHNt0jxm83wHZi44VDMQ7/fGNkjY3/jV1MC+1CmZbaHzugyeRtpg==

csstype@^2.6.8:
  version "2.6.21"
  resolved "https://registry.npmjs.org/csstype/-/csstype-2.6.21.tgz"
  integrity sha512-Z1PhmomIfypOpoMjRQB70jfvy/wxT50qW08YXO5lMIJkrdq4yOTR+AW7FqutScmB9NkLwxo+jU+kZLbofZZq/w==

dayjs@^1.10.5:
  version "1.11.5"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.11.5.tgz"
  integrity sha512-CAdX5Q3YW3Gclyo5Vpqkgpj8fSdLQcRuzfX6mC6Phy0nfJ0eGYOeS7m4mt2plDWLAtA4TqTakvbboHvUxfe4iA==

debug@^4.3.4:
  version "4.3.4"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

delegate@^3.1.2:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/delegate/-/delegate-3.2.0.tgz#b66b71c3158522e8ab5744f720d8ca0c2af59166"
  integrity sha512-IofjkYBZaZivn0V8nnsMJGBr4jVLxHDheKSW88PyxS5QC4Vo9ZbZVvhzlSxY87fVq3STR6r+4cGepyHkcWOQSw==

diagram-js-direct-editing@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/diagram-js-direct-editing/-/diagram-js-direct-editing-2.0.0.tgz"
  integrity sha512-/12OWL0B0RMCfaT1w3723c729MD42r5fay4wtm2DvxNFNBMdPaEvOHCTA/khLKjFzOzMVKxSzbAp7IEwBGonVw==
  dependencies:
    min-dash "^4.0.0"
    min-dom "^4.0.2"

diagram-js-minimap@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/diagram-js-minimap/-/diagram-js-minimap-2.1.1.tgz"
  integrity sha512-H+UM6qoIVgJAOJgm3kZ57zpPyTx41YtAQAs0c/rrRube25ljygA0PSO5x42CJNulJdgK0AEWdsnhefMzlxyKfQ==
  dependencies:
    css.escape "^1.5.1"
    min-dash "^3.5.2"
    min-dom "^3.1.1"
    tiny-svg "^2.2.2"

diagram-js@^9.1.0:
  version "9.1.0"
  resolved "https://registry.npmjs.org/diagram-js/-/diagram-js-9.1.0.tgz"
  integrity sha512-Kh74c7iKczxrDmnnRn3Fay8+1AnOIEm22A7yxPP/1jyfGiGM1Thxp08niKru60HSpFyOguhYu4jgqOSM9fJzdw==
  dependencies:
    css.escape "^1.5.1"
    didi "^9.0.0"
    hammerjs "^2.0.1"
    inherits-browser "^0.1.0"
    min-dash "^4.0.0"
    min-dom "^4.0.2"
    object-refs "^0.3.0"
    path-intersection "^2.2.1"
    tiny-svg "^3.0.0"

didi@^9.0.0:
  version "9.0.0"
  resolved "https://registry.npmjs.org/didi/-/didi-9.0.0.tgz"
  integrity sha512-bOZ7WAah3t8TxKV81pbIivHjWyABot49YXG1M3QztnUlZDHz3MRNJ1nZO87JbqrkqNI/2GR4ncHfXdGIP9LX+w==

diff-match-patch@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/diff-match-patch/-/diff-match-patch-1.0.5.tgz#abb584d5f10cd1196dfc55aa03701592ae3f7b37"
  integrity sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw==

dom-align@^1.12.1:
  version "1.12.3"
  resolved "https://registry.npmjs.org/dom-align/-/dom-align-1.12.3.tgz"
  integrity sha512-Gj9hZN3a07cbR6zviMUBOMPdWxYhbMI+x+WS0NAIu2zFZmbK8ys9R79g+iG9qLnlCwpFoaB+fKy8Pdv470GsPA==

dom-scroll-into-view@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/dom-scroll-into-view/-/dom-scroll-into-view-2.0.1.tgz"
  integrity sha512-bvVTQe1lfaUr1oFzZX80ce9KLDlZ3iU+XGNE/bz9HnGdklTieqsbmsLHe+rT2XWqopvL0PckkYqN7ksmm5pe3w==

domify@^1.3.1, domify@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/domify/-/domify-1.4.1.tgz"
  integrity sha512-x18nuiDHMCZGXr4KJSRMf/TWYtiaRo6RX8KN9fEbW54mvbQ6pieUuerC2ahBg+kEp1wycFj8MPUI0WkIOw5E9w==

esbuild-android-64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-android-64/-/esbuild-android-64-0.15.10.tgz#8a59a84acbf2eca96996cadc35642cf055c494f0"
  integrity sha512-UI7krF8OYO1N7JYTgLT9ML5j4+45ra3amLZKx7LO3lmLt1Ibn8t3aZbX5Pu4BjWiqDuJ3m/hsvhPhK/5Y/YpnA==

esbuild-android-arm64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-android-arm64/-/esbuild-android-arm64-0.15.10.tgz#f453851dc1d8c5409a38cf7613a33852faf4915d"
  integrity sha512-EOt55D6xBk5O05AK8brXUbZmoFj4chM8u3riGflLa6ziEoVvNjRdD7Cnp82NHQGfSHgYR06XsPI8/sMuA/cUwg==

esbuild-darwin-64@0.15.10:
  version "0.15.10"
  resolved "https://registry.npmjs.org/esbuild-darwin-64/-/esbuild-darwin-64-0.15.10.tgz"
  integrity sha512-hbDJugTicqIm+WKZgp208d7FcXcaK8j2c0l+fqSJ3d2AzQAfjEYDRM3Z2oMeqSJ9uFxyj/muSACLdix7oTstRA==

esbuild-darwin-arm64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.15.10.tgz#b30bbefb46dc3c5d4708b0435e52f6456578d6df"
  integrity sha512-M1t5+Kj4IgSbYmunf2BB6EKLkWUq+XlqaFRiGOk8bmBapu9bCDrxjf4kUnWn59Dka3I27EiuHBKd1rSO4osLFQ==

esbuild-freebsd-64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-freebsd-64/-/esbuild-freebsd-64-0.15.10.tgz#ab301c5f6ded5110dbdd611140bef1a7c2e99236"
  integrity sha512-KMBFMa7C8oc97nqDdoZwtDBX7gfpolkk6Bcmj6YFMrtCMVgoU/x2DI1p74DmYl7CSS6Ppa3xgemrLrr5IjIn0w==

esbuild-freebsd-arm64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.15.10.tgz#a5b09b867a6ff49110f52343b6f12265db63d43f"
  integrity sha512-m2KNbuCX13yQqLlbSojFMHpewbn8wW5uDS6DxRpmaZKzyq8Dbsku6hHvh2U+BcLwWY4mpgXzFUoENEf7IcioGg==

esbuild-linux-32@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-linux-32/-/esbuild-linux-32-0.15.10.tgz#5282fe9915641caf9c8070e4ba2c3e16d358f837"
  integrity sha512-guXrwSYFAvNkuQ39FNeV4sNkNms1bLlA5vF1H0cazZBOLdLFIny6BhT+TUbK/hdByMQhtWQ5jI9VAmPKbVPu1w==

esbuild-linux-64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-linux-64/-/esbuild-linux-64-0.15.10.tgz#f3726e85a00149580cb19f8abfabcbb96f5d52bb"
  integrity sha512-jd8XfaSJeucMpD63YNMO1JCrdJhckHWcMv6O233bL4l6ogQKQOxBYSRP/XLWP+6kVTu0obXovuckJDcA0DKtQA==

esbuild-linux-arm64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-linux-arm64/-/esbuild-linux-arm64-0.15.10.tgz#2f0056e9d5286edb0185b56655caa8c574d8dbe7"
  integrity sha512-GByBi4fgkvZFTHFDYNftu1DQ1GzR23jws0oWyCfhnI7eMOe+wgwWrc78dbNk709Ivdr/evefm2PJiUBMiusS1A==

esbuild-linux-arm@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-linux-arm/-/esbuild-linux-arm-0.15.10.tgz#40a9270da3c8ffa32cf72e24a79883e323dff08d"
  integrity sha512-6N8vThLL/Lysy9y4Ex8XoLQAlbZKUyExCWyayGi2KgTBelKpPgj6RZnUaKri0dHNPGgReJriKVU6+KDGQwn10A==

esbuild-linux-mips64le@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.15.10.tgz#90ce1c4ee0202edb4ac69807dea77f7e5804abc4"
  integrity sha512-BxP+LbaGVGIdQNJUNF7qpYjEGWb0YyHVSKqYKrn+pTwH/SiHUxFyJYSP3pqkku61olQiSBnSmWZ+YUpj78Tw7Q==

esbuild-linux-ppc64le@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.15.10.tgz#782837ae7bd5b279178106c9dd801755a21fabdf"
  integrity sha512-LoSQCd6498PmninNgqd/BR7z3Bsk/mabImBWuQ4wQgmQEeanzWd5BQU2aNi9mBURCLgyheuZS6Xhrw5luw3OkQ==

esbuild-linux-riscv64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.15.10.tgz#d7420d806ece5174f24f4634303146f915ab4207"
  integrity sha512-Lrl9Cr2YROvPV4wmZ1/g48httE8z/5SCiXIyebiB5N8VT7pX3t6meI7TQVHw/wQpqP/AF4SksDuFImPTM7Z32Q==

esbuild-linux-s390x@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-linux-s390x/-/esbuild-linux-s390x-0.15.10.tgz#21fdf0cb3494a7fb520a71934e4dffce67fe47be"
  integrity sha512-ReP+6q3eLVVP2lpRrvl5EodKX7EZ1bS1/z5j6hsluAlZP5aHhk6ghT6Cq3IANvvDdscMMCB4QEbI+AjtvoOFpA==

esbuild-netbsd-64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-netbsd-64/-/esbuild-netbsd-64-0.15.10.tgz#6c06b3107e3df53de381e6299184d4597db0440f"
  integrity sha512-iGDYtJCMCqldMskQ4eIV+QSS/CuT7xyy9i2/FjpKvxAuCzrESZXiA1L64YNj6/afuzfBe9i8m/uDkFHy257hTw==

esbuild-openbsd-64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-openbsd-64/-/esbuild-openbsd-64-0.15.10.tgz#4daef5f5d8e74bbda53b65160029445d582570cf"
  integrity sha512-ftMMIwHWrnrYnvuJQRJs/Smlcb28F9ICGde/P3FUTCgDDM0N7WA0o9uOR38f5Xe2/OhNCgkjNeb7QeaE3cyWkQ==

esbuild-sunos-64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-sunos-64/-/esbuild-sunos-64-0.15.10.tgz#5fe7bef267a02f322fd249a8214d0274937388a7"
  integrity sha512-mf7hBL9Uo2gcy2r3rUFMjVpTaGpFJJE5QTDDqUFf1632FxteYANffDZmKbqX0PfeQ2XjUDE604IcE7OJeoHiyg==

esbuild-windows-32@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-windows-32/-/esbuild-windows-32-0.15.10.tgz#48e3dde25ab0135579a288b30ab6ddef6d1f0b28"
  integrity sha512-ttFVo+Cg8b5+qHmZHbEc8Vl17kCleHhLzgT8X04y8zudEApo0PxPg9Mz8Z2cKH1bCYlve1XL8LkyXGFjtUYeGg==

esbuild-windows-64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-windows-64/-/esbuild-windows-64-0.15.10.tgz#387a9515bef3fee502d277a5d0a2db49a4ecda05"
  integrity sha512-2H0gdsyHi5x+8lbng3hLbxDWR7mKHWh5BXZGKVG830KUmXOOWFE2YKJ4tHRkejRduOGDrBvHBriYsGtmTv3ntA==

esbuild-windows-arm64@0.15.10:
  version "0.15.10"
  resolved "https://registry.yarnpkg.com/esbuild-windows-arm64/-/esbuild-windows-arm64-0.15.10.tgz#5a6fcf2fa49e895949bf5495cf088ab1b43ae879"
  integrity sha512-S+th4F+F8VLsHLR0zrUcG+Et4hx0RKgK1eyHc08kztmLOES8BWwMiaGdoW9hiXuzznXQ0I/Fg904MNbr11Nktw==

esbuild@^0.15.6:
  version "0.15.10"
  resolved "https://registry.npmjs.org/esbuild/-/esbuild-0.15.10.tgz"
  integrity sha512-N7wBhfJ/E5fzn/SpNgX+oW2RLRjwaL8Y0ezqNqhjD6w0H2p0rDuEz2FKZqpqLnO8DCaWumKe8dsC/ljvVSSxng==
  optionalDependencies:
    "@esbuild/android-arm" "0.15.10"
    "@esbuild/linux-loong64" "0.15.10"
    esbuild-android-64 "0.15.10"
    esbuild-android-arm64 "0.15.10"
    esbuild-darwin-64 "0.15.10"
    esbuild-darwin-arm64 "0.15.10"
    esbuild-freebsd-64 "0.15.10"
    esbuild-freebsd-arm64 "0.15.10"
    esbuild-linux-32 "0.15.10"
    esbuild-linux-64 "0.15.10"
    esbuild-linux-arm "0.15.10"
    esbuild-linux-arm64 "0.15.10"
    esbuild-linux-mips64le "0.15.10"
    esbuild-linux-ppc64le "0.15.10"
    esbuild-linux-riscv64 "0.15.10"
    esbuild-linux-s390x "0.15.10"
    esbuild-netbsd-64 "0.15.10"
    esbuild-openbsd-64 "0.15.10"
    esbuild-sunos-64 "0.15.10"
    esbuild-windows-32 "0.15.10"
    esbuild-windows-64 "0.15.10"
    esbuild-windows-arm64 "0.15.10"

estree-walker@^2.0.1, estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

execa@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

fast-glob@^3.2.12:
  version "3.2.12"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.12.tgz"
  integrity sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fastq@^1.6.0:
  version "1.13.0"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz"
  integrity sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==
  dependencies:
    reusify "^1.0.4"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

follow-redirects@^1.15.0:
  version "1.15.2"
  resolved "https://registry.yarnpkg.com/follow-redirects/-/follow-redirects-1.15.2.tgz#b460864144ba63f2681096f274c4e57026da2c13"
  integrity sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz"
  integrity sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

good-listener@^1.2.2:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/good-listener/-/good-listener-1.2.2.tgz#d53b30cdf9313dffb7dc9a0d477096aa6d145c50"
  integrity sha512-goW1b+d9q/HIwbVYZzZ6SsTr4IgE+WA44A0GmPIQstuOrgsFcT7VEJ48nmr9GaRtNu0XTKacFLGnBPAM6Afouw==
  dependencies:
    delegate "^3.1.2"

hammerjs@^2.0.1:
  version "2.0.8"
  resolved "https://registry.npmjs.org/hammerjs/-/hammerjs-2.0.8.tgz"
  integrity sha512-tSQXBXS/MWQOn/RKckawJ61vvsDpCom87JgxiYdGwHdOa0ht0vzUWDlfioofFCRU0L+6NGDt6XzbgoJvZkMeRQ==

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
  dependencies:
    function-bind "^1.1.1"

highlight.js@^11.6.0:
  version "11.6.0"
  resolved "https://registry.npmjs.org/highlight.js/-/highlight.js-11.6.0.tgz"
  integrity sha512-ig1eqDzJaB0pqEvlPVIpSSyMaO92bH1N2rJpLMN/nX396wTpDA4Eq0uK+7I/2XG17pFaaKE0kjV/XPeGt7Evjw==

htm@^3.1.1:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/htm/-/htm-3.1.1.tgz#49266582be0dc66ed2235d5ea892307cc0c24b78"
  integrity sha512-983Vyg8NwUE7JkZ6NmOqpCZ+sh1bKv2iYTlUkzlWmA5JD2acKoxd4KVxbMmxX/85mtfdnDmTFoNKcg5DGAvxNQ==

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

ids@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/ids/-/ids-1.0.0.tgz"
  integrity sha512-Zvtq1xUto4LttpstyOlFum8lKx+i1OmRfg+6A9drFS9iSZsDPMHG4Sof/qwNR4kCU7jBeWFPrY2ocHxiz7cCRw==

indexof@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/indexof/-/indexof-0.0.1.tgz"
  integrity sha512-i0G7hLJ1z0DE8dsqJa2rycj9dBmNKgXBvotXtZYXakU9oivfB9Uj2ZBC27qqef2U58/ZLwalxa1X/RDCdkHtVg==

inherits-browser@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/inherits-browser/-/inherits-browser-0.1.0.tgz"
  integrity sha512-CJHHvW3jQ6q7lzsXPpapLdMx5hDpSF3FSh45pwsj6bKxJJ8Nl8v43i5yXnr3BdfOimGHKyniewQtnAIp3vyJJw==

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-core-module@^2.9.0:
  version "2.10.0"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.10.0.tgz"
  integrity sha512-Erxj2n/LDAZ7H8WNJXd9tw38GYM3dv8rk8Zcs+jJuxYTW7sozH+SS8NtrSjVL1/vpLvWi1hxy96IzjJ3EHTJJg==
  dependencies:
    has "^1.0.3"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-plain-object@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/is-plain-object/-/is-plain-object-3.0.1.tgz"
  integrity sha512-Xnpx182SBMrr/aBik8y+GuR4U1L9FqMSojwDQwPMmxyC6bvEqly9UBCxhauBF5vNh2gwWJNX6oDV7O+OM4z34g==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

js-base64@^3.7.5:
  version "3.7.5"
  resolved "https://registry.yarnpkg.com/js-base64/-/js-base64-3.7.5.tgz#21e24cf6b886f76d6f5f165bfcd69cc55b9e3fca"
  integrity sha512-3MEt5DTINKqfScXKfJFrRbxkrnk2AxPWGBL/ycjz4dK8iqiSJ06UxD8jh8xuh6p10TX4t2+7FsBYVxxQbMg+qA==

"js-tokens@^3.0.0 || ^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

json-source-map@^0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/json-source-map/-/json-source-map-0.6.1.tgz#e0b1f6f4ce13a9ad57e2ae165a24d06e62c79a0f"
  integrity sha512-1QoztHPsMQqhDq0hlXY5ZqcEdUzxQEIxgFkKl4WUp2pgShObl+9ovi4kRh2TfvAfxAoHOJ9vIMEqk3k4iex7tg==

kolorist@^1.5.1, kolorist@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/kolorist/-/kolorist-1.6.0.tgz"
  integrity sha512-dLkz37Ab97HWMx9KTes3Tbi3D1ln9fCAy2zr2YVExJasDRPGRaKcoE4fycWNtnCAJfjFqe0cnY+f8KT2JePEXQ==

lang-feel@^0.0.3:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/lang-feel/-/lang-feel-0.0.3.tgz#2d96dc87a95735ce74f033c78dc43a8dd64bc81d"
  integrity sha512-YEs49jXQfLetXUr4Sj+pq9kcwHyNFcEYiXvm/bRvQyUwVfUEAHQdeFneqw+5zGeDuKDgIGxawXVs7uysXaLrjQ==
  dependencies:
    "@codemirror/autocomplete" "^6.0.0"
    "@codemirror/language" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    "@lezer/common" "^1.0.0"
    lezer-feel "^0.14.1"

lezer-feel@^0.14.1:
  version "0.14.1"
  resolved "https://registry.yarnpkg.com/lezer-feel/-/lezer-feel-0.14.1.tgz#11357bef9991734b4f62f308eab40f02f2010f94"
  integrity sha512-sfpzZvAtObFon74XiFp1L8pS1FminnfM8JAm4S2Kxk7Wk8qYe7crjJdhHqju/MKl9dV5s44NHDhbq5tCDWMTlw==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.2.3"

local-pkg@^0.4.2:
  version "0.4.2"
  resolved "https://registry.npmjs.org/local-pkg/-/local-pkg-0.4.2.tgz"
  integrity sha512-mlERgSPrbxU3BP4qBqAvvwlgW4MTg78iwJdGGnv7kibKjWcJksrG3t6LB5lXI93wXRDvG4NpUgJFmTG4T6rdrg==

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.15:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

magic-string@^0.25.7:
  version "0.25.9"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.25.9.tgz"
  integrity sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==
  dependencies:
    sourcemap-codec "^1.4.8"

magic-string@^0.26.3:
  version "0.26.5"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.26.5.tgz"
  integrity sha512-yXUIYOOQnEHKHOftp5shMWpB9ImfgfDJpapa38j/qMtTj5QHWucvxP4lUtuRmHT9vAzvtpHkWKXW9xBwimXeNg==
  dependencies:
    sourcemap-codec "^1.4.8"

matches-selector@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/matches-selector/-/matches-selector-1.2.0.tgz"
  integrity sha512-c4vLwYWyl+Ji+U43eU/G5FwxWd4ZH0ePUsFs5y0uwD9HUEFBXUQ1zUUan+78IpRD+y4pUfG0nAzNM292K7ItvA==

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

micromatch@^4.0.4:
  version "4.0.5"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz"
  integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

min-dash@^3.0.0, min-dash@^3.5.2, min-dash@^3.8.1:
  version "3.8.1"
  resolved "https://registry.npmjs.org/min-dash/-/min-dash-3.8.1.tgz"
  integrity sha512-evumdlmIlg9mbRVPbC4F5FuRhNmcMS5pvuBUbqb1G9v09Ro0ImPEgz5n3khir83lFok1inKqVDjnKEg3GpDxQg==

min-dash@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/min-dash/-/min-dash-4.0.0.tgz"
  integrity sha512-piIvVJ/nxuA4+LpnYIzF6oCtRvdtDvQJteSC+H768H2UvPKFKIt5oiJnUVtr0ZdchneXTcvUZ91vIrvWVIN0AA==

min-dom@^3.1.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/min-dom/-/min-dom-3.2.1.tgz"
  integrity sha512-v6YCmnDzxk4rRJntWTUiwggLupPw/8ZSRqUq0PDaBwVZEO/wYzCH4SKVBV+KkEvf3u0XaWHly5JEosPtqRATZA==
  dependencies:
    component-event "^0.1.4"
    domify "^1.3.1"
    indexof "0.0.1"
    matches-selector "^1.2.0"
    min-dash "^3.8.1"

min-dom@^4.0.1, min-dom@^4.0.3:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/min-dom/-/min-dom-4.0.3.tgz#659aa6017bb2eae5a22bfe62d4a7fb87248da3fa"
  integrity sha512-5zQyCMe8rtGiDIRjfGeqnF2YPJ7OAPFdJQeC7MakHais3dh4VG4PV2a0FacziKTzJjYK5qnPKm2sq1wSXB1wTQ==
  dependencies:
    component-event "^0.1.4"
    domify "^1.4.1"
    min-dash "^4.0.0"

min-dom@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/min-dom/-/min-dom-4.0.2.tgz"
  integrity sha512-zOlfZJ/mfgHKnmfAdZdNEaELq52z6Q8kWNZzupPv4aKLdpc67Uq6tCDAo/UIm1MKZrJIok7XMjRrYctG/jtqGw==
  dependencies:
    component-event "^0.1.4"
    domify "^1.4.1"
    min-dash "^3.8.1"

minimatch@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-5.1.0.tgz"
  integrity sha512-9TPBGGak4nHfGZsPBohm9AWg6NoT7QTCehS3BIJABslyZbzxfV78QM2Y6+i741OPZIafFAaiiEMh5OyIrJPgtg==
  dependencies:
    brace-expansion "^2.0.1"

mockjs@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/mockjs/-/mockjs-1.1.0.tgz#e6a0c378e91906dbaff20911cc0273b3c7d75b06"
  integrity sha512-eQsKcWzIaZzEZ07NuEyO4Nw65g0hdWAyurVol1IPl1gahRwY+svqzfgfey8U8dahLwG44d6/RwEzuK52rSa/JQ==
  dependencies:
    commander "*"

moddle-xml@^10.0.0:
  version "10.0.0"
  resolved "https://registry.yarnpkg.com/moddle-xml/-/moddle-xml-10.0.0.tgz#7b0815982b375ece4338258ec86bc6be4f05ed7b"
  integrity sha512-e5qG0uC9ebHXInLEKW9pZNqcAK7ALCOKmcLw8jmepBPY9BiUZFi+8Th/Cydog3S1rrUXyyo0pqaHaCvc1zt6VA==
  dependencies:
    min-dash "^4.0.0"
    moddle "^6.0.0"
    saxen "^8.1.2"

moddle-xml@^9.0.6:
  version "9.0.6"
  resolved "https://registry.npmjs.org/moddle-xml/-/moddle-xml-9.0.6.tgz"
  integrity sha512-tl0reHpsY/aKlLGhXeFlQWlYAQHFxTkFqC8tq8jXRYpQSnLVw13T6swMaourLd7EXqHdWsc+5ggsB+fEep6xZQ==
  dependencies:
    min-dash "^3.5.2"
    moddle "^5.0.2"
    saxen "^8.1.2"

moddle@^5.0.2:
  version "5.0.4"
  resolved "https://registry.npmjs.org/moddle/-/moddle-5.0.4.tgz"
  integrity sha512-Kjb+hjuzO+YlojNGxEUXvdhLYTHTtAABDlDcJTtTcn5MbJF9Zkv4I1Fyvp3Ypmfgg1EfHDZ3PsCQTuML9JD6wg==
  dependencies:
    min-dash "^3.0.0"

moddle@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/moddle/-/moddle-6.0.0.tgz#ccfae6936430e8294a98619bcbefbf1950eeaaa4"
  integrity sha512-dHYSJpGV0R0X8cJeWWUnE0VqCA0SFP0jZYQtO23EdsBk+MpAgSpdhXadYR6WbHElKroB6XTbifpsWro26UlP6Q==
  dependencies:
    min-dash "^4.0.0"

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

nanoid@^3.3.4:
  version "3.3.4"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.4.tgz"
  integrity sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw==

nanopop@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/nanopop/-/nanopop-2.2.0.tgz"
  integrity sha512-E9JaHcxh3ere8/BEZHAcnuD10RluTSPyTToBvoFWS9/7DcCx6gyKjbn7M7Bx7E1veCxCuY1iO6h4+gdAf1j73Q==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

object-refs@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/object-refs/-/object-refs-0.3.0.tgz"
  integrity sha512-eP0ywuoWOaDoiake/6kTJlPJhs+k0qNm4nYRzXLNHj6vh+5M3i9R1epJTdxIPGlhWc4fNRQ7a6XJNCX+/L4FOQ==

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-intersection@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/path-intersection/-/path-intersection-2.2.1.tgz"
  integrity sha512-9u8xvMcSfuOiStv9bPdnRJQhGQXLKurew94n4GPQCdH1nj9QKC9ObbNoIpiRq8skiOBxKkt277PgOoFgAt3/rA==

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz"
  integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.2, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pinia@^2.0.22:
  version "2.0.22"
  resolved "https://registry.npmjs.org/pinia/-/pinia-2.0.22.tgz"
  integrity sha512-u+b8/BC+tmvo3ACbYO2w5NfxHWFOjvvw9DQnyT0dW8aUMCPRQT5QnfZ5R5W2MzZBMTeZRMQI7V/QFbafmM9QHw==
  dependencies:
    "@vue/devtools-api" "^6.2.1"
    vue-demi "*"

postcss@^8.1.10, postcss@^8.4.16:
  version "8.4.16"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.16.tgz"
  integrity sha512-ipHE1XBvKzm5xI7hiHCZJCSugxvsdq2mPnsq5+UF+VHCjiBvtDrlxJfMBToWaP9D5XlgNmcFGqoHmUn0EYEaRQ==
  dependencies:
    nanoid "^3.3.4"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

preact-markup@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/preact-markup/-/preact-markup-2.1.1.tgz#0451e7eed1dac732d7194c34a7f16ff45a2cfdd7"
  integrity sha512-8JL2p36mzK8XkspOyhBxUSPjYwMxDM0L5BWBZWxsZMVW8WsGQrYQDgVuDKkRspt2hwrle+Cxr/053hpc9BJwfw==

preact@^10.11.0:
  version "10.11.1"
  resolved "https://registry.yarnpkg.com/preact/-/preact-10.11.1.tgz#35fdad092de8b2ad29df3a0bef9af1f4fdd2256b"
  integrity sha512-1Wz5PCRm6Fg+6BTXWJHhX4wRK9MZbZBHuwBqfZlOdVm2NqPe8/rjYpufvYCwJSGb9layyzB2jTTXfpCTynLqFQ==

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

regenerator-runtime@^0.13.4:
  version "0.13.9"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  integrity sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve@^1.22.1:
  version "1.22.1"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.1.tgz"
  integrity sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rollup@~2.78.0:
  version "2.78.1"
  resolved "https://registry.npmjs.org/rollup/-/rollup-2.78.1.tgz"
  integrity sha512-VeeCgtGi4P+o9hIg+xz4qQpRl6R401LWEXBmxYKOV4zlF82lyhgh2hTZnheFUbANE8l2A41F458iwj2vEYaXJg==
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

saxen@^8.1.2:
  version "8.1.2"
  resolved "https://registry.npmjs.org/saxen/-/saxen-8.1.2.tgz"
  integrity sha512-xUOiiFbc3Ow7p8KMxwsGICPx46ZQvy3+qfNVhrkwfz3Vvq45eGt98Ft5IQaA1R/7Tb5B5MKh9fUR9x3c3nDTxw==

scroll-into-view-if-needed@^2.2.25:
  version "2.2.29"
  resolved "https://registry.npmjs.org/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.29.tgz"
  integrity sha512-hxpAR6AN+Gh53AdAimHM6C8oTN1ppwVZITihix+WqalywBeFcQ6LdQP5ABNl26nX8GTEL7VT+b8lKpdqq65wXg==
  dependencies:
    compute-scroll-into-view "^1.0.17"

select@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/select/-/select-1.1.2.tgz#0e7350acdec80b1108528786ec1d4418d11b396d"
  integrity sha512-OwpTSOfy6xSs1+pwcNrv0RBMOzI39Lp3qQKUTPVVPRjCdNa5JH/oPRiqsesIskK8TVgmRiHwO4KXlV2Li9dANA==

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/semver-compare/-/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow==

shallow-equal@^1.0.0:
  version "1.2.1"
  resolved "https://registry.npmjs.org/shallow-equal/-/shallow-equal-1.2.1.tgz"
  integrity sha512-S4vJDjHHMBaiZuT9NPb616CSmLf618jawtv3sufLl6ivK8WocjAo58cXwbRV1cgqxH0Qbv+iUt6m05eqEa2IRA==

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

source-map-js@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.2.tgz"
  integrity sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==

source-map@^0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
  integrity sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

style-mod@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/style-mod/-/style-mod-4.0.0.tgz#97e7c2d68b592975f2ca7a63d0dd6fcacfe35a01"
  integrity sha512-OPhtyEjyyN9x3nhPsu76f52yUGXiZcgvsrFVtvTkyGRQJ0XK+GPc6ov1z+lRpbeabka+MYEQxOYRnt5nF30aMw==

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

tiny-emitter@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/tiny-emitter/-/tiny-emitter-2.1.0.tgz#1d1a56edfc51c43e863cbb5382a72330e3555423"
  integrity sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q==

tiny-svg@^2.2.2:
  version "2.2.4"
  resolved "https://registry.npmjs.org/tiny-svg/-/tiny-svg-2.2.4.tgz"
  integrity sha512-NOi39lBknf4UdDEahNkbEAJnzhu1ZcN2j75IS2vLRmIhsfxdZpTChfLKBcN1ShplVmPIXJAIafk6YY5/Aa80lQ==

tiny-svg@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/tiny-svg/-/tiny-svg-3.0.0.tgz"
  integrity sha512-+u6VomQO7MbI7CQe5q1IwNePpbVKG/HVdUQBmaEpSCdP/QmeyjhrS6WKFsNetXlvf9LWu/f5woRqjMdxBMe/0w==

tiny-svg@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/tiny-svg/-/tiny-svg-3.0.1.tgz#1cb79297b87abbc921396064098cbd08c3c34ea6"
  integrity sha512-P8T4iwiW1t95vpHVHqrD36Brn7TqFYCPSHIWk9WLJtYK1X4aDd+5cgqcAADIWSjf1/i5idKnpCh9mim8hEdRBg==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

unplugin-icons@^0.14.11:
  version "0.14.11"
  resolved "https://registry.npmjs.org/unplugin-icons/-/unplugin-icons-0.14.11.tgz"
  integrity sha512-szr6QA1ILT4RrJCmtvW41m2KPwUCCIx8QNgwEmTehvzEkSLDrdEaBhKGMWb/qTe5/zwnfwrDGcFT/6pGOSHJcA==
  dependencies:
    "@antfu/install-pkg" "^0.1.1"
    "@antfu/utils" "^0.5.2"
    "@iconify/utils" "^2.0.0"
    debug "^4.3.4"
    kolorist "^1.6.0"
    local-pkg "^0.4.2"
    unplugin "^0.9.6"

unplugin-vue-components@^0.22.7:
  version "0.22.7"
  resolved "https://registry.npmjs.org/unplugin-vue-components/-/unplugin-vue-components-0.22.7.tgz"
  integrity sha512-MJEAKJF9bRykTRvJ4WXF0FNMAyt1PX3iwpu2NN+li35sAKjQv6sC1col5aZDLiwDZDo2AGwxNkzLJFKaun9qHw==
  dependencies:
    "@antfu/utils" "^0.5.2"
    "@rollup/pluginutils" "^4.2.1"
    chokidar "^3.5.3"
    debug "^4.3.4"
    fast-glob "^3.2.12"
    local-pkg "^0.4.2"
    magic-string "^0.26.3"
    minimatch "^5.1.0"
    resolve "^1.22.1"
    unplugin "^0.9.5"

unplugin@^0.9.5, unplugin@^0.9.6:
  version "0.9.6"
  resolved "https://registry.npmjs.org/unplugin/-/unplugin-0.9.6.tgz"
  integrity sha512-YYLtfoNiie/lxswy1GOsKXgnLJTE27la/PeCGznSItk+8METYZErO+zzV9KQ/hXhPwzIJsfJ4s0m1Rl7ZCWZ4Q==
  dependencies:
    acorn "^8.8.0"
    chokidar "^3.5.3"
    webpack-sources "^3.2.3"
    webpack-virtual-modules "^0.4.5"

vite@^3.1.0:
  version "3.1.4"
  resolved "https://registry.npmjs.org/vite/-/vite-3.1.4.tgz"
  integrity sha512-JoQI08aBjY9lycL7jcEq4p9o1xUjq5aRvdH4KWaXtkSx7e7RpAh9D3IjzDWRD4Fg44LS3oDAIOG/Kq1L+82psA==
  dependencies:
    esbuild "^0.15.6"
    postcss "^8.4.16"
    resolve "^1.22.1"
    rollup "~2.78.0"
  optionalDependencies:
    fsevents "~2.3.2"

vue-clipboard3@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/vue-clipboard3/-/vue-clipboard3-2.0.0.tgz#79b026c765c0f6a5cde18a477c2dbfc7d3b9f178"
  integrity sha512-Q9S7dzWGax7LN5iiSPcu/K1GGm2gcBBlYwmMsUc5/16N6w90cbKow3FnPmPs95sungns4yvd9/+JhbAznECS2A==
  dependencies:
    clipboard "^2.0.6"

vue-demi@*:
  version "0.13.11"
  resolved "https://registry.npmjs.org/vue-demi/-/vue-demi-0.13.11.tgz"
  integrity sha512-IR8HoEEGM65YY3ZJYAjMlKygDQn25D5ajNFNoKh9RSDMQtlzCxtfQjdQgv9jjK+m3377SsJXY8ysq8kLCZL25A==

vue-types@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/vue-types/-/vue-types-3.0.2.tgz"
  integrity sha512-IwUC0Aq2zwaXqy74h4WCvFCUtoV0iSWr0snWnE9TnU18S66GAQyqQbRf2qfJtUuiFsBf6qp0MEwdonlwznlcrw==
  dependencies:
    is-plain-object "3.0.1"

vue@^3.2.37:
  version "3.2.40"
  resolved "https://registry.npmjs.org/vue/-/vue-3.2.40.tgz"
  integrity sha512-1mGHulzUbl2Nk3pfvI5aXYYyJUs1nm4kyvuz38u4xlQkLUn1i2R7nDbI4TufECmY8v1qNBHYy62bCaM+3cHP2A==
  dependencies:
    "@vue/compiler-dom" "3.2.40"
    "@vue/compiler-sfc" "3.2.40"
    "@vue/runtime-dom" "3.2.40"
    "@vue/server-renderer" "3.2.40"
    "@vue/shared" "3.2.40"

w3c-keyname@^2.2.4:
  version "2.2.6"
  resolved "https://registry.yarnpkg.com/w3c-keyname/-/w3c-keyname-2.2.6.tgz#8412046116bc16c5d73d4e612053ea10a189c85f"
  integrity sha512-f+fciywl1SJEniZHD6H+kUO8gOnwIr7f4ijKA6+ZvJFjeGi1r4PDLl53Ayud9O/rk64RqgoQine0feoeOU0kXg==

warning@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  integrity sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==
  dependencies:
    loose-envify "^1.0.0"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack-virtual-modules@^0.4.5:
  version "0.4.5"
  resolved "https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.4.5.tgz"
  integrity sha512-8bWq0Iluiv9lVf9YaqWQ9+liNgXSHICm+rg544yRgGYaR8yXZTVBaHZkINZSB2yZSWo4b0F6MIxqJezVfOEAlg==

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==
