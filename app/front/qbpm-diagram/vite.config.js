import {defineConfig, loadEnv} from 'vite';
import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import {AntDesignVueResolver} from 'unplugin-vue-components/resolvers';
import path from 'path';

export default ({mode}) => {
    process.env = {...process.env, ...loadEnv(mode, process.cwd())};
    return defineConfig({
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src/'),
                'mock': path.resolve(__dirname, 'mock/')
            }
        },
        server: {
            port: 8888,
           /* 方便本地调试时无需 nginx 的 proxy pass
           proxy: {
                '/gaea/api': {
                    target: `${process.env.VITE_API_URL}/api/proxy`,
                    changeOrigin: true
                },
                '/qbpm/api': {
                    target: `${process.env.VITE_API_URL}/api/proxy`,
                    changeOrigin: true
                }
            }*/
        },
        plugins: [
            vue(),
            Components({
                resolvers: [
                    AntDesignVueResolver()
                ]
            })
        ]
    })
}
