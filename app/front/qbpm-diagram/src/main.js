import { createApp } from 'vue';
import 'bpmn-js/dist/assets/diagram-js.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css';
import 'diagram-js-minimap/assets/diagram-js-minimap.css';
import 'highlight.js/styles/agate.css';
import xml from 'highlight.js/lib/languages/xml';
import './style.css';
// import 'mock';


import App from './App.vue';
import {createPinia} from 'pinia';
import hljs from 'highlight.js/lib/core';
import hljsVuePlugin from '@highlightjs/vue-plugin';

const pinia = createPinia()
const app = createApp(App)
app.use(pinia)
// 预览语法高亮
hljs.registerLanguage('xml', xml)
app.use(hljsVuePlugin)
app.mount('#app')
