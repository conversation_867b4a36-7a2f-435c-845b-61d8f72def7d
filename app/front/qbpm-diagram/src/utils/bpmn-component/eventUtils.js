// 获取节点事件定义
import {getBusinessObject, is} from 'bpmn-js/lib/util/ModelUtil.js';
import {find} from 'min-dash';

export function getEventDefinition(element, eventType){
    const businessObject = getBusinessObject(element)
    const eventDefinitions = businessObject['eventDefinitions'] || []
    return find(eventDefinitions, function (definition) {
        return is(definition, eventType)
    })
}

export function isEndEvent(element){
    return is(element, 'bpmn:EndEvent')
}