import modeler from '@/store/modeler';
import Modeler from 'bpmn-js/lib/Modeler';
import {markRaw} from 'vue';
import EventEmitter from '@/utils/eventEmitter';
import minimapModule from 'diagram-js-minimap';
import Grid from 'diagram-js/lib/features/grid-snapping/visuals';
import Translate from '@/additional-modules/translate/index';
import CustomPalette from '@/additional-modules/palette/index';
import CustomRenderer from '@/additional-modules/renderer/index';
import qbpmModdleDesciptors from '@/moddle-extensions/qbpm.json';
import {BpmnPropertiesPanelModule, BpmnPropertiesProviderModule} from 'bpmn-js-properties-panel';

export function initModeler(ref, modelerModules, emit) {
    const modelerStore = modeler()
    const options = {
        container: ref.value,
        additionalModules: modelerModules[0] || [],
        moddleExtensions: modelerModules[1] || {},
        ...modelerModules[2]
    }
    // 清除旧 modeler
    modelerStore.getModeler && modelerStore.getModeler.destroy()
    modelerStore.setModeler(null)

    const bpmnModeler = new Modeler(options)
    modelerStore.setModeler(markRaw(bpmnModeler))
    modelerStore.setModules('moddle', markRaw(bpmnModeler.get('moddle')))
    modelerStore.setModules('modeling', markRaw(bpmnModeler.get('modeling')))
    modelerStore.setModules('canvas', markRaw(bpmnModeler.get('canvas')))
    modelerStore.setModules('elementRegistry', markRaw(bpmnModeler.get('elementRegistry')))

    EventEmitter.emit('modeler-init', bpmnModeler)

    bpmnModeler.on('commandStack.changed', async (event) => {
        try {
            const {xml} = await bpmnModeler.saveXML({format: true})
            emit('update:xml', xml)
            emit('command-stack-changed', event)
        } catch (error) {
            console.error(error)
        }
    })
}

// 注册 provider modules
export const registerModules = settings => {
    const modules = []
    let moddle = {}
    const options = {}

    // 官方组件
    modules.push(BpmnPropertiesPanelModule, BpmnPropertiesProviderModule)

    if (settings.value.miniMap) {
        modules.push(minimapModule)
        options['minimap'] = {
            open: true
        }
    }

    // 官方网点背景
    if (settings.value.background === 'grid') {
        modules.push(Grid)
    }

    // 翻译
    modules.push(Translate)
    modules.push(CustomPalette)
    modules.push(CustomRenderer)

    // 七牛自定义属性和元素
    moddle['qbpm'] = qbpmModdleDesciptors

    // 绑定键盘事件
    options['keyboard'] = {
        bindTo: document
    }
    return [modules, moddle, options]
}

const createEmptyXML = (key, name) => {
    return `<?xml version='1.0' encoding='UTF-8'?>
<bpmn:definitions 
  xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
  xmlns:bpmn='http://www.omg.org/spec/BPMN/20100524/MODEL'
  xmlns:bpmndi='http://www.omg.org/spec/BPMN/20100524/DI'
  xmlns:dc='http://www.omg.org/spec/DD/20100524/DC'
  xmlns:di='http://www.omg.org/spec/DD/20100524/DI'
  targetNamespace='http://bpmn.io/schema/bpmn'
  id='Definitions_${key}'>
  <bpmn:process id='${key}' name='${name}'></bpmn:process>
  <bpmndi:BPMNDiagram id='BPMNDiagram_1'>
    <bpmndi:BPMNPlane id='BPMNPlane_1' bpmnElement='${key}'></bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`
}

export const createNewDiagram = async function (newXml, editorSettings) {
    try {
        const modelerStore = modeler()
        const timestamp = Date.now()
        const {processId, processName} = editorSettings || {}
        const newId = processId ? processId : `Process_${timestamp}`
        const newName = processName || `七牛IO审批流程图_${timestamp}`
        const xmlString = newXml || createEmptyXML(newId, newName)
        const bpmnModeler = modelerStore.getModeler
        const {warnings} = await bpmnModeler.importXML(xmlString)
        if (warnings && warnings.length) {
            warnings.forEach((warn) => console.warn(warn))
        }
    } catch (e) {
        console.error(`[Process Designer Warn]: ${typeof e === 'string' ? e : e.message}`)
    }
}