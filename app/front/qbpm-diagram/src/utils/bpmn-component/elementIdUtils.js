import modeler from '@/store/modeler.js';
import {notification} from "ant-design-vue";
import 'ant-design-vue/lib/notification/style/css';

export function getIdValue(element) {
    return element.businessObject.id
}

export function setIdValue(element, elementId) {
    const errorMsg = isIdValid(element.businessObject, elementId)

    if (errorMsg && errorMsg.length) {
        notification['error']({
            message: 'id 错误',
            description: errorMsg
        })
        return
    }

    const modelerStore = modeler()
    const modeling = modelerStore.getModeling

    modeling.updateProperties(element, {
        id: elementId
    })
}

const SPACE_REGEX = /\s/

// for QName validation as per http://www.w3.org/TR/REC-xml/#NT-NameChar
// | "-" | "." | [0-9] | #xB7 | [#x0300-#x036F] | [#x203F-#x2040]
const QNAME_REGEX = /^([a-z][\w-.]*:)?[a-z_][\w-.]*$/i

// for ID validation as per BPMN Schema (QName - Namespace)
const ID_REGEX = /^[a-z_][\w-.]*$/i

function containsSpace(value) {
    return SPACE_REGEX.test(value)
}

export function isIdValid(element, idValue) {
    const assigned = element.$model.ids.assigned(idValue)
    const idAlreadyExists = assigned && assigned !== element

    if (!idValue) {
        return 'ID 不能为空.'
    }

    if (idAlreadyExists) {
        return 'ID 必须是唯一的'
    }

    return validateId(idValue)
}
export function validateId(idValue) {
    if (containsSpace(idValue)) {
        return 'ID 不能包含空格'
    }

    if (!ID_REGEX.test(idValue)) {
        if (QNAME_REGEX.test(idValue)) {
            return 'ID 不能包含前缀'
        }

        return 'ID 必须符合 BPMN 规范'
    }
}

// 生成唯一性 ID
export function generateUniqueId(prefix){
    if(!prefix){
        prefix = 'qbpm'
    }
    return `${prefix}_${new Date().getTime()}`
}