import {createModdleElement} from '@/utils/bpmn-component/extensionElementUtils';
import {getBusinessObject} from 'bpmn-js/lib/util/ModelUtil';
import modeler from '@/store/modeler';
import editor from '@/store/editor';
import {without} from 'min-dash';

function getCounterSigns(element) {
    return getBusinessObject(element).get('counterSigns')
}

// 获取加签列表
export function getCounterSignList(element) {
    const counterSigns = getCounterSigns(element)
    return (counterSigns && counterSigns.signs) || []
}

// 加入新签
export function addCounterSign(element, counterSign) {
    const modelerStore = modeler()
    const editorStore = editor()

    const modeling = modelerStore.getModeling
    const prefix = editorStore.getProcessEngine
    // 判断 UserTask 是否有 CounterSigns
    let counterSignsElement = getCounterSigns(element)
    if (!counterSignsElement) {
        counterSignsElement = createModdleElement(`${prefix}:CounterSigns`,
            {signs: []},
            element)
        modeling.updateProperties(element, {
            counterSigns: counterSignsElement
        })
    }
    // 创建新属性并添加
    const newCounterSign = createModdleElement(`${prefix}:CounterSign`,
        {
            assignee: counterSign.assigneesEmail.join(','),
            assigneeType: counterSign.assigneeType,
            displayName: counterSign.displayName
        },
        counterSignsElement)
    modeling.updateModdleProperties(element, counterSignsElement, {
        signs: [...counterSignsElement?.get('signs'), newCounterSign]
    })
}

export function updateCounterSign(element, counterSign, index) {
    const modeling = modeler().getModeling
    const editorStore = editor()
    const counterSignsElement = getCounterSigns(element)
    const prefix = editorStore.getProcessEngine
    if (!counterSignsElement) {
        return
    }
    const counterSignList = counterSignsElement.get('signs')
    const newCounterSign = createModdleElement(`${prefix}:CounterSign`,
        {
            assignee: counterSign.assigneesEmail.join(','),
            assigneeType: counterSign.assigneeType,
            displayName: counterSign.displayName
        },
        counterSignsElement)
    counterSignList.splice(index, 1, newCounterSign)
    modeling.updateModdleProperties(element, counterSignsElement, {signs: counterSignList})
}

export function removeCounterSign(element, counterSign) {
    const modeling = modeler().getModeling
    const counterSignsElement = getCounterSigns(element)
    if (!counterSignsElement) {
        return
    }
    const counterSignList = without(counterSignsElement.get('signs'), counterSign)
    modeling.updateModdleProperties(element, counterSignsElement, {signs: counterSignList})
    if (!counterSignList || !counterSignList.length) {
        modeling.updateProperties(element, {
            counterSigns: undefined
        })
    }
}

// 清空 counterSigns 和子节点
export function removeAllCounterSigns(element) {
    const modeling = modeler().getModeling
    const counterSignsElement = getCounterSigns(element)
    if (!counterSignsElement) {
        return
    }
    modeling.updateProperties(element, {
        counterSigns: undefined
    })
}