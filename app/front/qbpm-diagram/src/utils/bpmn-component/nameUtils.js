import modeler from '@/store/modeler.js';
import {getBusinessObject, is} from 'bpmn-js/lib/util/ModelUtil';
import {isAny} from 'bpmn-js/lib/features/modeling/util/ModelingUtil';
import {add as collectionAdd} from 'diagram-js/lib/util/Collections';

// 根据元素获取 name 属性
export function getNameValue(element) {
    if (!element) {
        return undefined
    }
    if (isAny(element, ['bpmn:Collaboration', 'bpmn:DataAssociation', 'bpmn:Association'])) {
        return undefined
    }
    if (is(element, 'bpmn:TextAnnotation')) {
        return element.businessObject.text
    }
    if (is(element, 'bpmn:Group')) {
        const businessObject = getBusinessObject(element),
            categoryValueRef = businessObject?.categoryValueRef
        return categoryValueRef?.value
    }
    return element.businessObject.name
}

// 根据输入内容设置 name 属性
export function setNameValue(element, name) {
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    const canvas = modelerStore.getCanvas
    const bpmnFactory = modelerStore.getModeler?.get('bpmnFactory')

    if (isAny(element, ['bpmn:Collaboration', 'bpmn:DataAssociation', 'bpmn:Association'])) {
        return undefined
    }
    if (is(element, 'bpmn:TextAnnotation')) {
        return modeling?.updateProperties(element, {text: name})
    }
    if (is(element, 'bpmn:Group')) {
        const businessObject = getBusinessObject(element)
        const categoryValueRef = businessObject.categoryValueRef
        if (!categoryValueRef) {
            initializeCategory(businessObject, canvas?.getRootElement(), bpmnFactory)
        }
        return modeling?.updateLabel(element, name)
    }
    modeling?.updateProperties(element, {name: name})
}

function createCategoryValue(definitions, bpmnFactory) {
    const categoryValue = bpmnFactory.create('bpmn:CategoryValue')
    const category = bpmnFactory.create('bpmn:Category', {
        categoryValue: [categoryValue]
    })
    collectionAdd(definitions.get('rootElements'), category, undefined)
    getBusinessObject(category).$parent = definitions
    getBusinessObject(categoryValue).$parent = category

    return categoryValue
}

function initializeCategory(businessObject, rootElement, bpmnFactory) {
    const definitions = getBusinessObject(rootElement).$parent

    businessObject.categoryValueRef = createCategoryValue(definitions, bpmnFactory)
}
