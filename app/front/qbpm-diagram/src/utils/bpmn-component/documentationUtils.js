import modeler from '@/store/modeler.js';
import {getBusinessObject} from 'bpmn-js/lib/util/ModelUtil';
import {without} from 'min-dash';

export function setDocumentationValue(element, doc) {
    if (!element) {
        return
    }
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    const bpmnFactory = modelerStore.getModeler?.get('bpmnFactory')

    const businessObject = element.businessObject
    const documentation = findDocumentation(businessObject && businessObject.documentation)
    // (1) 更新或者移除 原有 documentation
    if (documentation) {
        if (doc) {
            return modeling.updateModdleProperties(element, documentation, { text: doc })
        } else {
            return modeling.updateModdleProperties(element, businessObject, {
                documentation: without(businessObject.documentation, documentation)
            })
        }
    }
    // (2) 创建新的 documentation
    if (doc) {
        const newDocumentation = bpmnFactory?.create('bpmn:Documentation', {
            text: doc
        })
        return modeling.updateModdleProperties(element, businessObject, {
            documentation: [...businessObject.documentation, newDocumentation]
        })
    }
}

export function getDocumentationValue(element){
    if(!element){
        return undefined
    }
    const documentation = getBusinessObject(element) && findDocumentation(getBusinessObject(element).documentation)
    return documentation && documentation.text
}

const DOCUMENTATION_TEXT_FORMAT = 'text/plain'
function findDocumentation(docs) {
    return docs.find(function (d) {
        return (d.textFormat || DOCUMENTATION_TEXT_FORMAT) === DOCUMENTATION_TEXT_FORMAT
    })
}