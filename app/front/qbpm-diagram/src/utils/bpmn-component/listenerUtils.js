import editor from '@/store/editor';
import modeler from '@/store/modeler';
import {getBusinessObject, isAny} from 'bpmn-js/lib/util/ModelUtil';
import {createModdleElement} from '@/utils/bpmn-component/extensionElementUtils';
import {without} from 'min-dash';

const listenerFireTypeOptions = [
    {label: '节点开始', value: 'begin'},
    {label: '节点结束', value: 'end'},
    {label: '节点任务分配', value: 'assign'},
    {label: '节点失败', value: 'completion'},
    {label: '节点完成', value: 'failure'}
]

const listenerEndEventFireTypeOptions = [
    {label: '流程成功', value: 'process.completion'},
    {label: '流程取消', value: 'process.cancellation'},
    {label: '流程失败', value: 'process.failure'},
    {label: '流程结束', value: 'process.end'},
]

const LISTENABLE_SOURCES = [
    'bpmn:ServiceTask',
    'bpmn:UserTask',
    'qbpm:SuggestTask',
    'bpmn:ExclusiveGateway',
    'bpmn:ParallelGateway',
    'bpmn:InclusiveGateway',
    'bpmn:StartEvent',
    'bpmn:EndEvent',
]

export function getFireTypeOptions(isEndEvent) {
    return isEndEvent ? listenerEndEventFireTypeOptions : listenerFireTypeOptions
}

export function getListeners(element) {
    return getBusinessObject(element).get('listener') || []
}

export function addListener(element, props) {
    const prefix = editor().getProcessEngine
    const modeling = modeler().getModeling
    const moddle = modeler().getModdle
    const {id, fire, actionRefs, params} = props
    const businessObject = getBusinessObject(element)
    const listenerElement = createModdleElement(`${prefix}:Listener`,
        {
            id,
            fire,
            actionRefs,
            params: []
        }, element)
    const paramElements = []
    if (params) {
        for (let paramName in params) {
            paramElements.push(moddle.create(`${prefix}:Param`, {name: paramName, value: params[paramName]}))
        }
        modeling.updateModdleProperties(element, getBusinessObject(listenerElement), {params: paramElements})
    }
    if (!businessObject.listener) {
        modeling.updateProperties(element, {listener: [listenerElement]})
    } else {
        modeling.updateProperties(element, {listener: [...businessObject.listener, listenerElement]})
    }
}

export function updateListener(element, newListener, index) {
    const modeling = modeler().getModeling
    const prefix = editor().getProcessEngine
    const moddle = modeler().getModdle
    const listeners = getListeners(element)
    const {id, fire, actionRefs, params} = newListener
    const listener = createModdleElement(`${prefix}:Listener`,
        {
            id,
            fire,
            actionRefs,
            params: []
        }, element)
    const paramElements = []
    if (params) {
        for (let paramName in params) {
            paramElements.push(moddle.create(`${prefix}:Param`, {name: paramName, value: params[paramName]}))
        }
        modeling.updateModdleProperties(element, getBusinessObject(listener), {params: paramElements})
    }
    listeners.splice(index, 1, listener)
    modeling.updateProperties(element, {listener: listeners})
}

export function removeListener(element, listener) {
    const modeling = modeler().getModeling
    const filtered = without(getListeners(element), listener)
    modeling.updateProperties(element, {listener: filtered})
}

export function isListenable(element) {
    return isAny(element, LISTENABLE_SOURCES)
}

export function uniqueListeners(listeners) {
    if (!Array.isArray(listeners)) {
        return []
    }
    // 相同的 fire 和 actionRefs 的 listener 去重
    return listeners.filter((item, index) => {
        return listeners.findIndex(element => element.fire === item.fire && element.actionRefs === item.actionRefs) === index
    })
}
