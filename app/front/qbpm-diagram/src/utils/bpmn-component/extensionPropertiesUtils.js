import {isAny} from 'bpmn-js/lib/util/ModelUtil.js';
import modeler from '@/store/modeler';
import {getBusinessObject, is} from 'bpmn-js/lib/util/ModelUtil';
import {isEndEvent} from '@/utils/bpmn-component/eventUtils';

const AVAILABLE_EXTENSION_PROPERTIES = [
    'bpmn:UserTask',
    'bpmn:ServiceTask',
    'qbpm:SuggestTask'
]

const assigneeTypeOptions = [
    {label: '静态(直接指定)', value: 'static'},
    {label: '动态(动态获取)', value: 'dynamic'},
    {label: '标签(预定义标签)', value: 'label'},
    {label: '经理(组织层级)', value: 'manager'},
]

export function canHaveExtensionProperties(element) {
    return isAny(element, AVAILABLE_EXTENSION_PROPERTIES)
}

export function isUserTask(element) {
    return is(element, 'bpmn:UserTask')
}

export function isServiceTask(element) {
    return is(element, 'bpmn:ServiceTask')
}

export function isSuggestTask(element) {
    return is(element, 'qbpm:SuggestTask')
}

export function getAssigneeTypeOptions() {
    return assigneeTypeOptions
}

export function setDisableNotify(element, disabled) {
    const modeling = modeler().getModeling
    // 目前只有 UserTask, SuggestTask 和 EndEvent 支持
    if (!modeling || !isUserTask(element) && !isSuggestTask(element) && !isEndEvent(element)) {
        return undefined
    }
    modeling.updateProperties(element, {
        disableNotify: disabled
    })
}

export function setMultiDecision(element, decision) {
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    if (!modeling || !canHaveExtensionProperties(element)) {
        return undefined
    }
    modeling.updateProperties(element, {
        multiDecision: decision
    })
}

export function getMultiDecision(element) {
    if (!element || !canHaveExtensionProperties(element)) {
        return undefined
    }
    return getBusinessObject(element).multiDecision
}

export function setMultiDecisionRate(element, decisionRate) {
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    if (!modeling || !canHaveExtensionProperties(element)) {
        return undefined
    }
    modeling.updateProperties(element, {
        multiDecisionRate: decisionRate
    })
}

export function getDisableNotify(element) {
    if (!element || !isUserTask(element) && !isSuggestTask(element) && !isEndEvent(element)) {
        return undefined
    }
    return getBusinessObject(element).disableNotify
}

export function getMultiDecisionRate(element) {
    if (!element || !canHaveExtensionProperties(element)) {
        return undefined
    }
    return getBusinessObject(element).multiDecisionRate
}

export function setMultiDecisionThreshold(element, decisionThreshold) {
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    if (!modeling || !canHaveExtensionProperties(element)) {
        return undefined
    }
    modeling.updateProperties(element, {
        multiDecisionThreshold: decisionThreshold
    })
}

export function getMultiDecisionThreshold(element) {
    if (!element || !canHaveExtensionProperties(element)) {
        return undefined
    }
    return getBusinessObject(element).multiDecisionThreshold
}

export function setIsSequential(element, isSequential) {
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    if (!modeling || !canHaveExtensionProperties(element)) {
        return undefined
    }
    modeling.updateProperties(element, {
        isSequential: isSequential
    })
}

export function getIsSequential(element) {
    if (!element || !canHaveExtensionProperties(element)) {
        return undefined
    }
    return getBusinessObject(element).isSequential
}

export function setAssigneeType(element, assigneeType) {
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    if (!modeling || !isUserTask(element) && !isSuggestTask(element)) {
        return undefined
    }
    modeling.updateProperties(element, {
        assigneeType: assigneeType
    })
}

export function getAssigneeType(element) {
    if (!element || !isUserTask(element) && !isSuggestTask(element)) {
        return undefined
    }
    return getBusinessObject(element).assigneeType
}

export function setAssignee(element, assignee) {
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    if (!modeling || !isUserTask(element) && !isSuggestTask(element)) {
        return undefined
    }
    modeling.updateProperties(element, {
        assignee: assignee
    })
}

export function getAssignee(element) {
    if (!element || !isUserTask(element) && !isSuggestTask(element)) {
        return undefined
    }

    return getBusinessObject(element).assignee
}

export function setActionRefValue(element, ref) {
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    if (!modeling || !isServiceTask(element)) {
        return undefined
    }
    modeling.updateProperties(element, {
        actionRef: ref
    })
}

export function getActionRefValue(element) {
    if (!element || !isServiceTask(element)) {
        return undefined
    }
    return getBusinessObject(element).actionRef
}

export function setAllowCounterSign(element, allow) {
    const modelerStore = modeler()
    const modeling = modelerStore.getModeling
    if (!modeling || !isUserTask(element)) {
        return undefined
    }
    modeling.updateProperties(element, {
        allowCounterSign: allow
    })
}

export function getAllowCounterSign(element) {
    if (!element || !isUserTask(element)) {
        return false
    }
    return getBusinessObject(element).allowCounterSign
}