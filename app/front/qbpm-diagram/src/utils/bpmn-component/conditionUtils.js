import {getBusinessObject, is, isAny} from 'bpmn-js/lib/util/ModelUtil.js';
import modeler from '@/store/modeler.js';
import {createModdleElement} from '@/utils/bpmn-component/extensionElementUtils.js';
import {getEventDefinition} from '@/utils/bpmn-component/eventUtils.js';

const CONDITION_SOURCES = [
    'bpmn:Activity',
    'bpmn:ExclusiveGateway',
    'bpmn:InclusiveGateway',
    'bpmn:ComplexGateway'
]

const conditionTypeOptions = [
    {label: '无', value: 'none'},
    {label: '默认', value: 'default'},
    {label: '条件表达式', value: 'expression'}
]

// 父节点符合条件的 sequence flow
export function isConditionalSource(element) {
    return isAny(element, CONDITION_SOURCES)
}

export function isConditionalEventDefinition(element) {
    return is(element, 'bpmn:Event') && !!getEventDefinition(element, 'bpmn:ConditionalEventDefinition')
}

// 是否可以设置条件表达式
export function isConditional(element) {
    let isConditionalSequenceFlow = is(element, 'bpmn:SequenceFlow') && isConditionalSource(element.source)
    return isConditionalSequenceFlow || isConditionalEventDefinition(element)
}

// 获取 sequence flow 条件类型
export function getConditionTypeValue(element) {
    if (!element) {
        return 'none'
    }
    const conditionExpression = getConditionExpression(element)
    if (conditionExpression) {
        return 'expression'
    }
    if (getBusinessObject(element.source)?.default === element.businessObject
        && getBusinessObject(element.source)?.defaultFlow) {
        return 'default'
    }
    return 'none'
}

// 设置 sequence flow 条件类型
export function setConditionTypeValue(element, type) {
    if (!type || type === 'none' || type === 'default') {
        updateCondition(element)
        return setDefaultCondition(element, type === 'default')
    }
    const parent = is(element, 'bpmn:SequenceFlow') ? getBusinessObject(element) :
        (getConditionalEventDefinition(element))
    const formalExpressionElement = createModdleElement('bpmn:FormalExpression', '', parent)
    updateCondition(element, formalExpressionElement)
}

export function setConditionExpressionValue(element, body) {
    const parent = is(element, 'bpmn:SequenceFlow')
        ? getBusinessObject(element)
        : getConditionalEventDefinition(element)
    const formalExpressionElement = createModdleElement('bpmn:FormalExpression', {body}, parent)
    updateCondition(element, formalExpressionElement)
}

export function getConditionExpressionValue(element) {
    const conditionExpression = getConditionExpression(element)
    if (conditionExpression) {
        return conditionExpression.get('body')
    }
}

export function getConditionTypeOptions(element) {
    if (is(element, 'bpmn:SequenceFlow')) {
        return conditionTypeOptions
    }
    return conditionTypeOptions.filter((condition) => condition.value !== 'default')
}

function getConditionalEventDefinition(element) {
    if (!is(element, 'bpmn:Event')) return false
    return getEventDefinition(element, 'bpmn:ConditionalEventDefinition')
}

//获取给定元素的条件表达式的值
function getConditionExpression(element) {
    const businessObject = getBusinessObject(element)
    if (is(businessObject, 'bpmn:SequenceFlow')) {
        return businessObject['conditionExpression']
    }
    if (getConditionalEventDefinition(businessObject)) {
        return getConditionalEventDefinition(businessObject).get('condition')
    }
}


function updateCondition(element, condition) {
    const modeling = modeler().getModeling
    if (is(element, 'bpmn:SequenceFlow')) {
        modeling.updateProperties(element, {conditionExpression: condition})
    } else {
        modeling.updateModdleProperties(element, getConditionalEventDefinition(element), {condition})
    }
}

//设置为默认 flow
function setDefaultCondition(element, isDefault) {
    const modeling = modeler().getModeling
    modeling.updateProperties(element.source, {
        defaultFlow: isDefault ? element : undefined,
        default: isDefault ? element : undefined
    })
}
