import {createModdleElement} from '@/utils/bpmn-component/extensionElementUtils.js';
import {getBusinessObject} from 'bpmn-js/lib/util/ModelUtil';
import modeler from '@/store/modeler.js';
import editor from '@/store/editor.js';

function getCounterSignTypes(element) {
    return getBusinessObject(element).get('counterSignTypes')
}

export function getCounterSignTypeList(element) {
    const counterSignTypes = getCounterSignTypes(element)
    return (counterSignTypes && counterSignTypes.types) || []
}

export function updateCounterSignTypes(element, signTypes) {
    const prefix = editor().getProcessEngine
    const modeling = modeler().getModeling
    let counterSignTypesElement = getCounterSignTypes(element)
    if (!counterSignTypesElement) {
        counterSignTypesElement = createModdleElement(`${prefix}:CounterSignTypes`,
            {types: []}, element)
        modeling.updateProperties(element, {
            counterSignTypes: counterSignTypesElement
        })
    }
    // 创建新属性并添加
    const counterSignTypeElements = []
    signTypes.forEach(type => {
        const newCounterSignTypeElement = createModdleElement(`${prefix}:CounterSignType`,
            {
                body: type
            },
            counterSignTypesElement)
        counterSignTypeElements.push(newCounterSignTypeElement)
    })
    modeling.updateModdleProperties(element, counterSignTypesElement, {
        types: counterSignTypeElements
    })
}

export function removeAllCounterSignTypes(element) {
    const modeling = modeler().getModeling
    const counterSignTypesElement = getCounterSignTypes(element)
    if (counterSignTypesElement) {
        modeling.updateProperties(element, {
            counterSignTypes: undefined
        })
    }
}