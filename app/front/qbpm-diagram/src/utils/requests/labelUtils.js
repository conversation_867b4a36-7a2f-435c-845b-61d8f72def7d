import {notification} from 'ant-design-vue';
import 'ant-design-vue/lib/notification/style/css';
import httpRequest from '@/utils/requests/httpRequest.js';

export async function getLabelOptions() {
    let labels = []
    await httpRequest.GET('/admin/labels')
        .then(response => {
            labels = response
        })
        .catch(e => notification.error({
            message: '获取标签失败',
            description: e.message || JSON.stringify(e)
        }))
    return labels
}