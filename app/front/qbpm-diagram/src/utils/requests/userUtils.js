// 从后端获取用户信息
import {notification} from 'ant-design-vue';
import 'ant-design-vue/lib/notification/style/css';
import httpRequest from '@/utils/requests/httpRequest.js';

export async function getUserOptions() {
    let users = []
    await httpRequest.GET('/admin/users').then(
        response => {
            users = response
        }
    ).catch(e => notification.error({
        message: '获取用户信息失败',
        description: e.message || JSON.stringify(e)
    }))
    return users
}