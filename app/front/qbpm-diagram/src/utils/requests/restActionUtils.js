// 从后端获取 restAction
import {notification} from 'ant-design-vue';
import 'ant-design-vue/lib/notification/style/css';
import httpRequest from '@/utils/requests/httpRequest.js';
import axios from "axios";

export async function getActionOptions() {
    let restActions = []
    // actions 接口直接返回的 json array，因此不能使用封装了的 httpRequest
    await axios.get(`${httpRequest.qbpmBaseURL}/system/actions`).then(
        response => {
            restActions = response.data
        }
    ).catch(e => notification.error({
        message: '获取执行器失败',
        description: e.message || e
    }))
    return restActions
}