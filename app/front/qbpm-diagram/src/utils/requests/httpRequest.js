import axios from 'axios';

const gaeaBaseURL = '/api/proxy/gaea/api'
const qbpmBaseURL = '/api/proxy/qbpm/api'

const httpService = axios.create({
    timeout: 6000,
    withCredentials: true,
})

httpService.interceptors.response.use(response => {
    const resp = response.data
    if (resp.code === 200) {
        return resp.data
    }
    return Promise.reject(new Error(resp.message))
})

export default {
    gaeaBaseURL: gaeaBaseURL,

    qbpmBaseURL: qbpmBaseURL,

    GET: (uri, payload = {}, bURL = gaeaBaseURL, config={}) => {
        return new Promise((resolve, reject) => {
            httpService.get(bURL + uri, {
                params: payload,
                ...config
            }).then(res => {
                resolve(res);
            }).catch(err => {
                reject(err);
            });
        })
    },

    POST: (uri, payload = {}, bURL = gaeaBaseURL, config={}) => {
        return new Promise((resolve, reject) => {
            httpService.post(bURL + uri, payload, config).then(res => {
                resolve(res);
            }).catch(err => {
                reject(err);
            });
        })
    },
}
