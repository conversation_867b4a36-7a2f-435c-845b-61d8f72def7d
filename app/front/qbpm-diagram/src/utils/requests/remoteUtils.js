// 从后端获取 bpmn + 向后端发送 bpmn
import {notification} from 'ant-design-vue';
import 'ant-design-vue/lib/notification/style/css';
import httpRequest from '@/utils/requests/httpRequest.js';
import axios from 'axios';

export async function uploadProcessDefinition(uploadForm, xml) {
    const form = new FormData()
    form.append('key', uploadForm.key)
    form.append('name', uploadForm.name)
    form.append('description', uploadForm.desc)
    const blob = new Blob([xml], {type: 'text/xml'})
    form.append('data', blob, `${uploadForm.name}.bpmn`)
    return await axios.post(`${httpRequest.qbpmBaseURL}/repository/process-definitions`,
        form,
        {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
}

export async function getProcessDefinitionsByName(processName, currentPage, pageSize) {
    let processDefinitions = []
    await axios.get(`${httpRequest.qbpmBaseURL}/repository/process-definitions/name/search`, {
        params: {
            name: processName,
            page: currentPage,
            page_size: pageSize,
        }
    }).then(response => {
        if(response.data.code){
            throw response.data.message
        }
        processDefinitions = response.data
    }).catch(e => notification.error({
        message: '获取已有流程图失败',
        description: e.message || JSON.stringify(e)
    }))
    return processDefinitions
}
