<template>
  <Toolbar></Toolbar>
  <div class="designer-container">
    <div class="main-content">
      <Designer v-model:xml="bpmnXML"></Designer>
      <Panel></Panel>
    </div>
<Setting></Setting>
  </div>
</template>

<script>
import Designer from '@/components/Designer.vue'
import Setting from '@/components/Setting.vue'
import Toolbar from '@/components/toolbar/Toolbar.vue'
import {defineComponent, ref} from 'vue';
import Panel from '@/components/panel/Panel.vue';

export default defineComponent({
  name: 'App',
  components: {
    Panel, Designer, Setting, Toolbar
  },
  setup() {
    const bpmnXML = ref(undefined)
    return {
      bpmnXML
    }
  }
})
</script>

<style scoped>
.designer-container .main-content {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: flex;
}
</style>
