<template>
  <div ref="designer" class="designer"/>
</template>

<script>
import editor from '@/store/editor.js';
import {storeToRefs} from 'pinia';
import {defineComponent, nextTick, ref, toRefs, watch} from 'vue';
import {createNewDiagram, initModeler, registerModules} from '@/utils/bpmn-component/designerUtils.js';
import _ from 'lodash';

export default defineComponent({
  name: 'Designer',
  props: {
    xml: {
      default: undefined
    }
  },
  emits: ['update:xml', 'command-stack-changed'],
  setup(props, {emit}) {
    const editorStore = editor()
    const {editorSettings} = storeToRefs(editorStore)
    const {xml} = toRefs(props)
    const designer = ref(null)
    watch(
        () => [editorSettings.value.miniMap, editorSettings.value.background],
        async (newValue, oldValue) => {
          // watch 数组可能出现 newValue 和 oldValue 值相同的情况
          if (!_.isEqual(newValue, oldValue)) {
            try {
              const modelerModules = registerModules(editorSettings)
              await nextTick()
              initModeler(designer, modelerModules, emit)

              await createNewDiagram(xml.value, editorSettings.value)
            } catch (e) {
              console.log(e)
            }
          }
        },
        {immediate: true}
    )
    return {
      designer
    }
  }
})
</script>

<style scoped>
.djs-minimap div.toggle {
  display: none;
}

.designer {
  flex: 1
}
</style>