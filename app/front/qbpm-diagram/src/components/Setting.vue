<template>
  <AButton type="primary" class="settings-button" @click="showDrawer">
    <SettingOutlined/>
  </AButton>
  <ADrawer title="偏好设置" :visible="settingsDrawerVisibility" @close="closeDrawer" placement="right">
    <AForm layout="horizontal">
      <AFormItem v-model:model="editorSettings" label="缩略图显示" name="miniMap">
        <ASwitch v-model:checked="editorSettings.miniMap"></ASwitch>
      </AFormItem>
      <AFormItem label="背景设置" name="background">
        <ASelect v-model:value="editorSettings.background">
          <ASelectOption value="grid">网格</ASelectOption>
          <ASelectOption value="none">无</ASelectOption>
        </ASelect>
      </AFormItem>
    </AForm>
  </ADrawer>
</template>

<script>
import editor from '@/store/editor';
import {defineComponent, reactive, ref, toRaw, watch} from 'vue';
import {SettingOutlined} from '@ant-design/icons-vue';
import {defaultSettings} from '@/config/editorDefaultSettings';

export default defineComponent({
  name: 'Setting',
  components: {
    SettingOutlined
  },
  setup() {
    const settingsDrawerVisibility = ref(false)
    const editorStore = editor()
    const editorSettings = reactive(defaultSettings)
    const showDrawer = () => {
      settingsDrawerVisibility.value = true
    }
    const closeDrawer = () => {
      settingsDrawerVisibility.value = false
    }
    watch(
        () => editorSettings,
        () => editorSettings && editorStore.updateConfiguration(toRaw(editorSettings)),
        {deep: true}
    )
    return {
      editorSettings,
      settingsDrawerVisibility,
      showDrawer,
      closeDrawer
    }
  }

})
</script>

<style scoped>
.settings-button {
  position: absolute;
  right: 0;
  width: 36px;
  height: 40px;
  padding: 8px;
  bottom: 20vh;
  z-index: 1000;
}
</style>
