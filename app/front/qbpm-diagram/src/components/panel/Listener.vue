<template>
  <ACollapsePanel name="listener">
    <template #header>
      <p>监听器</p>
    </template>
    <template #extra>
      <ATag type="success">{{ listeners.length }}</ATag>
    </template>
    <div class="element-listeners">
      <AFormItem v-if="canHaveEnableNotify" label="自动添加审批通知">
        <ASwitch v-model:checked="enableAutoNotify" @change="updateEnableAutoNotify"/>
      </AFormItem>
      <ATable :columns="listenerColumns" :data-source="listeners">
        <template #bodyCell="{column, record, index}">
          <template v-if="column.key==='operations'">
            <AButton type="link" @click="openListenerModal(record.key, record)">编辑</AButton>
            <AButton type="text" danger @click="removeCurrentListener(record.key)">删除</AButton>
          </template>
        </template>
      </ATable>
      <AButton type="primary" class="large-button" @click="openListenerModal(-1, undefined)">
        <PlusOutlined/>
        <span>添加监听器</span>
      </AButton>
    </div>
    <AModal v-model:visible="modalVisibility" title="操作监听器" :style="{ width: '640px' }"
            ok-text="确认" cancel-text="取消"
            @ok="saveListener">
      <AForm ref="listenerFormRef" :model="listenerForm" :rules="listenerFormRules" class="need-filled">
        <AFormItem name="listenerID" label="监听器ID">
          <AInput :disabled="true" v-model:value="listenerForm.id"></AInput>
        </AFormItem>
        <AFormItem name="fire" label="触发类型( fire )">
          <ASelect v-model:value="listenerForm.fire" :options="fireTypeOptions"></ASelect>
        </AFormItem>
        <AFormItem name="actionRefs" label="执行器( actionRef )">
          <ASelect v-model:value="listenerForm.actionRefs" @change="updateListenerActionRefs">
            <ASelectOption v-for="item in actionOptions" :key="item.id" :value="item.code">
              {{ item.name }}
            </ASelectOption>
          </ASelect>
        </AFormItem>
        <AFormItem v-for="(param,index) in actionParams" :key="index" :label="`参数 ${param}`" :name="param">
          <AInput v-model:value="listenerForm.params[param]"></AInput>
        </AFormItem>
      </AForm>
    </AModal>
  </ACollapsePanel>
</template>

<script>
import {computed, defineComponent, markRaw, nextTick, onMounted, ref} from 'vue';
import {PlusOutlined} from '@ant-design/icons-vue';
import modeler from '@/store/modeler';
import {
  addListener,
  getFireTypeOptions,
  getListeners,
  removeListener,
  uniqueListeners,
  updateListener
} from '@/utils/bpmn-component/listenerUtils';
import {getActionOptions} from '@/utils/requests/restActionUtils';
import {message} from 'ant-design-vue';
import 'ant-design-vue/lib/message/style/css';
import EventEmitter from '@/utils/eventEmitter';
import {isEndEvent} from "@/utils/bpmn-component/eventUtils";
import {generateUniqueId} from '@/utils/bpmn-component/elementIdUtils';
import {getDisableNotify, isUserTask, isSuggestTask, setDisableNotify} from '@/utils/bpmn-component/extensionPropertiesUtils';

export default defineComponent({
  name: 'Listener',
  components: {
    PlusOutlined
  },
  setup() {
    const listeners = ref([])
    let listenersRaw = markRaw([])
    const modalVisibility = ref(false)
    const modelerStore = modeler()
    const activeElement = computed(() => modelerStore.getActiveElement)
    let activeIndex = -1
    const listenerForm = ref({
      id: '',
      fire: '',
      actionRefs: '',
      params: {}
    })
    const listenerFormRef = ref()
    let actionParams = ref([])
    const actionOptions = []
    const fireTypeOptions = ref([])
    const canHaveEnableNotify = computed(() =>
        isEndEvent(activeElement.value) || isUserTask(activeElement.value) || isSuggestTask(activeElement.value)
    )
    const enableAutoNotify = ref(true)

    onMounted(async () => {
      actionOptions.push(...await getActionOptions())
      fireTypeOptions.value = getFireTypeOptions(isEndEvent(activeElement.value))
      reloadListeners()
      EventEmitter.on('element-update', reloadListeners)
    })

    const listenerFormRules = {
      fire: {required: true, trigger: ['blur', 'change'], message: '触发类型不能为空'},
      actionRefs: {required: true, trigger: ['blur', 'change'], message: '执行器不能为空'}
    }

    const listenerColumns = [
      {title: '触发类型', dataIndex: 'fire', width: 90},
      {title: '触发器名称', dataIndex: ['restAction', 'name'], ellipsis: {showTitle: true}},
      {title: '操作', key: 'operations', align: 'center', width: 100}
    ]

    const updateListenerId = (id) => {
      listenerForm.value.id = id
    }

    const updateListenerFireType = (fireType) => {
      const ft = fireTypeOptions.value.find(e=>e.label === fireType)
      if(ft){
        listenerForm.value.fire = ft.value
      }else {
        listenerForm.value.fire = undefined
      }
    }

    const updateEnableAutoNotify = (enabled) => {
      setDisableNotify(activeElement.value, !enabled)
    }

    const updateListenerActionRefs = (restActionCode) => {
      listenerForm.value.actionRefs = restActionCode
      actionParams.value = []
      // 获取 restAction 的参数
      let action = actionOptions.find(item => item.code === restActionCode)
      if (action && action.params) {
        actionParams.value = action.params
      }
      // 没有 params 时清空表单参数对象
      if (action && !action.params) {
        listenerForm.value.params = {}
      }
    }

    const updateListenerActionParams = (actionParams) => {
      listenerForm.value.params = actionParams
    }

    const openListenerModal = async (rowIndex, rowData) => {
      activeIndex = rowIndex
      updateListenerId(rowData?.id || generateUniqueId('listener'))
      updateListenerFireType(rowData?.fire || '')
      updateListenerActionRefs(rowData?.restAction.code || '')
      updateListenerActionParams(rowData?.restAction.params || {})
      modalVisibility.value = true
      await nextTick()
      listenerFormRef.value && listenerFormRef.value.clearValidate()
    }

    const saveListener = async () => {
      await listenerFormRef.value.validateFields()

      // 判断是否已经重复, 如果有了只能修改参数
      const list = markRaw(getListeners(activeElement.value)).map(item => ({
        id: item.id,
        fire: item.fire,
        actionRefs: item.actionRefs
      }))

      const actionExists = list.some(item => item.fire === listenerForm.value.fire &&
          item.actionRefs === listenerForm.value.actionRefs)

      // 允许添加
      if (activeIndex < 0 && !actionExists) {
        addListener(activeElement.value, listenerForm.value)
      } else if (activeIndex >= 0) {
        updateListener(activeElement.value, listenerForm.value, activeIndex)
      } else {
        message.error('监听器已存在')
      }
      reloadListeners()
    }

    const removeCurrentListener = (index) => {
      const listener = listenersRaw[index]
      removeListener(activeElement.value, listener)
      reloadListeners()
    }

    const reloadListeners = () => {
      modalVisibility.value = false
      fireTypeOptions.value = getFireTypeOptions(isEndEvent(activeElement.value))
      enableAutoNotify.value = !getDisableNotify(activeElement.value)
      updateListenerId('')
      updateListenerFireType('')
      updateListenerActionRefs('')
      updateListenerActionParams({})
      // 查询并去重
      listenersRaw = uniqueListeners(markRaw(getListeners(activeElement.value)))
      const list = listenersRaw.map(
          (item, index) => ({
                ...item,
                key: index,
                id: item.id,
                fire: fireTypeOptions.value.find(option => option.value === item.fire)?.label,
                restAction: {
                  code: actionOptions.find(action => action.code === item.actionRefs)?.code,
                  name: actionOptions.find(action => action.code === item.actionRefs)?.name,
                  params: item.params?.reduce((map, obj) => {
                    map[obj.name] = obj.value
                    return map
                  }, {}) || {}
                }
              }
          )
      )
      listeners.value = JSON.parse(JSON.stringify(list))
    }


    return {
      listeners,
      listenerColumns,
      modalVisibility,
      listenerForm,
      listenerFormRules,
      listenerFormRef,
      fireTypeOptions,
      actionOptions,
      actionParams,
      enableAutoNotify,
      canHaveEnableNotify,
      openListenerModal,
      removeCurrentListener,
      saveListener,
      updateListenerActionRefs,
      updateEnableAutoNotify,
    }
  }
})
</script>

<style scoped>
.need-filled {
  height: 520px;
}
</style>
