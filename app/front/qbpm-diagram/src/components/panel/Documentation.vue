<template>
  <ACollapsePanel name="documentation">
    <template #header>
      <p>说明信息</p>
    </template>
    <AFormItem label="内容">
      <ATextarea v-model:value="documentation" @change="updateDocumentation"></ATextarea>
    </AFormItem>
  </ACollapsePanel>
</template>

<script>
import {computed, defineComponent, onMounted, ref} from 'vue';
import modeler from '@/store/modeler.js';
import EventEmitter from '@/utils/eventEmitter.js';
import {getDocumentationValue, setDocumentationValue} from '@/utils/bpmn-component/documentationUtils.js';

export default defineComponent({
  name: 'Documentation',
  setup() {
    const documentation = ref('')
    const modelerStore = modeler()
    const activeElement = computed(()=>modelerStore.getActiveElement)

    onMounted(() => {
      documentation.value = getDocumentationValue(activeElement.value) || ''
      EventEmitter.on('element-update', () => {
        documentation.value = getDocumentationValue(activeElement.value) || ''
      })
    })

    const updateDocumentation = (e) => {
      let doc = e.target.value
      setDocumentationValue(activeElement.value, doc)
    }

    return {
      documentation,
      updateDocumentation
    }
  }
})
</script>

<style scoped>

</style>