<template>
  <ACollapsePanel name="conditionExpression">
    <template #header>
      <p>条件表达式</p>
    </template>
    <div class="element-condition">
      <AFormItem label="条件类型">
        <ASelect v-model:value="condition.conditionType" :options="conditionTypeOptions"
                 @change="updateConditionType"></ASelect>
      </AFormItem>
      <AFormItem label="表达式内容" v-if="condition.conditionType && condition.conditionType==='expression'">
        <ARow>
          <Codemirror v-model:value="condition.conditionExpression" :options="codeOptions" border class="code-mirror"
                      @change="updateConditionExpression">
          </Codemirror>
        </ARow>
      </AFormItem>
    </div>
  </ACollapsePanel>
</template>

<script>
import {computed, defineComponent, onMounted, ref} from 'vue';
import modeler from '@/store/modeler';
import {
  getConditionExpressionValue, getConditionTypeOptions, getConditionTypeValue,
  setConditionExpressionValue,
  setConditionTypeValue
} from '@/utils/bpmn-component/conditionUtils';
import EventEmitter from '@/utils/eventEmitter';
import 'codemirror/mode/javascript/javascript';
import 'codemirror/theme/darcula.css';
import Codemirror from 'codemirror-editor-vue3';

export default defineComponent({
  name: 'ConditionExpression',
  components: {
    Codemirror
  },
  setup() {
    const modelerStore = modeler()
    const condition = ref({
      conditionType: 'none',
      conditionExpression: ''
    })
    const codeOptions = ref({
      mode: 'text/javascript',
      lineNumbers: false,
      theme: 'darcula',
      smartIndent: true
    })
    const conditionTypeOptions = ref([])
    const activeElement = computed(() => modelerStore.getActiveElement)

    const updateConditionType = (type) => {
      setConditionTypeValue(activeElement.value, type)
    }

    const getConditionType = () => {
      condition.value.conditionType = getConditionTypeValue(activeElement.value)
      condition.value.conditionType === 'expression' && getConditionExpression()
    }

    const updateConditionExpression = (expression) => {
      setConditionExpressionValue(activeElement.value, expression)
    }

    const getConditionExpression = () => {
      condition.value.conditionExpression = getConditionExpressionValue(activeElement.value)
    }

    onMounted(() => {
      getConditionType()
      conditionTypeOptions.value = getConditionTypeOptions(activeElement.value)
      EventEmitter.on('element-update', () => {
        conditionTypeOptions.value = getConditionTypeOptions(activeElement.value)
        getConditionType()
      })
    })

    return {
      condition,
      conditionTypeOptions,
      codeOptions,
      updateConditionType,
      updateConditionExpression,
    }
  }

})
</script>

<style scoped>
.code-mirror {
  font-size: 15px;
  overflow: auto;
}
</style>