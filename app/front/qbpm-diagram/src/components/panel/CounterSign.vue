<template>
  <AFormItem name="counterSignType" label="加签类型">
    <ASelect v-model:value="counterSignType" @change="updateCounterSignType"
             :options="counterSignTypeOptions" mode="multiple"></ASelect>
  </AFormItem>
  <ATable :columns="counterSignColumns" :data-source="counterSignList">
    <template #bodyCell="{column, record, index}">
      <template v-if="column.key==='operations'">
        <AButton type="link" @click="openCounterSignModal(record.key, record)">编辑</AButton>
        <AButton type="text" danger @click="removeCurrentCounterSign(record.key)">删除</AButton>
      </template>
    </template>
  </ATable>
  <AButton type="primary" class="large-button" @click="openCounterSignModal(-1, undefined)">
    <PlusOutlined/>
    <span>添加CounterSign</span>
  </AButton>
  <AModal v-model:visible="modalVisibility" title="操作CounterSign" :style="{ width: '640px' }"
          ok-text="确认" cancel-text="取消"
          @ok="saveCounterSign">
    <AForm ref="counterSignFormRef" :model="counterSignForm" :rules="counterSignFormRules" class="need-filled">
      <AFormItem name="assigneeType" label="审批人设置方式">
        <ASelect v-model:value="counterSignForm.assigneeType" @change="updateAssigneeType"
                 :options="assigneeTypeOptions"></ASelect>
      </AFormItem>
      <AFormItem name="assignees" label="审批人"
                 v-if="['static', 'label', 'dynamic'].includes(counterSignForm.assigneeType)">
        <ASelect v-if="counterSignForm.assigneeType==='static'" v-model:value="assignees" @change="updateAssignee"
                 show-search mode="tags" :token-separators="[',']"
                 placeholder="若选项中找不到审批人请输入邮箱；多选请用英文逗号分隔">
          <ASelectOption v-for="item in assigneeOptions" :key="item.id" :value="item.email">
            {{ item.email }}
          </ASelectOption>
        </ASelect>
        <ASelect v-if="counterSignForm.assigneeType==='label'" v-model:value="assignees" @change="updateAssignee">
          <ASelectOption v-for="item in labelOptions" :key="item.id" :value="item.id">
            {{ item.name }}
          </ASelectOption>
        </ASelect>
        <ASelect show-search v-if="counterSignForm.assigneeType==='dynamic'" v-model:value="assignees"
                 :options="actionOptions.map(item=>({value:item.code, label: item.name}))"
                 :filter-option="filterSelectOptions"
                 @change="updateAssignee">
        </ASelect>
      </AFormItem>
      <AFormItem name="displayName" label="展示名">
        <AInput v-model:value="counterSignForm.displayName"></AInput>
      </AFormItem>
    </AForm>
  </AModal>
</template>

<script>
import {computed, defineComponent, markRaw, nextTick, onMounted, reactive, ref} from 'vue';
import {PlusOutlined} from '@ant-design/icons-vue';
import modeler from '@/store/modeler';
import {getUserOptions} from '@/utils/requests/userUtils';
import {
  addCounterSign,
  updateCounterSign,
  removeCounterSign,
  getCounterSignList
} from '@/utils/bpmn-component/counterSignUtils';
import EventEmitter from '@/utils/eventEmitter';
import {
  getCounterSignTypeList,
  updateCounterSignTypes
} from '@/utils/bpmn-component/counterSignTypeUtils';
import {getAssigneeTypeOptions} from '@/utils/bpmn-component/extensionPropertiesUtils';
import {getLabelOptions} from '@/utils/requests/labelUtils';
import {isArray} from 'min-dash';
import {getActionOptions} from '@/utils/requests/restActionUtils';

export default defineComponent({
  name: 'CounterSign',
  components: {
    PlusOutlined
  },
  setup() {
    const modelerStore = modeler()
    const modalVisibility = ref(false)
    const assignees = ref([])
    const counterSignForm = reactive({
      assigneesEmail: [],
      assigneeType: '',
      displayName: ''
    })
    const counterSignFormRef = ref()
    let counterSignsRaw = markRaw([])
    const counterSignList = ref([])
    const counterSignType = ref([])
    let activeIndex = -1

    const activeElement = computed(() => modelerStore.getActiveElement)

    const assigneeTypeOptions = getAssigneeTypeOptions()

    const counterSignTypeOptions = [
      {label: '前加签', value: 1},
      {label: '后加签', value: 2},
      {label: '并加签', value: 3},
    ]

    const counterSignFormRules = {
      displayName: {required: true, trigger: ['blur', 'change'], message: 'counterSign展示名必填'}
    }

    const counterSignColumns = [
      {title: '节点名', dataIndex: 'displayName', width: 120},
      {title: '审批人', dataIndex: 'assignees', ellipsis: true},
      {title: '获取类型', dataIndex: 'assigneeType', width: 90},
      {title: '操作', key: 'operations', align: 'center', width: 90}
    ]

    let assigneeOptions = []
    let labelOptions = []
    let actionOptions = []
    onMounted(async () => {
      assigneeOptions.push(...await getUserOptions())
      labelOptions.push(...await getLabelOptions())
      actionOptions.push(...await getActionOptions())
      reloadCounterSigns()
      EventEmitter.on('element-update', reloadCounterSigns)
    })

    const updateCounterSignType = (counterSignTypes) => {
      updateCounterSignTypes(activeElement.value, counterSignTypes)
    }

    const updateAssigneeType = () => {
      assignees.value = []
      counterSignForm.assigneesEmail = []
    }

    const updateAssignee = (assignee) => {
      if (isArray(assignee)) {
        const assigneeEmailList = assigneeOptions.filter(item => assignee.includes(item.email))
            .map(item => item.email)
        // 如果输入的是远程没有的
        if (assignee.length !== assigneeEmailList.length) {
          const nonexistentAssignee = assignee.filter(item => !assigneeOptions.map(e => e.email).includes(item))
          assigneeEmailList.push(nonexistentAssignee)
        }
        counterSignForm.assigneesEmail = assigneeEmailList
        return
      }
      counterSignForm.assigneesEmail = [assignee]
    }

    const openCounterSignModal = async (rowIndex, rowData) => {
      activeIndex = rowIndex
      counterSignForm.displayName = rowData && rowData.displayName || ''
      counterSignForm.assigneeType = rowData && rowData.assigneeType || 'static'
      if (!rowData) {
        assignees.value = []
        modalVisibility.value = true
        await nextTick()
        return
      }
      if (counterSignForm.assigneeType === 'label') {
        assignees.value = labelOptions.filter(e => e.name === rowData.assignees).map(e => e.id)
      }else if(counterSignForm.assigneeType === 'dynamic') {
        assignees.value = actionOptions.filter(e=>e.name === rowData.assignees).map(e=>e.code)
      }else {
        assignees.value = rowData.assignees.split(",")
      }
      counterSignForm.assigneesEmail = isArray(assignees.value)?assignees.value: [assignees.value]
      modalVisibility.value = true
      await nextTick()
    }

    const saveCounterSign = async () => {
      await counterSignFormRef.value.validateFields()
      if (activeIndex < 0) {
        addCounterSign(activeElement.value, counterSignForm)
      } else {
        updateCounterSign(activeElement.value, counterSignForm, activeIndex)
      }
      reloadCounterSigns()
    }

    const removeCurrentCounterSign = (index) => {
      removeCounterSign(activeElement.value, counterSignsRaw[index])
      reloadCounterSigns()
    }

    const reloadCounterSigns = () => {
      modalVisibility.value = false
      assignees.value = []
      counterSignForm.assigneesEmail = []
      counterSignForm.assigneeType = 'static'
      counterSignForm.displayName = ''
      counterSignType.value = getCounterSignTypeList(activeElement.value).map(item => item.body)
      counterSignsRaw = getCounterSignList(activeElement.value)
      const list = counterSignsRaw.map((item, index) => {
        let assigneeList = []
        // 如果是 label 类型，table 展示的就是 xml 的 assignees
        if (item.assigneeType === 'static') {
          const assignee = assigneeOptions.filter(e => item.assignee.split(',').includes(e.email))
          assigneeList = assignee.map(e => e.email)
          if (assigneeList.length < item.assignee.length) {
            const unknownEmails = item.assignee.split(',').filter(e => !assignee.map(a => a.email).includes(e))
            if (unknownEmails) {
              assigneeList.push(...unknownEmails)
            }
          }
        }
        // 如果是 label 类型，table 展示 label 名，xml assignees 存的是 label id
        if (item.assigneeType === 'label') {
          const assignee = labelOptions.filter(e => item.assignee.split(",").includes(e.id))
          assigneeList = assignee.map(e => e.name)
        }

        // 如果是 dynamic 类型，table 展示 action 名，xml assignees 存的是 action code
        if (item.assigneeType === 'dynamic') {
          const assignee = actionOptions.filter(e => item.assignee.split(",").includes(e.code))
          assigneeList = assignee.map(e => e.name)
        }

        return {
          key: index,
          assignees: assigneeList.join(',') || '--',
          assigneeType: item.assigneeType,
          displayName: item.displayName
        }
      })
      counterSignList.value = JSON.parse(JSON.stringify(list))
    }

    const filterSelectOptions = (inputValue, option) => {
      return option.label.indexOf(inputValue) >= 0 || option.value.indexOf(inputValue) >= 0
    }

    return {
      modalVisibility,
      assignees,
      counterSignForm,
      counterSignFormRef,
      counterSignType,
      assigneeOptions,
      counterSignTypeOptions,
      assigneeTypeOptions,
      labelOptions,
      actionOptions,
      counterSignFormRules,
      counterSignColumns,
      counterSignList,
      filterSelectOptions,
      updateAssignee,
      updateAssigneeType,
      updateCounterSignType,
      openCounterSignModal,
      removeCurrentCounterSign,
      saveCounterSign,
    }
  }
})
</script>

<style scoped>

</style>