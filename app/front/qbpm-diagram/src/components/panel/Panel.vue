<template>
  <div class="panel">
    <div class="panel-header">
      <span>{{ elementTypeName }}</span>
      <span>{{ elementChineseName }}</span>
    </div>
    <ACollapse :bordered="false">
      <!--加载 panel 组件-->
      <General key="general"></General>
      <Documentation key="documentation"></Documentation>
      <Listener v-if="isElementListenable" key="listener"></Listener>
      <ConditionExpression v-if="isElementConditional" key="conditionExpression"></ConditionExpression>
      <ExtensionProperties v-if="isElementHasExtensionProperties" key="extensionProperties"></ExtensionProperties>
    </ACollapse>
  </div>
</template>

<script>
import {computed, defineComponent, markRaw, onMounted, ref} from 'vue';
import debounce from 'lodash.debounce';
import modeler from '@/store/modeler';
import General from '@/components/panel/General.vue';
import EventEmitter from '@/utils/eventEmitter.js';
import Listener from '@/components/panel/Listener.vue';
import ConditionExpression from '@/components/panel/ConditionExpression.vue';
import {isConditional} from '@/utils/bpmn-component/conditionUtils.js';
import {isListenable} from '@/utils/bpmn-component/listenerUtils.js';
import ExtensionProperties from '@/components/panel/ExtensionProperties.vue';
import {canHaveExtensionProperties} from '@/utils/bpmn-component/extensionPropertiesUtils.js';
import {customTranslate} from '@/additional-modules/translate';
import Documentation from '@/components/panel/Documentation.vue';
import {notification} from 'ant-design-vue';
import 'ant-design-vue/lib/notification/style/css';

export default defineComponent({
  name: 'Panel',
  components: {Documentation, ExtensionProperties, ConditionExpression, Listener, General},
  setup() {
    const modelerStore = modeler()
    const elementTypeName = ref('Process')
    const currentElementId = ref(undefined)
    const elementChineseName = ref('业务流程')

    const isElementListenable = ref(false)
    const isElementConditional = ref(false)
    const isElementHasExtensionProperties = ref(false)

    const elementRegistry = computed(() => modelerStore.getElementRegistry)


    // 展示 panel 选项
    const setPanelComponents = (element) => {
      isElementListenable.value = isListenable(element)
      isElementConditional.value = isConditional(element)
      isElementHasExtensionProperties.value = canHaveExtensionProperties(element)
    }

    const setCurrentElement = debounce(element => {
      if (!elementRegistry) {
        notification.error({
          message: '错误',
          description: '无法获取 BPMN 元素'
        })
      }
      if (!element) {
        element = elementRegistry.value.find((element) => element.type === 'bpmn:Process') ||
            elementRegistry.value.find((element) => element.type === 'bpmn:Collaboration')
        // 如果 xml 里连 process 和 collaboration 都找不到
        if (!element) {
          notification.error({
            message: '错误',
            description: '没有找到 BPMN 元素，页面即将刷新'
          })
        }
      }
      modelerStore.setElement(markRaw(element), element.id)
      elementTypeName.value = element.type.split(':')[1]
      elementChineseName.value = customTranslate(elementTypeName.value || 'Process')
      setPanelComponents(element)
      EventEmitter.emit('element-update', element)
    }, 100)

    EventEmitter.on('modeler-init', (modeler) => {
      // 导入完成后默认选中 process 节点
      modeler.on('import.done', () => {
        setCurrentElement(null)
      })
      // 监听选择事件，修改当前激活的元素以及表单
      modeler.on('selection.changed', ({newSelection}) => {
        setCurrentElement(newSelection[0] || null)
      })
      modeler.on('element.changed', ({element}) => {
        // 保证 修改 "默认流转路径" 等类似需要修改多个元素的事件发生的时候，更新表单的元素与原选中元素不一致。
        if (element && element.id === currentElementId.value) {
          setCurrentElement(element)
        }
      })
    })

    onMounted(() => !currentElementId.value && setCurrentElement())

    return {
      elementTypeName,
      elementChineseName,
      isElementListenable,
      isElementConditional,
      isElementHasExtensionProperties
    }
  }
})
</script>

<style scoped>
.panel {
  width: 480px;
  box-sizing: border-box;
  padding: 0 4px;
  border-left: 1px solid #eee;
  box-shadow: 0 0 8px #ccc;
  max-height: 100%;
  overflow-y: auto;
}

.panel-header {
  align-items: center;
  background: #f5f5f7;
  margin: 0 auto;
}

.panel-header span {
  padding: 0;
  font-size: 24px;
  font-weight: bolder;
}

</style>