<template>
  <ACollapsePanel name="extensionProperties">
    <template #header>
      <p>扩展属性</p>
    </template>
    <div class="element-extension-properties">
      <AFormItem label="多选类型">
        <ASelect v-model:value="multiDecision" :options="multiDecisionOptions" @change="updateMultiDecision"></ASelect>
      </AFormItem>
      <AFormItem v-if="multiDecision === 'rate'" label="占比">
        <AInputNumber v-model:value="multiDecisionRate" :formatter="value => `${value}%`"
                      :parser="value => value.replace('%', '')"
                      @change="updateMultiDecisionRate"
                      :min="0" :max="100"
                      :keyboard="true">
        </AInputNumber>
      </AFormItem>
      <AFormItem v-if="multiDecision === 'threshold'" label="门槛比例">
        <AInputNumber
            v-model:value="multiDecisionThreshold" :formatter="value => `${value}%`"
            :parser="value => value.replace('%', '')"
            @change="updateMultiDecisionThreshold"
            :min="0" :max="100"
            :keyboard="true">
        </AInputNumber>
      </AFormItem>
      <AFormItem label="串行执行">
        <ACheckbox v-model:checked="isSequential" @change="updateIsSequential"></ACheckbox>
      </AFormItem>
      <AFormItem label="执行" v-if="canHaveActionRef">
        <ASelect show-search v-model:value="restAction.name"
                 :options="actionOptions.map(item=>({value:item.code, label: item.name}))"
                 :filter-option="filterSelectOptions" @change="updateActionRef">
        </ASelect>
      </AFormItem>
      <AFormItem label="审批人设置方式" v-if="canHaveAssignee">
        <ASelect v-model:value="assigneeType" :options="assigneeTypeOptions" @change="updateAssigneeType"></ASelect>
      </AFormItem>
      <AFormItem name="assignee" label="审批人"
                 v-if="canHaveAssignee && ['static', 'label', 'dynamic'].includes(assigneeType) ">
        <ASelect v-if="assigneeType==='static'" v-model:value="assignees" @change="updateAssignee" show-search
                 mode="tags" :token-separators="[',']"
                 placeholder="若选项中找不到审批人请输入邮箱；多选请用英文逗号分隔">
          <ASelectOption v-for="item in assigneeOptions" :key="item.id" :value="item.email">
            {{ item.email }}
          </ASelectOption>
        </ASelect>
        <ASelect v-if="assigneeType==='label'" v-model:value="assignees" @change="updateAssignee">
          <ASelectOption v-for="item in labelOptions" :key="item.id" :value="item.id">{{ item.name }}</ASelectOption>
        </ASelect>
        <ASelect show-search v-if="assigneeType==='dynamic'" v-model:value="assignees" @change="updateAssignee"
        :options="actionOptions.map(item=>({value: item.code, label: item.name}))"
        :filter-option="filterSelectOptions">
        </ASelect>
      </AFormItem>
      <AFormItem label="允许加签" v-if="canCounterSign">
        <ACheckbox v-model:checked="allowCounterSign" @change="updateAllowCounterSign"></ACheckbox>
      </AFormItem>
      <CounterSign v-if="allowCounterSign"></CounterSign>
    </div>
  </ACollapsePanel>
</template>

<script>
import {computed, onMounted, ref} from 'vue';
import modeler from '@/store/modeler.js';
import {
  getActionRefValue,
  getAllowCounterSign,
  getAssignee,
  getAssigneeType,
  getAssigneeTypeOptions,
  getIsSequential,
  getMultiDecision,
  getMultiDecisionRate,
  getMultiDecisionThreshold,
  isServiceTask,
  isUserTask,
  isSuggestTask,
  setActionRefValue,
  setAllowCounterSign,
  setAssignee,
  setAssigneeType,
  setIsSequential,
  setMultiDecision,
  setMultiDecisionRate,
  setMultiDecisionThreshold
} from '@/utils/bpmn-component/extensionPropertiesUtils.js';
import EventEmitter from '@/utils/eventEmitter.js';
import {getUserOptions} from '@/utils/requests/userUtils.js';
import {getActionOptions} from '@/utils/requests/restActionUtils.js';
import CounterSign from '@/components/panel/CounterSign.vue';
import {setNameValue} from '@/utils/bpmn-component/nameUtils.js';
import {removeAllCounterSigns} from '@/utils/bpmn-component/counterSignUtils.js';
import {removeAllCounterSignTypes} from '@/utils/bpmn-component/counterSignTypeUtils.js';
import {getLabelOptions} from '@/utils/requests/labelUtils.js';
import {isArray} from 'min-dash';


export default {
  name: 'ExtensionProperties',
  components: {CounterSign},
  setup() {
    const multiDecision = ref('all')
    const multiDecisionRate = ref(0)
    const multiDecisionThreshold = ref(0)
    const isSequential = ref(false)
    const assigneeType = ref('static')
    const assignees = ref([])
    const restAction = ref({
      code: ''
    })
    const allowCounterSign = ref(false)
    const loading = ref(false)
    const canHaveAssignee = ref(false)
    const canCounterSign = ref(false)
    const canHaveActionRef = ref(false)

    const modelerStore = modeler()
    const activeElement = computed(() => modelerStore.getActiveElement)

    onMounted(async () => {
      actionOptions.push(...await getActionOptions())
      assigneeOptions.push(...await getUserOptions())
      labelOptions.push(...await getLabelOptions())
      await reloadExtensionProperties()
      EventEmitter.on('element-update', reloadExtensionProperties)
    })

    const updateMultiDecision = (decision) => {
      setMultiDecision(activeElement.value, decision)
      // 更改多选类型为 any 和 all 时，decisionRate 和 decisionThreshold 都应该不存在
      if(decision === 'any' || decision === 'all') {
        setMultiDecisionRate(activeElement.value, undefined)
        setMultiDecisionThreshold(activeElement.value, undefined)
      }
    }

    const updateMultiDecisionRate = (decisionRate) => {
      setMultiDecisionRate(activeElement.value, decisionRate / 100)
    }

    const updateMultiDecisionThreshold = (decisionThreshold) => {
      setMultiDecisionThreshold(activeElement.value, decisionThreshold / 100)
    }

    const updateIsSequential = (e) => {
      let sequential = e.target.checked
      setIsSequential(activeElement.value, sequential)
    }

    const updateAssigneeType = async (assigneeType) => {
      // 当审批人类型为标签时，多选类型默为 any
      if(assigneeType === 'label') {
        multiDecision.value = 'any'
        updateMultiDecision('any')
      }
      setAssigneeType(activeElement.value, assigneeType)
      assignees.value = undefined
      setAssignee(activeElement.value, undefined)
    }

    const updateAssignee = (assignee) => {
      if (isArray(assignee)) {
        const assigneeEmailList = assigneeOptions.filter(item => assignee.includes(item.email))
            .map(item => item.email)
        // 如果输入的是远程没有的
        if (assignee.length !== assigneeEmailList.length) {
          const nonexistentAssignee = assignee.filter(item => !assigneeOptions.map(e => e.email).includes(item))
          assigneeEmailList.push(nonexistentAssignee)
        }
        setAssignee(activeElement.value, assigneeEmailList.join(','))
        return
      }
      setAssignee(activeElement.value, assignee)
    }

    const updateAllowCounterSign = (e) => {
      const allow = e.target.checked
      setAllowCounterSign(activeElement.value, allow)
      if (!allow) {
        removeAllCounterSigns(activeElement.value)
        removeAllCounterSignTypes(activeElement.value)
      }
    }

    const updateActionRef = (actionCode) => {
      setActionRefValue(activeElement.value, actionCode)
      // serviceTask 的 name 和 action 的名字相同
      const actionNames = actionOptions.filter(item => item.code === actionCode).map(item => item.name)
      if (actionNames.length === 1) {
        setNameValue(activeElement.value, actionNames[0])
      }
    }

    const reloadExtensionProperties = async () => {
      multiDecision.value = getMultiDecision(activeElement.value) || 'all'
      multiDecisionRate.value = getMultiDecisionRate(activeElement.value) * 100 || 0
      multiDecisionThreshold.value = getMultiDecisionThreshold(activeElement.value) * 100 || 0
      isSequential.value = !!getIsSequential(activeElement.value)
      canHaveActionRef.value = isServiceTask(activeElement.value)
      canHaveAssignee.value = isSuggestTask(activeElement.value) || isUserTask(activeElement.value)
      canCounterSign.value = isUserTask(activeElement.value)
      if (canHaveActionRef.value) {
        let actionRef = getActionRefValue(activeElement.value)
        restAction.value = actionOptions.find(item => item.code === actionRef) || {code: ''}
      }
      if (canHaveAssignee.value) {
        assigneeType.value = getAssigneeType(activeElement.value) || 'static'
        if (['static', 'label'].includes(assigneeType.value)) {
          const assigneeEmails = getAssignee(activeElement.value) && getAssignee(activeElement.value).split(',')
          const assignee = assigneeOptions.filter(item => assigneeEmails.includes(item.email))
          const assigneeList = assignee.map(item => item.email)
          if (assigneeList.length < assigneeEmails.length) {
            const unknownEmails = assigneeEmails.filter(e => !assignee.map(a => a.email).includes(e))
            assigneeList.push(...unknownEmails)
          }
          assignees.value = assigneeList
        }
        if(assigneeType.value === 'dynamic'){
          assignees.value = getAssignee(activeElement.value)
        }
        allowCounterSign.value = getAllowCounterSign(activeElement.value)
      }
    }

    const multiDecisionOptions = [
      {label: '仅全满足方可', value: 'all'},
      {label: '有一满足即可', value: 'any'},
      {label: '占比', value: 'rate'},
      {label: '占比至门槛比例', value: 'threshold'},
    ]

    // action 同时根据 label 和 value 筛选
    const filterSelectOptions = (inputValue, option)=> {
      return option.label.indexOf(inputValue) >= 0 || option.value.indexOf(inputValue) >= 0
    }

    const assigneeTypeOptions = getAssigneeTypeOptions()

    const actionOptions = []
    const assigneeOptions = []
    const labelOptions = []

    return {
      multiDecision,
      multiDecisionRate,
      multiDecisionThreshold,
      isSequential,
      assignees,
      assigneeType,
      allowCounterSign,
      loading,
      multiDecisionOptions,
      assigneeTypeOptions,
      canHaveAssignee,
      canCounterSign,
      canHaveActionRef,
      restAction,
      actionOptions,
      assigneeOptions,
      labelOptions,
      filterSelectOptions,
      updateMultiDecision,
      updateMultiDecisionRate,
      updateMultiDecisionThreshold,
      updateIsSequential,
      updateAssigneeType,
      updateAssignee,
      updateActionRef,
      updateAllowCounterSign
    }
  }
}
</script>

<style scoped>

</style>
