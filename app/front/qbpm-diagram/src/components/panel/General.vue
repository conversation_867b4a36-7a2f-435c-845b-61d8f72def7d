<template>
  <ACollapsePanel name="general">
    <template #header>
      <p>常规信息</p>
    </template>
    <AForm :model="general" ref="generalRef" :rules="generalRules">
      <AFormItem name="elementId" label="ID">
        <AInput v-model:value="general.elementId" :disabled="true" @change="updateElementId"/>
      </AFormItem>
      <AFormItem name="elementName" label="名称">
        <AInput v-model:value="general.elementName" @change="updateElementName"/>
      </AFormItem>
      <AFormItem name="processVersion" label="版本" v-if="isProcess">
        <AInputNumber v-model:value="editorSettings.processVersion" :min="1" @change="updateProcessVersion"></AInputNumber>
      </AFormItem>
    </AForm>
  </ACollapsePanel>
</template>

<script>
import {computed, defineComponent, onMounted, ref} from 'vue';
import modeler from '@/store/modeler.js';
import EventEmitter from '@/utils/eventEmitter.js';
import {getNameValue, setNameValue} from '@/utils/bpmn-component/nameUtils.js';
import {getProcessVersion, setProcessVersion} from '@/utils/bpmn-component/processUtils.js';
import {setIdValue} from '@/utils/bpmn-component/elementIdUtils.js';
import editor from '@/store/editor.js';
import {storeToRefs} from 'pinia';

export default defineComponent({
  name: 'General',
  setup() {
    const editorStore = editor()
    const {editorSettings} = storeToRefs(editorStore)
    const modelerStore = modeler()
    const general = ref({
      elementId: '',
      elementName: '',
    })
    const generalRef = ref()
    const isProcess = ref(false)

    const activeElement = computed(() => modelerStore.getActiveElement)
    const activeElementId = computed(() => modelerStore.getActiveId)

    const generalRules = {
      elementId: {required: true, trigger: ['blur'], message: 'id 不能为空'}
    }

    onMounted(() => {
      reloadGeneralData()
      EventEmitter.on('element-update', reloadGeneralData)
    })

    const reloadGeneralData = () => {
      general.value.elementId = activeElementId.value
      general.value.elementName = getNameValue(activeElement.value) || ''
      isProcess.value = !!activeElement.value && activeElement.value.type === 'bpmn:Process'
      if (isProcess.value) {
        editorSettings.value.processName = general.value.elementName
        editorSettings.value.processVersion = getProcessVersion(activeElement.value) || 1
      }
    }

    const updateElementId = () => {
      setIdValue(activeElement.value, general.value.elementId)
    }

    const updateElementName = () => {
      setNameValue(activeElement.value, general.value.elementName)
      if(isProcess.value){
        editorSettings.value.processName = general.value.elementName
      }
    }

    const updateProcessVersion = (version) => {
      setProcessVersion(activeElement.value, version)
    }

    return {
      general,
      generalRef,
      generalRules,
      isProcess,
      editorSettings,
      updateElementId,
      updateElementName,
      updateProcessVersion,
      reloadGeneralData
    }
  }
})
</script>

<style scoped>

</style>