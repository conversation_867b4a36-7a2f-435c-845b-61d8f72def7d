<template>
  <AButton type="primary" @click="openUploadModal">上传至数据库</AButton>
  <AModal v-model:visible="modalVisibility" title="上传至数据库" ok-text="确认上传" cancel-text="取消"
          @ok="saveToDatabase" :confirm-loading="loading">
    <AForm :model="uploadForm" :rules="uploadFormRules" ref="uploadFormRef">
      <AFormItem name="key" label="流程 key">
        <AInput v-model:value="uploadForm.key"></AInput>
      </AFormItem>
      <AFormItem name="name" label="流程图名称">
        <AInput v-model:value="uploadForm.name"></AInput>
      </AFormItem>
      <AFormItem label="描述">
        <AInput v-model:value="uploadForm.desc"></AInput>
      </AFormItem>
    </AForm>
  </AModal>
</template>

<script>
import {defineComponent, reactive, ref} from 'vue';
import modeler from '@/store/modeler.js';
import editor from '@/store/editor.js';
import {notification} from 'ant-design-vue';
import 'ant-design-vue/lib/notification/style/css';
import {uploadProcessDefinition} from '@/utils/requests/remoteUtils.js';

export default defineComponent({
  name: 'Save',
  setup() {
    const editorStore = editor()
    const modelerStore = modeler()
    const uploadForm = reactive({
      key: '',
      name: '',
      desc: ''
    })
    const uploadFormRef = ref()
    const modalVisibility = ref(false)
    const loading = ref(false)
    const uploadFormRules = {
      key: {required: true, trigger: ['blur', 'change'], message: '流程 key 不能为空'},
      name: {required: true, trigger: ['blur', 'change'], message: '流程名不能为空'}
    }

    const openUploadModal = () => {
      modalVisibility.value = true
      uploadForm.key = editorStore.getProcessDef.processKey
      uploadForm.name = editorStore.getProcessDef.processName
      uploadForm.version = editorStore.getProcessDef.processVersion
    }

    const saveToDatabase = async () => {
      await uploadFormRef.value.validateFields()
      loading.value = true
      const {err, xml} = await modelerStore.getModeler?.saveXML({format: true})
      if (err) {
        notification.error({
          message: '错误',
          description: `上传失败:${err.message || err}`
        })
        loading.value = false
        return
      }
      await uploadProcessDefinition(uploadForm, xml).then(response => {
        loading.value = false
        modalVisibility.value = false
        if (!response.data.code) {
          notification.success({
            message: '上传成功'
          })
        } else {
          throw new Error(response.data.message)
        }
      }).catch(e => {
        loading.value = false
        notification.error({
          message: '上传失败错误',
          description: `失败信息:${e.message || e}`
        })
      })
    }

    return {
      modalVisibility,
      loading,
      uploadForm,
      uploadFormRef,
      uploadFormRules,
      openUploadModal,
      saveToDatabase
    }
  }
})
</script>

<style scoped>

</style>
