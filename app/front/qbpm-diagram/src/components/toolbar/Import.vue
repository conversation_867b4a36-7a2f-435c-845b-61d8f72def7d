<template>
<span>
  <APopover v-model:visible="popOverVisibility" title="导入自" trigger="click">
    <template #content>
      <div class="import-buttons">
        <AUpload v-model:file-list="fileImported" :showUploadList="false" accept=".xml,.bpmn" :max-count="1"
                 :before-upload="importFromLocal">
           <AButton type="primary" style="width: 145px">导入自本地</AButton>
        </AUpload>
          <AButton type="primary" @click="openModal">导入自数据库</AButton>
      </div>
    </template>
    <AButton type="primary">导入</AButton>
  </APopover>
</span>
  <AModal v-model:visible="modalVisibility" wrap-class-name="import-modal" title="已有审批流" ok-text="确认"
          cancel-text="取消" @ok="importFromRemote" @cancel="clearSearchField" width="60%">
    <div class="search-process">
      <AForm :model="searchProcessForm" :layout="'inline'">
        <AFormItem>
          <AInput placeholder="流程名" v-model:value="searchProcessForm.processName" style="width: 160px;"
                  @pressEnter="searchProcessesByName">
            <template #suffix>
              <SearchOutlined/>
            </template>
          </AInput>
        </AFormItem>
        <AButton type="primary" @click="searchProcessesByName" :loading="searchLoading">搜索</AButton>
      </AForm>
    </div>
    <ADivider/>
    <ASpin :spinning="searchLoading" tip="搜索中...">
      <AEmpty v-if="processDefinitions.length === 0" class="empty-process" :description="'无结果'"></AEmpty>
      <ARow :gutter="[16,16]" v-else>
        <ACol :span="6" v-for="processName in processNameList" :key="processName">
          <ACard hoverable size="small" class="process-cards">
            <template #title>
              <ATooltip :title="processName">
                {{ processName }}
              </ATooltip>
            </template>
            <template #actions>
              <AForm>
                <ACheckbox v-model:checked="selectedCard[processName]"
                           :disabled="!selectedCard[processName] && checkboxCanBeDisable">选择
                </ACheckbox>
              </AForm>
            </template>
          </ACard>
        </ACol>
      </ARow>
    </ASpin>
    <APagination v-if="processDefinitions.length>0" v-model:current="currentPage" :total="processDefinitionsCount"
                 v-model:pageSize="pageSize" @change="searchNextPage"></APagination>
  </AModal>
</template>


<script>
import {computed, defineComponent, reactive, ref} from 'vue';
import modeler from '@/store/modeler.js';
import {getProcessDefinitionsByName} from '@/utils/requests/remoteUtils.js';
import {SearchOutlined, SettingOutlined} from '@ant-design/icons-vue';
import _ from 'lodash';
import {notification} from 'ant-design-vue';
import 'ant-design-vue/lib/notification/style/css';
import editor from '@/store/editor.js';
import {Base64} from 'js-base64';

export default defineComponent({
  name: 'Import',
  components: {
    SettingOutlined, SearchOutlined
  },
  setup() {
    const editorStore = editor()
    const modelerStore = modeler()
    const popOverVisibility = ref(false)
    const modalVisibility = ref(false)
    const searchLoading = ref(false)
    const fileImported = ref(null)
    const processDefinitions = ref([])
    const processDefinitionsCount = ref(0)
    const searchProcessForm = reactive({
      processName: ''
    })
    const selectedProcess = ref({})
    const selectedCard = ref({})
    const currentPage = ref(1)
    const pageSize = ref(8)

    const importFromLocal = (file, fileList) => {
      const reader = new FileReader()
      reader.readAsText(file)
      reader.onload = function () {
        modelerStore.getModeler.importXML(this.result)
      }
      return false
    }

    const openModal = () => {
      modalVisibility.value = true
    }

    const importFromRemote = () => {
      // 不能多选或者不选
      if (Object.values(selectedCard.value).filter(item => item).length > 1) {
        notification.error({
          message: '不能多选',
          description: '检查一下'
        })
      }
      // 实际上只应该有一个
      const selectedProcessName = _.findKey(selectedCard.value, item => item)
      selectedProcess.value = processDefinitions.value.find(item =>
          item.name === selectedProcessName)
      if (selectedProcess.value && selectedProcess.value.xml_data) {
        modelerStore.getModeler.importXML(Base64.decode(selectedProcess.value.xml_data))
        editorStore.updateConfiguration({processKey: selectedProcess.value.key})
        modalVisibility.value = false
        notification.success({
          message: '导入成功',
          description: `已成功导入${selectedProcess.value.name},版本${selectedProcess.value.version}`
        })
      } else {
        notification.error({
          message: '未选择任何流程',
          description: '检查一下'
        })
      }
      clearSearchField()
    }

    // 清空搜索结果
    const clearSearchField = () => {
      searchProcessForm.processName = ''
      processDefinitions.value = []
      processDefinitionsCount.value = 0
      selectedCard.value = {}
    }

    const processNameList = computed(() => {
      if (!processDefinitions) {
        return []
      }
      return Object.keys(_.keyBy(processDefinitions.value, 'name'))
    })

    const checkboxCanBeDisable = computed(() => {
      if (!selectedCard.value) {
        return false
      }
      return Object.values(selectedCard.value).filter(item => item === true).length === 1
    })

    const searchProcessesByName = async () => {
      // 服务端根据 name 搜索所有符合条件的结果
      searchLoading.value = true
      currentPage.value = 1
      const pds = await getProcessDefinitionsByName(searchProcessForm.processName.trim(), currentPage.value, pageSize.value)
      processDefinitions.value = pds.process_definitions
      processDefinitionsCount.value = pds.count
      console.log(processDefinitions.value)
      searchLoading.value = false
    }

    const searchNextPage = async () => {
      searchLoading.value = true
      const pds = await getProcessDefinitionsByName(searchProcessForm.processName.trim(), currentPage.value, pageSize.value)
      processDefinitions.value = pds.process_definitions
      searchLoading.value = false
    }

    return {
      popOverVisibility,
      modalVisibility,
      searchLoading,
      fileImported,
      processDefinitions,
      processDefinitionsCount,
      searchProcessForm,
      processNameList,
      currentPage,
      pageSize,
      selectedCard,
      checkboxCanBeDisable,
      importFromLocal,
      importFromRemote,
      openModal,
      searchProcessesByName,
      searchNextPage,
      clearSearchField
    }
  }
})
</script>

<style scoped>
.import-buttons {
  display: grid;
  grid-template-columns: 1fr;
  grid-row-gap: 8px;
  padding: 8px 0;
}

.empty-process {
  margin: 0 auto;
}

.process-cards {
  text-align: center;
  margin: 5px 0;
}
</style>