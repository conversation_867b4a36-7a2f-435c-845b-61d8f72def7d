<template>
  <APopover v-model:visible="popOverVisibility" title="导出为" trigger="click">
    <template #content>
      <div class="export-buttons">
        <AButton type="primary" @click="saveAsXml">导出为XML</AButton>
        <AButton type="primary" @click="saveAsBpmn">导出为BPMN</AButton>
        <AButton type="primary" @click="saveAsSvg">导出为SVG</AButton>
      </div>
    </template>
    <AButton type="primary">导出</AButton>
  </APopover>
</template>

<script>
import {defineComponent, ref} from 'vue';
import modeler from '@/store/modeler.js';
import {downloadFile, setEncoded} from '@/utils/fileUtils.js';
import editor from '@/store/editor.js';
import {notification} from 'ant-design-vue';
import 'ant-design-vue/lib/notification/style/css';

export default defineComponent({
  name: 'Export',
  setup() {
    const popOverVisibility = ref(false)
    const modelerStore = modeler()
    const editorStore = editor()

    const saveAsFile = async (fileType, fileName) => {
      let bpmnModeler = modelerStore.getModeler

      if (!bpmnModeler) {
        notification.error({
          message: '错误',
          description: '无法导出'
        })
      }
      let error = null
      let file = null
      if (fileType === 'xml' || fileType === 'bpmn') {
        const {err, xml} = await bpmnModeler.saveXML({format: true})
        error = err
        file = xml
      }
      if (fileType === 'svg') {
        const {err, svg} = await bpmnModeler.saveSVG()
        error = err
        file = svg
      }
      if (error || !file) {
        notification.error({
          message: '错误',
          description: `无法导出: ${error?.message}`
        })
      }
      const {href, filename} = setEncoded(fileType.toUpperCase(), fileName, file)
      downloadFile(href, filename)
    }

    const saveAsXml = () => {
      saveAsFile('xml', editorStore.getProcessDef.processName)
    }

    const saveAsBpmn = () => {
      saveAsFile('bpmn', editorStore.getProcessDef.processName)
    }

    const saveAsSvg = () => {
      saveAsFile('svg', editorStore.getProcessDef.processName)
    }

    return {
      popOverVisibility,
      saveAsSvg,
      saveAsBpmn,
      saveAsXml
    }
  }
})
</script>

<style scoped>
.export-buttons {
  display: grid;
  grid-template-columns: 1fr;
  grid-row-gap: 8px;
  padding: 8px 0;
}
</style>