<template>
  <div class="toolbar">
    <AButtonGroup class="common-buttons">
      <Import></Import>
      <Export></Export>
      <Preview></Preview>
    </AButtonGroup>
    <div id="toolbar-platform-name">
      七牛云审批流 BPMN 图绘制工具
    </div>
    <Save></Save>
  </div>
</template>

<script>
import {defineComponent} from 'vue';
import Export from '@/components/toolbar/Export.vue';
import Preview from '@/components/toolbar/Preview.vue';
import Import from '@/components/toolbar/Import.vue';

export default defineComponent({
  name: 'Toolbar',
  components: [
    Import, Export, Preview
  ]
})
</script>

<style scoped>
.toolbar {
  width: 100%;
  height: min-content;
  box-sizing: border-box;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #eeeeee;
}
#toolbar-platform-name{
  font-size: 20px;
  font-weight: bold;
  text-align: center;
}
</style>