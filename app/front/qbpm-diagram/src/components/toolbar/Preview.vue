<template>
  <AButton type="primary" @click="openXMLPreviewModal">预览XML</AButton>
  <AModal title="XML文件" :visible="modalVisibility" @cancel="closeXMLPreviewModal" :footer="null" :width="800">
    <div class="preview-modal-content" ref="modalContent">
      <AButton type="primary" @click="copyToClipboard" style="margin-bottom: 10px">点击复制
        <CopyOutlined/>
      </AButton>
      <div class="preview-modal-code">
        <highlightCode language="xml" :code="bpmnXML"></highlightCode>
      </div>
    </div>
  </AModal>
</template>

<script>
import {defineComponent, ref} from 'vue';
import modeler from '@/store/modeler.js';
import {CopyOutlined} from '@ant-design/icons-vue';
import hljsVuePlugin from '@highlightjs/vue-plugin';
import {notification} from 'ant-design-vue';
import useClipboard from 'vue-clipboard3';
import 'ant-design-vue/lib/notification/style/css';

export default defineComponent({
  name: 'Preview',
  components: {
    highlightCode: hljsVuePlugin.component,
    CopyOutlined
  },
  setup() {
    const modalVisibility = ref(false)
    const modelerStore = modeler()
    const bpmnXML = ref('')
    const {toClipboard} = useClipboard()
    const modalContent = ref()

    const openXMLPreviewModal = async () => {
      modalVisibility.value = true
      const bpmnModeler = modelerStore.getModeler
      if (!modeler) {
        notification['error']({
          message: '预览XML错误',
          description: 'XML加载失败，请刷新重试'
        })
      }
      const {xml} = await bpmnModeler.saveXML({format: true, preamble: true})
      bpmnXML.value = xml
    }

    const closeXMLPreviewModal = () => {
      modalVisibility.value = false
    }

    const copyToClipboard = async () => {
      try {
        await toClipboard(bpmnXML.value)
        notification.success({
          message: '复制成功'
        })
      } catch (e) {
        console.error(e)
        notification.error({
          message: '复制失败'
        })
      }
    }

    return {
      modalVisibility,
      bpmnXML,
      modalContent,
      openXMLPreviewModal,
      closeXMLPreviewModal,
      copyToClipboard
    }
  }
})
</script>

<style scoped>
.preview-modal-content {
  width: auto;
  max-height: 450px;
}

.preview-modal-code {
  max-height: 400px;
  overflow-y: auto;
}

.preview-modal pre {
  font-size: 15px;
  width: 750px;
  white-space: pre-wrap;
}
</style>