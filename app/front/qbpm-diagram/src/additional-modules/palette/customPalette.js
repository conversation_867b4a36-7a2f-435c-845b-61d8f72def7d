import translate from '@/additional-modules/translate/index';
import {assign} from 'min-dash';
import PaletteProvider from 'bpmn-js/lib/features/palette/PaletteProvider';

class CustomPalette extends PaletteProvider {
    constructor(create, elementFactory, palette, translate) {
        super(palette, create, elementFactory, translate);
        this.create = create;
        this.elementFactory = elementFactory;
        this.translate = translate;
    }

    getPaletteEntries() {
        const {
            create,
            elementFactory,
            translate
        } = this;
        const actions = {}

        function createAction(type, group, className, title, options) {
            function createListener(event) {
                const shape = elementFactory.createShape(assign({type: type}, options))
                if (options) {
                    !shape.businessObject.di && (shape.businessObject.di = {})
                    shape.businessObject.di.isExpanded = options.isExpanded
                }

                create.start(event, shape)
            }

            const shortType = type.replace(/^bpmn:/, '')
            return {
                group: group,
                className: className,
                title: title || this.translate('Create {type}', {type: shortType}),
                action: {
                    dragstart: createListener,
                    click: createListener
                }
            }
        }

        function createSuggestTask(event) {
            const suggestTask = elementFactory.createShape({type: 'qbpm:SuggestTask'})
            create.start(event, suggestTask)
        }

        assign(actions, {
            'create.exclusive-gateway': createAction(
                'bpmn:ExclusiveGateway',
                'gateway',
                'bpmn-icon-gateway-xor',
                translate('Create Exclusive Gateway')
            ),
            'create.parallel-gateway': createAction(
                'bpmn:ParallelGateway',
                'gateway',
                'bpmn-icon-gateway-parallel',
                translate('Create Parallel Gateway')
            ),
            'create.inclusive-gateway': createAction(
                'bpmn:InclusiveGateway',
                'gateway',
                'bpmn-icon-gateway-or',
                translate('Create Inclusive Gateway')
            ),
            'gateway-separator': {
                group: 'gateway',
                separator: true
            },
            'create.user-task': createAction(
                'bpmn:UserTask',
                'activity',
                'bpmn-icon-user-task',
                translate('Create User Task')
            ),
            'create.service-task': createAction(
                'bpmn:ServiceTask',
                'activity',
                'bpmn-icon-service-task',
                translate('Create Service Task')
            ),
            // SuggestTask 需要自己定义
            'create.suggest-task': {
                group: 'activity',
                className: 'bpmn-icon-user-task green',
                title: translate('Create Suggest Task'),
                action: {
                    dragstart: createSuggestTask,
                    click: createSuggestTask,
                }
            },
        })

        return actions
    }
}

CustomPalette.$inject = [
    'create',
    'elementFactory',
    'palette',
    'translate'
]

export default CustomPalette