import BpmnRenderer from 'bpmn-js/lib/draw/BpmnRenderer';
import { create as svgCreate, attr as svgAttr, append as svgAppend } from 'tiny-svg';
import { getFillColor, getStrokeColor } from 'bpmn-js/lib/draw/BpmnRenderUtil.js';


// 模仿官方示例写的
const HIGH_PRIORITY = 3000
class CustomRenderer extends BpmnRenderer {
    constructor(config, eventBus, styles, pathMap, canvas, textRenderer) {
        super(config, eventBus, styles, pathMap, canvas, textRenderer, HIGH_PRIORITY);

        function drawPath(parentGfx, d, attrs) {
            attrs = styles.computeStyle(attrs, ['no-fill'], {
                strokeWidth: 2,
                stroke: 'black'
            })

            const path = svgCreate('path')
            svgAttr(path, { d: d })
            svgAttr(path, attrs)

            svgAppend(parentGfx, path)

            return path
        }

        // SuggestTask 是 UserTask 的变体
        this.handlers['qbpm:SuggestTask'] = (parentGfx, element, attr) => {
            const task = this.handlers['bpmn:Task'](parentGfx, element)
            const x = 15
            const y = 12
            const pathData = pathMap.getScaledPath('TASK_TYPE_USER_1', {
                abspos: {
                    x: x,
                    y: y
                }
            })

            drawPath(parentGfx, pathData, {
                strokeWidth: 0.5,
                fill: getFillColor(element, '#ffffff'),
                stroke: getStrokeColor(element,'#000000')
            })
            return task
        }
    }
}

CustomRenderer.$inject = [
    'config.bpmnRenderer',
    'eventBus',
    'styles',
    'pathMap',
    'canvas',
    'textRenderer'
]

export default CustomRenderer