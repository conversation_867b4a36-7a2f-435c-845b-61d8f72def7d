import {defineStore} from 'pinia';
import {defaultSettings} from '@/config/editorDefaultSettings.js';

const editorState = {
    editorSettings: defaultSettings
}

export default defineStore('editor', {
    state: () => editorState,
    getters: {
        getProcessDef: state => ({
            processId: state.editorSettings.processId,
            processKey: state.editorSettings.processKey,
            processName: state.editorSettings.processName,
            processVersion: state.editorSettings.processVersion
        }),
        getProcessEngine: (state) => state.editorSettings.processEngine,
        getEditorConfig: state => ({
            background: state.editorSettings.background,
            miniMap: state.editorSettings.miniMap
        })
    },
    actions: {
        updateConfiguration(conf) {
            this.$state.editorSettings = {...this.$state.editorSettings, ...conf}
        }
    }
})