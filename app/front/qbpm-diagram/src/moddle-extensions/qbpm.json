{"name": "<PERSON><PERSON>", "uri": "https://qiniu.com", "prefix": "qbpm", "xml": {"tagAlias": "lowerCase"}, "associations": [], "types": [{"name": "StartEvent", "extends": ["bpmn:StartEvent"], "properties": [{"name": "listener", "type": "Listener", "isMany": true}]}, {"name": "EndEvent", "extends": ["bpmn:EndEvent"], "properties": [{"name": "listener", "type": "Listener", "isMany": true}, {"name": "disableNotify", "isAttr": true, "type": "Boolean", "default": false}]}, {"name": "UserTask", "extends": ["bpmn:UserTask"], "properties": [{"name": "multiDecision", "isAttr": true, "type": "String", "default": "all"}, {"name": "multiDecisionRate", "isAttr": true, "type": "Number"}, {"name": "multiDecisionThreshold", "isAttr": true, "type": "Number"}, {"name": "isSequential", "isAttr": true, "type": "Boolean"}, {"name": "assigneeType", "isAttr": true, "type": "String"}, {"name": "assignee", "isAttr": true, "type": "String", "default": ""}, {"name": "allowCounterSign", "isAttr": true, "type": "Boolean", "default": false}, {"name": "counterSigns", "type": "CounterSigns"}, {"name": "counterSignTypes", "type": "CounterSignTypes"}, {"name": "listener", "type": "Listener", "isMany": true}, {"name": "disableNotify", "isAttr": true, "type": "Boolean", "default": false}]}, {"name": "ServiceTask", "extends": ["bpmn:ServiceTask"], "properties": [{"name": "multiDecision", "isAttr": true, "type": "String", "default": "all"}, {"name": "multiDecisionRate", "isAttr": true, "type": "Number"}, {"name": "multiDecisionThreshold", "isAttr": true, "type": "Number"}, {"name": "isSequential", "isAttr": true, "type": "Boolean"}, {"name": "actionRef", "isAttr": true, "type": "String"}, {"name": "param", "type": "Param", "isMany": true}, {"name": "listener", "type": "Listener", "isMany": true}]}, {"name": "ExclusiveGateway", "extends": ["bpmn:ExclusiveGateway"], "properties": [{"name": "listener", "type": "Listener", "isMany": true}, {"name": "defaultFlow", "type": "bpmn:SequenceFlow", "isAttr": true, "isReference": true}]}, {"name": "ParallelGateway", "extends": ["bpmn:ParallelGateway"], "properties": [{"name": "listener", "type": "Listener", "isMany": true}]}, {"name": "InclusiveGateway", "extends": ["bpmn:InclusiveGateway"], "properties": [{"name": "listener", "type": "Listener", "isMany": true}, {"name": "defaultFlow", "type": "bpmn:SequenceFlow", "isAttr": true, "isReference": true}]}, {"name": "ComplexGateway", "extends": ["bpmn:ComplexGateway"], "properties": [{"name": "defaultFlow", "type": "bpmn:SequenceFlow", "isAttr": true, "isReference": true}]}, {"name": "Listener", "superClass": ["Element"], "meta": {"allowedIn": ["bpmn:Task", "bpmn:ServiceTask", "bpmn:UserTask", "bpmn:BusinessRuleTask", "bpmn:ScriptTask", "bpmn:ReceiveTask", "bpmn:ManualTask", "bpmn:ExclusiveGateway", "bpmn:SequenceFlow", "bpmn:ParallelGateway", "bpmn:InclusiveGateway", "bpmn:StartEvent", "bpmn:IntermediateCatchEvent", "bpmn:IntermediateThrowEvent", "bpmn:EndEvent", "bpmn:BoundaryEvent", "bpmn:CallActivity", "bpmn:SubProcess"]}, "properties": [{"name": "id", "type": "String", "isAttr": true}, {"name": "fire", "type": "String", "isAttr": true}, {"name": "actionRefs", "type": "String", "isAttr": true}, {"name": "params", "type": "Param", "isMany": true}]}, {"name": "InputOutputParameterDefinition", "isAbstract": true}, {"name": "Param", "superClass": ["Element"], "meta": {"allowedIn": ["qbpm:Listener", "bpmn:ServiceTask"]}, "properties": [{"name": "name", "isAttr": true, "type": "String"}, {"name": "value", "isAttr": true, "type": "String"}]}, {"name": "CounterSign", "superClass": ["Element"], "meta": {"allowedIn": ["qbpm:CounterSigns"]}, "properties": [{"name": "assigneeType", "isAttr": true, "type": "String"}, {"name": "assignee", "isAttr": true, "type": "String"}, {"name": "displayName", "isAttr": true, "type": "String"}]}, {"name": "CounterSigns", "superClass": ["Element"], "meta": {"allowedIn": ["bpmn:UserTask"]}, "properties": [{"name": "signs", "type": "CounterSign", "isMany": true}]}, {"name": "CounterSignType", "superClass": ["Element"], "meta": {"allowedIn": ["qbpm:CounterSignTypes"]}, "properties": [{"name": "body", "isBody": true, "type": "Integer"}]}, {"name": "CounterSignTypes", "superClass": ["Element"], "meta": {"allowedIn": ["bpmn:UserTask"]}, "properties": [{"name": "types", "type": "CounterSignType", "isMany": true}]}, {"name": "SuggestTask", "superClass": ["bpmn:Task"], "properties": [{"name": "multiDecision", "isAttr": true, "type": "String", "default": "all"}, {"name": "multiDecisionRate", "isAttr": true, "type": "Number"}, {"name": "multiDecisionThreshold", "isAttr": true, "type": "Number"}, {"name": "isSequential", "isAttr": true, "type": "Boolean"}, {"name": "assigneeType", "isAttr": true, "type": "String"}, {"name": "assignee", "isAttr": true, "type": "String", "default": ""}, {"name": "listener", "type": "Listener", "isMany": true}, {"name": "disableNotify", "isAttr": true, "type": "Boolean", "default": false}]}]}