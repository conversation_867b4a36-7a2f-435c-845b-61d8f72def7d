<div id="print-body" class="qn-standard-page">
    <main>
        <div style="text-align:center">
            <button type="button" ng-show="isLoading" class="btn btn-success btn-inline" name="button">
                <i ng-show="isLoading" class="fa fa-spinner fa-spin"></i> 报价单数据加载中...
            </button>
            <button type="button" disabled="true" ng-show="!isLoading && !quotation"
                class="btn btn-danger btn-inline" name="button">获取报价单数据失败</button>
        </div>

        <div ng-show="!isLoading && quotation">

            <div class="qiniu-header header">
	        	<img src="http://assets.qiniu.com/qiniu-410x180.png" class="icon"/>
                <button ng-click="myprint()" class="print-btn">打印</button>
                <label class="contract_num" ng-show="contract_num">合同编号：{{contract_num}}</label>
	        </div>

            <div class="center">
                <div class="title">七牛云服务报价单</div>
            </div>

            <div class="clearfix center" ng-show="type == time_point_quotation">
                <label class="time_border"> 有效时间：{{start_time | formatDateFilter:"/"}} ~ {{end_time | formatDateFilter:"/"}}</label>
            </div>

            <div ng-repeat="(product, items) in quotation">
                <div class="center product">
                    <h3>{{product}}</h3>
                </div>

                <div ng-repeat="price_item in items track by $index">
                    <div class="center">
                        <label class="quotation_title">{{price_item.title}}</label>
                    </div>

                    <div>
                        <center>
                            <table class="info">
                                <tbody>
                                    <tr>
                                        <th class="info_key">{{info_key_desc[price_item.info[0].key]}}</th>
                                        <td class="info_value">{{price_item.info[0].value}}</td>
                                        <th class="info_key">{{info_key_desc[price_item.info[1].key]}}</th>
                                        <td class="info_value">{{price_item.info[1].value}}</td>
                                    </tr>
                                    <tr ng-show="price_item.info.length >= 3">
                                        <th class="info_key" ng-show="price_item.info.length >= 3">{{info_key_desc[price_item.info[2].key]}}</th>
                                        <td class="info_value" ng-show="price_item.info.length >= 3">{{price_item.info[2].value}}</td>
                                        <th class="info_key" ng-show="price_item.info.length >= 4">{{info_key_desc[price_item.info[3].key]}} </th>
                                        <td class="info_value" ng-show="price_item.info.length >= 4">{{price_item.info[3].value}}</td>
                                    </tr>
                                    <tr ng-show="price_item.info.length >= 5">
                                        <th class="info_key">{{info_key_desc[price_item.info[4].key]}}</th>
                                        <td class="info_value">{{price_item.info[4].value}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </center>
                    </div>

                    <div>
                        <center>
                            <table class="table_tair">
                                <thead>
                                    <tr>
                                        <th>阶梯</th>
                                        <th>价格</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="stair in price_item.stairs track by $index">
                                        <td>{{stair.stair_range}}</td>
                                        <td>{{stair.price}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </center>
                    </div>
                </div>
            </div>
            
            <div class="tip">
                <div style="font-size:25px;">计费说明：</div>
		        <ol class="tip_info">
		        	<li>
		        		未在上述报价单内体现的产品计费项若产生使用费用，将以官网价格计费出账，详细价格可参考 <a class="tip_info" href="www.qiniu.com/prices">www.qiniu.com/prices</a>
		        	</li>
		        	<li>
		        		成为七牛注册用户可获得每月 1G 国内HTTP流量和 1G 国内存储空间免费额度，升级为认证用户后可升级免费额度至每月 10G 国内HTTP流量和 10G 国内存储空间，免费额度将在每月账单中自动体现
		        	</li>
		        	<li>
		        		产品计费项单价最多保留小数点后四位，实际每月出账后若账单有小数，将计算账单实际金额后保留小数后两位精确到分，即按 ¥ 0.01元展示
		        	</li>
		        </ol>
            </div>

        </div>
    </main>
</div>
