<div class="standard-list qn-standard-page approval-detail-page">
    <main>
        <div class="header clearfix">
            <div class="row" style="margin-bottom: 20px;">
                <h1 class="col-sm-12" ng-if="!isRegen">{{ developer.companyName }}报价申请</h1>
                <h1 class="col-sm-12" ng-if="isRegen">{{ developer.companyName }}重出账申请</h1>
            </div>
        </div>
        <div style="text-align:center">
            <button type="button" ng-show="isLoading" class="btn btn-success btn-inline" name="button">
                <i ng-show="isLoading" class="fa fa-spinner fa-spin"></i> 价格表数据加载中...
            </button>
            <button type="button" disabled="true" ng-show="!isLoading && !groupByProduct"
                class="btn btn-danger btn-inline" name="button">获取价格表数据失败</button>
        </div>
        <div class="brief clearfix" style="margin-bottom: 20px;" ng-show="!isLoading">
            <div class="row">
                <h3 class="col-sm-12"><strong>报价单基本信息</strong></h3>
            </div>
            <div class="row">
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row">
                        <div class="col-sm-6" ng-if="developer.sfOpportunityId">
                            <div class="row">
                                <div class="col-sm-4">商业机会:</div>
                                <div class="col-sm-8"><a target="_blank" href="https://portal.qiniu.io/bo/crm/opportunity?id={{ developer.sfOpportunityId }}">{{ developer.sfOpportunityId }}</a></div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="developer.customer_name">
                            <div class="row">
                                <div class="col-sm-4">客户名:</div>
                                <div class="col-sm-8">{{ developer.customer_name }}</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="developer.uid">
                            <div class="row">
                                <div class="col-sm-4">UID:</div>
                                <div class="col-sm-8">
                                    <a ui-sref="layout.developers.developerInfo({key: developer.uid})" target="_blank">
                                        {{ developer.uid }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="effectTime || deadTime">
                            <div class="row">
                                <div class="col-sm-4">报价生效时间:</div>
                                <div class="col-sm-8">{{ effectTime | formatDateFilter }} ~ {{ deadTime | formatDateFilter }}(不含)</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="responsibleOwner">
                            <div class="row">
                                <div class="col-sm-4">责任方:</div>
                                <div class="col-sm-8">{{ RESPONSIBLE_OWNER_MAP[responsibleOwner] }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row" ng-if="remark">
                        <div class="col-sm-2">申请理由:</div>
                        <div class="col-sm-10" >{{ remark }}</div>
                    </div>
                </div>
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <h4>
                        <span ng-class="{'text-danger': isLowPriceChanged}">{{lowPriceUserText}}</span>
                    </h4>
                </div>
            </div>
        </div>
        <div class="brief clearfix" style="margin-bottom: 20px;" ng-if="showFiles">
            <div class="row">
                <h3 class="col-sm-12"><strong>报价单附件信息</strong></h3>
            </div>

            <div class="row">   
                <div ng-repeat="fileInfo in uploadedFiles" class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row">
                        <div class="col-sm-12">
                            <a 
                                ng-disabled="!fileInfo.url"
                                class="btn btn-primary"
                                href="{{fileInfo.url}}"
                                target="_blank"
                            >
                                下载
                            </a>
                            <label>{{fileInfo.file}}</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <uid-discounts uid="{{developer.uid}}" ng-if="developer.uid" content-permission="price-biz-pricing-discounts"></uid-discounts>
        <div class="detail clearfix" style="margin-bottom: 20px;" ng-show="!isLoading">
            <div class="row">
                <h3 class="col-sm-12" style="margin-bottom: 20px;"><strong>产品价格明细</strong></h3>
            </div>
            <div class="row" ng-repeat="item in groupByProduct" style="border: 1px solid #eee; padding-bottom: 20px;">
                <h2 style="margin-bottom: 10px;" class="col-sm-12">{{ item.product.name }}</h2>
                <div class="clearfix">
                        <h4 class="current col-sm-6">新报价单</h4>
                        <h4 class="current col-sm-6" ng-show="!item.isNew" >当前报价单</h4>
                </div>
                <div ng-repeat="price in item.list" class="clearfix" >
                    <div class="after col-sm-6">
                        <table class="table table-bordered text-center" >
                            <tr>
                                <td colspan="2">
                                    <strong>
                                        {{ price.after.price_item.zone.title }} {{ price.after.price_item.item.name }}
                                        <span ng-class="{'text-red': price.after.algorithm.diff}" >
                                            {{ price.after.price_item.algorithm.name }}
                                        </span>
                                    </strong>
                                </td>
                            </tr>
                            <tr>
                                <td width="50%">阶梯规则: {{ STAIR_PRICE_TYPE[price.after.price_item.price_item.stair_price_type] }}</td>
                                <td>进制单位: {{ price.after.price_item.price_item.unit_rate === '0' ? '默认' : price.after.price_item.price_item.unit_rate }}</td>
                            </tr>
                            <tr>
                                <td width="50%">阶梯</td>
                                <td>单价</td>
                            </tr>
                            <tr ng-repeat="p in price.after.stairs" ng-class="{ 'text-red': isDiff(p.types) }" >
                                <td ng-show="isFirstStair(p) && !isLastStair(p)">
                                    0 {{ p.end.unit.name }} ~ {{ p.end.price_item_stair.quantity }} {{ p.end.unit.name }}</td>
                                <td ng-show="isFirstStair(p) && isLastStair(p)">
                                    0 {{ p.end.unit.name }} 以上</td>
                                <td ng-show="!isFirstStair(p) && isLastStair(p)">
                                    {{ p.start.price_item_stair.quantity }} {{ p.start.unit.name }} 以上
                                </td>
                                <td ng-show="!isFirstStair(p) && !isLastStair(p)">
                                    {{ p.start.price_item_stair.quantity }} {{ p.start.unit.name }} ~ {{ p.end.price_item_stair.quantity }} {{ p.end.unit.name }}
                                </td>
                                <td ng-if="showPriceUnit(price.after.price_item.price_item.stair_price_type, p)" >
                                    {{ price.after.price_item.price_item.currency_type | currencySymbol }} {{ p.end.price_item_stair.price | moneyFormatter:8:100000000:true }} /{{ price.after.price_item.price_unit.name }}
                                    <span ng-show="p.cost_diff == 'LT'" class="text-blue">&nbsp;&nbsp;低于审批价</span>
                                </td>
                                <td ng-if="!showPriceUnit(price.after.price_item.price_item.stair_price_type, p)" >
                                    {{ price.after.price_item.price_item.currency_type | currencySymbol }} {{ p.end.price_item_stair.price | moneyFormatter:8:100000000:true }}
                                    <span ng-show="p.cost_diff == 'LT'" class="text-blue">&nbsp;&nbsp;&nbsp;低于审批价</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="before col-sm-6">
                        <table class="table table-bordered text-center" >
                            <tr>
                                <td colspan="2">
                                    <strong>
                                        {{ price.before.price_item.zone.title }} {{ price.before.price_item.item.name }} {{ price.before.price_item.algorithm.name }}
                                    </strong>
                                </td>
                            </tr>
                            <tr>
                                <td width="50%">阶梯规则: {{ STAIR_PRICE_TYPE[price.before.price_item.price_item.stair_price_type] }}</td>
                                <td>进制单位: {{ price.before.price_item.price_item.unit_rate === '0' ? '默认' : price.before.price_item.price_item.unit_rate }}</td>
                            </tr>
                            <tr>
                                <td width="50%">阶梯</td>
                                <td>单价</td>
                            </tr>
                            <tr ng-repeat="p in price.before.stairs" >
                                <td ng-show="!p.start && p.end.price_item_stair.quantity != 0">0 {{ p.end.unit.name }} ~ {{ p.end.price_item_stair.quantity }} {{ p.end.unit.name }}</td>
                                <td ng-show="!p.start && p.end.price_item_stair.quantity == 0">0 {{ p.end.unit.name }} 以上</td>
                                <td ng-show="p.start && p.start.price_item_stair.quantity==p.end.price_item_stair.quantity">{{ p.start.price_item_stair.quantity }} {{ p.start.unit.name }} 以上</td>
                                <td ng-show="p.start && p.start.price_item_stair.quantity!=p.end.price_item_stair.quantity">{{ p.start.price_item_stair.quantity }} {{ p.start.unit.name }} ~ {{ p.end.price_item_stair.quantity }} {{ p.end.unit.name }}</td>
                                <td ng-if="showPriceUnit(price.before.price_item.price_item.stair_price_type, p)" >{{ price.before.price_item.price_item.currency_type | currencySymbol }} {{ p.end.price_item_stair.price | moneyFormatter:8:100000000:true }} /{{ price.before.price_item.price_unit.name }}</td>
                                <td ng-if="!showPriceUnit(price.before.price_item.price_item.stair_price_type, p)" >{{ price.before.price_item.price_item.currency_type | currencySymbol }} {{ p.end.price_item_stair.price | moneyFormatter:8:100000000:true }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <!-- 测算 v1 (分产品账单金额) -->
                <div class="col-sm-12 measure" ng-if="showProductMeasurement(item.measurements)">
                    <div class="row">
                        <h4 class="col-sm-12">模拟测算结果</h4>
                        <div class="col-sm-12">
                            <table class="table table-bordered text-center">
                                <thead>
                                <tr>
                                    <th ng-show="showColumn('month')" >历史月份</th>
                                    <th ng-show="showColumn('bill_money')" >原账单金额</th>
                                    <th ng-show="showColumn('measure_money')" >测算账单金额</th>
                                    <th ng-show="showColumn('diff_money')" >账单差额</th>
                                    <th ng-show="showColumn('diff_ratio')" >账单差比例</th>
                                    <th ng-show="showColumn('gpm')">原毛利率</th>
                                    <th ng-show="showColumn('measure_gpm')">测算毛利率</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="measurement in item.measurements">
                                    <td ng-show="showColumn('month') && measurement.month" >{{ measurement.month }}</td>
                                    <td ng-show="showColumn('month') && !measurement.month" >总计</td>
                                    <td ng-show="showColumn('bill_money')" >
                                        {{ measurement.bill_money | moneyFormatter:4:10000:true }} 元
                                        <div ng-if="!measurement.month">{{ measurement.avg_bill_money | moneyFormatter:4:10000:true }} 元/月</div>
                                    </td>
                                    <td ng-show="showColumn('measure_money')" >
                                        {{ measurement.measure_money | moneyFormatter:4:10000:true }} 元
                                        <div ng-if="!measurement.month">{{ measurement.avg_measure_money | moneyFormatter:4:10000:true }} 元/月</div>
                                    </td>
                                    <td ng-show="showColumn('diff_money')" >
                                        {{ measurement.diff_money | moneyFormatter:4:10000:true }} 元
                                        <div ng-if="!measurement.month">{{ measurement.avg_bill_diff | moneyFormatter:4:10000:true }} 元/月</div>
                                    </td>
                                    <td ng-show="showColumn('diff_ratio')" ng-class="{ 'text-red': isDiffRatioOverflow(measurement) }">
                                        {{ measurement.diff_ratio * 100 | formatDecimalFilter }} %
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 测算 v2021 (财务口径) -->
       <!-- <div class="clearfix" style="margin-bottom: 20px;" ng-if="!isLoading && measurementsVersion == 2021">
            <div class="row">
                <h3 class="col-sm-12" style="margin-bottom: 20px;"><strong>模拟测算</strong></h3>
            </div>

            &lt;!&ndash; directive 自带 div.row &ndash;&gt;
            <measurement-v2021-results uid="developer.uid" measurements="measurementsV2021" have-cost="true" />
        </div>-->

        <!-- v2021 新用户信息 -->
        <div class="clearfix" style="margin-bottom: 20px;" ng-if="havePricingExtra">
            <div class="row">
                <h3 class="col-sm-12" style="margin-bottom: 20px;"><strong>新用户信息</strong></h3>
            </div>
            <div class="row">
                <div class="col-sm-2 pricing-extra-key">客户业务介绍：</div>
                <div class="col-sm-4 pricing-extra-value">{{ customer_introduction }}</div>
            </div>
            <div class="row">
                <div class="col-sm-2 pricing-extra-key">已申请的测试费用：</div>
                <div class="col-sm-10 pricing-extra-value">
                    <testing-applications processes="testingApplications" uid="developer.uid" />
                </div>
            </div>
        </div>
        <discussion excode="excode"></discussion>
        <div ng-if="!isLoading && !isOverflow">
            <qbpm-activities excode="{{excode}}"/>
        </div>
        <div ng-if="!isLoading && isOverflow">
            <qbpm-activities excode="{{excode}}"
                approve-countdown="5" 
                approve-tips="我已向价格委员会报备并得到授权审批通过该报价单"
                limit-nodes="['subManager', 'manager']"
            />
        </div>
    </main>
</div>
