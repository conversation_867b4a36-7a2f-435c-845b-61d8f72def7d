<div class="standard-list qn-standard-page approval-detail-portalio-role">
  <main>

    <div class="header clearfix">
      <div class="row">
        <h1 class="col-sm-12">Portal IO 权限申请</h1>
      </div>
    </div>

    <div class="brief clearfix">
      <div class="row" style="margin-bottom: 20px;">
        <h3 class="col-sm-12"><strong>基本信息</strong></h3>
      </div>

      <div class="text-center">
        <button type="button" ng-show="isPageLoading" class="btn btn-success btn-inline" name="button">
          <i class="fa fa-spinner fa-spin"></i>&nbsp;基本信息加载中...
        </button>
        <button type="button" disabled="true" ng-show="!isPageLoading && !applyInfo" class="btn btn-danger btn-inline"
          name="button">
          获取基本信息失败
        </button>
      </div>

      <div class="row" style="margin-bottom: 20px;" ng-show="!isPageLoading && applyInfo">
        <div class="col-sm-12" style="margin-bottom: 40px;">
          <h4>申请原因：</h4>
          <div style="white-space:pre;line-height:1.4em;">{{ applyInfo.reason }}</div>
        </div>

        <div class="col-sm-12">
          <h4>申请的角色列表：</h4>
          <table class="table table-bordered text-center">
            <thead>
              <tr>
                <th>角色</th>
                <th>描述</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="role in applyInfo.roles">
                <td>{{ role.name }}</td>
                <td>{{ role.memo }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <discussion excode="excode"></discussion>
    <qbpm-activities excode="{{excode}}" />

  </main>
</div>
