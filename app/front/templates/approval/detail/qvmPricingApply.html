<div class="qn-standard-page approval-detail-offer">
    <uid-nav></uid-nav>
    <section class="margin-top-30">
        <h2>QVM报价审批详情</h2>
    </section>
    <section class="font-size-16">
        <h4 class="font-size-23"><strong>报价单基本信息</strong></h4>
        <div class="form-group">
            <label class="col-sm-1 label-width control-label-left">客户名：</label>
            <label class="col-sm-4 control-label-left">{{fullName}}</label>
            <label class="col-sm-1 label-width control-label-left">UID：</label>
            <label class="col-sm-2 label-width control-label-left" style="text-align: left;">
                <a ui-sref="layout.developers.developerInfo({key:qvmPricingData.uid})">{{qvmPricingData.uid}}</a>
            </label>
        </div>
        <div class="form-group">
            <div class="col-sm-1 control-label-left">报价生效时间:</div>
            <div class="col-sm-11 control-label-left">{{start_time}} ~ {{end_time}}</div>
        </div>
        <div class="form-group">
            <div class="col-sm-1 control-label-left">描述:</div>
            <div class="col-sm-11 label-width control-label-left">{{qvmPricingData.description ? qvmPricingData.description : ''}}</div>
        </div>
    </section>
    <section>
        <uid-discounts uid="{{qvmPricingData.uid}}" ng-if="qvmPricingData.uid" content-permission="price-biz-pricing-discounts"></uid-discounts>
    </section>
    <section>
        <div>
            <h4 class="font-size-23"><strong>报价明细</strong></h4>
            <table class="table table-bordered text-center table-hover">
                <thead>
                  <tr>
                    <th>资源类型</th>
                    <th>地区</th>
                    <th>购买时长</th>
                    <th>折扣等级</th>
                    <th>折扣比例</th>
                    <th>计费组code</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="item in qvmPricingData.group_prices">
                    <td>{{item.resource_type}}</td>
                    <td>{{item.zone_name}}</td>
                    <td>{{item.duration}}</td>
                    <td class="{{item.discount_level == 99 ? 'text-red' : ''}}">{{DiscountMap[item.discount_level]}}</td>
                    <td class="{{item.discount_level == 99 ? 'text-red' : ''}}">{{item.discount}}%</td>
                    <td>{{item.group_code}}</td>
                  </tr>
                </tbody>
            </table>
        </div>
    </section>
    <section>
        <discussion excode="excode"></discussion>
    </section>
    <section>
        <qbpm-activities excode="{{excode}}"/>
    </section>
</div>
