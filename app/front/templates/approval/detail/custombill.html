<div class="standard-list qn-standard-page risk-approval-detail">
    <uid-nav></uid-nav>
    <section class="margin-top-30">
        <h2>{{op_type}}-申请详情</h2>
    </section>
    <section class="font-size-16">
        <h4>
            <strong>客户信息</strong>
        </h4>
        <div class="form-group">
            <label class="col-sm-2 label-width control-label-left">UID：</label>
            <label class="col-sm-4 label-width control-label-left" style="text-align: left;">
                <a ui-sref="layout.developers.developerInfo({key:uid})">{{ uid }}</a>
            </label>
            <label class="col-sm-2 label-width control-label-left">客户名称：</label>
            <label class="col-sm-4 control-label-left" style="text-align: left;">
                {{userInfo.fullName}}
            </label>
        </div>
        <h4>
            <strong>申请信息</strong>
        </h4>
        <div class="form-group">
            <table class="table table-bordered table-striped table-hover text-center">
                <tbody>
                    <tr>
                        <td>申请类型</td>
                        <td ng-if="!original_quote_sheet"><strong>{{op_type}}</strong></td>
                        <td ng-if="original_quote_sheet"><strong>原自定义账单</strong></td>
                        <td ng-if="original_quote_sheet"><strong>修改后自定义账单</strong></td>
                    </tr>
                    <tr>
                        <td>已关联合同</td>
                        <td ng-if="original_quote_sheet">
                            <a href="/bo/financial/contract/detail?id={{original_quote_sheet.contract_id}}" target="_blank">
                                {{original_quote_sheet.contract_id}}
                            </a>
                        </td>
                        <td ng-class="{'text-red': original_quote_sheet && original_quote_sheet.contract_id != approvalInfo.quote_sheet.contract_id}">
                            <a href="/bo/financial/contract/detail?id={{approvalInfo.quote_sheet.contract_id}}" target="_blank">
                                {{approvalInfo.quote_sheet.contract_id}}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>账单月份</td>
                        <td ng-if="original_quote_sheet" >
                            {{moment(original_quote_sheet.custom_sheets.bill_month).format('YYYY-MM')}}
                        </td>
                        <td ng-class="{'text-red': original_quote_sheet && original_quote_sheet.custom_sheets.bill_month != approvalInfo.quote_sheet.custom_sheets.bill_month}">
                            {{moment(approvalInfo.quote_sheet.custom_sheets.bill_month).format('YYYY-MM')}}
                        </td>
                    </tr>
                    <tr>
                        <td>费用类型</td>
                        <td ng-if="original_quote_sheet">
                            {{original_quote_sheet.custom_sheets.fee_category}}
                        </td>
                        <td ng-class="{'text-red': original_quote_sheet && original_quote_sheet.custom_sheets.fee_category != approvalInfo.quote_sheet.custom_sheets.fee_category}">
                            {{approvalInfo.quote_sheet.custom_sheets.fee_category}}
                        </td>
                    </tr>
                    <tr>
                        <td>归属产品</td>
                        <td ng-if="original_quote_sheet">
                            {{  qiniuProduct[original_quote_sheet.custom_sheets.item_bills[0].beneficiary.toUpperCase()].display
                                || original_quote_sheet.custom_sheets.item_bills[0].beneficiary  }}
                        </td>
                        <td ng-class="{'text-red': original_quote_sheet && original_quote_sheet.custom_sheets.item_bills[0].beneficiary != approvalInfo.quote_sheet.custom_sheets.item_bills[0].beneficiary}">
                            {{  qiniuProduct[approvalInfo.quote_sheet.custom_sheets.item_bills[0].beneficiary.toUpperCase()].display
                                || approvalInfo.quote_sheet.custom_sheets.item_bills[0].beneficiary }}
                        </td>
                    </tr>
                    <tr>
                        <td>账单金额</td>
                        <td ng-if="original_quote_sheet">
                            {{original_quote_sheet.custom_sheets.item_bills[0].amount|
                            moneyWithCurrencyFilter:1:original_quote_sheet.custom_sheets.item_bills[0].currency_type }}
                        </td>
                        <td ng-class="{'text-red': original_quote_sheet && original_quote_sheet.custom_sheets.item_bills[0].amount != approvalInfo.quote_sheet.custom_sheets.item_bills[0].amount}">
                            {{approvalInfo.quote_sheet.custom_sheets.item_bills[0].amount|
                            moneyWithCurrencyFilter:1:approvalInfo.quote_sheet.custom_sheets.item_bills[0].currency_type}}
                        </td>
                    </tr>
                    <tr>
                        <td>账单描述</td>
                        <td ng-if="original_quote_sheet">
                            {{original_quote_sheet.custom_sheets.item_bills[0].desc}}
                        </td>
                        <td ng-class="{'text-red': original_quote_sheet && original_quote_sheet.custom_sheets.item_bills[0].desc != approvalInfo.quote_sheet.custom_sheets.item_bills[0].desc}">
                            {{approvalInfo.quote_sheet.custom_sheets.item_bills[0].desc}}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <h4></h4>

        <div class="form-group" ng-if="approvalInfo.quote_sheet.reason">
            <label class="col-sm-2 label-width control-label-left"><strong>申请原因：</strong></label>
            <label class="col-sm-4 label-width control-label-left" style="font-size: medium;">
                {{approvalInfo.quote_sheet.reason}}
            </label>
        </div>
    </section>
    <section>
        <discussion excode="excode"></discussion>
    </section>
    <section>
        <qbpm-activities excode="{{excode}}"/>
    </section>
</div>
