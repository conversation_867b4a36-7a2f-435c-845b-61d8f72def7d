<div class="standard-list qn-standard-page approval-detail-page">
    <developer-info uid="uid" currency-type="currencyType"></developer-info>
    <main>
        <div class="header clearfix">
            <div class="row" style="margin-bottom: 20px;">
                <h1>
                    合同审批详情
                    <a style="font-size: 14px;" href="{{host}}/bo/financial/contract/detail?id={{excode}}">点此查看提交记录</a>
                </h1>
            </div>
        </div>

        <div style="text-align:center">
            <button type="button" ng-show="isLoading" class="btn btn-success btn-inline" name="button">
                <span><i class="fa fa-spinner fa-spin"></i> 合同申请数据加载中...</span>
            </button>
            <button type="button" disabled="true" ng-show="!isLoading && !data"
                class="btn btn-danger btn-inline" name="button">获取合同申请数据失败</button>
        </div>

        <div class="brief clearfix" style="margin-bottom: 20px;" ng-show="!isLoading">
            <div class="row" ng-if="data.contract_info">
                <h3 class="col-sm-12"><strong>合同基本信息</strong></h3>
            </div>

            <div class="row" ng-if="data.contract_info">   
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row">
                        <div class="col-sm-6" ng-if="data.contract_info.contract_no">
                            <div class="row">
                                <div class="col-sm-4">合同编号:</div>
                                <div class="col-sm-8" id="contract_number">{{ data.contract_info.contract_no }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" content-permission="contract-operation" ng-if="!data.contract_info.contract_no">
                            <div class="row">
                                <div class="col-sm-4">合同编号:</div>
                                <input id="contract_number_edit" maxlength="50" style="display:inline; width:140px" class="form-control" ng-model="contractInfo.contract_no" required/>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.contract_info.record_type.developer_name">
                            <div class="row">
                                <div class="col-sm-4">合同类型:</div>
                                <div class="col-sm-8" id="contract_number">{{ contract_type_map[data.contract_info.record_type.developer_name] }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.contract_info.is_framework_contract">
                            <div class="row">
                                <div class="col-sm-4">是否框架合同:</div>
                                <div class="col-sm-8">{{ data.contract_info.is_framework_contract ? '是' : '否' }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.contract_info.parent_contract.id">
                            <div class="row">
                                <div class="col-sm-4">父合同:</div>
                                <div class="col-sm-8">
                                    <a ui-sref="layout.ContractApprovalDetail({id: data.contract_info.parent_contract.id, uid: data.contract_info.parent_contract.uid})" target="_blank">查看</a>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.contract_info.uid">
                            <div class="row">
                                <div class="col-sm-4">UID:</div>
                                <div class="col-sm-8">
                                    <a ui-sref="layout.developers.developerInfo({key: data.contract_info.uid})" target="_blank">
                                        {{ data.contract_info.uid }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.contract_info.contracting_body">
                            <div class="row">
                                <div class="col-sm-4">签约主体:</div>
                                <div class="col-sm-8">
                                    {{ ContractingBodyTextMap[data.contract_info.contracting_body] }}
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.contract_info.start_date">
                            <div class="row">
                                <div class="col-sm-4">合同有效时间:</div>
                                <div class="col-sm-8">{{ data.contract_info.start_date | formatDateFilter }} ~ {{ data.contract_info.end_date | formatDateFilter }}</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.contract_info.opportunity">
                            <div class="row">
                                <div class="col-sm-4">商业机会:</div>
                                <div class="col-sm-8">{{ data.contract_info.opportunity}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.contract_info.solution">
                            <div class="row">
                                <div class="col-sm-4">场景方案:</div>
                                <div class="col-sm-8">{{ data.contract_info.solution}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6", ng-if="data.contract_info.created_by.name">
                            <div class="row">
                                <div class="col-sm-4">合同创建人:</div>
                                <div class="col-sm-8">{{ data.contract_info.created_by.name }}</div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="row">
                                <div class="col-sm-4">账期:</div>
                                <div class="col-sm-8" ng-if="data.contract_info.account_period">
                                    {{ data.contract_info.account_period }}&nbsp;
                                    {{ data.contract_info.account_period_unit | creditPeriodUnit }}
                                </div>
                                <div class="col-sm-8" ng-if="!data.contract_info.account_period">0</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="identityInfo">
                            <div class="row">
                                <div class="col-sm-4">认证状态:</div>
                                <div class="col-sm-8">
                                    {{ identityInfo|identityFilter}}
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="identityInfo">
                            <div class="row">
                                <div class="col-sm-4">认证类型:</div>
                                <div class="col-sm-8">
                                    {{ identityInfo.type|identityTypeFilter}}
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="identityInfo">
                            <div class="row">
                                <div class="col-sm-4">认证企业（个人）名称:</div>
                                <div class="col-sm-8">
                                    {{ identityInfo.enterprise_name || identityInfo.contact_name }}
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12", ng-if="data.contract_info.description">
                            <div class="row">
                                <div class="col-sm-2">合同描述:</div>
                                <div class="col-sm-10">{{ data.contract_info.description }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" style="margin-top: 20px;" ng-if="data.attachments">
                <h3 class="col-sm-12"><strong>合同主体</strong></h3>
            </div>

            <div class="row" ng-if="data.attachments">   
                <div ng-repeat="attachment in data.attachments" class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row">
                        <div class="col-sm-12">
                            <a 
                                ng-disabled="!attachment.file_url"
                                class="btn btn-primary"
                                href="{{attachment.file_url}}"
                                target="_blank"
                            >
                                下载
                            </a>
                            <label> {{attachment.file_name}} ({{attachment.size | byteSizeFilter}})</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" style="margin-top: 20px;" ng-if="quoteSheets">
                <h3 class="col-sm-12"><strong>报价单</strong></h3>
            </div>

            <div class="row" ng-if="quoteSheets">
                <div class="col-sm-12">
                    <table class="table table-bordered text-center contract-table">
                        <thead>
                            <tr>
                                <th>报价单编号</th>
                                <th>有效时间</th>
                                <th>操作</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="quotation in quoteSheets">
                                <td>{{quotation.sn}}</td>
                                <td>{{quotation.start_time | formatDateFilter }} ~ {{ quotation.end_time | formatDateFilter }}</td>
                                <td>{{ quote_sheet_status_map[quotation.status] }}</td>
                                <td>
                                    <a href="/bo/financial/quote-sheet/detail?uid={{quotation.uid}}&sn={{quotation.sn}}" target="_blank">
                                        查看
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="row" style="margin-top: 20px;" ng-if="!isStandardContract">
                <h3 class="col-sm-12"><strong>非标报价和投入</strong></h3>
            </div>

            <div class="row" style="margin-top: 20px;" ng-if="data.custom_pricing">
                <h4 class="col-sm-12"><strong>服务内容</strong></h4>
            </div>

            <div class="row" ng-if="data.custom_pricing">   
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row">
                        <div class="col-sm-6" ng-if="data.custom_pricing.record_type.developer_name">
                            <div class="row">
                                <div class="col-sm-4">合同类型:</div>
                                <div class="col-sm-8">{{ charge_type_map[data.custom_pricing.record_type.developer_name] }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.total_amount != null">
                            <div class="row">
                                <div class="col-sm-4">合同总金额:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.total_amount | moneyWithCurrencyFilter:1:currencyType }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.products">
                            <div class="row">
                                <div class="col-sm-4">涉及产品线:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.products | listExpand:product_map}}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.kodo_secondary_product">
                            <div class="row">
                                <div class="col-sm-4">KODO二级产品:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.kodo_secondary_product}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.custom_pricing.total_contract_capacity">
                            <div class="row">
                                <div class="col-sm-4">合同签约总容量:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.total_contract_capacity}} TB</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.custom_pricing.deploy_unit_price">
                            <div class="row">
                                <div class="col-sm-4">首年部署单价:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.deploy_unit_price | moneyWithCurrencyFilter:1:currencyType }} / TB</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.custom_pricing.maintenance_unit_price">
                            <div class="row">
                                <div class="col-sm-4">维保单价:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.maintenance_unit_price | moneyWithCurrencyFilter:1:currencyType}} / TB</div>
                            </div>
                        </div>

                        <div class="col-sm-12", ng-if="data.custom_pricing.custom_billing_memo">
                            <div class="row">
                                <div class="col-sm-2">非标计费描述:</div>
                                <div class="col-sm-10">{{ data.custom_pricing.custom_billing_memo }}</div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="row" style="margin-top: 40px;" ng-if="data.custom_pricing && data.custom_pricing.items">
                <h4 class="col-sm-12"><strong>服务项目</strong></h4>
            </div>

            <div class="row" ng-repeat="(key, value) in data.custom_pricing.items" ng-if="data.custom_pricing && data.custom_pricing.items">
                
                <div class="col-sm-12" style="font-size: 16px;">
                    {{value.project_type}}
                </div>

                <div class="row" style="padding-bottom: 20px; margin-left:30px; margin-top:30px; font-size: 16px;">
                    <div class="col-sm-6">
                        <div class="row">
                            <div class="col-sm-4">金额:</div>
                            <div class="col-sm-8">{{ value.amount | moneyWithCurrencyFilter:1:currencyType }}</div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="row">
                            <div class="col-sm-4">税率:</div>
                            <div class="col-sm-8">{{ value.tax_rate }}</div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="row">
                            <div class="col-sm-4">预期交付时间:</div>
                            <div class="col-sm-8">{{ value.delivery_date | formatDateFilter}}</div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="row">
                            <div class="col-sm-4">预期收款时间:</div>
                            <div class="col-sm-8">{{ value.collection_date | formatDateFilter}}</div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="row">
                            <div class="col-sm-4">是否外采:</div>
                            <div class="col-sm-8">{{ value.is_purchase ? "是" : "否"}}</div>
                        </div>
                    </div>

                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-2">备注:</div>
                            <div class="col-sm-10">{{ value.memo }}</div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="row" style="margin-top: 40px;" ng-if="!isStandardContract && !isPublicCustomPricing">
                <h4 class="col-sm-12"><strong>投入详情</strong></h4>
            </div>

            <div class="row" ng-if="!isStandardContract && !isPublicCustomPricing">   
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row">

                        <div class="col-sm-6" ng-if="data.custom_pricing.deployed_machine_number != null">
                            <div class="row">
                                <div class="col-sm-4">部署机器台数:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.deployed_machine_number }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.purchase_machine_number != null">
                            <div class="row">
                                <div class="col-sm-4">外采机器台数:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.purchase_machine_number }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.auth_software_name != null">
                            <div class="row">
                                <div class="col-sm-4">授权软件名称:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.auth_software_name }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.sla_level != null">
                            <div class="row">
                                <div class="col-sm-4">SLA等级:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.sla_level }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.business_man_day != null">
                            <div class="row">
                                <div class="col-sm-4">商务预计投入人天:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.business_man_day }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.develop_man_day != null">
                            <div class="row">
                                <div class="col-sm-4">技术研发预计投入人天:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.develop_man_day }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.operation_man_day != null">
                            <div class="row">
                                <div class="col-sm-4">运维预计投入人天:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.operation_man_day }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.training_man_day != null">
                            <div class="row">
                                <div class="col-sm-4">培训预计投入人天:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.training_man_day }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.product_man_day != null">
                            <div class="row">
                                <div class="col-sm-4">产品线投入人天:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.product_man_day }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.ts_man_day != null">
                            <div class="row">
                                <div class="col-sm-4">技术支持投入人天:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.ts_man_day }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.managed_machine_number != null">
                            <div class="row">
                                <div class="col-sm-4">预计托管机器台数/月:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.managed_machine_number }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.cabinet_count != null">
                            <div class="row">
                                <div class="col-sm-4">占用机柜个数/月:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.cabinet_count }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.port_count != null">
                            <div class="row">
                                <div class="col-sm-4">占用端口个数/月:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.port_count }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.ip_count != null">
                            <div class="row">
                                <div class="col-sm-4">占用IP个数/月:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.ip_count }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.bandwidth != null">
                            <div class="row">
                                <div class="col-sm-4">预计带宽:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.bandwidth }} {{data.custom_pricing.bandwidth_unit}}</div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="row" style="margin-top: 40px;" ng-if="showFinanceHeader">
                <h4 class="col-sm-12"><strong>投入预估</strong></h4>
            </div>
            <div class="row" style="margin-top: 40px;" content-permission="contract-finance-form" ng-if="!showFinanceHeader && showFinanceEdit">
                <h4 class="col-sm-12"><strong>投入预估</strong></h4>
            </div>

            <div class="row" ng-if="showFinanceHeader || showFinanceEdit">   
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row">
                        <div class="col-sm-6" ng-if="data.custom_pricing.machine_trustee_cost != null">
                            <div class="row">
                                <div class="col-sm-4">机器托管投入:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.machine_trustee_cost | moneyWithCurrencyFilter:1:currencyType }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.bandwidth_cost != null">
                            <div class="row">
                                <div class="col-sm-4">预计带宽投入/月:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.bandwidth_cost | moneyWithCurrencyFilter:1:currencyType }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.purchase_develop_cost != null">
                            <div class="row">
                                <div class="col-sm-4">第三方采购开发服务投入:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.purchase_develop_cost | moneyWithCurrencyFilter:1:currencyType }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.hardware_purchase_agent_cost != null">
                            <div class="row">
                                <div class="col-sm-4">硬件代采预估:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.hardware_purchase_agent_cost | moneyWithCurrencyFilter:1:currencyType }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.human_cost != null">
                            <div class="row">
                                <div class="col-sm-4">人力投入预估:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.human_cost | moneyWithCurrencyFilter:1:currencyType }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.total_cost != null">
                            <div class="row">
                                <div class="col-sm-4">投入预估总计:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.total_cost | moneyWithCurrencyFilter:1:currencyType }}</div>
                            </div>
                        </div>

                        <div class="col-sm-6" ng-if="data.custom_pricing.gross_profit != null">
                            <div class="row">
                                <div class="col-sm-4">利润预估:</div>
                                <div class="col-sm-8">{{ data.custom_pricing.gross_profit | moneyWithCurrencyFilter:1:currencyType }}</div>
                            </div>
                        </div>
                        
                        <!-- edit -->

                        <div class="col-sm-6" content-permission="contract-finance-form" ng-if="data.custom_pricing.record_type.developer_name == 'MServer' && data.custom_pricing.machine_trustee_cost == null">
                            <div class="row">
                                <div class="col-sm-4">机器托管投入:</div>
                                <div class="col-sm-8" >
                                    <span class="input-group-addon" style="display:inline; margin-left:-5px">{{ currencyType == 'USD' ? '$' : '&yen;'}}</span>
                                    <input type="number" ng-keyup="checkTwoDecimals($event)" step="0.01" style="display:inline; width:140px" class="form-control" ng-model="financeParams.machine_trustee_cost" required/>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" content-permission="contract-finance-form" ng-if="data.custom_pricing.record_type.developer_name == 'MServer' && data.custom_pricing.bandwidth_cost == null">
                            <div class="row">
                                <div class="col-sm-4">预计带宽投入/月:</div>
                                <div class="col-sm-8" >
                                    <span class="input-group-addon" style="display:inline; margin-left:-5px">{{ currencyType == 'USD' ? '$' : '&yen;'}}</span>
                                    <input type="number" ng-keyup="checkProfit($event)" step="0.01" style="display:inline; width:140px" class="form-control" ng-model="financeParams.bandwidth_cost" required/>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" content-permission="contract-finance-form" ng-if="data.custom_pricing.record_type.developer_name == 'PrivateProject' && data.custom_pricing.hardware_purchase_agent_cost == null">
                            <div class="row">
                                <div class="col-sm-4">硬件代采预估:</div>
                                <div class="col-sm-8" >
                                    <span class="input-group-addon" style="display:inline; margin-left:-5px">{{ currencyType == 'USD' ? '$' : '&yen;'}}</span>
                                    <input type="number" style="display:inline; width:140px" class="form-control" ng-keyup="checkTwoDecimals($event)" step="0.01" ng-model="financeParams.hardware_purchase_agent_cost" required/>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" content-permission="contract-finance-form" ng-if="data.custom_pricing.record_type.developer_name != 'PublicCustom' && data.custom_pricing.human_cost == null">
                            <div class="row">
                                <div class="col-sm-4">人力投入预估:</div>
                                <div class="col-sm-8" >
                                    <span class="input-group-addon" style="display:inline; margin-left:-5px">{{ currencyType == 'USD' ? '$' : '&yen;'}}</span>
                                    <input type="number" style="display:inline; width:140px" ng-keyup="checkTwoDecimals($event)" step="0.01" class="form-control" ng-model="financeParams.human_cost" required/>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" content-permission="contract-finance-form" ng-if="data.custom_pricing.record_type.developer_name != 'PublicCustom' && data.custom_pricing.total_cost == null">
                            <div class="row">
                                <div class="col-sm-4">投入预估总计:</div>
                                <div class="col-sm-8" >
                                    <span class="input-group-addon" style="display:inline; margin-left:-5px">{{ currencyType == 'USD' ? '$' : '&yen;'}}</span>
                                    <input type="number" style="display:inline; width:140px" ng-keyup="checkTwoDecimals($event)" step="0.01" class="form-control" ng-model="financeParams.total_cost" required/>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" content-permission="contract-finance-form" ng-if="data.custom_pricing.record_type.developer_name != 'PublicCustom' && data.custom_pricing.gross_profit == null">
                            <div class="row">
                                <div class="col-sm-4">利润预估:</div>
                                <div class="col-sm-8" >
                                    <span class="input-group-addon" style="display:inline; margin-left:-5px">{{ currencyType == 'USD' ? '$' : '&yen;'}}</span>
                                    <input id="profit" type="number" ng-keyup="checkProfit($event)" step="0.01" style="display:inline; width:140px" class="form-control" ng-model="financeParams.gross_profit" required/>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <discussion excode="excode"></discussion>
        <qbpm-activities excode="{{excode}}"/>
    </main>
</div>
