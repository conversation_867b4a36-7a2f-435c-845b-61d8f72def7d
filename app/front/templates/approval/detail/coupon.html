<div class="qn-standard-page approval-detail-coupon">
  <uid-nav></uid-nav>
  <section class="margin-top-30">
    <h1>包年包月（订单）抵用券审批详情</h1>
  </section>
  <section class="font-size-16">
    <h3><strong>基本信息</strong></h3>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">UID：</label>
      <label class="col-sm-2 label-width control-label-left" style="text-align: left;">
        <a ui-sref="layout.developers.developerInfo({key:coupon.uid})">{{ coupon.uid }}</a>
      </label>
      <label class="col-sm-2 label-width control-label-left">客户：</label>
      <label class="col-sm-4 control-label-left" style="text-align: left;">{{ coupon.fullname }}</label>
    </div>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">名称：</label>
      <div class="col-sm-4">
        <span class="control-label-left">{{ coupon.name }}</span>
      </div>
    </div>

    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">描述：</label>
      <div class="col-sm-4">
        <span class="control-label-left">{{ coupon.description}}</span>
      </div>
    </div>

    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">事由分类：</label>
      <div class="col-sm-4">
        <select class="form-control" name="reason" ng-model="coupon.reason" 
          ng-options="v.value as v.display for (k, v) in BIZ_TYPE" ng-disabled="true">
          <option value="">---- 请选择申请本次抵用券的事由分类 ----</option>
        </select>
        <span class="help-block" ng-messages="salesApplyForm.reason.$error" ng-show="salesApplyForm.reason.$dirty">
          <span ng-message="required">必选</span>
        </span>
      </div>
    </div>

    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">事由：</label>
      <div class="col-sm-4">
        <span class="control-label-left">{{ coupon.reason_desc}}</span>
      </div>
    </div>
    <uid-discounts uid="{{coupon.uid}}" ng-if="coupon.uid" content-permission="price-biz-pricing-discounts"></uid-discounts>
    <h3><strong>抵用券规则</strong></h3>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">满减类型：</label>
      <label class="col-sm-4 label-width control-label-left" style="text-align: left; width: 200px;" ng-show="coupon.threshold_money > 0 ">有门槛（满 {{coupon.threshold_money | priceFilter}} 元）</label>
      <label class="col-sm-4 label-width control-label-left" style="text-align: left; width: 200px;" ng-show="coupon.threshold_money == 0">无门槛</label>
    </div>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">使用次数：</label>
      <label class="col-sm-2 label-width control-label-left" style="text-align: left;">可多次使用</label>
    </div>
    <div class="form-group">
      <div class="form-inline">
        <label class="col-sm-2 label-width control-label-left">抵用券面额：</label>
        <div class="col-sm-2 label-width">
          <span class="control-label-left">{{ coupon.coupon_money | priceFilter }}元</span>
        </div>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">生效时间：</label>
      <div class="col-sm-4">
        <span class="control-label-left">
          {{ coupon.coupon_effect_time }} ~ {{ coupon.coupon_dead_time }}
        </span>
      </div>
    </div>
    <div class="form-group">
      <div class="form-inline">
        <label class="col-sm-2 label-width control-label-left">预付费商家：</label>
        <div class="col-sm-8">
          <span class="control-label-left">{{ coupon.sellers }}</span>
        </div>
      </div>
    </div>
    <div class="form-group">
      <div class="form-inline">
        <label class="col-sm-2 label-width control-label-left">使用范围文案：</label>
        <span class="control-label-left col-sm-8">{{ coupon.coupon_scope_desc }}</span>
      </div>
    </div>
  </section>
  <section>
    <discussion excode="excode"></discussion>
  </section>
  <section>
    <qbpm-activities excode="{{excode}}"/>
  </section>
</div>