<div class="standard-list qn-standard-page">
  <uid-nav></uid-nav>
  <section class="margin-top-30">
    <h2>订单转后付费审批申请</h2>
  </section>
  <section class="font-size-16">
    <h4>
      <strong>客户信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-1 label-width control-label-left">UID：</label>
      <label class="col-sm-2 label-width control-label-left" style="text-align: left;">
        <a ui-sref="layout.developers.developerInfo({key:uid})">{{ uid }}</a>
      </label>
      <label class="col-sm-1 label-width control-label-left">认证名称：</label>
      <label class="col-sm-3 control-label-left" style="text-align: left;">
        {{userInfo.fullName}}
      </label>
      <label class="col-sm-1 label-width control-label-left">用户归属：</label>
      <label class="col-sm-3 control-label-left" style="text-align: left;">
        <span ng-show="salesInfo">{{salesInfo.email}}</span>
        <span ng-show="!salesInfo">{{userInfo.sfSalesId}}</span>
      </label>
    </div>
    <h4>
      <strong>订单信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-1 label-width control-label-left">订单编号：</label>
      <label class="col-sm-5 control-label-left" style="text-align: left;"><a href="/bo/financial/valet/detail/{{orderHash}}" target="_blank">{{orderHash}}</a></label>
      <label class="col-sm-1 label-width control-label-left">下单时间：</label>
      <label class="col-sm-5 control-label-left" style="text-align: left;">{{orderDetail.create_time | financialUnixMSecFormatter}}</label>
    </div>
    <div class="form-group">
      <table class="table table-bordered table-hover">
        <thead style="background: #f5f5f5">
          <tr>
            <td>商品名称</td>
            <td>数量</td>
            <td>时长</td>
            <th>分配方式</th>
            <td>原应付金额</td>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="po in orderDetail.product_orders">
            <td>{{po.product_name}}</td>
            <td>{{po.quantity}}</td>
            <td>{{po.duration}} {{PRODUCT_UNIT_MAP[po.product.unit]}}</td>
            <td>{{po | parsePoCarryPolicyFilter}}</td>
            <td>{{po.c_fee | moneyWithCurrencyFilter:1:po.currency_type}}</td>
          </tr>
        </tbody>
      </table>
      <p class="text-right new-fee-block">
        <span>订单需支付总费用：</span>
        <span>{{ orderDetail.c_fee.toFixed(2) | moneyWithCurrencyFilter:1:orderDetail.currency_type }}</span>
      </p>
      <p class="text-right new-fee-block">
        <span class="red">将此订单转为后付费</span>
      </p>
    </div>
    <h4>
      <strong>申请原因</strong>
    </h4>
    <div class="form-group">
      <p style="font-size: large;">{{approvalInfo.reason}}</p>
    </div>
  </section>
  <section>
    <discussion excode="excode"></discussion>
  </section>
  <section>
    <qbpm-activities excode="{{excode}}" process-instance-id="{{process_instance_id}}" />
  </section>
</div>
