<div class="standard-list qn-standard-page">
  <uid-nav></uid-nav>
  <section class="margin-top-30">
    <h2>添加组织成员账号申请详情</h2>
  </section>
  <section class="font-size-16">
    <div ng-show="detail && detail.org" class="form-group">
      <h4>
        <strong>组织信息</strong>
      </h4>
      <div class="form-group">
        <label class="col-sm-2 label-width control-label-left">组织 ID：</label>
        <label class="col-sm-4 label-width control-label-left" style="text-align: left;">
          <a href="/bo/developers/enterprise/organization/members/{{detail.org.id}}">
            {{detail.org.id}}
          </a>
        </label>
        <label class="col-sm-2 label-width control-label-left">组织昵称：</label>
        <label class="col-sm-4 control-label-left" style="text-align: left;">
          {{detail.org.name}}
        </label>
      </div>
      <div class="form-group">
        <label class="col-sm-2 label-width control-label-left">组织管理员 UID：</label>
        <label class="col-sm-4 control-label-left" style="text-align: left;">
          <a ui-sref="layout.developers.developerInfo({key:detail.org.manager_uid})">{{detail.org.manager_uid}}</a>
        </label>
        <label class="col-sm-2 label-width control-label-left">管理员实名认证名称：</label>
        <label class="col-sm-4 control-label-left" style="text-align: left;">
          {{detail.org.manager_name}}
        </label>
      </div>
    </div>
    <div ng-show="detail && detail.parent" class="form-group">
      <h4>
        <strong>财务主账号信息</strong>
      </h4>
      <div class="form-group">
        <label class="col-sm-2 label-width control-label-left">财务主账号 UID：</label>
        <label class="col-sm-4 control-label-left" style="text-align: left;">
          <a ui-sref="layout.developers.developerInfo({key:detail.parent.uid})">{{detail.parent.uid}}</a>
        </label>
        <label class="col-sm-2 label-width control-label-left">实名认证名称：</label>
        <label class="col-sm-4 control-label-left" style="text-align: left;">
          {{detail.parent.name}}
        </label>
      </div>
      <div class="form-group">
        <label class="col-sm-2 label-width control-label-left">财务模式：</label>
        <label class="col-sm-4 control-label-left" style="text-align: left;">
          {{FinancialTypeDisplay[detail.parent.financial_type]}}
        </label>
      </div>
    </div>
    <div ng-show="detail && detail.children" class="form-group">
      <h4>
        <strong>财务从账号信息</strong>
      </h4>
      <div ng-repeat="item in detail.children">
        <div class="form-group">
          <label class="col-sm-2 label-width control-label-left">财务从账号 UID：</label>
          <label class="col-sm-4 control-label-left">
            <a ui-sref="layout.developers.developerInfo({key:item.uid})">{{item.uid}}</a>
          </label>
          <label class="col-sm-2 label-width control-label-left">实名认证名称：</label>
          <label class="col-sm-4 control-label-left">
            {{item.name}}
          </label>
        </div>
        <div class="form-group">
          <label class="col-sm-2 label-width control-label-left">合并余额至主账号：</label>
          <label class="col-sm-4 control-label-left">
            {{item.merge_balance ? '是' : '否'}}
          </label>
          <label class="col-sm-2 label-width control-label-left">备注：</label>
          <label class="col-sm-4 control-label-left">
            {{item.memo}}
          </label>
        </div>
      </div>
    </div>
    <h4>
      <strong>申请信息</strong>
    </h4>
    <div class="from-group">
      <label class="col-sm-2 label-width control-label-left">申请理由：</label>
      <label class="col-sm-10 control-label-left">
        {{detail.reason}}
      </label>
    </div>
    <div class="form-group" ng-show="detail.attachments">
      <label class="col-sm-2 label-width control-label-left">附件：</label>
      <label class="col-sm-10 control-label-left">
        <a ng-repeat="attachment in attachments" href="{{attachment.url}}" target="_blank" style="display: block">
          {{attachment.file}}
        </a>
    </div>
  </section>
  <section>
    <discussion excode="excode"></discussion>
  </section>
  <section>
    <qbpm-activities excode="{{excode}}"/>
  </section>
</div>
