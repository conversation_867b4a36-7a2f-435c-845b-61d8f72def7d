<div class="standard-list qn-standard-page">
  <uid-nav></uid-nav>
  <section class="margin-top-30">
    <h2>资源包延期审批申请</h2>
  </section>
  <section class="font-size-16">
    <h4>
      <strong>客户信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-1 label-width control-label-left">UID：</label>
      <label class="col-sm-2 label-width control-label-left" style="text-align: left;">
        <a ui-sref="layout.developers.developerInfo({key:uid})">{{ uid }}</a>
      </label>
      <label class="col-sm-1 label-width control-label-left">认证名称：</label>
      <label class="col-sm-3 control-label-left" style="text-align: left;">
        {{userInfo.fullName}}
      </label>
      <label class="col-sm-1 label-width control-label-left">用户归属：</label>
      <label class="col-sm-3 control-label-left" style="text-align: left;">
        <span ng-show="salesInfo">{{salesInfo.email}}</span>
        <span ng-show="!salesInfo">{{userInfo.sfSalesId}}</span>
      </label>
    </div>
    <h4>
      <strong>订单信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-1 label-width control-label-left">订单编号：</label>
      <label class="col-sm-5 control-label-left" style="text-align: left;"><a href="/bo/financial/valet/detail/{{orderHash}}" target="_blank">{{orderHash}}</a></label>
      <label class="col-sm-1 label-width control-label-left">下单时间：</label>
      <label class="col-sm-5 control-label-left" style="text-align: left;">{{orderDetail.create_time | financialUnixMSecFormatter}}</label>
    </div>
    <div class="form-group">
      <table class="table table-bordered table-hover">
        <thead style="background: #f5f5f5">
          <tr>
            <td>商品名称</td>
            <td>数量</td>
            <td>时长</td>
            <td ng-if="approvalInfo.req_product_order_extend_duration.capacity>0">总量</td>
            <td ng-if="approvalInfo.req_product_order_extend_duration.capacity>0">已用(含未出账)</td>
            <td>原结束时间</td>
            <td>新结束时间</td>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="po in orderDetail.product_orders" ng-class="{'red': po.new_start_time}">
            <td>{{po.product_name}}</td>
            <td>{{po.quantity}}</td>
            <td>{{po.duration}} {{PRODUCT_UNIT_MAP[po.product.unit]}}</td>
            <td ng-if="approvalInfo.req_product_order_extend_duration.capacity>0">
              {{approvalInfo.req_product_order_extend_duration.capacity}}
              {{approvalInfo.req_product_order_extend_duration.respack_unit}}
            </td>
            <td ng-if="approvalInfo.req_product_order_extend_duration.capacity>0">
              {{(approvalInfo.req_product_order_extend_duration.used/approvalInfo.req_product_order_extend_duration.capacity*100).toFixed(2)}}%
            </td>
            <td>{{po.end_time | tradeTimeFormat }}</td>
            <td>{{po.new_end_time || '-'}}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <h4>
      <strong>申请原因</strong>
    </h4>
    <div class="form-group">
      <p style="font-size: large;">{{approvalInfo.reason}}</p>
    </div>
  </section>
  <section>
    <discussion excode="excode"></discussion>
  </section>
  <section>
    <qbpm-activities excode="{{excode}}" process-instance-id="{{process_instance_id}}" />
  </section>
</div>
