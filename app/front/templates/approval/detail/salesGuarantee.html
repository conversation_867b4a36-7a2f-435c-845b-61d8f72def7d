<div class="standard-list qn-standard-page">
  <main>

    <div class="header clearfix">
      <div class="row">
        <h1 class="col-sm-12">大客户认证审批详情</h1>
      </div>
    </div>

    <div class="brief clearfix">
      <div class="row" style="margin-bottom: 20px;">
        <h3 class="col-sm-12"><strong>基本信息</strong></h3>
      </div>

      <div class="text-center">
        <button
          type="button"
          ng-show="isPageLoading"
          class="btn btn-success btn-inline"
          name="button"
        >
          <i class="fa fa-spinner fa-spin"></i>&nbsp;基本信息加载中...
        </button>
        <button
          type="button"
          disabled="true"
          ng-show="!isPageLoading && !currentApply"
          class="btn btn-danger btn-inline"
          name="button"
        >
          获取基本信息失败
        </button>
      </div>

      <div class="row" style="margin-bottom: 20px;" ng-show="!isPageLoading && currentApply">
        <div class="col-sm-12">
          <div class="col-sm-6" ng-if="currentApply.uid">
            <div class="row">
              <div class="col-sm-6">用户 UID：</div>
              <div class="col-sm-6">
                <a href="javascript:void(0);" ui-sref="layout.developers.developerInfo({key: currentApply.uid})" target="_blank">
                  {{ currentApply.uid }}
                </a>
              </div>
            </div>
          </div>
          <div class="col-sm-6" ng-if="currentApply.enterprise_name">
            <div class="row">
              <div class="col-sm-6">企业名称：</div>
              <div class="col-sm-6">
                {{ currentApply.enterprise_name }}
                <del ng-if="historySalesGuarantee && currentApply.enterprise_name !== historySalesGuarantee.enterprise_name">
                  （{{ historySalesGuarantee.enterprise_name }}）
                </del>
              </div>
            </div>
          </div>
          <div class="col-sm-6" ng-if="currentApply.enterprise_code">
            <div class="row">
              <div class="col-sm-6">营业执照编号：</div>
              <div class="col-sm-6">
                {{ currentApply.enterprise_code }}
                <del ng-if="historySalesGuarantee && currentApply.enterprise_code !== historySalesGuarantee.enterprise_code">
                  （{{ historySalesGuarantee.enterprise_code }}）
                </del>
              </div>
            </div>
          </div>
          <div class="col-sm-6" ng-if="currentApply.industry_classification">
            <div class="row">
              <div class="col-sm-6">所属行业：</div>
              <div class="col-sm-6">
                {{ currentApply.industry_classification }}
                <span ng-if="currentApply.industry_sub_classification">
                  - {{ currentApply.industry_sub_classification }}
                </span>
                <del ng-if="historySalesGuarantee && currentApply.industry_classification !== historySalesGuarantee.industry_classification">
                  （{{ historySalesGuarantee.industry_classification }}
                  <span ng-if="historySalesGuarantee.industry_sub_classification">
                    （{{ historySalesGuarantee.industry_sub_classification }}）
                  </span>）
                </del>
              </div>
            </div>
          </div>
          <div class="col-sm-12">
            <div class="row" ng-if="currentApply.memo">
              <div class="col-sm-12">申请原因：</div>
              <div class="col-sm-12">{{ currentApply.memo }}</div>
            </div>
            <div class="row" ng-if="currentApply.enterprise_code_copy_url">
              <div class="col-sm-12">营业执照：</div>
              <div class="col-sm-12">
                <img width="400" class="bordered" ng-src="{{currentApply.enterprise_code_copy_url}}"/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <discussion excode="excode"></discussion>
    <qbpm-activities excode="{{excode}}" />

  </main>
</div>
