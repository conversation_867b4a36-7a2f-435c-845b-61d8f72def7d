<div class="standard-list qn-standard-page risk-approval-detail">
    <uid-nav></uid-nav>
    <section class="margin-top-30">
        <h2>{{approvalInfo.product.toUpperCase()}}账单-重出账申请详情</h2>
    </section>
    <loading ng-show="isLoading"></loading>
    <section class="font-size-16">
        <h4>
            <strong>客户信息</strong>
        </h4>
        <div class="form-group">
            <label class="col-sm-2 label-width control-label-left">UID：</label>
            <label class="col-sm-4 label-width control-label-left" style="text-align: left;">
                <a ui-sref="layout.developers.developerInfo({key:uid})">{{ uid }}</a>
            </label>
            <label class="col-sm-2 label-width control-label-left">客户名称：</label>
            <label class="col-sm-4 control-label-left" style="text-align: left;">
                {{userInfo.fullName}}
            </label>
        </div>
        <h4>
            <strong>申请信息</strong>
        </h4>
        <div class="form-group">
            <label class="col-sm-2 label-width control-label-left">申请重出月份：</label>
            <label class="col-sm-4 label-width control-label-left" style="font-size: medium;">
                {{approvalInfo.month_start}} ~ {{approvalInfo.month_end}}
            </label>
        </div>
        <div class="form-group">
            <label class="col-sm-2 label-width control-label-left">申请理由：</label>
            <label class="col-sm-10 label-width control-label-left" style="font-size: medium;">
                {{approvalInfo.reason}}
            </label>
        </div>
        <section>
            <h4><strong>账单对比</strong></h4>
            <div class="form-group">
                <table class="table table-bordered table-striped table-hover text-center">
                    <thead>
                    <tr>
                        <th width="15%">月份</th>
                        <th width="35%">账单项目</th>
                        <th width="15%">原账单金额</th>
                        <th width="15%">新账单金额</th>
                        <th width="20%">差额</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat-start="month in comparedBills">
                        <td rowspan="{{getUniqueItems(month).length + 1}}">{{month.month.substring(0,4)}}年{{month.month.substring(4)}}月</td>
                        <td>{{getUniqueItems(month)[0]}}</td>
                        <td>
                            <span ng-if="getBillByItemStr(month.oldBills, getUniqueItems(month)[0]) !== null && getBillByItemStr(month.oldBills, getUniqueItems(month)[0]) !== undefined">
                                {{getBillByItemStr(month.oldBills, getUniqueItems(month)[0]) | nmoneyFormatter:2}}
                            </span>
                            <span ng-if="getBillByItemStr(month.oldBills, getUniqueItems(month)[0]) === null || getBillByItemStr(month.oldBills, getUniqueItems(month)[0]) === undefined">-</span>
                        </td>
                        <td>
                            <span ng-if="getBillByItemStr(month.newBills, getUniqueItems(month)[0]) !== null && getBillByItemStr(month.newBills, getUniqueItems(month)[0]) !== undefined">
                                {{getBillByItemStr(month.newBills, getUniqueItems(month)[0]) | nmoneyFormatter:2}}
                            </span>
                            <span ng-if="getBillByItemStr(month.newBills, getUniqueItems(month)[0]) === null || getBillByItemStr(month.newBills, getUniqueItems(month)[0]) === undefined">-</span>
                        </td>
                        <td ng-class="{
                            'text-danger': getDiff(getBillByItemStr(month.newBills, getUniqueItems(month)[0]), getBillByItemStr(month.oldBills, getUniqueItems(month)[0])) > 0,
                            'text-success': getDiff(getBillByItemStr(month.newBills, getUniqueItems(month)[0]), getBillByItemStr(month.oldBills, getUniqueItems(month)[0])) < 0
                        }">
                            <span ng-if="getBillByItemStr(month.oldBills, getUniqueItems(month)[0]) === null || getBillByItemStr(month.oldBills, getUniqueItems(month)[0]) === undefined || getBillByItemStr(month.newBills, getUniqueItems(month)[0]) === null || getBillByItemStr(month.newBills, getUniqueItems(month)[0]) === undefined">-</span>
                            <span ng-if="getBillByItemStr(month.oldBills, getUniqueItems(month)[0]) !== null && getBillByItemStr(month.oldBills, getUniqueItems(month)[0]) !== undefined && getBillByItemStr(month.newBills, getUniqueItems(month)[0]) !== null && getBillByItemStr(month.newBills, getUniqueItems(month)[0]) !== undefined">
                                {{getDiff(getBillByItemStr(month.newBills, getUniqueItems(month)[0]), getBillByItemStr(month.oldBills, getUniqueItems(month)[0])) | nmoneyFormatter:2}}
                                <i ng-if="getDiff(getBillByItemStr(month.newBills, getUniqueItems(month)[0]), getBillByItemStr(month.oldBills, getUniqueItems(month)[0])) > 0" class="fa fa-arrow-up"></i>
                                <i ng-if="getDiff(getBillByItemStr(month.newBills, getUniqueItems(month)[0]), getBillByItemStr(month.oldBills, getUniqueItems(month)[0])) < 0" class="fa fa-arrow-down"></i>
                            </span>
                        </td>
                    </tr>
                    <tr ng-repeat="item_str in getUniqueItems(month).slice(1)">
                        <td>{{item_str}}</td>
                        <td>
                            <span ng-if="getBillByItemStr(month.oldBills, item_str) !== null && getBillByItemStr(month.oldBills, item_str) !== undefined">
                                {{getBillByItemStr(month.oldBills, item_str) | nmoneyFormatter:2}}
                            </span>
                            <span ng-if="getBillByItemStr(month.oldBills, item_str) === null || getBillByItemStr(month.oldBills, item_str) === undefined">-</span>
                        </td>
                        <td>
                            <span ng-if="getBillByItemStr(month.newBills, item_str) !== null && getBillByItemStr(month.newBills, item_str) !== undefined">
                                {{getBillByItemStr(month.newBills, item_str) | nmoneyFormatter:2}}
                            </span>
                            <span ng-if="getBillByItemStr(month.newBills, item_str) === null || getBillByItemStr(month.newBills, item_str) === undefined">-</span>
                        </td>
                        <td ng-class="{
                            'text-danger': getDiff(getBillByItemStr(month.newBills, item_str), getBillByItemStr(month.oldBills, item_str)) > 0,
                            'text-success': getDiff(getBillByItemStr(month.newBills, item_str), getBillByItemStr(month.oldBills, item_str)) < 0
                        }">
                            <span ng-if="getBillByItemStr(month.oldBills, item_str) === null || getBillByItemStr(month.oldBills, item_str) === undefined || getBillByItemStr(month.newBills, item_str) === null || getBillByItemStr(month.newBills, item_str) === undefined">-</span>
                            <span ng-if="getBillByItemStr(month.oldBills, item_str) !== null && getBillByItemStr(month.oldBills, item_str) !== undefined && getBillByItemStr(month.newBills, item_str) !== null && getBillByItemStr(month.newBills, item_str) !== undefined">
                                {{getDiff(getBillByItemStr(month.newBills, item_str), getBillByItemStr(month.oldBills, item_str)) | nmoneyFormatter:2}}
                                <i ng-if="getDiff(getBillByItemStr(month.newBills, item_str), getBillByItemStr(month.oldBills, item_str)) > 0" class="fa fa-arrow-up"></i>
                                <i ng-if="getDiff(getBillByItemStr(month.newBills, item_str), getBillByItemStr(month.oldBills, item_str)) < 0" class="fa fa-arrow-down"></i>
                            </span>
                        </td>
                    </tr>
                    <tr ng-repeat-end class="info">
                        <td><strong>月度合计</strong></td>
                        <td>
                            <span ng-if="month.totalOld !== null && month.totalOld !== undefined">{{month.totalOld | nmoneyFormatter:2}}</span>
                            <span ng-if="month.totalOld === null || month.totalOld === undefined">-</span>
                        </td>
                        <td>
                            <span ng-if="month.totalNew !== null && month.totalNew !== undefined">{{month.totalNew | nmoneyFormatter:2}}</span>
                            <span ng-if="month.totalNew === null || month.totalNew === undefined">-</span>
                        </td>
                        <td ng-class="{
                            'text-danger': getDiff(month.totalNew, month.totalOld) > 0,
                            'text-success': getDiff(month.totalNew, month.totalOld) < 0
                        }">
                            <span ng-if="month.totalOld === null || month.totalOld === undefined || month.totalNew === null || month.totalNew === undefined">-</span>
                            <span ng-if="month.totalOld !== null && month.totalOld !== undefined && month.totalNew !== null && month.totalNew !== undefined">
                                <strong>{{getDiff(month.totalNew, month.totalOld) | nmoneyFormatter:2}}</strong>
                                <i ng-if="getDiff(month.totalNew, month.totalOld) > 0" class="fa fa-arrow-up"></i>
                                <i ng-if="getDiff(month.totalNew, month.totalOld) < 0" class="fa fa-arrow-down"></i>
                            </span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </section>
        <h4>
            <strong>特别提醒</strong>
        </h4>
        <div class="form-group">
            <p style="color: red;font-weight: bold;font-size: medium;">产品线审批通过之前请先确保账单已经调整正确，一旦最终审批节点通过，系统会立即发起账单重出</p>
        </div>
    </section>
    <section>
        <discussion excode="excode"></discussion>
    </section>
    <section>
        <qbpm-activities excode="{{excode}}"/>
    </section>
</div>
