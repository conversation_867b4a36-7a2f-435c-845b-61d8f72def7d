<div class="standard-list qn-standard-page">
    <main>
        <div class="header clearfix">
            <div class="row" style="margin-bottom: 20px;">
                <h1 class="col-sm-12">测试申请</h1>
            </div>
        </div>
        <div style="text-align:center">
            <button type="button" ng-show="isLoading" class="btn btn-success btn-inline" name="button">
                <i ng-show="isLoading" class="fa fa-spinner fa-spin"></i> 测试申请数据加载中...
            </button>
            <button type="button" disabled="true" ng-show="!isLoading && !data"
                class="btn btn-danger btn-inline" name="button">获取测试申请数据失败</button>
        </div>
        <div class="brief clearfix" style="margin-bottom: 20px;" ng-show="!isLoading">
            <div class="row">
                <h3 class="col-sm-12"><strong>基本信息</strong></h3>
            </div>
            <div class="row">
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row">
                        <div class="col-sm-6" ng-if="data.starter">
                            <div class="row">
                                <div class="col-sm-4">申请人:</div>
                                <div class="col-sm-8">{{ data.starter }}</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.starter_role">
                            <div class="row">
                                <div class="col-sm-4">部门:</div>
                                <div class="col-sm-8">{{ data.starter_role }}</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.uid">
                            <div class="row">
                                <div class="col-sm-4">测试UID:</div>
                                <div class="col-sm-8">
                                    <a ui-sref="layout.developers.developerInfo({key: data.uid})" target="_blank">
                                        {{ data.uid }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.company">
                            <div class="row">
                                <div class="col-sm-4">公司:</div>
                                <div class="col-sm-8">{{data.company}}</div>
                            </div>
                        </div>
                        <div class="col-sm-6" ng-if="data.start_at || data.end_at">
                            <div class="row">
                                <div class="col-sm-4">生效时间:</div>
                                <div class="col-sm-8">{{ data.start_at | formatDateFilter }} ~ {{ data.end_at | formatDateFilter }}(不含)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row" ng-if="data.memo">
                        <div class="col-sm-2">预估上量和报价:</div>
                        <div class="col-sm-10" >{{ data.memo }}</div>
                    </div>
                </div>
                <div class="col-sm-12" style="font-size: 16px; min-width: 700px;">
                    <div class="row" ng-if="data.reason">
                        <div class="col-sm-2">测试原因:</div>
                        <div class="col-sm-10" >{{ data.reason }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="detail clearfix" style="margin-bottom: 20px;" ng-show="!isLoading">
            <div class="row">
                <h3 class="col-sm-12" style="margin-bottom: 20px;"><strong>产品测试明细</strong></h3>
            </div>
            <div class="row" ng-repeat="itemGroup in data.itemGroups" style="border: 1px solid #eee; padding-bottom: 20px;">
                <h2 style="margin-bottom: 10px;" class="col-sm-12">{{ itemGroup.product }}</h2>

                <div class="col-sm-12">
                    <table class="table table-bordered text-center">
                        <thead>
                        <tr>
                            <th class="col-sm-4">计费项</th>
                            <th class="col-sm-2">按带宽/流量计费</th>
                            <th class="col-sm-2">计费点</th>
                            <th class="col-sm-2">申请额度</th>
                            <th class="col-sm-2">申请费用</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="item in itemGroup.items">
                            <td>{{ item.item_name }}</td>
                            <td>{{ item.data_type | dataTypeFilter }}</td>
                            <td>{{ item.data_type | dataAlgorithmFilter }}</td>
                            <td>{{ item.amount }} {{ item.unit }}</td>
                            <td>{{ item.money }} 元</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <discussion excode="excode"></discussion>
        <qbpm-activities excode="{{excode}}"/>
    </main>
</div>
