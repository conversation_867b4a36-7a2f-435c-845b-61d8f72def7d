<div class="standard-list qn-standard-page">
  <uid-nav></uid-nav>
  <section class="margin-top-30">
    <h2>订单退款审批详情</h2>
  </section>
  <section class="font-size-16">
    <h4>
      <strong>客户信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-1 label-width control-label-left">UID：</label>
      <label class="col-sm-2 label-width control-label-left" style="text-align: left;">
        <a ui-sref="layout.developers.developerInfo({key:uid})">{{ uid }}</a>
      </label>
      <label class="col-sm-1 label-width control-label-left">认证名称：</label>
      <label class="col-sm-3 control-label-left" style="text-align: left;">
        {{userInfo.fullName}}
      </label>
      <label class="col-sm-1 label-width control-label-left">用户归属：</label>
      <label class="col-sm-3 control-label-left" style="text-align: left;">
        <span ng-show="salesInfo">{{salesInfo.email}}</span>
        <span ng-show="!salesInfo">{{userInfo.sfSalesId}}</span>
      </label>
    </div>
    <h4>
      <strong>订单信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-1 label-width control-label-left">订单编号：</label>
      <label class="col-sm-5 control-label-left" style="text-align: left;"><a href="/bo/financial/valet/detail/{{orderHash}}" target="_blank">{{orderHash}}</a></label>
      <label class="col-sm-1 label-width control-label-left">下单时间：</label>
      <label class="col-sm-5 control-label-left" style="text-align: left;">{{orderDetail.create_time | financialUnixMSecFormatter}}</label>
    </div>
    <div class="form-group">
      <table class="table table-bordered table-hover">
        <thead style="background: #f5f5f5">
          <tr>
            <td>商品名称</td>
            <td>数量</td>
            <td>时长</td>
            <td>开始生效时间</td>
            <td>实付金额</td>
            <td>退款类型</td>
            <td>退款金额</td>
          </tr>
        </thead>
        <tbody ng-repeat="po in orderDetail.product_orders">
          <tr ng-class="{'red': po.new_start_time}">
            <td>{{po.product_name}}</td>
            <td>{{po.quantity}}</td>
            <td>{{po.duration}} {{PRODUCT_UNIT_MAP[po.product.unit]}}</td>
            <td>{{po.start_time | tradeTimeFormat }}</td>
            <td>{{po.c_fee | moneyWithCurrencyFilter:1:po.currency_type}}</td>
            <td>
              <span ng-if="po.refundMoney">{{po.isAllRefund ? '全部退款' : '部分退款'}}</span>
              <span ng-if="!po.refundMoney">-</span>
            </td>
            <td>
              <span ng-if="po.refundMoney" class="red">{{po.refundMoney | moneyWithCurrencyFilter:1:po.currency_type}}</span>
              <span ng-if="!po.refundMoney">-</span>
            </td>
          </tr>
          <tr ng-show="isRespackUsed(po.respackUsageDetail)">
            <td colspan="7" style="border: none">
              <div class="alert respack-tip">
                <i class="fa fa-warning warning-icon"></i>
                <div>
                  <h4>请注意你在进行已用资源包退款！</h4>
                  <p ng-show="po.respackRevenueRecognition">该资源包已使用且财务收入已确认 {{amountTranform(po.respackRevenueRecognition.total_recognized_revenue || 0)}}元，建议退款最大金额不超过 {{respackMaxRefundAmount(po)}}元 。</p>
                  <p ng-show="!po.respackRevenueRecognition">未查询到此资源包财务收入确认信息，请根据实际使用情况合理计算退费金额。</p>
                  <p>分配类型：{{CarryOverPolicyMap[po.respackUsageDetail.carryover_policy]}}</p>
                  <p ng-show="po.respackUsageDetail.carryover_policy == CarryOverPolicyConst.Lifetime">
                    总量：{{po.respackUsageDetail.capacity+po.respackUsageDetail.respack_unit}}、
                    已出账用量：{{po.respackUsageDetail.real_used+po.respackUsageDetail.respack_unit}}、
                    当前剩余：{{po.respackUsageDetail.real_remain+po.respackUsageDetail.respack_unit}}
                  </p>
                  <p ng-show="po.respackUsageDetail.carryover_policy != CarryOverPolicyConst.Lifetime">
                    总有效期：{{moment(po.respackUsageDetail.end_time).diff(moment(po.respackUsageDetail.start_time), 'months')}}个月、
                    剩余有效期：{{moment(po.respackUsageDetail.end_time).diff(moment().startOf('month'), 'months')}}个月
                  </p>
                  <p class="color-red">注意：退款后，该资源包从申请月份的1号开始不再可用！！！请关注审批进度，若月底申请有跨月自动驳回风险！</p>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <h4>
      <strong>申请原因</strong>
    </h4>
    <div class="form-group">
      <p style="font-size: large;">{{approvalInfo.reason}}</p>
    </div>
  </section>
  <section>
    <discussion excode="excode"></discussion>
  </section>
  <section>
    <qbpm-activities excode="{{excode}}" process-instance-id="{{process_instance_id}}" />
  </section>
</div>
