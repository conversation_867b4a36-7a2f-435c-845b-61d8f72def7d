<div class="standard-list qn-standard-page">
  <uid-nav></uid-nav>
  <section class="margin-top-30">
    <h2>充值赠送金申请详情</h2>
  </section>
  <section class="font-size-16">
    <h4>
      <strong>申请信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">UID：</label>
      <label class="col-sm-4 label-width control-label-left" style="text-align: left;">
        <a ui-sref="layout.developers.developerInfo({key:uid})">{{ uid }}</a>
      </label>
      <label class="col-sm-2 label-width control-label-left">认证名称：</label>
      <label class="col-sm-4 control-label-left" style="text-align: left;">
        {{developerInfo.full_name}}
      </label>
    </div>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">申请金额：</label>
      <label class="col-sm-4 control-label-left" style="text-align: left;">
        {{approvalInfo.amount | moneyWithCurrencyFilter:'':developerInfo.currency_type}}
      </label>
      <label class="col-sm-2 label-width control-label-left">申请理由：</label>
      <label class="col-sm-4 control-label-left" style="text-align: left;">
        {{approvalInfo.reason}}
      </label>
    </div>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">关联充值记录：</label>
      <label class="col-sm-10 control-label-left" style="text-align: left;">
        <div style="display: flex; gap: 20px;">
          <span>{{approvalInfo.transaction.time / 10000 | dateFormatter}}</span>
          <span>{{approvalInfo.transaction.money | moneyWithCurrencyFilter:'':developerInfo.currency_type}}</span>
          <span>{{TransactionTypeTranslation[approvalInfo.transaction.prefix][approvalInfo.transaction.type]}}</span>
          <span>{{approvalInfo.transaction.serial_num}}</span>
        </div>
      </label>
    </div>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">是否有关联合同：</label>
      <label class="col-sm-4 control-label-left" style="text-align: left;">
        {{approvalInfo.contract ? '有' : '没有'}}
      </label>
    </div>
    <div class="form-group" ng-show="approvalInfo.contract">
      <label class="col-sm-2 label-width control-label-left">关联合同：</label>
      <label class="col-sm-10 control-label-left" style="text-align: left;">
        <div style="display: flex; gap: 20px;">
          <span>{{approvalInfo.contract.contract_no}}</span>
          <span>{{approvalInfo.contract.record_type.id | ContractRecordTypeFilter}}</span>
          <span>{{approvalInfo.contract.start_date | YYYYMMDDFormat: 'YYYY-MM-DD'}}</span>
          <span>至</span>
          <span>{{approvalInfo.contract.end_date | YYYYMMDDFormat: 'YYYY-MM-DD'}}</span>
          <span>{{approvalInfo.contract.approval_status}}</span>
        </div>
      </label>
    </div>
    <uid-discounts uid="{{uid}}" ng-if="uid" content-permission="price-biz-pricing-discounts"></uid-discounts>
    <h4>
      <strong>消费 & 回款</strong>
      </h4>
    <div class="form-group">
      <label class="label-width control-label-left">使用产品：</label>
      <label class="label-width control-label-left">
        <span ng-class="{'red': product == 'sms'}" ng-repeat="product in dummyProducts">{{qiniuProduct[product.toUpperCase()].display}}&nbsp;&nbsp;</span>
      </label>
    </div>
    <div class="form-group">
      <label class="label-width control-label-left">近6个月消费和充值</label>
      <a href="/bo/financial/expense?uid={{uid}}" target="_blank">查看更多</a>
    </div>
    <user-recharge-bills
      ng-if="uid"
      currency-type="developerInfo.currency_type"
      uid="{{ uid }}"
      months-range="{{ 6 }}"
      show-total-row="{{ true }}"
      show-average-row="{{ true }}"
      hideheader
    />
  </section>
  <section>
    <discussion excode="excode"></discussion>
  </section>
  <section>
    <qbpm-activities excode="{{excode}}"/>
  </section>
</div>
