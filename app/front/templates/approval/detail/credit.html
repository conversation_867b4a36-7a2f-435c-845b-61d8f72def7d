<div class="standard-list qn-standard-page approval-detail-credit approval-detail-page">
  <developer-info uid="creditInfo.uid" currency-type="currencyType"></developer-info>
  <main>
    <div class="header clearfix">
      <div class="row margin-b-20">
        <h1 class="col-sm-12">{{ creditInfo.account_name }}&nbsp;授信审批详情</h1>
      </div>
    </div>
    <div class="brief clearfix">
      <div class="row margin-b-20">
        <h3 class="col-sm-12"><strong>授信申请基本信息</strong></h3>
      </div>
      <div class="text-center">
        <button
          type="button"
          ng-show="isInfoLoading"
          class="btn btn-success btn-inline"
          name="button">
          <i ng-show="isInfoLoading" class="fa fa-spinner fa-spin"></i>&nbsp;授信审批详情加载中...
        </button>
        <button
          type="button"
          disabled="true"
          ng-show="!isInfoLoading && !creditInfo"
          class="btn btn-danger btn-inline" name="button">
          获取授信审批详情失败
        </button>
      </div>
      <div class="row margin-b-20" ng-show="!isInfoLoading && creditInfo">
        <div class="col-sm-12 font-size-16">
          <div class="row">
            <div class="col-sm-6" ng-if="creditInfo.uid">
              <div class="row">
                <div class="col-sm-6">七牛用户：</div>
                <div class="col-sm-6">
                  <a href="javascript:void(0);" ui-sref="layout.developers.developerInfo({key: creditInfo.uid})" target="_blank">
                    {{ creditInfo.uid }}
                  </a>
                </div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="creditInfo.account_name">
              <div class="row">
                <div class="col-sm-6">客户名称：</div>
                <div class="col-sm-6">{{ creditInfo.account_name }}</div>
              </div>
            </div>
              <div class="col-sm-6">
                <div class="row">
                  <div class="col-sm-6">签约主体：</div>
                  <div class="col-sm-6">{{ ContractingBodyTextMap[creditInfo.contracting_body] }}</div>
                </div>
              </div>
              <div class="col-sm-6" ng-if="creditInfo.contract_id">
                <div class="row">
                  <div class="col-sm-3">关联合同：</div>
                  <div class="col-sm-9">
                    <a href="javascript:void(0);" ui-sref="layout.ContractApprovalDetail({id: creditInfo.contract_id})" target="_blank">
                      {{ creditInfo.contract_id }}
                    </a>
                  </div>
                </div>
              </div>
            <div class="col-sm-6" ng-if="creditInfo.credit_amount">
              <div class="row">
                <div class="col-sm-6">信用额度：</div>
                <div class="col-sm-6">{{ creditInfo.credit_amount | moneyWithCurrencyFilter:1:currencyType }}</div>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="row">
                <div class="col-sm-6">账期：</div>
                <div class="col-sm-6" ng-if="creditInfo.account_period">
                  {{ creditInfo.account_period }}&nbsp;{{ creditInfo.account_period_unit | creditPeriodUnit }}
                </div>
                <div class="col-sm-6" ng-if="!creditInfo.account_period"> 0 </div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="creditInfo.staff_scale">
              <div class="row">
                <div class="col-sm-6">客户员工人数：</div>
                <div class="col-sm-6">{{ creditInfo.staff_scale | creditStaffScale }}</div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="creditInfo.prev_two_years_revenue">
              <div class="row">
                <div class="col-sm-6">客户上一年度和当年累计营收：</div>
                <div class="col-sm-6">{{ creditInfo.prev_two_years_revenue | creditPrevTwoYearsRevenue }}</div>
              </div>
            </div>
            <div class="col-sm-12" ng-if="creditInfo.monthly_consume">
              <div class="row">
                <div class="col-sm-3">预估未来月消费：</div>
                <div class="col-sm-9">{{ creditInfo.monthly_consume | moneyWithCurrencyFilter:1:currencyType }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-sm-12">
          <div class="row" ng-if="creditInfo.reason">
            <div class="col-sm-3 font-size-16">是否使用短信产品：</div>
            <div class="col-sm-9 font-size-14">{{ creditInfo.is_using_sms_products ? '是' : '否' }}</div>
          </div>
        </div>
        <div class="col-sm-12">
          <div class="row" ng-if="creditInfo.reason">
            <div class="col-sm-12 font-size-16">申请原因：</div>
            <div class="col-sm-12 font-size-14">{{ creditInfo.reason }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="brief clearfix">
      <div class="row margin-b-20">
        <h3 class="col-sm-12"><strong>附件</strong></h3>
      </div>
      <div class="text-center">
        <button
          type="button"
          ng-show="isAttachmentLoading"
          class="btn btn-success btn-inline"
          name="button">
          <i ng-show="isAttachmentLoading" class="fa fa-spinner fa-spin"></i>&nbsp;附件信息加载中...
        </button>
        <button
          type="button"
          disabled="true"
          ng-show="!isAttachmentLoading && attachmentInfoList.length == 0"
          class="btn btn-danger btn-inline" name="button">
          获取附件信息失败
        </button>
      </div>
      <div class="row margin-b-20" ng-show="!isAttachmentLoading && attachmentInfoList.length > 0">
        <div ng-repeat="attachment in attachmentInfoList">
          <a 
            ng-disabled="!attachment.file_url"
            class="btn btn-primary"
            href="{{attachment.file_url}}"
            target="_blank"
          >
          下载
          </a>
          <label> {{attachment.file_name}} ({{attachment.size | byteSizeFilter}})</label>
        </div>
      </div>
    </div>
    <user-recharge-bills
      ng-if="creditInfo.uid"
      currency-type="currencyType"
      uid="{{ creditInfo.uid }}"
      months-range="{{ 6 }}"
      show-total-row="{{ true }}"
      show-average-row="{{ true }}">
    </user-recharge-bills>
    <discussion excode="excode"></discussion>
    <qbpm-activities excode="{{excode}}"/>
  </main>
</div>