<div class="standard-list qn-standard-page approval-detail-iam approval-detail-page">
  <main>

    <div class="header clearfix">
      <div class="row">
        <h1 class="col-sm-12">IAM 审批详情</h1>
      </div>
    </div>

    <div class="brief clearfix">
      <div class="row" style="margin-bottom: 20px;">
        <h3 class="col-sm-12"><strong>基本信息</strong></h3>
      </div>

      <div class="text-center">
        <button
          type="button"
          ng-show="isPageLoading"
          class="btn btn-success btn-inline"
          name="button"
        >
          <i class="fa fa-spinner fa-spin"></i>&nbsp;基本信息加载中...
        </button>
        <button
          type="button"
          disabled="true"
          ng-show="!isPageLoading && !iamInfo"
          class="btn btn-danger btn-inline"
          name="button"
        >
          获取基本信息失败
        </button>
      </div>

      <div class="row" style="margin-bottom: 20px;" ng-show="!isPageLoading && iamInfo">
        <div class="col-sm-12">
          <div class="row">
            <div class="col-sm-6" ng-if="iamInfo.uid">
              <div class="row">
                <div class="col-sm-6">用户 UID：</div>
                <div class="col-sm-6">
                  <a href="javascript:void(0);" ui-sref="layout.developers.developerInfo({key: iamInfo.uid})" target="_blank">
                    {{ iamInfo.uid }}
                  </a>
                </div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="iamInfo.email">
              <div class="row">
                <div class="col-sm-6">用户邮箱：</div>
                <div class="col-sm-6">{{ iamInfo.email }}</div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="iamInfo.fullName">
              <div class="row">
                <div class="col-sm-6">用户名：</div>
                <div class="col-sm-6">{{ iamInfo.fullName }}</div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="iamInfo.companyName">
              <div class="row">
                <div class="col-sm-6">公司名：</div>
                <div class="col-sm-6">{{ iamInfo.companyName }}</div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="iamInfo.products">
              <div class="row">
                <div class="col-sm-6">开通产品：</div>
                <div class="col-sm-6">{{ iamInfo.products }}</div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="iamInfo.alias">
              <div class="row">
                <div class="col-sm-6">根用户别名：</div>
                <div class="col-sm-6">{{ iamInfo.alias }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-sm-12">
          <div class="row" ng-if="iamInfo.reason">
            <div class="col-sm-12">申请原因：</div>
            <div class="col-sm-12">{{ iamInfo.reason }}</div>
          </div>
        </div>
      </div>
    </div>
    <discussion excode="excode"></discussion>
    <qbpm-activities excode="{{excode}}" />

  </main>
</div>
