<div class="standard-list qn-standard-page approval-detail-finance-relation approval-detail-page">
  <main>

    <div class="header clearfix">
      <div class="row">
        <h1 class="col-sm-12">财务父子账号绑定审批详情</h1>
      </div>
    </div>

    <div class="brief clearfix">
      <div class="row" style="margin-bottom: 20px;">
        <h3 class="col-sm-12"><strong>基本信息</strong></h3>
      </div>

      <div class="text-center">
        <button
          type="button"
          ng-show="isPageLoading"
          class="btn btn-success btn-inline"
          name="button"
        >
          <i class="fa fa-spinner fa-spin"></i>&nbsp;基本信息加载中...
        </button>
        <button
          type="button"
          disabled="true"
          ng-show="!isPageLoading && !financeRelationInfo"
          class="btn btn-danger btn-inline"
          name="button"
        >
          获取基本信息失败
        </button>
      </div>

      <div class="row" style="margin-bottom: 20px;" ng-show="!isPageLoading && financeRelationInfo">
        <div class="col-sm-12">
          <div class="row">
            <div class="col-sm-6" ng-if="financeRelationInfo.parent_uid">
              <div class="row">
                <div class="col-sm-6">主账号 UID：</div>
                <div class="col-sm-6">
                  <a href="javascript:void(0);" ui-sref="layout.developers.developerInfo({key: financeRelationInfo.parent_uid})" target="_blank">
                    {{ financeRelationInfo.parent_uid }}
                  </a>
                </div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="financeRelationInfo.type">
              <div class="row">
                <div class="col-sm-6">主账号类型：</div>
                <div class="col-sm-6">
                  {{ financeRelationInfo.type | financialConstFormater : 'financialRelationType'}}
                </div>
              </div>
            </div>
            <div class="col-sm-6" ng-if="financeRelationInfo.uid">
              <div class="row">
                <div class="col-sm-6">子账号 UID：</div>
                <div class="col-sm-6">
                  <a href="javascript:void(0);" ui-sref="layout.developers.developerInfo({key: financeRelationInfo.uid})" target="_blank">
                    {{ financeRelationInfo.uid }}
                  </a>
                </div>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="row">
                <div class="col-sm-6">
                  是否合并余额至主账号&nbsp;<i class="fa fa-question-circle" tooltip="该操作将转移从账号的现金 / 牛币 / 抵用券至主账号，过程不可逆" tooltip-animation="true" tooltip-trigger="mouseenter"></i>：
                </div>
                <div class="col-sm-6">{{ financeRelationInfo.transfer ? '是' : '否' }}</div>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="row">
                <div class="col-sm-6">
                  子账号是否继承父账号认证信息&nbsp;<i class="fa fa-question-circle" tooltip="该操作仅面向已认证的父账号且未认证的子账号使用" tooltip-animation="true" tooltip-trigger="mouseenter"></i>：
                </div>
                <div class="col-sm-6">{{ financeRelationInfo.copy_identity ? '是' : '否' }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-sm-12" ng-if="financeRelationInfo.memo">
          <div class="row">
            <div class="col-sm-12">备注：</div>
            <div class="col-sm-12">{{ financeRelationInfo.memo }}</div>
          </div>
        </div>

      </div>
    </div>
    <discussion excode="excode"></discussion>
    <qbpm-activities excode="{{excode}}" process-instance-id="{{process_instance_id}}" />

  </main>
</div>
