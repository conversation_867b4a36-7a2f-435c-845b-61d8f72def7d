<div class="standard-list qn-standard-page" xmlns="http://www.w3.org/1999/html">
  <uid-nav></uid-nav>
  <section class="margin-top-30">
    <h2>订单改价并转后付费审批申请</h2>
  </section>
  <section class="font-size-16">
    <h4>
      <strong>客户信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-1 label-width control-label-left">UID：</label>
      <label class="col-sm-2 label-width control-label-left" style="text-align: left;">
        <a ui-sref="layout.developers.developerInfo({key:uid})">{{ uid }}</a>
      </label>
      <label class="col-sm-1 label-width control-label-left">认证名称：</label>
      <label class="col-sm-3 control-label-left" style="text-align: left;">
        {{userInfo.fullName}}
      </label>
      <label class="col-sm-1 label-width control-label-left">用户归属：</label>
      <label class="col-sm-3 control-label-left" style="text-align: left;">
        <span ng-show="salesInfo">{{salesInfo.email}}</span>
        <span ng-show="!salesInfo">{{userInfo.sfSalesId}}</span>
      </label>
    </div>
    <h4>
      <strong>订单信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-1 label-width control-label-left">订单编号：</label>
      <label class="col-sm-5 control-label-left" style="text-align: left;"><a href="/bo/financial/valet/detail/{{orderHash}}" target="_blank">{{orderHash}}</a></label>
      <label class="col-sm-1 label-width control-label-left">下单时间：</label>
      <label class="col-sm-5 control-label-left" style="text-align: left;">{{orderDetail.create_time | financialUnixMSecFormatter}}</label>
    </div>
    <div class="form-group">
      <table class="table table-bordered table-hover">
        <thead style="background: #f5f5f5">
          <tr>
            <td>商品名称</td>
            <td>数量</td>
            <td>时长</td>
            <th>分配方式</th>
            <td>原应付金额</td>
            <td>改后费用</td>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat-start="po in orderDetail.product_orders">
            <td>{{po.product_name}}</td>
            <td>{{po.quantity}}</td>
            <td>{{po.duration}} {{PRODUCT_UNIT_MAP[po.product.unit]}}</td>
            <td>{{po | parsePoCarryPolicyFilter}}</td>
            <td>{{po.c_fee | moneyWithCurrencyFilter:1:po.currency_type}}</td>
            <td ng-if="poUnitPrices[po.id]&&poUnitPrices[po.id].c_fee!=po.c_fee">{{ poUnitPrices[po.id].c_fee |
              moneyWithCurrencyFilter:1:po.currency_type:2}}
            </td>
            <td ng-if="!poUnitPrices[po.id]||poUnitPrices[po.id].c_fee==po.c_fee">-</td>
          </tr>
          <tr ng-repeat-end>
            <td colspan="6" class="warning" ng-if="poUnitPrices[po.id]&&poUnitPrices[po.id].unit_price&&poUnitPrices[po.id].c_fee!=po.c_fee">
            <span>
            本次改价后单价：
              <strong ng-if="poUnitPrices[po.id].is_respack">
              {{poUnitPrices[po.id].unit_price|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}/{{poUnitPrices[po.id].unit}}
              </strong>
              <strong ng-if="!poUnitPrices[po.id].is_respack">
              {{poUnitPrices[po.id].unit_price|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}
              </strong>
            </span>
              <span ng-if="poUnitPrices[po.id].is_respack">，较审批线对比:</span>
              <span ng-if="poUnitPrices[po.id].is_respack&&!poUnitPrices[po.id].cost_price" class="text-muted">暂无</span>
              <span ng-if="poUnitPrices[po.id].cost_price&&poUnitPrices[po.id].compare==-1" class="text-success">低于</span>
              <span ng-if="poUnitPrices[po.id].cost_price&&poUnitPrices[po.id].compare==0" class="text-warning">正中</span>
              <span ng-if="poUnitPrices[po.id].cost_price&&poUnitPrices[po.id].compare==1" class="text-danger">高于</span>
              <span>。</span>
              <span>上次审批价:</span>
              <span ng-if="!poUnitPrices[po.id].last_approved" class="text-muted">暂无</span>
              <span ng-if="poUnitPrices[po.id].last_approved">
                <strong ng-if="poUnitPrices[po.id].is_respack">
                {{poUnitPrices[po.id].last_approved|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}/{{poUnitPrices[po.id].unit}}
                </strong>
                <strong ng-if="!poUnitPrices[po.id].is_respack">
                {{poUnitPrices[po.id].last_approved|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}
                </strong>
              </span>
              <span ng-if="poUnitPrices[po.id].last_approved">
                ，较上次:
                <strong ng-if="poUnitPrices[po.id].delta>0">高</strong>
                <strong ng-if="poUnitPrices[po.id].delta<0">低</strong>
                <strong ng-if="poUnitPrices[po.id].delta===0">无变化</strong>
                <strong ng-if="poUnitPrices[po.id].delta!==0">
                  {{poUnitPrices[po.id].delta_abs}}%
                </strong>
              </span>
              <span>。最低审批价:</span>
              <span ng-if="!poUnitPrices[po.id].lowest_approved" class="text-muted">暂无</span>
              <span ng-if="poUnitPrices[po.id].lowest_approved">
                <strong ng-if="poUnitPrices[po.id].is_respack">
                {{poUnitPrices[po.id].lowest_approved|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}/{{poUnitPrices[po.id].unit}}。
                </strong>
                <strong ng-if="!poUnitPrices[po.id].is_respack">
                {{poUnitPrices[po.id].lowest_approved|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}。
                </strong>
              </span>
            </td>
          </tr>
        </tbody>
      </table>
      <p class="text-right new-fee-block">
        <span>订单需支付总费用：</span>
        <span>
          <strike>
          {{ orderDetail.c_fee.toFixed(2) | moneyWithCurrencyFilter:1:orderDetail.currency_type }}
          </strike>
        </span>
      </p>
      <p class="text-right new-fee-block">
        <span>改价后支付总费用：</span>
        <span class="red" ng-if="!approvalInfo.req_update_price.po_actually_fee_sum">
          {{ approvalInfo.req_update_price.actually_fee | moneyWithCurrencyFilter:1:orderDetail.currency_type }}
        </span>
        <span class="red" ng-if="approvalInfo.req_update_price.po_actually_fee_sum">
          {{ approvalInfo.req_update_price.po_actually_fee_sum | moneyWithCurrencyFilter:1:orderDetail.currency_type }}
        </span>
      </p>
      <p class="text-right new-fee-block">
        <span class="red">将此订单转为后付费</span>
      </p>
    </div>
    <div>
      <h4>
        <span ng-class="{'text-danger': isLowPriceChanged}">{{lowPriceUserText}}</span>
      </h4>
    </div>
    <h4>
      <strong>申请原因</strong>
    </h4>
    <div class="form-group">
      <p style="font-size: large;">{{approvalInfo.reason}}</p>
    </div>
  </section>
  <section>
    <discussion excode="excode"></discussion>
  </section>
  <section>
    <qbpm-activities excode="{{excode}}" process-instance-id="{{process_instance_id}}" />
  </section>
</div>
