<div class="standard-list qn-standard-page risk-approval-detail">
  <uid-nav></uid-nav>
  <section class="margin-top-30">
    <h2>临时欠费保护期申请详情</h2>
  </section>
  <section class="font-size-16">
    <h4>
      <strong>客户信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">UID：</label>
      <label class="col-sm-4 label-width control-label-left" style="text-align: left;">
        <a ui-sref="layout.developers.developerInfo({key:uid})">{{ uid }}</a>
      </label>
      <label class="col-sm-2 label-width control-label-left">客户名称：</label>
      <label class="col-sm-4 control-label-left" style="text-align: left;">
        {{userInfo.customer_name}}
      </label>
    </div>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">营业执照：</label>
      <label class="col-sm-4 control-label-left" style="text-align: left;">
        <a href="{{detail.enterprise_code_copy.url}}" ng-show="detail.enterprise_code_copy.url">点击查看</a>
        <span ng-show="!detail.enterprise_code_copy.url">-</span>
      </label>
    </div>
    <h4>
      <strong>申请信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">申请临时欠费保护期：</label>
      <label class="col-sm-4 label-width control-label-left">{{detail.days_to_extend}}&nbsp;&nbsp;天</label>
      <label class="col-sm-2 label-width control-label-left">申请理由：</label>
      <label class="col-sm-4 label-width control-label-left">{{detail.reason}}</label>
    </div>
    <div class="form-group">
      <label class="col-sm-12 label-width control-label-left">附件：</label>
      <div class="col-sm-12">
        <label class="col-sm-2 label-width control-label-left">付款承诺书：</label>
        <div class="col-sm-10">
          <div ng-repeat="file in detail.attachments.written_undertakings">
            <a href="{{file.url}}" target="_blank" rel="noopener" download>{{file.file_name}}</a>
          </div>
        </div>
      </div>
      <div class="col-sm-12">
        <label class="col-sm-2 label-width control-label-left">信用证明：</label>
        <div class="col-sm-6">
          <div ng-repeat="file in detail.attachments.credit_reports">
            <a href="{{file.url}}" target="_blank" rel="noopener" download>{{file.file_name}}</a>
          </div>
        </div>
      </div>
      <div class="col-sm-12">
        <label class="col-sm-2 label-width control-label-left">财务证明：</label>
        <div class="col-sm-6">
          <div ng-repeat="file in detail.attachments.financial_statements">
            <a href="{{file.url}}" target="_blank" rel="noopener" download>{{file.file_name}}</a>
          </div>
        </div>
      </div>
    </div>
    <h4>
      <strong>冻结&消费信息</strong>
    </h4>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">预计冻结时间：</label>
      <label class="col-sm-4 label-width control-label-left">{{detail.estimated_freeze_time | dateFormatter}}</label>
      <label class="col-sm-3 label-width control-label-left">账期：</label>
      <label class="col-sm-3 label-width control-label-left" ng-show="detail.credit_period">{{detail.credit_period}}（单位：{{detail.humanized_credit_period_unit}}）</label>
      <label class="col-sm-3 label-width control-label-left" ng-show="!detail.credit_period">-</label>
    </div>
    <div class="form-group">
      <label class="col-sm-2 label-width control-label-left">使用产品：</label>
      <label class="col-sm-4 label-width control-label-left">
        <span ng-class="{'red': product == 'sms'}" ng-repeat="product in dummyProducts">{{qiniuProduct[product.toUpperCase()].display}}&nbsp;&nbsp;</span>
      </label>
      <label class="col-sm-3 label-width control-label-left">申请时欠费金额（不包含信用额度）：</label>
      <label class="col-sm-3 label-width control-label-left">{{detail.arrears_money | moneyWithCurrencyFilter:'':userInfo.currency_type}}</label>
    </div>
    <div class="form-group" ng-show="hasSms">
      <span class="col-sm-2 label-width control-label-left red">提示：</span>
      <span class="col-sm-10 label-width control-label-left red">短信产品禁止增加临时保护期，需预先充值再使用。</span>
    </div>
    <div class="form-group">
      <label class="label-width control-label-left">历史冻结记录</label>
      <a ui-sref="layout.developers.listFreeze({uid: uid})" target="_blank">查看更多</a>
    </div>
    <div class="form-group">
      <table class="table table-bordered table-hover table-striped text-center">
        <thead>
        <tr>
          <th>UID</th>
          <th colspan="2">操作类型</th>
          <th>操作人</th>
          <th>操作时间</th>
          <th>操作原因</th>
          <th>当前欠费金额</th>
          <th>现金+牛币-未支付金额</th>
          <th>信用额度</th>
          <th>实时消费</th>
          <th>账单抵用券余额</th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="item in showFreezeList track by $index">
          <td><a ui-sref="layout.developers.developerInfo({key:item.uid})">{{ item.uid }}</a></td>
          <td>{{ FREEZE_TYPE_TEXT_MAP[item.type] }}</td>
          <td>
            {{ item.type == FREEZE_TYPE.FREEZE ? FREEZE_FREEZE_TYPE[item.freeze_type].display : '/'}}</td>
          <td>{{ item.operator ? item.operator : '系统'}}</td>
          <td>{{ item.updated_at | dateFormatter : 'YYYY-MM-DD HH:mm' }}</td>
          <td>{{ item.type == FREEZE_TYPE.UNFREEZE ? '/' : item.reason}}</td>
          <td>{{ (item.payload.freeze.money_to_unfreeze / 10000).toFixed(2)}}</td>
          <td>{{ (item.payload.finance.balance / 10000).toFixed(2)}}</td>
          <td>{{ (item.payload.finance.credit_line / 10000).toFixed(2)}}</td>
          <td>{{ (item.payload.finance.fee / 10000).toFixed(2)}}</td>
          <td>{{ (item.payload.finance.coupon / 10000).toFixed(2)}}</td>
        </tr>
        <tr ng-if="showFreezeList.length==0">
          <td colspan="12">没有结果!</td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="form-group">
      <label class="label-width control-label-left">近6个月消费和充值</label>
      <a href="/bo/financial/expense?uid={{uid}}" target="_blank">查看更多</a>
    </div>
    <user-recharge-bills
      ng-if="uid"
      currency-type="userInfo.currency_type"
      uid="{{ uid }}"
      months-range="{{ 6 }}"
      show-total-row="{{ true }}"
      show-average-row="{{ true }}"
      hideheader
    />
  </section>
  <section>
    <discussion excode="excode"></discussion>
  </section>
  <section>
    <qbpm-activities excode="{{excode}}"/>
  </section>
</div>
