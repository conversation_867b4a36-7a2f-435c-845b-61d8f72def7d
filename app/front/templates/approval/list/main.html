<ul class="nav nav-tabs">
  <li ng-repeat="v in viewTypes" ng-class="{'active':query.view_type==v.value}"
    ng-click="onTabSel(v.value)">
    <a href="#" >{{v.display}}</a>
  </li>
</ul>

<div id="myTabContent" class="tab-content">
  <div class="tab-pane fade in active form-group" >
  <div class="col-sm-12">
    <div class="col-sm-3 form-group">
      <div class="input-group">
        <div class="input-group-addon">业务类型</div>
        <select class="form-control" ng-model="query.biz_type" ng-options="k as v.display for (k, v) in bizTypes.toMap()">
          <option value="">-- 无 --</option>
        </select>
      </div>
    </div>
    <div class="col-sm-3 form-group">
      <div class="input-group">
        <div class="input-group-addon">UID</div>
        <uid-searcher ng-model="query.uid"/>
      </div>
    </div>
    <div class="col-sm-3 form-group" ng-show="query.view_type==0">
      <div class="input-group">
        <div class="input-group-addon">审批发起人</div>
        <io-user-searcher ng-model="query.email"/>
      </div>
    </div>
    <div class="col-sm-3 form-group">
      <div class="input-group">
        <div class="input-group-addon">审批状态</div>
        <select class="form-control" ng-model="query.status">
          <option value="0">全部</option>
          <option ng-repeat="(status, name) in PROCESS_STATUS_MAP" value="{{status}}">{{name}}</option>
        </select>
      </div>
    </div>
    <div class="col-sm-12 form-group">
      <div class="text-center">
        <button type="button" class="btn btn-primary btn-inline" ng-click="filterReq()">确定</button>
      </div>
    </div>
  </div>
  </div>
</div>

<div>
  <div style="margin-top: 10px;padding-top: 5px;">
    <nav>
      <ul class="pager" style="float: none;">
        <li ng-show="query.page>=1" ng-click="previouspage()"><a>上一页</a></li>
        <li ng-show="processList.length>=query.page_size" ng-click="nextPage()"><a>下一页</a></li>
      </ul>
    </nav>

    <table
      class="table table-bordered table-hover table-striped hidden-xs"
      style="margin-bottom: 0;"
    >
      <thead>
        <tr>
          <th>业务类型</th>
          <th>UID</th>
          <th ng-show="uidInfoMap">客户</th>
          <th>审批发起人</th>
          <th ng-show="statusCode == REVIEW" style="max-width: 200px;">当前操作人</th>
          <th>创建时间</th>
          <th>更新时间</th>
          <th>审批状态</th>
          <th>操作</th>
        </tr>
      </thead>
      
      <tbody>
        <tr ng-show="!isLoading && processList" ng-repeat="process in processList">
          <td>{{bizTypes.display(process.process_definition_key)}}</td>
          <td>
            <a ng-if="process.uid && process.uid > 0" ui-sref="layout.developers.developerInfo({key: process.uid})" target="_blank">
              {{ process.uid }}
            </a>
          </td>
          <td ng-show="uidInfoMap">{{displayAccount(process.uid)}}</td>
          <td>{{process.initiator.name}}</td>
          <td ng-show="statusCode == REVIEW" style="max-width: 200px;">{{process.assignees_str}}</td>
          <td>{{process.created_at.substring(0, 19)}}</td>
          <td>{{process.updated_at.substring(0, 19)}}</td>
          <td>{{PROCESS_STATUS_MAP[process.status]}}</td>
          <td>
            <a href="javascript:void(0)" class="btn btn-primary btn-inline" ui-sref="{{bizTypes.link(process.process_definition_key)}}">详情</a>
          </td>
        </tr>
      </tbody>
    </table>

    <div class="process_list_mobile">
      <div class="process_item" ng-show="!isLoading && processList" ng-repeat="process in processList" ui-sref="{{bizTypes.link(process.process_definition_key)}}">
        <div class="process_item_header">
          <div class="process_item_title">{{bizTypes.display(process.process_definition_key)}}</div>
          <div class="label {{process.status | processStatusStyleFilter}}">{{PROCESS_STATUS_MAP[process.status]}}</div>
        </div>
        <div>
          UID：
          <a ng-if="process.uid && process.uid > 0" ui-sref="layout.developers.developerInfo({key: process.uid})" target="_blank" ng-click="stopProp($event)">
            {{ process.uid }}
          </a>
        </div>
        <div ng-show="uidInfoMap">客户：{{displayAccount(process.uid)}}</div>
        <div>创建时间：{{process.created_at.substring(0, 19)}}</div>
        <div class="process_item_footer">
          <div>{{process.updated_at.substring(0, 19)}}</div>
          <div class="process_sponsor" ng-show="query.view_type == viewTypes.PROCESS_VIEW_ASSIGNEE.value">{{process.initiator.name}}</div>
          <div class="process_operator" ng-show="statusCode == REVIEW && query.view_type == viewTypes.PROCESS_VIEW_STARTER.value">{{process.assignees_str}}</div>
        </div>
      </div>
    </div>

    <div
      style="padding: 10px 0; border: 1px solid #E4E4E4; border-top: none; background-color: #F9F9F9; text-align: center;"
      ng-show="isLoading"
    >
      <button type="button" class="btn btn-success btn-inline" name="button">
        <i ng-show="isLoading" class="fa fa-spinner fa-spin"></i> 数据加载中...
      </button>
    </div>

    <div
      style="padding: 10px 0; border: 1px solid #E4E4E4; border-top: none; background-color: #F9F9F9; text-align: center;"
      ng-show="!isLoading && (!processList || !processList.length)"
    >
      <button type="button" disabled="true" class="btn" name="button">NO DATA</button>
    </div>

    <div style="height: 60px;">
      <ul class="pager" style="float: none;">
        <li ng-show="query.page>=1" ng-click="previouspage()"><a>上一页</a></li>
        <li ng-show="processList.length>=query.page_size" ng-click="nextPage()"><a>下一页</a></li>
      </ul>
    </div>
  </div>

</div>
