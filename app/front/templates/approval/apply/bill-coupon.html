<div class="modal-header"><h4>按量计费抵用券申请</h4></div>
<div class="modal-body">
  <form name="couponCreateForm">
    <div class="form-group" ng-show="!developerInfo">
      <label class="col-sm-2 control-label">UID</label>
      <div class="col-sm-4">
        <uid-searcher ng-model="applyUid" />
      </div>
      <div class="col-sm-2">
        <button class="btn btn-primary" type="button" ng-click="query()">查询</button>
      </div>
    </div>
    <section ng-show="developerInfo">
      <h4>申请信息</h4>
      <div class="form-group">
        <label class="col-sm-2 control-label">UID:</label>
        <div class="col-sm-4">
          <span style="line-height: 32px">{{applyUid}}</span>
        </div>
        <label class="col-sm-2 control-label">客户名称:</label>
        <div class="col-sm-4">
          <span style="line-height: 32px">{{developerInfo.full_name}}</span>
        </div>
      </div>

      <div class="form-group">
        <label class="col-sm-2 control-label">名称<span class="red"> *</span></label>
        <div class="col-sm-4">
          <input class="form-control" placeholder="如 XXX 市场活动" name="applicationName" ng-model="coupon.title" required/>
          <span class="help-block" ng-messages="couponCreateForm.applicationName.$error" ng-show="couponCreateForm.applicationName.$dirty">
            <span ng-message="required">必填</span>
          </span>
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-2 control-label">描述<span class="red"> *</span></label>
        <div class="col-sm-4">
          <input class="form-control" placeholder="抵用券描述" name="desc" ng-model="coupon.desc" required/>
          <span class="help-block" ng-messages="couponCreateForm.desc.$error" ng-show="couponCreateForm.desc.$dirty">
            <span ng-message="required">必填</span>
          </span>
        </div>
      </div>

      <div class="form-group">
        <label class="col-sm-2 control-label">事由分类<span class="red"> *</span></label>
        <div class="col-sm-4">
          <select class="form-control" name="applicationBizType" ng-model="coupon.biz_type"
            ng-options="v.value as v.display for (k, v) in BIZ_TYPE" required>
              <option value="">---- 请选择申请本次抵用券的事由分类 ----</option>
          </select>
          <span class="help-block" ng-messages="couponCreateForm.applicationBizType.$error" ng-show="couponCreateForm.applicationBizType.$dirty">
            <span ng-message="required">必选</span>
          </span>
        </div>
      </div>

      <div class="form-group">
        <label class="col-sm-2 control-label">事由<span class="red"> *</span></label>
        <div class="col-sm-4">
          <textarea class="form-control" style="resize:none;" placeholder="本次申请的背景"
            name="reason" ng-model="reason" required>
          </textarea>
          <span class="help-block" ng-messages="couponCreateForm.reason.$error" ng-show="couponCreateForm.reason.$dirty">
            <span ng-message="required">必填</span>
          </span>
        </div>
      </div>

      <div class="coupon-detail">
        <h4>抵用券规格</h4>
        <div class="form-group">
          <label class="col-sm-2 control-label">适用产品线<span class="red"> *</span></label>
          <div class="col-sm-4">
            <select name="products" class="form-control" ng-model="coupon.products">
              <option value="" default>请选择适用的产品线</option>
              <option value="{{product.id}}" ng-repeat="product in productsList" ng-selected="product.id == 'market'">{{product.label}}</option>
            </select>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-2 control-label">面额<span class="red"> *</span></label>
          <div class="col-sm-10">
            <div class="input-group" style="width: 200px;">
              <span class="input-group-addon">{{developerInfo.currency_type | currencySymbolFilter}}</span>
              <input type="number" class="form-control" placeholder="请填写抵用券面额" name="quota" required
                ng-model="coupon.quota" step="0.01" min="0.01" max="{{quotaIsLimited ? qvmLimitedBalanceQuota || limitedBalanceQuota : ''}}" ng-pattern="/^(\d)+(\.\d{1,2})?$/" />
            </div>
            <span class="help-block" ng-messages="couponCreateForm.quota.$error" ng-show="couponCreateForm.quota.$dirty">
              <span ng-message="required">必填</span>
              <span ng-message="min">面额需要大于 0 </span>
              <span ng-message="pattern">最多精确到 0.01</span>
              <span ng-message="number">仅允许输入数字</span>
            </span>
            <span class="help-block" style="color: #c09853;">
              <span ng-show="overQuotaLabel">{{overQuotaLabel}}</span>
              <span ng-show="!isQvmLimited && quotaIsLimited">{{limitedLabel}}</span>
              <span ng-show="isQvmLimited && quotaIsLimited">{{qvmLimitedLabel}}</span>
            </span>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-2 control-label">可用月份<span class="red"> *</span></label>
          <div class="col-sm-10" style="position: relative; display: inline-flex; align-items: center;" >
            <div style="position: relative;">
              <span id="effecttime" data-toggle="dropdown">
                <input type="text" class="form-control" ng-model="coupon.effecttime" name="effecttime" required
                  date-time-input="YYYY-MM" placeholder="选择月份" autocomplete="off">
              </span>
              <ul class="dropdown-menu">
                <datetimepicker
                  before-render="beforeRender($view, $dates)"
                  data-ng-model="coupon.effecttime"
                  datetimepicker-config="{
                      minView: 'month',
                      startView: 'month',
                      dropdownSelector: '#effecttime'
                  }"
                ></datetimepicker>
              </ul>
            </div>
            <span>&nbsp;~&nbsp;</span>
            <div style="position: relative; ">
              <span id="expiredtime" data-toggle="dropdown">
                <input type="text" class="form-control" ng-model="coupon.expiredtime" name="expiredtime" required
                  date-time-input="YYYY-MM" placeholder="选择月份" autocomplete="off">
              </span>
              <ul class="dropdown-menu">
                <datetimepicker
                  before-render="beforeRender($view, $dates)"
                  data-ng-model="coupon.expiredtime"
                  datetimepicker-config="{
                      minView: 'month',
                      startView: 'month',
                      dropdownSelector: '#expiredtime'
                  }"
                ></datetimepicker>
              </ul>
            </div>
          </div>
        </div>
        <div class="form-group">
          <div class="col-sm-2"></div>
          <div class="col-sm-10">
            <p class="red">
              提示：若审批通过月份大于所选的开始月份，以审批通过月份为准；
            </p>
            <p class="red">
              按量计费抵用券不能抵扣历史月份账单，例如 9 月份申请的按量计费抵用券不能抵扣 8 月份及之前月份账单。
            </p>
          </div>
        </div>
      </div>
    </section>
  </form>
</div>
<div class="modal-footer">
  <button class="btn btn-primary" ng-click="submit()" ng-disabled="shouldForbidSubmit || couponCreateForm.$invalid || !coupon.biz_type || !coupon.products">申请</button>
  <button class="btn btn-default" ng-click="close()">关闭</button>
</div>
