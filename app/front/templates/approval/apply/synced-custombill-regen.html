<div class="modal-header"><h4>{{product.toUpperCase()}}客户账单重出</h4></div>
<div class="modal-body">
    <div class="form-group" ng-show="!fullname">
        <label class="col-sm-2 control-label">UID</label>
        <div class="col-sm-4">
            <uid-searcher ng-model="uid" />
        </div>
        <div class="col-sm-2">
            <button class="btn btn-primary" type="button" ng-click="query()">查询</button>
        </div>
    </div>
    <form name="syncedCustombillRegenApplyForm" ng-show="fullname">
        <section>
            <h4>基本信息</h4>
            <div class="form-group">
                <label class="col-sm-2 control-label">UID：</label>
                <label class="col-sm-2 control-label" style="text-align: left;">{{ uid }}</label>
                <label class="col-sm-2 control-label">客户：</label>
                <label class="col-sm-4 control-label" style="text-align: left;">{{ fullname }}</label>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">重出月份：<span class="red"> *</span></label>
                <div class="col-md-4" style="padding-right: 0px;">
                  <span data-toggle="dropdown" id="monthStart">
                    <input type="text" class="form-control"
                           ng-model="month_start"
                           date-time-input="YYYY-MM" readonly placeholder="点击选择起始月份">
                  </span>
                  <ul class="dropdown-menu">
                      <datetimepicker before-render="monthStartBeforeRender($dates)"
                                      ng-model="month_start"
                                      on-set-time="monthStartOnSetTime()"
                                      datetimepicker-config="{
                                      dropdownSelector: '#monthStart',
                                      minView: 'month',
                                      startView: 'month',
                                      renderOn: 'month-end-changed'
                                      }">
                      </datetimepicker>
                  </ul>
                </div>
                <div class="col-md-4" style="padding-left: 0px;">
                  <span data-toggle="dropdown" id="monthEnd">
                    <input type="text" class="form-control"
                           ng-model="month_end"
                           date-time-input="YYYY-MM" readonly placeholder="点击选择结束月份">
                  </span>
                  <ul class="dropdown-menu">
                      <datetimepicker before-render="monthEndBeforeRender($dates)"
                                      ng-model="month_end"
                                      on-set-time="monthEndOnSetTime()"
                                      datetimepicker-config="{
                                      dropdownSelector: '#monthEnd',
                                      minView: 'month',
                                      startView: 'month',
                                      renderOn: 'month-start-changed'
                                      }">
                      </datetimepicker>
                  </ul>
                </div>
            </div>
            <div class="form-group">
                <div class="form-inline">
                    <label class="col-sm-2 control-label">重出账原因：<span class="red"> *</span></label>
                    <div class="col-sm-8">
                        <textarea class="form-control" style="width: 600px;" name="regen_reason" ng-model="regen_reason" required
                                  placeholder="为便于核对，建议理由中写明调整金额、预期最终金额等...">
                        </textarea>
                        <span class="help-block" ng-messages="syncedCustombillRegenApplyForm.regen_reason.$error" ng-show="syncedCustombillRegenApplyForm.regen_reason.$dirty">
                          <span ng-message="required">必填</span>
                        </span>
                    </div>
                </div>
            </div>
        </section>
    </form>
</div>
<div class="modal-footer">
    <span class="glyphicon glyphicon-info-sign" style="color:red;position: relative;right: 140px;font-weight: bold;">
        重出账审批单审批通过后会更新该UID对应月份的所有账单数据，无需重复申请
    </span>
    <button class="btn btn-primary" ng-click="submit()" ng-disabled="syncedCustombillRegenApplyForm.$invalid || isLoading">申请</button>
    <button class="btn btn-default" ng-click="close()">关闭</button>
</div>
