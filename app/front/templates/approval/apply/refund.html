<div class="modal-header"><h4>订单退款申请</h4></div>
<div class="modal-body order-approval-modal refund-modal">
  <div class="order-detail-item">
    <label for="">订单号</label>
    <div class="col-sm-4">
      <input type="text" class="form-control" ng-model="orderHash" ng-show="edit">
      <span ng-show="!edit">{{orderHash}}</span>
    </div>
    <button class="btn btn-default" type="button" ng-click="queryOrder()" ng-show="edit">确认</button>
    <button class="btn btn-default" type="button" ng-click="editOrderHash()" ng-show="!edit">修改</button>
  </div>
  <div class="order-detail-item" ng-show="orderDetail.buyer_id">
    <label for="">UID</label>
    <div class="col-sm-4">{{orderDetail.buyer_id}}</div>
  </div>
  <div ng-show="isLoading" style="text-align:center">
    <button type="button" class="btn btn-success btn-inline" name="button">
        <span><i class="fa fa-spinner fa-spin"></i> 订单数据加载中...</span>
    </button>
  </div>
  <div ng-show="orderDetail && !isLoading">
    <div class="text-right form-group" ng-show="hasSelected">
      <button class="btn btn-default" type="button" ng-click="reSelect()" ng-disabled="forceRefundAllGoods">重新选择商品</button>
    </div>
    <table class="table table-bordered table-hover" ng-show="orderDetail && !hasSelected">
      <thead style="background: #f5f5f5">
        <tr>
          <th>商品名称</th>
          <th>数量</th>
          <th>时长</th>
          <th>开始生效时间</th>
          <th>实付金额</th>
          <th>
            <div ng-show="orderDetail.product_orders.length == 1">操作</div>
            <div ng-show="orderDetail.product_orders.length > 1">
              <input type="checkbox" ng-model="refundAllGoods" ng-checked="refundAllGoods" ng-disabled="forceRefundAllGoods">
              <span>全选</span>
              <i class="fa fa-question-circle" tooltip="一次只能申请退款整个订单或者单个商品,后付状态的订单只能选择整个订单" tooltip-animation="true" tooltip-trigger="mouseenter"></i>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="po in orderDetail.product_orders">
          <td>{{po.product_name}}</td>
          <td>{{po.quantity}}</td>
          <td>{{po.duration}}  {{PRODUCT_UNIT_MAP[po.product.unit]}}</td>
          <td>{{po.start_time | tradeTimeFormat}}</td>
          <td>{{po.c_fee | moneyWithCurrencyFilter:1:po.currency_type}}</td>
          <td>
            <div ng-show="!refundAllGoods">
              <input type="radio" name="refundPo" ng-click="selectPoID(po.id)">&nbsp;&nbsp;<label for="refundPo">{{selectedPoID == po.id ? '已选中' : '未选中'}}</label>
            </div>
            <div ng-show="refundAllGoods">
              <input ng-show="refundAllGoods" type="checkbox" name="refundPo" checked ng-click="unCheck($event)" ng-disabled="forceRefundAllGoods">&nbsp;&nbsp;<label for="">已选中</label>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <table class="table table-bordered table-hover" ng-show="selectedGoods && hasSelected">
      <thead style="background: #f5f5f5">
        <tr>
          <th>商品名称</th>
          <th>数量</th>
          <th>时长</th>
          <th>开始生效时间</th>
          <th>实付金额</th>
          <th>退款类型</th>
          <th>退款金额</th>
        </tr>
      </thead>
      <tbody ng-repeat="po in selectedGoods">
        <tr>
          <td class="vertical-middle">{{po.product_name}}</td>
          <td class="vertical-middle">{{po.quantity}}</td>
          <td class="vertical-middle">{{po.duration}}  {{PRODUCT_UNIT_MAP[po.product.unit]}}</td>
          <td class="vertical-middle">{{po.start_time | tradeTimeFormat}}</td>
          <td class="vertical-middle">{{po.c_fee | moneyWithCurrencyFilter:1:po.currency_type}}</td>
          <td class="vertical-middle" ng-show="!isRespackUsed(po.respackUsageDetail)">
            <select class="form-control" ng-show="po.product.category_id != 3 && !refundAllGoods" ng-model="refund.refundType">
              <option value="allRefund">全额退款</option>
              <option value="partRefund">部分退款</option>
            </select>
            <span ng-show="po.product.category_id == 3 || refundAllGoods">全额退款</span>
          </td>
          <td class="vertical-middle" ng-show="isRespackUsed(po.respackUsageDetail)">
            <span ng-show="po.product.category_id == 3 || refundAllGoods">部分退款</span>
          </td>
          <td class="vertical-middle">
            <div ng-if="!isRespackUsed(po.respackUsageDetail)">
              <span ng-show="po.product.category_id == 3 || refundAllGoods || refund.refundType == 'allRefund'">{{po.c_fee | moneyWithCurrencyFilter:1:po.currency_type}}</span>
              <div ng-show="(po.product.category_id != 3 && !refundAllGoods && refund.refundType == 'partRefund')">
                <span>{{po.currency_type == CurrencyType.CNY ? "¥" : "$"}}</span>
                <input type="number" class="form-control" style="display: inline-block; width: 100px;" ng-model="po.refundQuota" ng-init="po.refundQuota = po.c_fee" max="{{po.c_fee}}" min="0">
                <span style="display: block;color: red; margin: 2px 0;" ng-show="!po.refundQuota">退款金额：0 ~ {{po.c_fee}}</span>
              </div>
            </div>
            
            <div ng-if="isRespackUsed(po.respackUsageDetail)">
                <span>{{po.currency_type == CurrencyType.CNY ? "¥" : "$"}}</span>
                <input
                  type="number"
                  class="form-control"
                  style="display: inline-block; width: 100px;"
                  ng-model="po.refundQuota"
                  ng-init="po.refundQuota = undefined;refundAllGoods = false;refund.refundType = 'partRefund'"
                  max="{{po.c_fee}}"
                  min="0"
                >
                <span style="display: block;color: red; margin: 2px 0;" ng-show="!po.refundQuota">退款金额：0 ~ {{po.c_fee}}</span>
            </div>
          </td>
        </tr>
        <tr ng-show="isRespackUsed(po.respackUsageDetail)">
          <td colspan="7" style="border: none">
            <div class="alert respack-tip">
              <i class="fa fa-warning warning-icon"></i>
              <div>
                <h4>请注意你在进行已用资源包退款！</h4>
                <p ng-show="po.respackRevenueRecognition">该资源包已使用且财务收入已确认 {{amountTranform(po.respackRevenueRecognition.total_recognized_revenue || 0)}}元，建议退款最大金额不超过 {{respackMaxRefundAmount(po)}}元 。</p>
                <p ng-show="!po.respackRevenueRecognition">未查询到此资源包财务收入确认信息，请根据实际使用情况合理计算退费金额。</p>
                <p>分配类型：{{CarryOverPolicyMap[po.respackUsageDetail.carryover_policy]}}</p>
                <p ng-show="po.respackUsageDetail.carryover_policy == CarryOverPolicyConst.Lifetime">
                  总量：{{po.respackUsageDetail.capacity+po.respackUsageDetail.respack_unit}}、
                  已出账用量：{{po.respackUsageDetail.real_used+po.respackUsageDetail.respack_unit}}、
                  当前剩余：{{po.respackUsageDetail.real_remain+po.respackUsageDetail.respack_unit}}
                </p>
                <p ng-show="po.respackUsageDetail.carryover_policy != CarryOverPolicyConst.Lifetime">
                  总有效期：{{moment(po.respackUsageDetail.end_time).diff(moment(po.respackUsageDetail.start_time), 'months')}}个月、
                  剩余有效期：{{moment(po.respackUsageDetail.end_time).diff(moment().startOf('month'), 'months')}}个月
                </p>
                <p class="color-red">注意：退款后，该资源包从申请月份的1号开始不再可用！！！请关注审批进度，若月底申请有跨月自动驳回风险！</p>
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <p class="red" ng-show="orderDetail.status == TRADE_ORDER_STATUS.POSTPAY">注意：此次为后付费商品订单退款，实际退款金额按已支付的现金退还</p>
    <div class="order-detail-item" ng-show="hasSelected">
      <label for=""><span class="red">* </span>申请原因</label>
    </div>
    <div class="order-detail-item" ng-show="hasSelected">
      <textarea name="reason" class="form-control" id="reason" cols="30" rows="6" ng-model="reason"></textarea>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button class="btn btn-primary" type="button" ng-show="!hasSelected" ng-click="selectGoods()" ng-disabled="!orderDetail">确定</button>
  <button class="btn btn-primary" ng-show="hasSelected" ng-click="submit()" ng-disabled="disableSubmit || isLoading">确认</button>
  <button class="btn btn-default" ng-click="close()">关闭</button>
</div>
