<div class="modal-header"><h4>资源包延期申请</h4></div>
<div class="modal-body order-approval-modal respack-start-time-modal">
  <div class="order-detail-item">
    <label for="">订单号</label>
    <div class="col-sm-4">
      <input type="text" class="form-control" ng-model="orderHash" ng-show="edit">
      <span ng-show="!edit">{{orderHash}}</span>
    </div>
    <button class="btn btn-default" type="button" ng-click="queryOrder()" ng-show="edit">确认</button>
    <button class="btn btn-default" type="button" ng-click="editOrderHash()" ng-show="!edit">修改</button>
  </div>
  <div class="order-detail-item" ng-show="orderDetail.buyer_id">
    <label for="">UID</label>
    <div class="col-sm-4">{{orderDetail.buyer_id}}</div>
  </div>
  <div ng-show="orderDetail">
    <div class="text-right form-group" ng-show="hasSelected">
      <button class="btn btn-default" type="button" ng-click="reSelect()">重新选择商品</button>
    </div>
    <table class="table table-bordered table-hover" ng-show="orderDetail && !hasSelected">
      <thead style="background: #f5f5f5">
        <tr>
          <th>商品名称</th>
          <th>数量</th>
          <th>时长</th>
          <th>总量</th>
          <th>已用(含未出账)</th>
          <th>结束时间</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="po in orderDetail.product_orders">
          <td>{{po.product_name}}</td>
          <td>{{po.quantity}}</td>
          <td>{{po.duration}} {{PRODUCT_UNIT_MAP[po.product.unit]}}</td>
          <td>{{po.respackUsageDetail.capacity}}{{po.respackUsageDetail.respack_unit}}</td>
          <td>{{(po.respackUsageDetail.used/po.respackUsageDetail.capacity*100).toFixed(2)}}%</td>
          <td>
            <span>{{po.end_time | tradeTimeFormat}}</span>
            <span ng-if="po.extend_duration" class="green" style="margin: 0 5px;">➡️</span>
            <span ng-if="po.extend_duration" class="red">{{po.new_end_time | tradeTimeFormat}}</span>
          </td>
          <td>
            <span class="green" ng-if="po.approvalAccepted">已申请</span>
            <div ng-if="!po.approvalAccepted">
              <button class="btn btn-primary" type="button" ng-click="extendDuration(po)">延期</button>
              <button class="btn btn-default" type="button" ng-show="po.extend_duration" ng-click="cancelAlter(po)">撤销</button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="order-detail-item" ng-show="hasChange">
      <label for=""><span class="red">* </span>申请原因</label>
    </div>
    <div class="order-detail-item" ng-show="hasChange">
      <textarea name="reason" class="form-control" id="reason" cols="30" rows="6" ng-model="reason"></textarea>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button class="btn btn-primary" ng-click="submit()" ng-disabled="disableSubmit || !hasChange || isLoading">申请</button>
  <button class="btn btn-default" ng-click="close()">关闭</button>
</div>
