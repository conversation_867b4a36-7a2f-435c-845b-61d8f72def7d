<form name="form" ng-submit="submit()">
  <div class="modal-header"><h4>充值赠送金申请</h4></div>
  <div class="modal-body">
    <div class="form-group">
      <label class="col-sm-2 control-label">UID</label>
      <div class="col-sm-4">
        <uid-searcher ng-model="uid" />
      </div>
      <div class="col-sm-2">
        <button class="btn btn-primary" type="button" ng-click="query()">查询</button>
      </div>
    </div>
    <div class="form-group" ng-show="developerInfo">
      <label class="col-sm-2 control-label">认证名称：</label>
      <div class="col-sm-10">{{developerInfo.customer_name}}</div>
    </div>
  </div>
  <div class="modal-body" style="border-top: 1px solid #e5e5e5" ng-show="developerInfo">
    <div class="form-group">
      <label class="col-sm-2 control-label">申请金额：</label>
      <div class="col-sm-6">
        <div class="input-group">
          <span class="input-group-addon">{{developerInfo.currency_type | currencySymbolFilter}}</span>
          <input 
            type="number" 
            ng-model="amount" 
            class="form-control" 
            min="0.01" 
            step="0.01"
            ng-change="handleAmountChange()"
            pattern="^\d*\.?\d{0,2}$"
            required
            aria-label="申请金额"
          >
        </div>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-2 control-label">申请理由：</label>
      <div class="col-sm-6">
        <textarea class="form-control" rows="4" ng-model="reason" required></textarea>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-2 control-label">关联充值记录：</label>
      <div class="col-sm-10">
        <a href="javascript:void(0)" ng-click="showRechargeTransactions()">选择充值记录</a>
        <div style="display: flex; gap: 10px; margin-top: 10px;" ng-show="transaction">
          <span>{{transaction.time / 10000 | dateFormatter}}</span>
          <span>{{transaction.money | moneyWithCurrencyFilter:'':currency}}</span>
          <span>{{TransactionTypeTranslation[transaction.prefix][transaction.type]}}</span>
          <span>{{transaction.serial_num}}</span>
        </div>
      </div>
    </div>
    <div class="form-group">
    <label class="col-sm-2 control-label" style="line-height: 30px">是否有关联合同：</label>
      <div class="col-sm-10">
        <label class="radio-inline">
          <input type="radio" name="hasContract" ng-value={{true}} ng-model="hasContract"> 有
        </label>
        <label class="radio-inline">
          <input type="radio" name="hasContract" ng-value={{false}} ng-model="hasContract"> 没有
        </label>
      </div>
    </div>
    <div class="form-group" ng-show="hasContract">
      <label class="col-sm-2 control-label">关联合同：</label>
      <div class="col-sm-10">
        <a href="javascript:void(0)" ng-click="showContracts()">选择合同</a>
        <div style="display: flex; gap: 20px;" ng-show="relationContract">
          <span>{{relationContract.contract_no}}</span>
          <span>{{relationContract.record_type.id | ContractRecordTypeFilter}}</span>
          <span>{{relationContract.start_date | YYYYMMDDFormat: 'YYYY-MM-DD'}}</span>
          <span>{{relationContract.end_date | YYYYMMDDFormat: 'YYYY-MM-DD'}}</span>
          <span>{{relationContract.approval_status}}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-primary" type="submit" ng-disabled="form.$invalid || invalidForm || isSubmitting">申请</button>
    <button class="btn btn-default" ng-click="close()">关闭</button>
  </div>
</form>