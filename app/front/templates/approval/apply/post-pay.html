<div class="modal-header"><h4>订单转后付费申请</h4></div>
<div class="modal-body order-approval-modal change-order-price-modal">
  <div class="order-detail-item">
    <label for="">订单号</label>
    <div class="col-sm-4">
      <input type="text" class="form-control" ng-model="orderHash" ng-show="edit">
      <span ng-show="!edit">{{orderHash}}</span>
    </div>
    <button class="btn btn-default" type="button" ng-click="queryOrder()" ng-show="edit">确认</button>
    <button class="btn btn-default" type="button" ng-click="editOrderHash()" ng-show="!edit">修改</button>
  </div>
  <div class="order-detail-item" ng-show="orderDetail.buyer_id">
    <label for="">UID</label>
    <div class="col-sm-4">{{orderDetail.buyer_id}}</div>
  </div>
  <div ng-show="orderDetail">
    <table class="table table-bordered table-hover" ng-show="orderDetail">
      <thead style="background: #f5f5f5">
        <tr>
          <th>商品名称</th>
          <th>数量</th>
          <th>时长</th>
          <th>分配方式</th>
          <th>实际费用</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="po in orderDetail.product_orders">
          <td>{{po.product_name}}</td>
          <td>{{po.quantity}}</td>
          <td>{{po.duration}} {{PRODUCT_UNIT_MAP[po.product.unit]}}</td>
          <td>{{po | parsePoCarryPolicyFilter}}</td>
          <td>{{po.c_fee | moneyWithCurrencyFilter:1:po.currency_type}}</td>
        </tr>
      </tbody>
    </table>
    <p class="text-right new-fee-block">
      <span>订单需支付总费用：</span>
      <span>{{ orderDetail.c_fee.toFixed(2) | moneyWithCurrencyFilter:1:orderDetail.currency_type }}</span>
    </p>
    <p class="text-right red">⚠️&nbsp;&nbsp;此订单将转为后付费订单！</p>
    <div class="order-detail-item">
      <label for=""><span class="red">* </span>申请原因</label>
    </div>
    <div class="order-detail-item">
      <textarea name="reason" class="form-control" id="reason" cols="30" rows="6" ng-model="reason"></textarea>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button class="btn btn-primary" ng-click="submit()" ng-disabled="disableSubmit || isLoading">确认</button>
  <button class="btn btn-default" ng-click="close()">关闭</button>
</div>
