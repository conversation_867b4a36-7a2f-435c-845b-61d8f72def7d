<form name="form" ng-submit="submit()">
  <div class="modal-header"><h4>余额转移申请</h4></div>
  <div class="modal-body">
    <div class="form-group">
      <label class="col-sm-2 control-label">UID<span class="red">*</span></label>
      <div class="col-sm-4">
        <uid-searcher ng-model="formData.from_uid" />
      </div>
      <div class="col-sm-2">
        <button class="btn btn-primary" type="button" ng-click="query()">查询</button>
      </div>
    </div>
  </div>
  <div class="modal-body" style="border-top: 1px solid #e5e5e5" ng-show="formData.from_uid">
    <div class="form-group">
      <label class="col-sm-2 control-label">转入 UID<span class="red">*</span></label>
      <div class="col-sm-6">
        <uid-searcher ng-model="formData.to_uid" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-2 control-label">转移现金</label>
      <div class="col-sm-6">
        <input type="number" class="form-control" step="0.01" min="0" max="{{fromUserInfo.walletOverview.cash / 10000}}" name="cashAmount" ng-model="formData.cash_amount" placeholder="请输入">
        <p class="yellow" style="margin-top: 5px">提示：转出金额仍在原充值账号上开票。</p>
      </div>
      <div class="col-sm-4" style="line-height: 32px;">(转出账号现金余额：{{fromUserInfo.walletOverview.cash | moneyWithCurrencyFilter:'':fromUserInfo.currency_type}})</div>
    </div>
    <div class="form-group">
      <label class="col-sm-2 control-label">转移赠送金(牛币)</label>
      <div class="col-sm-6">
        <input type="number" class="form-control" step="0.01" min="0" name="cashAmount" max="{{fromUserInfo.walletOverview.nb / 10000}}" ng-model="formData.nb_amount" placeholder="请输入">
      </div>
      <div class="col-sm-4">(转出账号赠送金余额：{{fromUserInfo.walletOverview.nb | moneyWithCurrencyFilter:'':fromUserInfo.currency_type}})</div>
    </div>
    <div class="form-group">
      <label class="col-sm-2 control-label">转移理由<span class="red">*</span></label>
      <div class="col-sm-6">
        <textarea class="form-control" rows="3" ng-model="formData.reason" required placeholder="请输入转移理由"></textarea>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-sm-2">上传附件<span class="red">*</span></label>
      <div class="col-sm-10">
        <ul style="list-style: auto; padding-left: 16px;">
          <li>企业账号转出：转出方需要填写《<a href="https://cf.qiniu.io/download/attachments/39844969/%E4%BD%99%E9%A2%9D%E8%BD%AC%E7%A7%BB%E7%94%B3%E8%AF%B7.docx?version=1&modificationDate=1587347733000&api=v2" target="_blank">余额转移申请</a>》并加盖公章，将扫描件上传。原件请寄往：上海市浦东新区亮秀路81号、销售运营收 、联系电话：18017393053。</li>
          <li>个人账号转给企业账号：转出方需要填写《<a href="https://cf.qiniu.io/download/attachments/39844969/%E4%BD%99%E9%A2%9D%E8%BD%AC%E7%A7%BB%E7%94%B3%E8%AF%B7%EF%BC%88%E4%B8%AA%E4%BA%BA%E8%BD%AC%E4%BC%81%E4%B8%9A%EF%BC%89.docx?version=1&modificationDate=1587347795000&api=v2" target="_blank">余额转移申请（个人转企业）</a>》并加盖公章，将扫描件上传。原件请寄往：上海市浦东新区亮秀路81号、销售运营收 、联系电话：18017393053。</li>
          <li>个人账号转给个人账号：转出 uid 需发起工单，说明需要转入的金额和账号，转入 uid 发工单确认同意接收转入金额。上传工单截图。</li>
        </ul>
        <qn-ag-file-upload
          size="5 * 1024 * 1024"
          files="formData.attachments"
          accept="image/png,image/jpeg,image/bmp,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        >
          <p>支持文件格式：DOC、PDF、JPG、PNG、BMP，文件不超过 5MB。</p>
        </qn-ag-file-upload>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-primary" type="submit" ng-disabled="form.$invalid || isSubmitting">申请</button>
    <button class="btn btn-default" type="button" ng-click="close()">关闭</button>
  </div>
</form>
