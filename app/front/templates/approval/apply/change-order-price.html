<div class="modal-header"><h4>订单改价申请</h4></div>
<div class="modal-body order-approval-modal change-order-price-modal">
  <div class="order-detail-item">
    <label for="">订单号</label>
    <div class="col-sm-4">
      <input type="text" class="form-control" ng-model="orderHash" ng-show="edit">
      <span ng-show="!edit">{{orderHash}}</span>
    </div>
    <button class="btn btn-default" type="button" ng-click="queryOrder()" ng-show="edit">确认</button>
    <button class="btn btn-default" type="button" ng-click="editOrderHash()" ng-show="!edit">修改</button>
  </div>
  <div class="order-detail-item" ng-show="orderDetail.buyer_id">
    <label for="">UID</label>
    <div class="col-sm-4">{{orderDetail.buyer_id}}</div>
  </div>
  <div class="order-detail-item" ng-show="orderDetail.product_orders.length>1">
    <label>改价方式</label>
    <div>
      <label class="radio-inline">
        <input class="control-label" ng-model="changedType"
               type="radio" name="changedType"
               value="0" /> 整单改价
      </label>
      <label class="radio-inline">
        <input class="control-label" ng-model="changedType"
               type="radio" name="changedType"
               value="1" /> 部分改价
      </label>
    </div>
  </div>
  <div ng-show="orderDetail">
    <table class="table table-bordered table-hover" ng-show="orderDetail">
      <thead style="background: #f5f5f5">
        <tr>
          <th>商品名称</th>
          <th>数量</th>
          <th>时长</th>
          <th>分配方式</th>
          <th>实际费用</th>
          <th>改后费用</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat-start="po in orderDetail.product_orders">
          <td>{{po.product_name}}</td>
          <td>{{po.quantity}}</td>
          <td>{{po.duration}} {{PRODUCT_UNIT_MAP[po.product.unit]}}</td>
          <td>{{po | parsePoCarryPolicyFilter}}</td>
          <td>{{po.c_fee | moneyWithCurrencyFilter:1:po.currency_type}}</td>
          <td ng-if="poUnitPrices[po.id]&&changedType == 0">{{ poUnitPrices[po.id].c_fee |
            moneyWithCurrencyFilter:1:po.currency_type:2}}
          </td>
          <td ng-if="!poUnitPrices[po.id]&&changedType == 0">-</td>
          <td ng-if="changedType == 1">
            <input type="number" class="form-control" ng-model="po.changedPrice">
          </td>
        </tr>
        <tr ng-repeat-end>
          <td colspan="6" class="warning" ng-if="poUnitPrices[po.id]&&poUnitPrices[po.id].c_fee!=po.c_fee">
            <span>
            本次改价后单价：
              <strong ng-if="poUnitPrices[po.id].is_respack">
                {{poUnitPrices[po.id].unit_price| moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}/{{poUnitPrices[po.id].unit}}
              </strong>
              <strong ng-if="!poUnitPrices[po.id].is_respack">
                {{poUnitPrices[po.id].unit_price| moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}
              </strong>
            </span>
            <span ng-if="poUnitPrices[po.id].is_respack">，较审批线对比:</span>
            <span ng-if="poUnitPrices[po.id].is_respack&&!poUnitPrices[po.id].cost_price" class="text-muted">暂无</span>
            <span ng-if="poUnitPrices[po.id].cost_price&&poUnitPrices[po.id].compare==-1" class="text-success">低于</span>
            <span ng-if="poUnitPrices[po.id].cost_price&&poUnitPrices[po.id].compare==0" class="text-warning">正中</span>
            <span ng-if="poUnitPrices[po.id].cost_price&&poUnitPrices[po.id].compare==1" class="text-danger">高于</span>
            <span>。</span>
            <span>上次审批价:</span>
            <span ng-if="!poUnitPrices[po.id].last_approved" class="text-muted">暂无</span>
            <span ng-if="poUnitPrices[po.id].last_approved">
              <strong ng-if="poUnitPrices[po.id].is_respack">{{poUnitPrices[po.id].last_approved|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}/{{poUnitPrices[po.id].unit}}</strong>
              <strong ng-if="!poUnitPrices[po.id].is_respack">{{poUnitPrices[po.id].last_approved|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}</strong>
            </span>
            <span ng-if="poUnitPrices[po.id].last_approved">
                ，较上次:
                <strong ng-if="poUnitPrices[po.id].delta>0">高</strong>
                <strong ng-if="poUnitPrices[po.id].delta<0">低</strong>
                <strong ng-if="poUnitPrices[po.id].delta===0">无变化</strong>
                <strong ng-if="poUnitPrices[po.id].delta!==0">
                  {{poUnitPrices[po.id].delta_abs}}%
                </strong>
              </span>
            <span>。最低审批价:</span>
            <span ng-if="!poUnitPrices[po.id].lowest_approved" class="text-muted">暂无</span>
            <span ng-if="poUnitPrices[po.id].lowest_approved">
              <strong ng-if="poUnitPrices[po.id].is_respack">{{poUnitPrices[po.id].lowest_approved|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}/{{poUnitPrices[po.id].unit}}。</strong>
              <strong ng-if="!poUnitPrices[po.id].is_respack">{{poUnitPrices[po.id].lowest_approved|moneyWithCurrencyFilter:1:orderDetail.currency_type:5}}。</strong>
            </span>
          </td>
        </tr>
      </tbody>
    </table>
    <p class="text-right new-fee-block">
      <span>订单改价前需支付总费用：</span>
      <span>{{ orderDetail.c_fee.toFixed(2) | moneyWithCurrencyFilter:1:orderDetail.currency_type }}</span>
      <span style="padding-right: 40px;"></span>
      <span>改价后支付总费用：{{orderDetail.currency_type == CurrencyType.CNY ? "¥" : "$"}}&nbsp;&nbsp;</span>
      <span ng-show="changedType == 0">
        <input type="number"
               class="form-control" ng-model="changedPrice">
      </span>
      <span ng-show="changedType == 1">{{changedPrice}}</span>
    </p>
    <div class="order-detail-item" ng-show="canDisplayLowPriceUser">
        <input type="checkbox" 
               ng-model="isLowPriceUser"
               ng-disabled="!canEditLowPriceUser">
        <span style="margin-left: 8px;">SMB 低价线路客户<span ng-show="canEditLowPriceUser">(请在您了解业务意义的前提下选择)</span></span>
    </div>
    <div class="order-detail-item">
      <label for=""><span class="red">* </span>申请原因</label>
    </div>
    <div class="order-detail-item">
      <textarea name="reason" class="form-control" id="reason" cols="30" rows="6" ng-model="reason"></textarea>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button class="btn btn-primary" ng-click="submit()" ng-disabled="disableSubmit || isLoading">确认</button>
  <button class="btn btn-default" ng-click="close()">关闭</button>
</div>
