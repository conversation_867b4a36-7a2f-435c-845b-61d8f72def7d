<div class="modal-header"><h4>包年包月（订单）抵用券申请</h4></div>
<div class="modal-body">
  <div class="form-group" ng-show="!developerInfo">
    <label class="col-sm-2 control-label">UID</label>
    <div class="col-sm-4">
      <uid-searcher ng-model="uid" />
    </div>
    <div class="col-sm-2">
      <button class="btn btn-primary" type="button" ng-click="query()">查询</button>
    </div>
  </div>
  <div class="form-group" ng-show="isUSDUser">
    <label for="" class="col-sm-2"></label>
    <p class="col-sm-4 red">
      美元用户不能申请包年包月（订单）抵用券！
    </p>
  </div>
  <form name="salesApplyForm" ng-show="developerInfo">
    <section>
      <h4>基本信息</h4>
      <div class="form-group">
        <label class="col-sm-2 control-label">UID：</label>
        <label class="col-sm-2 control-label" style="text-align: left;">{{ uid }}</label>
        <label class="col-sm-2 control-label">客户：</label>
        <label class="col-sm-4 control-label" style="text-align: left;">{{ developerInfo.full_name }}</label>
      </div>
      <div class="form-group">
        <label class="col-sm-2 control-label">名称：<span class="red"> *</span></label>
        <div class="col-sm-6">
          <input class="form-control" placeholder="抵用券名称将对用户展示，请认真填写。" name="name"
            ng-model="coupon.name" required />
          <span class="help-block" ng-messages="salesApplyForm.name.$error" ng-show="salesApplyForm.name.$dirty">
            <span ng-message="required">必填</span>
          </span>
        </div>
      </div>

      <div class="form-group">
        <label class="col-sm-2 control-label">描述：<span class="red"> *</span></label>
        <div class="col-sm-6">
          <input class="form-control" placeholder="请描述该券的使用场景、使用限制等等，将对用户展示，请认真填写。" name="description"
            ng-model="coupon.description" required />
          <span class="help-block" ng-messages="salesApplyForm.description.$error" ng-show="salesApplyForm.description.$dirty">
            <span ng-message="required">必填</span>
          </span>
        </div>
      </div>

      <div class="form-group">
        <label class="col-sm-2 control-label">事由分类：<span class="red"> *</span></label>
        <div class="col-sm-6">
          <select class="form-control" name="reason" ng-model="coupon.reason"
            ng-options="v.value as v.display for (k, v) in BIZ_TYPE" required>
            <option value="">---- 请选择申请本次抵用券的事由分类 ----</option>
          </select>
          <span class="help-block" ng-messages="salesApplyForm.reason.$error" ng-show="salesApplyForm.reason.$dirty">
            <span ng-message="required">必选</span>
          </span>
        </div>
      </div>

      <div class="form-group">
        <label class="col-sm-2 control-label">事由：<span class="red"> *</span></label>
        <div class="col-sm-6">
          <textarea class="form-control" style="resize:none;" placeholder="请说明本次抵用券申请的原因，客情关系原因请说明当前报价和消费体量，测试费用原因请注明预估上量和报价"
            name="reason_desc" ng-model="coupon.reason_desc" required>
          </textarea>
          <span class="help-block" ng-messages="salesApplyForm.reason_desc.$error" ng-show="salesApplyForm.reason_desc.$dirty">
            <span ng-message="required">必填</span>
          </span>
        </div>
      </div>
      <h4>抵用券规则</h4>
      <div class="form-group">
        <label class="col-sm-2 control-label">满减类型：<span class="red"> *</span></label>
        <input class="control-label" ng-model="discountType" name="discountType" type="radio" value="0" ng-disabled="isDisabledForm || (isEdit && inProgress) || (isEdit && isEnd)" />
        <label class="control-label">无门槛</label>
        <input class="control-label" ng-model="discountType" name="discountType" type="radio" value="1" ng-disabled="isDisabledForm || (isEdit && inProgress) || (isEdit && isEnd)" />
        <label class="control-label">有门槛</label>
      </div>
      <div class="form-group">
        <label for="" class="col-sm-2"></label>
        <span class="red" style="font-weight: 400;">无门槛代表没有金额限制，有门槛代表订单金额需达到xxx元才可用券，即通常说的“满xxx元减yyy元”</span>
      </div>
      <div class="form-group" ng-if="discountType == 1">
        <div class="form-inline">
          <label class="col-sm-2 control-label">使用金额门槛：<span class="red"> *</span></label>
          <div class="col-sm-4">
            满 <input class="form-control" type="number" name="threshold_money" min="0" step="0.01" larger-than-coupon-money ng-model="coupon.threshold_money" ng-disabled="isDisabledForm || (isEdit && (isEdit && isEnd))" required/> 元
            <span class="help-block" ng-messages="newCouponCreateForm.threshold_money.$error" ng-show="newCouponCreateForm.threshold_money.$dirty">
              <span ng-message="required">必填</span>
              <span ng-message="min">不得小于 0</span>
              <span ng-message="largerThanCouponMoney">必须高于抵用券面额</span>
            </span>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-sm-2">使用次数：</label>
        <label class="control-label" for="is_multiple_use">可多次使用</label>
      </div>
      <div class="form-group">
        <div class="form-inline">
          <label class="col-sm-2 control-label">预付费商家：<span class="red"> *</span></label>
          <div class="col-sm-8">
            <select class="form-control" name="seller" ng-model="sellers"
            ng-options="v as v.label for (k, v) in sellerMapList" required>
              <option value="">---- 请选择产品线 ----</option>
            </select>
          </div>
        </div>
      </div>
      <div class="form-group">
        <div class="form-inline">
          <label class="col-sm-2 control-label">抵用券面额：<span class="red"> *</span></label>
          <div class="col-sm-4">
            <input class="form-control" type="number" name="coupon_money" step="0.01"
              ng-model="coupon.coupon_money" min="0" required/> 元
            <span class="help-block" ng-messages="salesApplyForm.coupon_money.$error" ng-show="salesApplyForm.coupon_money.$dirty">
              <span ng-message="required">必填</span>
              <span ng-message="min">不得小于 0</span>
              <span ng-show="qvmMoneyInvalid">云主机产品由于成本原因，单笔测试费用上限为 1000 元</span>
            </span>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-2 control-label">生效时间：<span class="red"> *</span></label>
        <div class="form-inline col-sm-8" style="display: flex; align-items: center;">
          <input type="date" class="form-control" name="coupon_effect_time"
            ng-model="coupon.coupon_effect_time" required />
          <span class="help-block" ng-messages="salesApplyForm.coupon_effect_time.$error" ng-show="salesApplyForm.coupon_effect_time.$dirty">
            <span ng-message="required">必填</span>
          </span>
          <span>~</span>
          <input type="date" class="form-control" name="coupon_dead_time"
            ng-model="coupon.coupon_dead_time" required />
          <span class="help-block" ng-messages="salesApplyForm.coupon_dead_time.$error" ng-show="salesApplyForm.coupon_dead_time.$dirty">
            <span ng-message="required">必填</span>
          </span>
        </div>
      </div>
      <div class="form-group">
        <label for="" class="col-sm-2"></label>
        <span class="red" style="font-weight: 400">
          发放时间和生效时间默认左闭右开，如需整月有效请填写如“ 2020-1-1 至 2020-2-1
        </span>
      </div>
      <div class="form-group">
        <div class="form-inline">
          <label class="col-sm-2 control-label">适用范围文案：<span class="red"> *</span></label>
          <div class="col-sm-8">
            <textarea class="form-control" style="width: 600px;" name="coupon_scope_desc" ng-model="coupon.coupon_scope_desc" required ng-disabled="true"></textarea>
            <span class="help-block" ng-messages="salesApplyForm.coupon_scope_desc.$error" ng-show="salesApplyForm.coupon_scope_desc.$dirty">
              <span ng-message="required">必填</span>
            </span>
          </div>
        </div>
      </div>
    </section>
  </form>
</div>
<div class="modal-footer">
  <button class="btn btn-primary" ng-click="submit()" ng-disabled="salesApplyForm.$invalid || qvmMoneyInvalid || isSubmitting">申请</button>
  <button class="btn btn-default" ng-click="close()">关闭</button>
</div>
