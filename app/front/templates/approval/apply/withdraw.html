<div class="modal-header"><h4>提现申请</h4></div>
<div class="modal-body withdraw-modal">
  <form class="form-inline">
    <div class="form-group">
      <uid-searcher ng-model="applyRequestBody.uid" />
    </div>
    <div class="form-group">
      <button class="btn btn-primary" ng-click="query()">查询</button>
    </div>
    <div ng-show="isIllegalFreezed" class="form-group">
      <span class="red">
        该用户因违规被冻结，请先申请解冻再提现。
      </span>
      <a href="https://cf.qiniu.io/pages/viewpage.action?pageId=136118342" target="_blank">参考文档</a>
    </div>
  </form>
  
  <loading ng-show="isQueryLoading"></loading>

  <section class="section" ng-show="availableAmount && !isIllegalFreezed && !isQueryLoading">
    <div class="section-item">
      <label>认证名称：</label>
      <span>{{identityName}}</span>
    </div>
    <div class="section-item">
      <label>参考数据：</label>
      <div class="overview">
        <div class="item">可提现金额</div>
        <div class="item">=</div>
        <div class="item">现金余额</div>
        <div class="item">-</div>
        <div class="item">未支付金额</div>
        <div class="item">-</div>
        <div class="item">欠票金额</div>
        <div class="item">-</div>
        <div class="item">提现中金额</div>
        <div class="item">-</div>
        <div class="item">转账金额</div>
        <div class="item">{{availableAmount.available_withdraw_cash | moneyWithCurrencyFilter:'':currency}}</div>
        <div class="item">=</div>
        <div class="item">{{availableAmount.cash_balance | moneyWithCurrencyFilter:'':currency}}</div>
        <div class="item">-</div>
        <div class="item">{{availableAmount.arrears | moneyWithCurrencyFilter:'':currency}}</div>
        <div class="item">-</div>
        <div class="item">{{availableAmount.invoice_amount | moneyWithCurrencyFilter:'':currency}}</div>
        <div class="item">-</div>
        <div class="item">{{availableAmount.processing_withdraw_cash | moneyWithCurrencyFilter:'':currency}}</div>
        <div class="item">-</div>
        <div class="item">{{availableAmount.transfer_withdraw_cash | moneyWithCurrencyFilter:'':currency}}</div>
      </div>
    </div>
  </section>
</div>
<div class="modal-body withdraw-modal" style="border-top: 1px solid #e5e5e5" ng-show="availableAmount && !isIllegalFreezed">
  <div class="form-group">
    <label class="col-sm-2 control-label text-right">申请提现金额：</label>
    <div class="col-sm-4">
      <span>{{applyMoney | moneyWithCurrencyFilter:'':currency}}</span>
      <a href="javascript:void(0)" ng-click="showRechargeTransactions()">选择充值记录</a>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label text-right">银行开户名：</label>
    <div class="col-sm-4">
      <input type="text" class="form-control" ng-model="applyRequestBody.payee_info.name">
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label text-right">收款银行名称：</label>
    <div class="col-sm-4">
      <input type="text" class="form-control" placeholder="例如：中国银行" ng-model="applyRequestBody.payee_info.bank">
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label text-right">收款银行网点：</label>
    <div class="col-sm-4">
      <input type="text" class="form-control" placeholder="张江支行" ng-model="applyRequestBody.payee_info.branch_bank">
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label text-right">收款银行账号：</label>
    <div class="col-sm-4">
      <input type="text" class="form-control" placeholder="请填写收款银行账号" ng-model="applyRequestBody.payee_info.account">
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label text-right">提现原因：</label>
    <div class="col-sm-6" style="display: flex">
      <select
        class="form-control"
        ng-options="v.value as v.display for (k, v) in WithdrawReasonList"
        style="border-top-right-radius: 0; border-bottom-right-radius: 0; width: 120px;" ng-model="applyRequestBody.withdraw_reason_type">
        <option value="">请选择</option>
      </select>
      <input type="text" class="form-control" style="border-top-left-radius: 0; border-bottom-left-radius: 0;" placeholder="请输入详情，不会对外展示" ng-model="applyRequestBody.withdraw_reason">
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label text-right">上传附件：</label>
    <div class="col-sm-5">
      <p>若特殊情况可上传证明材料，文件不超过 5MB。<br/>支持文件格式：DOC、PDF、JPG、PNG、BMP</p>
      <input type="file" multiple id="withdrawFormFile" accept="image/png,image/jpeg,image/bmp,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document">
    </div>
  </div>
</div>
<div class="modal-footer">
  <button class="btn btn-primary" ng-click="submit()" ng-disabled="invalid || isLoading">申请</button>
  <button class="btn btn-default" ng-click="close()">关闭</button>
</div>