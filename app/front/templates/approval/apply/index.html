<ul class="nav nav-tabs">
  <li ng-repeat="v in viewTypes" ng-class="{'active': v.value == viewTypes.PROCESS_APPLY.value}"
    ng-click="onTabSel(v.value)">
    <a href="#" >{{v.display}}</a>
  </li>
</ul>
<div class="approval-apply main-wrapper">
  <ul class="left-menu">
    <li class="menu-item" ng-repeat="f in featureList" ng-class="{'active': current == f.value}" ng-click="switchFeat(f.value, $index)">{{f.display}}</li>
  </ul>
  <div class="right-content" ng-class="{'show': current == featureList.ORDER.value}" ng-show="current == featureList.ORDER.value">
    <h2>订单相关审批</h2>
    <ul class="feature-list">
      <li>
        <div class="content">
          <span class="feature-name">订单改价</span>
          <span class="feature-desc">对订单支付金额进行修改</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="订单改价发起规则"
            content="订单状态为【未支付】且【未过支付过期时间】，才可以申请订单改价。（注意：改价审批通过前，请勿让客户进行支付）。"></alert-info>
          <a href="javascript:void(0)" ng-click="applyChangeOrderPrice()" class="btn btn-primary">发起</a>
        </div>
      </li>
      <li>
        <div class="content">
          <span class="feature-name">订单转后付费</span>
          <span class="feature-desc">后付费定义：订单商品先发货给客户使用，客户后付钱</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="订单转后付费发起规则"
            content="订单状态为【未支付】且【未过支付过期时间】，才可以申请订单转后付费。（注意：改价审批通过前，请勿让客户进行支付）。"
          >
          </alert-info>
          <a href="javascript:void(0)" ng-click="applyPostPay()" class="btn btn-primary">发起</a>
        </div>
      </li>
      <li>
        <div class="content">
          <span class="feature-name">改价并转后付费</span>
          <span class="feature-desc">改完价格并将订单转为后付费状态</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="改价并后付费发起规则"
            content="订单状态为【未支付】且【未过支付过期时间】，才可以申请改价并后付费。（注意：改价审批通过前，请勿让客户进行支付）。"
          >
          </alert-info>
          <a href="javascript:void(0)" ng-click="applyChangePriceAndPostPay()" class="btn btn-primary">发起</a>
        </div>
      </li>
      <li>
        <div class="content">
          <span class="feature-name">订单退款</span>
          <span class="feature-desc">资源包和普通订单商品退款（不包含QVM，主机退款请点击发起规则查看）</span>
        </div>
        <div class="operation">
          <alert-info
            width="500"
            text="发起规则"
            alert-title="订单退款"
            content="{{refund_content}}"
          >
          </alert-info>
          <a href="javascript:void(0)" ng-click="applyRefund()" class="btn btn-primary">发起</a>
        </div>
      </li>
      <li>
        <div class="content">
          <span class="feature-name">资源包更改开始时间申请</span>
          <span class="feature-desc">更改资源包的开始生效时间</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="资源包改开始时间发起规则"
            content="原始的开始生效的月份需晚于等于当前操作月份（如当前是2022年6月，资源包开始时间需满足2022年6月及以后月份才允许更改）。"
          >
          </alert-info>
          <a href="javascript:void(0)" ng-click="applyModifyRespackStartTime()" class="btn btn-primary">发起</a>
        </div>
      </li>
      <li>
        <div class="content">
          <span class="feature-name">资源包延期</span>
          <span class="feature-desc">延长一次性分配资源包的结束时间</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="资源包延期发起规则"
            content="结转方式为【一次性分配】且资源包【未用完】【未到结束时间】。"
          >
          </alert-info>
          <a href="javascript:void(0)" ng-click="applyExtendRespackDuration()" class="btn btn-primary">发起</a>
        </div>
      </li>
    </ul>
  </div>
  <div class="right-content" ng-class="{'show': current == featureList.CONTRACT.value}" ng-show="current == featureList.CONTRACT.value">
    <h3>功能未开放</h3>
  </div>
  <div class="right-content" ng-class="{'show': current == featureList.OFFER.value}" ng-show="current == featureList.OFFER.value">
    <h3>功能未开放</h3>
  </div>
  <div class="right-content" ng-class="{'show': current == featureList.COUPON.value}" ng-show="current == featureList.COUPON.value">
    <h3>抵用券相关审批<a href="https://cf.qiniu.io/pages/viewpage.action?pageId=140223289" target="_blank" style="margin-left: 20px; font-size: 14px;">抵用券使用规则</a></h3>
    <ul class="feature-list">
      <li>
        <div class="content">
          <span class="feature-name">按量计费抵用券申请</span>
          <span class="feature-desc">可用于抵扣按量付费的产品费用。</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="按量计费抵用券申请发起规则"
            content="仅支持申请未出账月份抵用券，例如25年3月抵用券，请在25年3月末之前申请并审批通过。"></alert-info>
          <a href="javascript:void(0)" ng-click="applyBillCoupon()" class="btn btn-primary">发起</a>
        </div>
      </li>
      <li>
        <div class="content">
          <span class="feature-name">包年包月（订单）券申请</span>
          <span class="feature-desc">可用于抵扣资源包、SSL证书、包年包月的云主机等一次性结算的订单产品费用。</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="包年包月（订单）券申请发起规则"
            content="请在订单支付前申请并审批通过。"></alert-info>
          <a href="javascript:void(0)" ng-click="applyOrderCoupon()" class="btn btn-primary">发起</a>
        </div>
      </li>
    </ul>
  </div>
  <div class="right-content" ng-class="{'show': current == featureList.CAPITAL.value}" ng-show="current == featureList.CAPITAL.value">
    <h2>账户资金相关审批</h2>
    <ul class="feature-list">
      <li>
        <div class="content">
          <span class="feature-name">提现</span>
          <span class="feature-desc">账户余额提现至客户银行卡</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="提现发起规则"
            content="账户余额大于0，申请提现前，需将充值对应的增值税发票退回。"></alert-info>
          <a href="javascript:void(0)" ng-click="applyWithdraw()" class="btn btn-primary">发起</a>
        </div>
      </li>
      <li>
        <div class="content">
          <span class="feature-name">申请充值赠送金</span>
          <span class="feature-desc">根据合同约定为用户申请充值赠送金，赠送金不可提现，不可开票。</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="申请充值赠送金发起规则"
            content="客户先进行现金充值，充值后才能申请对应充值的赠送金。"></alert-info>
          <a href="javascript:void(0)" ng-click="applyRechargeReward()" class="btn btn-primary">发起</a>
        </div>
      </li>
      <li>
        <div class="content">
          <span class="feature-name">余额转移</span>
          <span class="feature-desc">余额转移 将 A 账号现金、赠送金（牛币）转移至 B 账号。</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="余额转移发起规则"
            content="余额转移发起规则 转出账号的现金或赠送金余额大于 0。"></alert-info>
          <a href="javascript:void(0)" ng-click="applyBalanceTransfer()" class="btn btn-primary">发起</a>
        </div>
      </li>
    </ul>
  </div>
  <div class="right-content" ng-class="{'show': current == featureList.RISK_CONTROL.value}" ng-show="current == featureList.RISK_CONTROL.value">
    <h2>风控相关审批</h2>
    <ul class="feature-list">
      <li>
        <div class="content">
          <span class="feature-name">解除欠费保护期/冻结</span>
          <span class="feature-desc">恢复用户正常使用</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="解除欠费保护期/冻结发起规则"
            content="用户已进入欠费保护期/冻结状态才能发起申请。"></alert-info>
          <a href="javascript:void(0)" ng-click="applyRemoveFreeze()" class="btn btn-primary">发起</a>
        </div>
      </li>
      <li>
        <div class="content">
          <span class="feature-name">申请临时欠费保护期</span>
          <span class="feature-desc">延长用户保护期时长，推迟进入冻结时间。仅一次有效，使用后自动清零。</span>
        </div>
        <div class="operation">
          <alert-info
            text="发起规则"
            alert-title="申请临时欠费保护期发起规则"
            content="用户处于非冻结状态才能发起。"></alert-info>
          <a href="javascript:void(0)" ng-click="applyExtendBuffer()" class="btn btn-primary">发起</a>
        </div>
      </li>
    </ul>
  </div>
  <div class="right-content" ng-class="{'show': current == featureList.BILL.value}" ng-show="current == featureList.BILL.value">
    <h2>账单相关审批</h2>
    <ul class="feature-list">
      <li>
        <div class="content">
          <span class="feature-name">LCDN重出账</span>
          <span class="feature-desc"> 发起 LCDN 客户账单重出申请</span>
        </div>
        <div class="operation">
          <alert-info
                  text="发起规则"
                  alert-title="LCDN重出账发起规则"
                  content="申请对 LCDN 客户进行重出账。仅限 LCDN 客户"></alert-info>
          <a href="javascript:void(0)" ng-click="applySyncedRegenBill('lcdn')" class="btn btn-primary">发起</a>
        </div>
      </li>
        <li>
            <div class="content">
                <span class="feature-name">LAS重出账</span>
                <span class="feature-desc"> 发起 LAS 客户账单重出申请</span>
            </div>
            <div class="operation">
                <alert-info
                        text="发起规则"
                        alert-title="LAS重出账发起规则"
                        content="申请对 LAS 客户进行重出账。仅限 LAS 客户"></alert-info>
                <a href="javascript:void(0)" ng-click="applySyncedRegenBill('las')" class="btn btn-primary">发起</a>
            </div>
        </li>
    </ul>
  </div>
</div>
