language: go

go:
  - 1.21.x

go_import_path: qiniu.io/qbpm

services:
  - mysql
  - redis

branches:
  only:
    - master
    - develop

cache:
  directories:
    - $HOME/.cache/go-build
    - $GOPATH/pkg/mod

before_install:
  - git config --global url."ssh://**************/qbox/".insteadOf "https://github.com/qbox/"
  - go install -mod=mod github.com/onsi/ginkgo/v2/ginkgo@latest
  - go install honnef.co/go/tools/cmd/staticcheck@latest

install:
  - echo

before_script:
  - mysql -e 'SET GLOBAL sql_mode = "STRICT_TRANS_TABLES";'
script:
  - scripts/ci/travis.sh
