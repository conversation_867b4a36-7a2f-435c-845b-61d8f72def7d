# 工作流引擎 QBPM(Qiniu Business Process Management)

项目文档：https://cf.qiniu.io/pages/viewpage.action?pageId=67724750

### 测试

qbpm 的测试基于 [`ginkgo`](http://onsi.github.io/ginkgo/) 进行，如果需要在本机安装 ginkgo 的命令行工具，安装命令 `go get github.com/onsi/ginkgo/ginkgo`。

#### 单元测试

单元测试的代码在 `src/qiniu.io/qbpm` 的各个 package 下组织。一般要求每个包下都使用 `ginkgo bootstrap` 生成一个统一的 test 入口文件，再根据每个待测文件使用 `ginkgo generate <TARGET>.go` 生成对应的 test 文件。

### API SDK

SDK 基于 swagger 自动生成

#### api 定义
https://github.com/qbox/qbpm/blob/develop/sdk/qbpm.yml

#### inspector
1. 使用  `open -a Google\ Chrome --args --disable-web-security --user-data-dir` 打开chrome，以解决跨域导致的问题
2. 把 [api定义](https://github.com/qbox/qbpm/blob/develop/sdk/qbpm.yml) 粘贴到 http://editor.swagger.io/
3. 点击 Authorize 获取token

#### SDK 生成和代码格式化
```
make gen_sdk
make format
```
由于 go-swagger 生成客户端时不能区分相同包的不同 Import path，在编写 sdk 配置的时候需要注意 tags 不能指定为 sdk 中可能引入的依赖包，如 runtime、strfmt 等。
