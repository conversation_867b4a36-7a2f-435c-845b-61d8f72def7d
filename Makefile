SHELL = /bin/bash

TOP := $(shell pwd)

.PHONY: all dev build build-coverage rebuild clean install test integration-test travis install_ginkgo install_watcher gen_sdk format

all: build dev

dev: install_watcher
	reflex -s -R '^vendor/' \
		-- go run app/main.go -c app/env/config/config.yml web

build:
	go build -o app/qbpmd ./app/main.go

build-coverage:
	cd app && goc build -o ./qbpmd .

rebuild: clean
	make build

clean:
	rm -rf pkg bin

install:
	GODEBUG=netdns=go go install ./...

test:
	CGO_ENABLED=1 ginkgo -v -flakeAttempts 3 -r ./app ./engine ./swagger

integration-test:
	CGO_ENABLED=1 ginkgo -v -r ./test/integration

travis: build
	make test

install_ginkgo:
	@type ginkgo || go install -mod=mod github.com/onsi/ginkgo/v2/ginkgo@latest

install_watcher:
	@type reflex || go install github.com/cespare/reflex@latest

gen_sdk:
	rm -r $(TOP)/sdk/client
	rm -r $(TOP)/sdk/models
	bash $(TOP)/scripts/gen_http_sdk.sh

format:
	gofmt -s -w ./app
	gofmt -s -w ./engine
