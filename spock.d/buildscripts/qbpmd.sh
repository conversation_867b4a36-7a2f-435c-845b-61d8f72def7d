#!/bin/bash

set -e

export WORKDIR=$WORKSPACE/src/qiniu.io/qbpm
export PATH=$PATH:$WORKDIR

export GOPROXY=https://goproxy.cn,direct
git config --global url."**************:".insteadOf "https://github.com/"


# cat > ~/.ssh/config <<EOF
# Host github.com
# ProxyCommand nc -x ************:1080 %h %p
# EOF


# 编译
cd $WORKDIR
git clean -fdx
make clean
make build

# 打包
#tmpdir="$(mktemp -d)"
pushd "${WORKSPACE}"
    mkdir -p _package/
    # 打包相关文件
    cp "${WORKDIR}/app/qbpmd" _package
    tar -czvf "${DIST_DIR}/${PKG_FILE}" _package
    rm -rf _package
popd


export RES=$WORKDIR/spock.d/dockerfiles

# 推镜像
cp $WORKDIR/app/qbpmd $RES
pushd $RES/
    docker buildx build --rm --builder=kube -t $IMAGE -f qbpm.dockerfile . --push
popd
