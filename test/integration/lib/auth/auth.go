package auth

import (
	"fmt"

	"qiniu.io/qbpm/test/integration/lib/config"
	"qiniu.io/qbpm/test/integration/lib/qnhttp"
)

// AccTokenResult acc 鉴权 Model
type AccTokenResult struct {
	AccToken string `json:"access_token"`
	Expires  int    `json:"expires_in"`
	RefToken string `json:"refresh_token"`
}

// GetAccToken 签 acc 鉴权 token
func GetAccToken(username, password string) string {
	url := fmt.Sprintf("%s/oauth2/token", config.Keys.AccHost)
	payload := "grant_type=password&username=" + username + "&password=" + password

	resp, _ := qnhttp.New().Post(url, payload, nil, nil)
	result := AccTokenResult{}
	resp.Unmarshal(&result)
	return result.AccToken
}
