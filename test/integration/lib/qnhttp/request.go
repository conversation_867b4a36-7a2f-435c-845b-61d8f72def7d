package qnhttp

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// A Params is a map containing URL parameters.
type Params map[string]string

// AsURLValues parses params to url values
func (p Params) AsURLValues() url.Values {
	result := url.Values{}
	for key, value := range p {
		result.Set(key, value)
	}
	return result
}

// A Request describes an HTTP request to be executed, data structures into
// which the result will be unmarshalled, and the server's response. By using
// a  single object for both the request and the response we allow easy access
// to Result and Error objects without needing type assertions.
type Request struct {
	URL     string      // Raw URL string
	Method  string      // HTTP method to use
	Params  *url.Values // URL query parameters
	Payload interface{} // Data to JSON-encode and POST

	// Can be set to true if Payload is of type *bytes.Buffer and client wants
	// to send it as-is
	RawPayload bool

	// Result is a pointer to a data structure.  On success (HTTP status < 300),
	// response from server is unmarshaled into Result.
	Result interface{}

	// CaptureResponseBody can be set to capture the response body for external use.
	CaptureResponseBody bool

	// ResponseBody exports the raw response body if CaptureResponseBody is true.
	ResponseBody *bytes.Buffer

	// Error is a pointer to a data structure.  On error (HTTP status >= 300),
	// response from server is unmarshaled into Error.
	Error interface{}

	// Optional
	Userinfo *url.Userinfo
	Header   *http.Header

	// The following fields are populated by Send().
	timestamp time.Time      // Time when HTTP request was sent
	status    int            // HTTP status for executed request
	response  *http.Response // Response object from http package
	body      []byte         // Body of server's response (JSON or otherwise)
}

// A Response is a Request object that has been executed.
type Response Request

// Timestamp returns the time when HTTP request was sent.
func (r *Response) Timestamp() time.Time {
	return r.timestamp
}

// RawText returns the body of the server's response as raw text.
func (r *Response) RawText() string {
	return strings.TrimSpace(string(r.body))
}

// BodyToByte gets body of current response
func (r *Response) BodyToByte() []byte {
	return r.body
}

// ResponseBodyAsJSON returns the body of the server's response as JSON format.
func (r *Response) ResponseBodyAsJSON() (res map[string]interface{}) {
	if err := json.Unmarshal(r.body, &res); err != nil {
		panic(err)
	}
	return
}

// ResponseBodyAsRel returns the body of the server's response as slice.
func (r *Response) ResponseBodyAsRel(rel interface{}) {
	if err := json.Unmarshal(r.body, &rel); err != nil {
		panic(err)
	}
	return
}

// Status returns the HTTP status for the executed request, or 0 if request has
// not yet been sent.
func (r *Response) Status() int {
	return r.status
}

// HTTPResponse returns the underlying Response object from http package.
func (r *Response) HTTPResponse() *http.Response {
	return r.response
}

// Unmarshal parses the JSON-encoded data in the server's response, and stores
// the result in the value pointed to by v.
func (r *Response) Unmarshal(v interface{}) error {
	return json.Unmarshal(r.body, v)
}

// GetResponseHeader Gets the header from the response
func (r *Response) GetResponseHeader() http.Header {
	return r.HTTPResponse().Header
}

// GetResponseXlog Gets X-log from the response header
func (r *Response) GetResponseXlog() string {
	return r.HTTPResponse().Header.Get("X-log")
}

// ContenLength returns content-length
func (r *Response) ContenLength() (int64, error) {
	s := r.GetResponseHeader().Get("Content-Length")
	if s == "" {
		// return 0, errors.New("Header don't have Content-Length")
		return 0, nil
	}
	slen, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		// return 0, errors.New("Parse Content-Length as int failed")
		return 0, nil
	}
	return slen, nil
}

// GetResponseXReqid gets x-reqid from current response header
func (r *Response) GetResponseXReqid() string {
	return r.HTTPResponse().Header.Get("X-Reqid")
}

// GetResponseXUid gets x-uid from current response header
func (r *Response) GetResponseXUid() string {
	return r.HTTPResponse().Header.Get("X-Uid")
}

// HeaderInfo gets headers from current response
func (r *Response) HeaderInfo() string {
	msg := "X-log: ["
	msg += r.GetResponseXlog()
	msg += "], X-Reqid: ["
	msg += r.GetResponseXReqid()
	msg += "]. "
	return msg
}

//===================================================

// pretty pretty-prints an interface using the JSON marshaler
func pretty(v interface{}) string {
	b, _ := json.MarshalIndent(v, "", "\t")
	return string(b)
}

// PutJSONByCookie send an interface value body with HTTP method PUT
// body will be Marshaled
// Authorization: Cookie
func PutJSONByCookie(url string, body interface{}, cookie string) (*Response, error) {
	s := New()
	if cookie != "" {
		s.Header.Set("Cookie", cookie)
	}
	s.Header.Set("Content-Type", "application/json")

	return s.Put(url, body, nil, nil)
}

// DeleteByCookie send a DELETE request
// Authorization: Cookie
func DeleteByCookie(url string, p *url.Values, cookie string) (*Response, error) {
	s := New()
	s.Header.Set("Cookie", cookie)

	return s.Delete(url, p, nil, nil)
}

// GetByCookie send a GET request
// Authorization: Cookie
func GetByCookie(url string, p *url.Values, cookie string) (*Response, error) {
	s := New()
	s.Header.Set("Cookie", cookie)

	return s.Get(url, p, nil, nil)
}
