package qnhttp

/*
This module provides a Session object to manage and persist settings across
requests (cookies, auth, proxies).
*/

import (
	"bytes"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"io"
	"log"
	"mime/multipart"
	"net"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"sync/atomic"
	"time"
	"unsafe"

	"github.com/onsi/ginkgo/v2"
	"github.com/pkg/errors"
)

// Session defines a conn session
type Session struct {
	Client *http.Client
	Log    bool // Log request and response

	// Optional
	Userinfo *url.Userinfo

	// Optional defaults - can be overridden in a Request
	Header *http.Header
	Params *url.Values

	// Method specifies the HTTP method (GET, POST, PUT, etc.).
	// For client requests an empty string means GET.
	Method string

	// URL specifies either the URI being requested (for server
	// requests) or the URL to access (for client requests).
	//
	// For server requests the URL is parsed from the URI
	// supplied on the Request-Line as stored in RequestURI.  For
	// most requests, fields other than Path and RawQuery will be
	// empty. (See RFC 2616, Section 5.1.2)
	//
	// For client requests, the URL's Host specifies the server to
	// connect to, while the Request's Host field optionally
	// specifies the Host header value to send in the HTTP
	// request.
	URL string

	// Body is the request's body.
	Body string

	RetryTimeout int // 默认是 0，不开启超时重试

	Hosts []string // 随机选取Host
}

func isTimeoutErr(err error) bool {
	// go ver < 1.6 的 url.Error 没有实现 net.Error
	if uerr, ok := err.(*url.Error); ok {
		err = uerr.Err
	}
	if nerr, ok := err.(net.Error); ok {
		return nerr.Timeout()
	}
	return false
}

// Send sends a http request to get response
func (s *Session) Send(r *Request) (response *Response, err error) {

	if len(s.Hosts) > 0 && strings.Index(r.URL, "http") != 0 {
		h := randomSelect(s.Hosts)
		r.URL = h + r.URL
	}

	for i := 0; i < s.RetryTimeout+1; i++ {
		response, err = s.send(r)
		if err != nil && s.RetryTimeout > 0 && isTimeoutErr(err) {
			s.log("Request Timeout, try again ", err)
			continue
		}
		break
	}
	return
}

// Send constructs and sends an HTTP request.
func (s *Session) send(r *Request) (response *Response, err error) {
	r.Method = strings.ToUpper(r.Method)
	//
	// Create a URL object from the raw url string.  This will allow us to compose
	// query parameters programmatically and be guaranteed of a well-formed URL.
	//
	u, err := url.Parse(r.URL)
	if err != nil {
		s.log("URL", r.URL)
		s.log(err)
		return
	}
	//
	// Default query parameters
	//
	p := url.Values{}
	if s.Params != nil {
		for k, v := range *s.Params {
			p[k] = v
		}
	}
	//
	// Parameters that were present in URL
	//
	if u.Query() != nil {
		for k, v := range u.Query() {
			p[k] = v
		}
	}
	//
	// User-supplied params override default
	//
	if r.Params != nil {
		for k, v := range *r.Params {
			p[k] = v
		}
	}
	//
	// Encode parameters
	// By JICHANGJUN, 2015-1-5
	// Encode 之后会破坏我们URL的结构，比如在做镜像回源时，我们的URL可以是这样：
	// http://80cyt7.com1.z0.glb.clouddn.com/Fy76txJL?qiniu_mirror=aHR0cDovLzE5Mi4xNjguMjAuMTAxOjgwOTAvY3VzdG9tSGVhZGVycw==&e=1451992706&token=0tf5awMVxwf8WrEvrjtbiZrdRZRJU-91JgCqTOC8:DQS-9PSp5io_3zcREHAPJE-xvhQ
	// 但是经过Encode之后:
	// http://80cyt7.com1.z0.glb.clouddn.com/Fy76txJL?e=1451992706&qiniu_mirror=aHR0cDovLzE5Mi4xNjguMjAuMTAxOjgwOTAvY3VzdG9tSGVhZGVycw%3D%3D&token=0tf5awMVxwf8WrEvrjtbiZrdRZRJU-91JgCqTOC8%3ADQS-9PSp5io_3zcREHAPJE-xvhQ
	// u.RawQuery = p.Encode()
	//
	// Attach params to response
	//
	r.Params = &p
	//
	// Create a Request object; if populated, Data field is JSON encoded as
	// request body
	//
	header := http.Header{}
	if s.Header != nil {
		for k := range *s.Header {
			v := s.Header.Get(k)
			header.Set(k, v)
		}
	}
	var req *http.Request
	var buf *bytes.Buffer
	if r.Payload != nil {
		if r.RawPayload {
			switch r.Payload.(type) {
			case *bytes.Buffer:
				req, err = http.NewRequest(r.Method, u.String(), r.Payload.(*bytes.Buffer))
			case *bytes.Reader:
				req, err = http.NewRequest(r.Method, u.String(), r.Payload.(*bytes.Reader))
			default:
				err = errors.New("Payload must be of type *bytes.Buffer or *bytes.Reader if RawPayload is set to true")
				return
			}
		} else if s.Header.Get("Content-Type") == "multipart/form-data" {
			var params = make(map[string]interface{})

			switch v := r.Payload.(type) {
			case map[string]string:
				for key, vv := range v {
					params[key] = vv
				}
			case map[string]interface{}:
				params = v
			}

			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			if v, ok := params["filepath"]; ok && v != nil {
				fp := v.(string)
				file, err := os.Open(fp)
				if err != nil {
					s.log("os.Open(fp) failed ", err)
					return nil, err
				}
				part, err := writer.CreateFormFile("file", filepath.Base(fp))
				if err != nil {
					s.log("writer.CreateFormFile failed", err)
					return nil, err
				}
				_, err = io.Copy(part, file)
				if err != nil {
					s.log("io.Copy failed", err)
					return nil, err
				}

				delete(params, "filepath")
			}

			for key, val := range params {
				switch vv := val.(type) {
				case string:
					_ = writer.WriteField(key, vv)
				default:
					// should be a custom struct, we expect marshall it as string
					b, err := json.Marshal(vv)
					if err != nil {
						return nil, err
					}
					_ = writer.WriteField(key, string(b))

				}
			}
			err = writer.Close()
			if err != nil {
				return nil, err
			}
			req, err = http.NewRequest(r.Method, u.String(), body)
			header.Set("Content-Type", writer.FormDataContentType())

		} else if s.Header.Get("Content-Type") == "text/plain" {
			b := []byte(r.Payload.(string))
			buf = bytes.NewBuffer(b)
			req, err = http.NewRequest(r.Method, u.String(), buf)
		} else if s.Header.Get("Content-Type") == "application/text" {
			b := []byte(r.Payload.(string))
			buf = bytes.NewBuffer(b)
			req, err = http.NewRequest(r.Method, u.String(), buf)
		} else {
			var b []byte
			if s.Header.Get("Content-Type") == "application/x-www-form-urlencoded" ||
				s.Header.Get("Content-Type") == "application/octet-stream" {
				b = []byte(r.Payload.(string))
			} else {
				b, err = json.Marshal(&r.Payload)
				if err != nil {
					s.log(err)
					return
				}
			}
			buf = bytes.NewBuffer(b)
			if buf != nil {
				req, err = http.NewRequest(r.Method, u.String(), buf)
			} else {
				req, err = http.NewRequest(r.Method, u.String(), nil)
			}
		}
		if err != nil {
			s.log(err)
			return
		}
		// println(req.Header.Get("Content-Type"))
		// Overwrite the content type to json since we're pushing the payload as json
		// header.Set("Content-Type", "application/x-www-form-urlencoded")
	} else { // no data to encode
		req, err = http.NewRequest(r.Method, u.String(), nil)
		if err != nil {
			s.log(err)
			return
		}

	}
	//
	// Merge Session and Request options
	//
	var userinfo *url.Userinfo
	if u.User != nil {
		userinfo = u.User
	}
	if s.Userinfo != nil {
		userinfo = s.Userinfo
	}
	// Prefer Request's user credentials
	if r.Userinfo != nil {
		userinfo = r.Userinfo
	}
	if r.Header != nil {
		for k, v := range *r.Header {
			header.Set(k, v[0]) // Is there always guarnateed to be at least one value for a header?
		}
	}
	// if header.Get("Accept") == "" {
	// 	header.Add("Accept", "application/json") // Default, can be overridden with Opts
	// }

	if host := header.Get("Host"); host != "" {
		req.Host = host
	}

	req.Header = header
	//
	// Set HTTP Basic authentication if userinfo is supplied
	//
	if userinfo != nil {
		pwd, _ := userinfo.Password()
		req.SetBasicAuth(userinfo.Username(), pwd)
		if u.Scheme != "https" {
			s.log("WARNING: Using HTTP Basic Auth in cleartext is insecure.")
		}
	}
	//
	// Execute the HTTP request
	//

	// Debug log request
	s.log("--------------------------------------------------------------------------------")
	s.log("REQUEST")
	s.log("--------------------------------------------------------------------------------")
	s.log("Method:", req.Method)
	s.log("URL:", req.URL)
	s.log("Header:", req.Header)
	s.log("Form:", req.Form)
	s.log("Payload:")
	s.printRequestBody(r, buf)

	r.timestamp = time.Now()
	var client *http.Client
	if s.Client != nil {
		client = s.Client
	} else {
		client = http.DefaultClient
	}

	// https://jira.qiniu.io/browse/KODO-2062
	if client.Transport == nil {
		client.Transport = http.DefaultTransport
	}
	if _, ok := client.Transport.(*http.Transport); ok {
		atomic.StorePointer((*unsafe.Pointer)((unsafe.Pointer)(&client.Transport.(*http.Transport).TLSClientConfig)), unsafe.Pointer(&tls.Config{
			MinVersion: tls.VersionTLS12,
		}))
	}

	resp, err := client.Do(req)
	if err != nil {
		s.log(err)
		return
	}
	defer resp.Body.Close()
	r.status = resp.StatusCode
	r.response = resp
	//
	// Unmarshal
	//
	r.body, err = io.ReadAll(resp.Body)
	if err != nil {
		s.log("WARN: Failed to read repsonse body, with exception ", err)
	}
	if string(r.body) != "" {
		if resp.StatusCode < 300 && r.Result != nil {
			err = json.Unmarshal(r.body, r.Result)
			if err != nil {
				s.log("==============================> ", err)
			}
		}
		if resp.StatusCode >= 400 && r.Error != nil {
			json.Unmarshal(r.body, r.Error) // Should we ignore unmarshall error?
		}
	}
	if r.CaptureResponseBody {
		r.ResponseBody = bytes.NewBuffer(r.body)
	}
	rsp := Response(*r)
	response = &rsp

	// Debug log response
	s.log("--------------------------------------------------------------------------------")
	s.log("RESPONSE")
	s.log("--------------------------------------------------------------------------------")
	s.log("Status: ", response.status)
	s.log("Header:")
	s.log(response.HTTPResponse().Header)
	s.log("Body:")
	s.printResponseBody(response)

	//if response.body == nil {
	//	s.log("Empty response body")
	//} else if len(response.body) <= 2048 {
	//	raw := json.RawMessage{}
	//	if json.Unmarshal(response.body, &raw) == nil {
	//		s.log(pretty(&raw))
	//	} else {
	//		s.log(pretty(response.RawText()))
	//	}
	//} else {
	//	s.log("Response body size was too large (>=2048)")
	//}

	return
}

// Get sends a GET request.
func (s *Session) Get(url string, p *url.Values, result, errMsg interface{}) (*Response, error) {
	r := Request{
		Method: "GET",
		URL:    url,
		Params: p,
		Result: result,
		Error:  errMsg,
	}
	return s.Send(&r)
}

// Options sends an OPTIONS request.
func (s *Session) Options(url string, result, errMsg interface{}) (*Response, error) {
	r := Request{
		Method: "OPTIONS",
		URL:    url,
		Result: result,
		Error:  errMsg,
	}
	return s.Send(&r)
}

// Head sends a HEAD request.
func (s *Session) Head(url string, result, errMsg interface{}) (*Response, error) {
	r := Request{
		Method: "HEAD",
		URL:    url,
		Result: result,
		Error:  errMsg,
	}
	return s.Send(&r)
}

// Post sends a POST request.
func (s *Session) Post(url string, payload, result, errMsg interface{}) (*Response, error) {
	r := Request{
		Method:  "POST",
		URL:     url,
		Payload: payload,
		Result:  result,
		Error:   errMsg,
	}

	//// disable HTTP2
	//s.SetTransport(&http.Transport{
	//	TLSNextProto: make(map[string]func(authority string, c *tls.Conn) http.RoundTripper),
	//})

	return s.Send(&r)
}

// PostAndCaptureResponseBody sends a POST request and capture response body.
func (s *Session) PostAndCaptureResponseBody(url string, payload, result, errMsg interface{}) (*Response, error) {
	r := Request{
		Method:              "POST",
		URL:                 url,
		Payload:             payload,
		Result:              result,
		CaptureResponseBody: true,
		Error:               errMsg,
	}
	return s.Send(&r)
}

// PostBody sends a POST request.
// payload should be *bytes.buffer type
func (s *Session) PostBody(url string, payload, result, errMsg interface{}) (*Response, error) {
	r := Request{
		Method:     "POST",
		URL:        url,
		Payload:    payload,
		RawPayload: true,
		Result:     result,
		Error:      errMsg,
	}
	return s.Send(&r)
}

// Put sends a PUT request.
func (s *Session) Put(url string, payload, result, errMsg interface{}) (*Response, error) {
	r := Request{
		Method:  "PUT",
		URL:     url,
		Payload: payload,
		Result:  result,
		Error:   errMsg,
	}
	return s.Send(&r)
}

// Patch sends a PATCH request.
func (s *Session) Patch(url string, payload, result, errMsg interface{}) (*Response, error) {
	r := Request{
		Method:  "PATCH",
		URL:     url,
		Payload: payload,
		Result:  result,
		Error:   errMsg,
	}
	return s.Send(&r)
}

// Delete sends a DELETE request.
func (s *Session) Delete(url string, payload, result, errMsg interface{}) (*Response, error) {
	r := Request{
		Method:  "DELETE",
		URL:     url,
		Payload: payload,
		Result:  result,
		Error:   errMsg,
	}
	return s.Send(&r)
}

// Debug method for logging
// Centralizing logging in one method
// avoids spreading conditionals everywhere
func (s *Session) log(args ...interface{}) {
	if s.Log {
		log.SetOutput(ginkgo.GinkgoWriter)
		log.Println(args...)
	}
}

func (s *Session) printResponseBody(response *Response) {
	raw := json.RawMessage{}
	if json.Unmarshal(response.body, &raw) == nil {
		s.limitPrint(string(pretty(&raw)))
	} else {
		s.limitPrint(string(pretty(response.RawText())))
	}
}

func (s *Session) printRequestBody(r *Request, body *bytes.Buffer) {
	if r.RawPayload && s.Log && body != nil {
		s.limitPrint(base64.StdEncoding.EncodeToString(body.Bytes()))
	} else {
		s.limitPrint(pretty(r.Payload))
	}
}

func (s *Session) limitPrint(bs string) {
	if len(bs) > 2048 {
		s.log("body size was more then 512 bytes, Not print")
	} else {
		s.log(bs)
	}
}
