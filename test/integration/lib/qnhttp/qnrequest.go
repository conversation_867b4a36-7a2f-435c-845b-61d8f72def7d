package qnhttp

import (
	"math/rand"
	"net/http"
	"os"
	"strconv"
	"time"
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

func randomSelect(hosts []string) string {
	i := rand.Intn(len(hosts))
	return hosts[i]
}

// New instances a new session with default
func New() *Session {
	s := Session{RetryTimeout: 2} // 超时重试 2 次
	s.Header = &http.Header{}
	s.Header.Add("User-Agent", "qiniu_qa")
	s.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	s.Log, _ = strconv.ParseBool(os.Getenv("DEBUG"))
	return &s
}

// NewWithMultiHosts instances a new session with multi hosts
func NewWithMultiHosts(hs []string) *Session {
	s := New()
	s.Hosts = hs
	return s
}

// Set sets header uses key and value
func (s *Session) Set(header, value string) *Session {
	s.Header.Set(header, value)
	return s
}

// Del deletes specific key of header
func (s *Session) Del(header string) *Session {
	s.Header.Del(header)
	return s
}

// SetClient sets a client to current session
func (s *Session) SetClient(client *http.Client) *Session {
	s.Client = client
	return s
}

// SetTransport sets a transport to current session
func (s *Session) SetTransport(transport *http.Transport) *Session {
	s.Client = &http.Client{Transport: transport}
	return s
}

// DisableKeepAlive disables or enables keep-alive property
func (s *Session) DisableKeepAlive(isDisable bool) *Session {
	tr := &http.Transport{DisableKeepAlives: isDisable}
	s.Client = &http.Client{Transport: tr}
	return s
}
