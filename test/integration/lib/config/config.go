package config

import (
	"os"
)

// Config defines service config to run test
type Config struct {
	QbpmHost string
	AccHost  string
	AccUser  string
	AccPwd   string
	TestEnv  string
}

// Keys defines values obtains from os env
var Keys = Config{
	QbpmHost: os.Getenv("TEST_QBPM_HOST"),
	AccHost:  os.Getenv("TEST_ACC_HOST"),
	AccUser:  os.<PERSON>env("TEST_ACC_USERNAME"),
	AccPwd:   os.Getenv("TEST_ACC_PASSWORD"),
	TestEnv:  os.Getenv("TEST_ENV"),
}
