package qbpm

import (
	"encoding/json"
	"fmt"

	qbpm "qiniu.io/qbpm/engine/models"

	"qiniu.io/qbpm/test/integration/lib/auth"
	"qiniu.io/qbpm/test/integration/lib/config"
	"qiniu.io/qbpm/test/integration/lib/qnhttp"
)

// defines service config info
var (
	QbpmHost string
	AccHost  string
	AccUser  string
	AccPwd   string
	s        *qnhttp.Session
)

// InitQbpm 初始化操作
func InitQbpm() {
	QbpmHost = config.Keys.QbpmHost
	AccHost = config.Keys.AccHost
	AccUser = config.Keys.AccUser
	AccPwd = config.Keys.AccPwd
	token := auth.GetAccToken(AccUser, AccPwd)

	s = qnhttp.New()
	s.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	s.Set("Content-Type", "application/json")
}

// Print 打印格式化的数据，方便写用例时调试
func Print(i interface{}) {
	fmt.Println("\n======= Print Start ==========")
	bytes, _ := json.MarshalIndent(i, "", "		")
	fmt.Printf("%+v", string(bytes))
	fmt.Println("\n======= Print End ==========")
}

// GetRuntimeExecutions 列举指定条件的运行时任务
func GetRuntimeExecutions() (runExecutions []qbpm.RunExecution, err error) {
	url := fmt.Sprintf("%s/api/runtime/processes", QbpmHost)
	resp, err := s.Get(url, nil, nil, nil)
	err = resp.Unmarshal(&runExecutions)
	return runExecutions, err
}
