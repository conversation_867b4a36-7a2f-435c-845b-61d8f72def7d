package qbpm

import (
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("QBPM Test", func() {
	It("Get runtime executions", func() {
		QbpmInitAt := time.Date(2018, 5, 1, 0, 0, 0, 0, time.Local)
		runExecutions, _ := GetRuntimeExecutions()
		// Expect(err).Should(BeNil())
		Expect(len(runExecutions)).Should(Equal(1))
		Expect(runExecutions[0].CreatedAt).Should(BeTemporally(">", QbpmInitAt))
		Expect(runExecutions[0].UpdatedAt).Should(BeTemporally(">", QbpmInitAt))
		Expect(runExecutions[0].Name).Should(Equal("总监审批"))
		Expect(runExecutions[0].StartByID).Should(Equal("admin"))
		Expect(runExecutions[0].Status).Should(Equal(int32(1)))
		// Expect(runExecutions[0].OriginalAssignee).Should(Equal("<EMAIL>"))
		// Expect(runExecutions[0].Assignee).Should(Equal("<EMAIL>"))
	})
})
