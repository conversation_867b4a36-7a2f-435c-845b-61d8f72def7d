// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// UpdateProcessDefinitionInput update process definition input
//
// swagger:model UpdateProcessDefinitionInput
type UpdateProcessDefinitionInput struct {

	// action
	Action string `json:"action,omitempty"`

	// include instances
	IncludeInstances bool `json:"include_instances,omitempty"`

	// type
	Type string `json:"type,omitempty"`
}

// Validate validates this update process definition input
func (m *UpdateProcessDefinitionInput) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this update process definition input based on context it is used
func (m *UpdateProcessDefinitionInput) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *UpdateProcessDefinitionInput) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *UpdateProcessDefinitionInput) UnmarshalBinary(b []byte) error {
	var res UpdateProcessDefinitionInput
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
