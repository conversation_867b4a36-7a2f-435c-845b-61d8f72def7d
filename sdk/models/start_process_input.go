// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// StartProcessInput start process input
//
// swagger:model StartProcessInput
type StartProcessInput struct {

	// external business code
	Excode string `json:"excode,omitempty"`

	// the process definition key
	Key string `json:"key,omitempty"`

	// external business code
	Operator string `json:"operator,omitempty"`

	// params to be used in process
	Params string `json:"params,omitempty"`

	// uid
	UID int64 `json:"uid,omitempty"`
}

// Validate validates this start process input
func (m *StartProcessInput) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this start process input based on context it is used
func (m *StartProcessInput) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *StartProcessInput) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *StartProcessInput) UnmarshalBinary(b []byte) error {
	var res StartProcessInput
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
