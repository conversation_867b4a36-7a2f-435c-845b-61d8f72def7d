// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// ActivityInstance activity instance
//
// swagger:model ActivityInstance
type ActivityInstance struct {

	// activity define key
	ActivityDefineKey string `json:"activity_define_key,omitempty"`

	// actor
	Actor string `json:"actor,omitempty"`

	// call process instance id
	CallProcessInstanceID int64 `json:"call_process_instance_id,omitempty"`

	// created at
	// Format: date-time
	CreatedAt strfmt.DateTime `json:"created_at,omitempty"`

	// due action
	DueAction int32 `json:"due_action,omitempty"`

	// due at
	// Format: date-time
	DueAt strfmt.DateTime `json:"due_at,omitempty"`

	// end at
	// Format: date-time
	EndAt strfmt.DateTime `json:"end_at,omitempty"`

	// execution id
	ExecutionID int64 `json:"execution_id,omitempty"`

	// fail action
	FailAction int32 `json:"fail_action,omitempty"`

	// id
	ID int64 `json:"id,omitempty"`

	// memo
	Memo string `json:"memo,omitempty"`

	// name
	Name string `json:"name,omitempty"`

	// original actor
	OriginalActor string `json:"original_actor,omitempty"`

	// params
	Params string `json:"params,omitempty"`

	// perform condition
	PerformCondition string `json:"perform_condition,omitempty"`

	// perform type
	PerformType int32 `json:"perform_type,omitempty"`

	// process instance id
	ProcessInstanceID int64 `json:"process_instance_id,omitempty"`

	// start at
	// Format: date-time
	StartAt strfmt.DateTime `json:"start_at,omitempty"`

	// status
	Status int32 `json:"status,omitempty"`

	// type
	Type string `json:"type,omitempty"`

	// updated at
	// Format: date-time
	UpdatedAt strfmt.DateTime `json:"updated_at,omitempty"`
}

// Validate validates this activity instance
func (m *ActivityInstance) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateCreatedAt(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateDueAt(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateEndAt(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateStartAt(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateUpdatedAt(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ActivityInstance) validateCreatedAt(formats strfmt.Registry) error {
	if swag.IsZero(m.CreatedAt) { // not required
		return nil
	}

	if err := validate.FormatOf("created_at", "body", "date-time", m.CreatedAt.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *ActivityInstance) validateDueAt(formats strfmt.Registry) error {
	if swag.IsZero(m.DueAt) { // not required
		return nil
	}

	if err := validate.FormatOf("due_at", "body", "date-time", m.DueAt.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *ActivityInstance) validateEndAt(formats strfmt.Registry) error {
	if swag.IsZero(m.EndAt) { // not required
		return nil
	}

	if err := validate.FormatOf("end_at", "body", "date-time", m.EndAt.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *ActivityInstance) validateStartAt(formats strfmt.Registry) error {
	if swag.IsZero(m.StartAt) { // not required
		return nil
	}

	if err := validate.FormatOf("start_at", "body", "date-time", m.StartAt.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *ActivityInstance) validateUpdatedAt(formats strfmt.Registry) error {
	if swag.IsZero(m.UpdatedAt) { // not required
		return nil
	}

	if err := validate.FormatOf("updated_at", "body", "date-time", m.UpdatedAt.String(), formats); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this activity instance based on context it is used
func (m *ActivityInstance) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ActivityInstance) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ActivityInstance) UnmarshalBinary(b []byte) error {
	var res ActivityInstance
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
