// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// RunExecution run execution
//
// swagger:model RunExecution
type RunExecution struct {

	// activity define key
	ActivityDefineKey string `json:"activity_define_key,omitempty"`

	// assignee
	Assignee string `json:"assignee,omitempty"`

	// created at
	// Format: date-time
	CreatedAt strfmt.DateTime `json:"created_at,omitempty"`

	// excode
	Excode string `json:"excode,omitempty"`

	// id
	ID int64 `json:"id,omitempty"`

	// memo
	Memo string `json:"memo,omitempty"`

	// name
	Name string `json:"name,omitempty"`

	// original assignee
	OriginalAssignee string `json:"original_assignee,omitempty"`

	// params
	Params string `json:"params,omitempty"`

	// process definition id
	ProcessDefinitionID int64 `json:"process_definition_id,omitempty"`

	// process instance id
	ProcessInstanceID int64 `json:"process_instance_id,omitempty"`

	// start at
	// Format: date-time
	StartAt strfmt.DateTime `json:"start_at,omitempty"`

	// start by id
	StartByID string `json:"start_by_id,omitempty"`

	// status
	Status int32 `json:"status,omitempty"`

	// uid
	UID int64 `json:"uid,omitempty"`

	// updated at
	// Format: date-time
	UpdatedAt strfmt.DateTime `json:"updated_at,omitempty"`
}

// Validate validates this run execution
func (m *RunExecution) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateCreatedAt(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateStartAt(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateUpdatedAt(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *RunExecution) validateCreatedAt(formats strfmt.Registry) error {
	if swag.IsZero(m.CreatedAt) { // not required
		return nil
	}

	if err := validate.FormatOf("created_at", "body", "date-time", m.CreatedAt.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *RunExecution) validateStartAt(formats strfmt.Registry) error {
	if swag.IsZero(m.StartAt) { // not required
		return nil
	}

	if err := validate.FormatOf("start_at", "body", "date-time", m.StartAt.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *RunExecution) validateUpdatedAt(formats strfmt.Registry) error {
	if swag.IsZero(m.UpdatedAt) { // not required
		return nil
	}

	if err := validate.FormatOf("updated_at", "body", "date-time", m.UpdatedAt.String(), formats); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this run execution based on context it is used
func (m *RunExecution) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *RunExecution) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *RunExecution) UnmarshalBinary(b []byte) error {
	var res RunExecution
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
