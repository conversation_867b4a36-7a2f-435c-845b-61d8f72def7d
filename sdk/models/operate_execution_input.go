// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// OperateExecutionInput operate execution input
// Example: {"memo":"memo","operator":"operator","params":"params"}
//
// swagger:model OperateExecutionInput
type OperateExecutionInput struct {

	// memo
	Memo string `json:"memo,omitempty"`

	// operator
	Operator string `json:"operator,omitempty"`

	// params
	Params string `json:"params,omitempty"`
}

// Validate validates this operate execution input
func (m *OperateExecutionInput) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this operate execution input based on context it is used
func (m *OperateExecutionInput) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *OperateExecutionInput) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *OperateExecutionInput) UnmarshalBinary(b []byte) error {
	var res OperateExecutionInput
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
