// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// AssigneeInput assignee input
//
// swagger:model AssigneeInput
type AssigneeInput struct {

	// assignee
	Assignee string `json:"assignee,omitempty"`
}

// Validate validates this assignee input
func (m *AssigneeInput) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this assignee input based on context it is used
func (m *AssigneeInput) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *AssigneeInput) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *AssigneeInput) UnmarshalBinary(b []byte) error {
	var res AssigneeInput
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
