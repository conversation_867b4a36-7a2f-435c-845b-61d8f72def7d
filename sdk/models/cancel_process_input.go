// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// CancelProcessInput cancel process input
//
// swagger:model CancelProcessInput
type CancelProcessInput struct {

	// memo
	Memo string `json:"memo,omitempty"`

	// operator
	Operator string `json:"operator,omitempty"`
}

// Validate validates this cancel process input
func (m *CancelProcessInput) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this cancel process input based on context it is used
func (m *CancelProcessInput) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *CancelProcessInput) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *CancelProcessInput) UnmarshalBinary(b []byte) error {
	var res CancelProcessInput
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
