// Code generated by go-swagger; DO NOT EDIT.

package history

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// GetProcessReader is a Reader for the GetProcess structure.
type GetProcessReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetProcessReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetProcessOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewGetProcessBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewGetProcessUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewGetProcessNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewGetProcessDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewGetProcessOK creates a GetProcessOK with default headers values
func NewGetProcessOK() *GetProcessOK {
	return &GetProcessOK{}
}

/*
	GetProcessOK describes a response with status code 200, with default header values.

OK
*/
type GetProcessOK struct {
	Payload *models.ProcessInstance
}

func (o *GetProcessOK) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}][%d] getProcessOK  %+v", 200, o.Payload)
}
func (o *GetProcessOK) GetPayload() *models.ProcessInstance {
	return o.Payload
}

func (o *GetProcessOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ProcessInstance)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetProcessBadRequest creates a GetProcessBadRequest with default headers values
func NewGetProcessBadRequest() *GetProcessBadRequest {
	return &GetProcessBadRequest{}
}

/*
	GetProcessBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type GetProcessBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *GetProcessBadRequest) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}][%d] getProcessBadRequest  %+v", 400, o.Payload)
}
func (o *GetProcessBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *GetProcessBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetProcessUnauthorized creates a GetProcessUnauthorized with default headers values
func NewGetProcessUnauthorized() *GetProcessUnauthorized {
	return &GetProcessUnauthorized{}
}

/*
	GetProcessUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type GetProcessUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *GetProcessUnauthorized) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}][%d] getProcessUnauthorized  %+v", 401, o.Payload)
}
func (o *GetProcessUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *GetProcessUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetProcessNotFound creates a GetProcessNotFound with default headers values
func NewGetProcessNotFound() *GetProcessNotFound {
	return &GetProcessNotFound{}
}

/*
	GetProcessNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type GetProcessNotFound struct {
}

func (o *GetProcessNotFound) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}][%d] getProcessNotFound ", 404)
}

func (o *GetProcessNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewGetProcessDefault creates a GetProcessDefault with default headers values
func NewGetProcessDefault(code int) *GetProcessDefault {
	return &GetProcessDefault{
		_statusCode: code,
	}
}

/*
	GetProcessDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type GetProcessDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the get process default response
func (o *GetProcessDefault) Code() int {
	return o._statusCode
}

func (o *GetProcessDefault) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}][%d] getProcess default  %+v", o._statusCode, o.Payload)
}
func (o *GetProcessDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *GetProcessDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
