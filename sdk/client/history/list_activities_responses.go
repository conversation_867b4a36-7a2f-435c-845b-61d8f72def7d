// Code generated by go-swagger; DO NOT EDIT.

package history

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// ListActivitiesReader is a Reader for the ListActivities structure.
type ListActivitiesReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *ListActivitiesReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewListActivitiesOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewListActivitiesBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewListActivitiesUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewListActivitiesNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewListActivitiesDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewListActivitiesOK creates a ListActivitiesOK with default headers values
func NewListActivitiesOK() *ListActivitiesOK {
	return &ListActivitiesOK{}
}

/*
	ListActivitiesOK describes a response with status code 200, with default header values.

OK
*/
type ListActivitiesOK struct {
	Payload []*models.ActivityInstance
}

func (o *ListActivitiesOK) Error() string {
	return fmt.Sprintf("[GET /api/history/activities][%d] listActivitiesOK  %+v", 200, o.Payload)
}
func (o *ListActivitiesOK) GetPayload() []*models.ActivityInstance {
	return o.Payload
}

func (o *ListActivitiesOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListActivitiesBadRequest creates a ListActivitiesBadRequest with default headers values
func NewListActivitiesBadRequest() *ListActivitiesBadRequest {
	return &ListActivitiesBadRequest{}
}

/*
	ListActivitiesBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type ListActivitiesBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *ListActivitiesBadRequest) Error() string {
	return fmt.Sprintf("[GET /api/history/activities][%d] listActivitiesBadRequest  %+v", 400, o.Payload)
}
func (o *ListActivitiesBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListActivitiesBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListActivitiesUnauthorized creates a ListActivitiesUnauthorized with default headers values
func NewListActivitiesUnauthorized() *ListActivitiesUnauthorized {
	return &ListActivitiesUnauthorized{}
}

/*
	ListActivitiesUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type ListActivitiesUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *ListActivitiesUnauthorized) Error() string {
	return fmt.Sprintf("[GET /api/history/activities][%d] listActivitiesUnauthorized  %+v", 401, o.Payload)
}
func (o *ListActivitiesUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListActivitiesUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListActivitiesNotFound creates a ListActivitiesNotFound with default headers values
func NewListActivitiesNotFound() *ListActivitiesNotFound {
	return &ListActivitiesNotFound{}
}

/*
	ListActivitiesNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type ListActivitiesNotFound struct {
}

func (o *ListActivitiesNotFound) Error() string {
	return fmt.Sprintf("[GET /api/history/activities][%d] listActivitiesNotFound ", 404)
}

func (o *ListActivitiesNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewListActivitiesDefault creates a ListActivitiesDefault with default headers values
func NewListActivitiesDefault(code int) *ListActivitiesDefault {
	return &ListActivitiesDefault{
		_statusCode: code,
	}
}

/*
	ListActivitiesDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type ListActivitiesDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the list activities default response
func (o *ListActivitiesDefault) Code() int {
	return o._statusCode
}

func (o *ListActivitiesDefault) Error() string {
	return fmt.Sprintf("[GET /api/history/activities][%d] listActivities default  %+v", o._statusCode, o.Payload)
}
func (o *ListActivitiesDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListActivitiesDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
