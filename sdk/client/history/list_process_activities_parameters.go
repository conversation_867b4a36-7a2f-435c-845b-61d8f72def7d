// Code generated by go-swagger; DO NOT EDIT.

package history

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewListProcessActivitiesParams creates a new ListProcessActivitiesParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListProcessActivitiesParams() *ListProcessActivitiesParams {
	return &ListProcessActivitiesParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListProcessActivitiesParamsWithTimeout creates a new ListProcessActivitiesParams object
// with the ability to set a timeout on a request.
func NewListProcessActivitiesParamsWithTimeout(timeout time.Duration) *ListProcessActivitiesParams {
	return &ListProcessActivitiesParams{
		timeout: timeout,
	}
}

// NewListProcessActivitiesParamsWithContext creates a new ListProcessActivitiesParams object
// with the ability to set a context for a request.
func NewListProcessActivitiesParamsWithContext(ctx context.Context) *ListProcessActivitiesParams {
	return &ListProcessActivitiesParams{
		Context: ctx,
	}
}

// NewListProcessActivitiesParamsWithHTTPClient creates a new ListProcessActivitiesParams object
// with the ability to set a custom HTTPClient for a request.
func NewListProcessActivitiesParamsWithHTTPClient(client *http.Client) *ListProcessActivitiesParams {
	return &ListProcessActivitiesParams{
		HTTPClient: client,
	}
}

/*
ListProcessActivitiesParams contains all the parameters to send to the API endpoint

	for the list process activities operation.

	Typically these are written to a http.Request.
*/
type ListProcessActivitiesParams struct {

	/* ID.

	   the process id

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list process activities params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListProcessActivitiesParams) WithDefaults() *ListProcessActivitiesParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list process activities params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListProcessActivitiesParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the list process activities params
func (o *ListProcessActivitiesParams) WithTimeout(timeout time.Duration) *ListProcessActivitiesParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list process activities params
func (o *ListProcessActivitiesParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list process activities params
func (o *ListProcessActivitiesParams) WithContext(ctx context.Context) *ListProcessActivitiesParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list process activities params
func (o *ListProcessActivitiesParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list process activities params
func (o *ListProcessActivitiesParams) WithHTTPClient(client *http.Client) *ListProcessActivitiesParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list process activities params
func (o *ListProcessActivitiesParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithID adds the id to the list process activities params
func (o *ListProcessActivitiesParams) WithID(id int64) *ListProcessActivitiesParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the list process activities params
func (o *ListProcessActivitiesParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *ListProcessActivitiesParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
