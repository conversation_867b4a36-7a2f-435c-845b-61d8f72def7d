// Code generated by go-swagger; DO NOT EDIT.

package history

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewListProcessesParams creates a new ListProcessesParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListProcessesParams() *ListProcessesParams {
	return &ListProcessesParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListProcessesParamsWithTimeout creates a new ListProcessesParams object
// with the ability to set a timeout on a request.
func NewListProcessesParamsWithTimeout(timeout time.Duration) *ListProcessesParams {
	return &ListProcessesParams{
		timeout: timeout,
	}
}

// NewListProcessesParamsWithContext creates a new ListProcessesParams object
// with the ability to set a context for a request.
func NewListProcessesParamsWithContext(ctx context.Context) *ListProcessesParams {
	return &ListProcessesParams{
		Context: ctx,
	}
}

// NewListProcessesParamsWithHTTPClient creates a new ListProcessesParams object
// with the ability to set a custom HTTPClient for a request.
func NewListProcessesParamsWithHTTPClient(client *http.Client) *ListProcessesParams {
	return &ListProcessesParams{
		HTTPClient: client,
	}
}

/*
ListProcessesParams contains all the parameters to send to the API endpoint

	for the list processes operation.

	Typically these are written to a http.Request.
*/
type ListProcessesParams struct {

	/* Assignees.

	   the executions assignees
	*/
	Assignees []string

	/* Excode.

	   external business code
	*/
	Excode *string

	/* Page.

	   page index for queries, 0 is default

	   Format: int32
	*/
	Page *int32

	/* PageSize.

	   page size for queries, 200 is default, max value 1000 is limited

	   Format: int32
	*/
	PageSize *int32

	/* ProcessDefinitionID.

	   process definition id

	   Format: int64
	*/
	ProcessDefinitionID *int64

	/* ProcessDefinitionKey.

	   process definition key
	*/
	ProcessDefinitionKey *string

	/* ProcessInstanceID.

	   process instance id

	   Format: int64
	*/
	ProcessInstanceID *int64

	/* Starters.

	   the executions starters
	*/
	Starters []string

	/* Status.

	   the process instance status

	   Format: int32
	*/
	Status *int32

	/* UID.

	   uid

	   Format: int64
	*/
	UID *int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list processes params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListProcessesParams) WithDefaults() *ListProcessesParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list processes params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListProcessesParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the list processes params
func (o *ListProcessesParams) WithTimeout(timeout time.Duration) *ListProcessesParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list processes params
func (o *ListProcessesParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list processes params
func (o *ListProcessesParams) WithContext(ctx context.Context) *ListProcessesParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list processes params
func (o *ListProcessesParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list processes params
func (o *ListProcessesParams) WithHTTPClient(client *http.Client) *ListProcessesParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list processes params
func (o *ListProcessesParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithAssignees adds the assignees to the list processes params
func (o *ListProcessesParams) WithAssignees(assignees []string) *ListProcessesParams {
	o.SetAssignees(assignees)
	return o
}

// SetAssignees adds the assignees to the list processes params
func (o *ListProcessesParams) SetAssignees(assignees []string) {
	o.Assignees = assignees
}

// WithExcode adds the excode to the list processes params
func (o *ListProcessesParams) WithExcode(excode *string) *ListProcessesParams {
	o.SetExcode(excode)
	return o
}

// SetExcode adds the excode to the list processes params
func (o *ListProcessesParams) SetExcode(excode *string) {
	o.Excode = excode
}

// WithPage adds the page to the list processes params
func (o *ListProcessesParams) WithPage(page *int32) *ListProcessesParams {
	o.SetPage(page)
	return o
}

// SetPage adds the page to the list processes params
func (o *ListProcessesParams) SetPage(page *int32) {
	o.Page = page
}

// WithPageSize adds the pageSize to the list processes params
func (o *ListProcessesParams) WithPageSize(pageSize *int32) *ListProcessesParams {
	o.SetPageSize(pageSize)
	return o
}

// SetPageSize adds the pageSize to the list processes params
func (o *ListProcessesParams) SetPageSize(pageSize *int32) {
	o.PageSize = pageSize
}

// WithProcessDefinitionID adds the processDefinitionID to the list processes params
func (o *ListProcessesParams) WithProcessDefinitionID(processDefinitionID *int64) *ListProcessesParams {
	o.SetProcessDefinitionID(processDefinitionID)
	return o
}

// SetProcessDefinitionID adds the processDefinitionId to the list processes params
func (o *ListProcessesParams) SetProcessDefinitionID(processDefinitionID *int64) {
	o.ProcessDefinitionID = processDefinitionID
}

// WithProcessDefinitionKey adds the processDefinitionKey to the list processes params
func (o *ListProcessesParams) WithProcessDefinitionKey(processDefinitionKey *string) *ListProcessesParams {
	o.SetProcessDefinitionKey(processDefinitionKey)
	return o
}

// SetProcessDefinitionKey adds the processDefinitionKey to the list processes params
func (o *ListProcessesParams) SetProcessDefinitionKey(processDefinitionKey *string) {
	o.ProcessDefinitionKey = processDefinitionKey
}

// WithProcessInstanceID adds the processInstanceID to the list processes params
func (o *ListProcessesParams) WithProcessInstanceID(processInstanceID *int64) *ListProcessesParams {
	o.SetProcessInstanceID(processInstanceID)
	return o
}

// SetProcessInstanceID adds the processInstanceId to the list processes params
func (o *ListProcessesParams) SetProcessInstanceID(processInstanceID *int64) {
	o.ProcessInstanceID = processInstanceID
}

// WithStarters adds the starters to the list processes params
func (o *ListProcessesParams) WithStarters(starters []string) *ListProcessesParams {
	o.SetStarters(starters)
	return o
}

// SetStarters adds the starters to the list processes params
func (o *ListProcessesParams) SetStarters(starters []string) {
	o.Starters = starters
}

// WithStatus adds the status to the list processes params
func (o *ListProcessesParams) WithStatus(status *int32) *ListProcessesParams {
	o.SetStatus(status)
	return o
}

// SetStatus adds the status to the list processes params
func (o *ListProcessesParams) SetStatus(status *int32) {
	o.Status = status
}

// WithUID adds the uid to the list processes params
func (o *ListProcessesParams) WithUID(uid *int64) *ListProcessesParams {
	o.SetUID(uid)
	return o
}

// SetUID adds the uid to the list processes params
func (o *ListProcessesParams) SetUID(uid *int64) {
	o.UID = uid
}

// WriteToRequest writes these params to a swagger request
func (o *ListProcessesParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.Assignees != nil {

		// binding items for assignees
		joinedAssignees := o.bindParamAssignees(reg)

		// query array param assignees
		if err := r.SetQueryParam("assignees", joinedAssignees...); err != nil {
			return err
		}
	}

	if o.Excode != nil {

		// query param excode
		var qrExcode string

		if o.Excode != nil {
			qrExcode = *o.Excode
		}
		qExcode := qrExcode
		if qExcode != "" {

			if err := r.SetQueryParam("excode", qExcode); err != nil {
				return err
			}
		}
	}

	if o.Page != nil {

		// query param page
		var qrPage int32

		if o.Page != nil {
			qrPage = *o.Page
		}
		qPage := swag.FormatInt32(qrPage)
		if qPage != "" {

			if err := r.SetQueryParam("page", qPage); err != nil {
				return err
			}
		}
	}

	if o.PageSize != nil {

		// query param page_size
		var qrPageSize int32

		if o.PageSize != nil {
			qrPageSize = *o.PageSize
		}
		qPageSize := swag.FormatInt32(qrPageSize)
		if qPageSize != "" {

			if err := r.SetQueryParam("page_size", qPageSize); err != nil {
				return err
			}
		}
	}

	if o.ProcessDefinitionID != nil {

		// query param process_definition_id
		var qrProcessDefinitionID int64

		if o.ProcessDefinitionID != nil {
			qrProcessDefinitionID = *o.ProcessDefinitionID
		}
		qProcessDefinitionID := swag.FormatInt64(qrProcessDefinitionID)
		if qProcessDefinitionID != "" {

			if err := r.SetQueryParam("process_definition_id", qProcessDefinitionID); err != nil {
				return err
			}
		}
	}

	if o.ProcessDefinitionKey != nil {

		// query param process_definition_key
		var qrProcessDefinitionKey string

		if o.ProcessDefinitionKey != nil {
			qrProcessDefinitionKey = *o.ProcessDefinitionKey
		}
		qProcessDefinitionKey := qrProcessDefinitionKey
		if qProcessDefinitionKey != "" {

			if err := r.SetQueryParam("process_definition_key", qProcessDefinitionKey); err != nil {
				return err
			}
		}
	}

	if o.ProcessInstanceID != nil {

		// query param process_instance_id
		var qrProcessInstanceID int64

		if o.ProcessInstanceID != nil {
			qrProcessInstanceID = *o.ProcessInstanceID
		}
		qProcessInstanceID := swag.FormatInt64(qrProcessInstanceID)
		if qProcessInstanceID != "" {

			if err := r.SetQueryParam("process_instance_id", qProcessInstanceID); err != nil {
				return err
			}
		}
	}

	if o.Starters != nil {

		// binding items for starters
		joinedStarters := o.bindParamStarters(reg)

		// query array param starters
		if err := r.SetQueryParam("starters", joinedStarters...); err != nil {
			return err
		}
	}

	if o.Status != nil {

		// query param status
		var qrStatus int32

		if o.Status != nil {
			qrStatus = *o.Status
		}
		qStatus := swag.FormatInt32(qrStatus)
		if qStatus != "" {

			if err := r.SetQueryParam("status", qStatus); err != nil {
				return err
			}
		}
	}

	if o.UID != nil {

		// query param uid
		var qrUID int64

		if o.UID != nil {
			qrUID = *o.UID
		}
		qUID := swag.FormatInt64(qrUID)
		if qUID != "" {

			if err := r.SetQueryParam("uid", qUID); err != nil {
				return err
			}
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindParamListProcesses binds the parameter assignees
func (o *ListProcessesParams) bindParamAssignees(formats strfmt.Registry) []string {
	assigneesIR := o.Assignees

	var assigneesIC []string
	for _, assigneesIIR := range assigneesIR { // explode []string

		assigneesIIV := assigneesIIR // string as string
		assigneesIC = append(assigneesIC, assigneesIIV)
	}

	// items.CollectionFormat: "multi"
	assigneesIS := swag.JoinByFormat(assigneesIC, "multi")

	return assigneesIS
}

// bindParamListProcesses binds the parameter starters
func (o *ListProcessesParams) bindParamStarters(formats strfmt.Registry) []string {
	startersIR := o.Starters

	var startersIC []string
	for _, startersIIR := range startersIR { // explode []string

		startersIIV := startersIIR // string as string
		startersIC = append(startersIC, startersIIV)
	}

	// items.CollectionFormat: "multi"
	startersIS := swag.JoinByFormat(startersIC, "multi")

	return startersIS
}
