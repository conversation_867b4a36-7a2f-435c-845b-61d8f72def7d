// Code generated by go-swagger; DO NOT EDIT.

package history

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetProcessParams creates a new GetProcessParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetProcessParams() *GetProcessParams {
	return &GetProcessParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetProcessParamsWithTimeout creates a new GetProcessParams object
// with the ability to set a timeout on a request.
func NewGetProcessParamsWithTimeout(timeout time.Duration) *GetProcessParams {
	return &GetProcessParams{
		timeout: timeout,
	}
}

// NewGetProcessParamsWithContext creates a new GetProcessParams object
// with the ability to set a context for a request.
func NewGetProcessParamsWithContext(ctx context.Context) *GetProcessParams {
	return &GetProcessParams{
		Context: ctx,
	}
}

// NewGetProcessParamsWithHTTPClient creates a new GetProcessParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetProcessParamsWithHTTPClient(client *http.Client) *GetProcessParams {
	return &GetProcessParams{
		HTTPClient: client,
	}
}

/*
GetProcessParams contains all the parameters to send to the API endpoint

	for the get process operation.

	Typically these are written to a http.Request.
*/
type GetProcessParams struct {

	/* ID.

	   the process id

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get process params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetProcessParams) WithDefaults() *GetProcessParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get process params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetProcessParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get process params
func (o *GetProcessParams) WithTimeout(timeout time.Duration) *GetProcessParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get process params
func (o *GetProcessParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get process params
func (o *GetProcessParams) WithContext(ctx context.Context) *GetProcessParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get process params
func (o *GetProcessParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get process params
func (o *GetProcessParams) WithHTTPClient(client *http.Client) *GetProcessParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get process params
func (o *GetProcessParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithID adds the id to the get process params
func (o *GetProcessParams) WithID(id int64) *GetProcessParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the get process params
func (o *GetProcessParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *GetProcessParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
