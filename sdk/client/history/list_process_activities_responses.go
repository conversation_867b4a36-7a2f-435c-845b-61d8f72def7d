// Code generated by go-swagger; DO NOT EDIT.

package history

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// ListProcessActivitiesReader is a Reader for the ListProcessActivities structure.
type ListProcessActivitiesReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *ListProcessActivitiesReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewListProcessActivitiesOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewListProcessActivitiesBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewListProcessActivitiesUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewListProcessActivitiesNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewListProcessActivitiesDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewListProcessActivitiesOK creates a ListProcessActivitiesOK with default headers values
func NewListProcessActivitiesOK() *ListProcessActivitiesOK {
	return &ListProcessActivitiesOK{}
}

/*
	ListProcessActivitiesOK describes a response with status code 200, with default header values.

OK
*/
type ListProcessActivitiesOK struct {
	Payload []*models.ActivityInstance
}

func (o *ListProcessActivitiesOK) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}/activities][%d] listProcessActivitiesOK  %+v", 200, o.Payload)
}
func (o *ListProcessActivitiesOK) GetPayload() []*models.ActivityInstance {
	return o.Payload
}

func (o *ListProcessActivitiesOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListProcessActivitiesBadRequest creates a ListProcessActivitiesBadRequest with default headers values
func NewListProcessActivitiesBadRequest() *ListProcessActivitiesBadRequest {
	return &ListProcessActivitiesBadRequest{}
}

/*
	ListProcessActivitiesBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type ListProcessActivitiesBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *ListProcessActivitiesBadRequest) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}/activities][%d] listProcessActivitiesBadRequest  %+v", 400, o.Payload)
}
func (o *ListProcessActivitiesBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListProcessActivitiesBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListProcessActivitiesUnauthorized creates a ListProcessActivitiesUnauthorized with default headers values
func NewListProcessActivitiesUnauthorized() *ListProcessActivitiesUnauthorized {
	return &ListProcessActivitiesUnauthorized{}
}

/*
	ListProcessActivitiesUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type ListProcessActivitiesUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *ListProcessActivitiesUnauthorized) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}/activities][%d] listProcessActivitiesUnauthorized  %+v", 401, o.Payload)
}
func (o *ListProcessActivitiesUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListProcessActivitiesUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListProcessActivitiesNotFound creates a ListProcessActivitiesNotFound with default headers values
func NewListProcessActivitiesNotFound() *ListProcessActivitiesNotFound {
	return &ListProcessActivitiesNotFound{}
}

/*
	ListProcessActivitiesNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type ListProcessActivitiesNotFound struct {
}

func (o *ListProcessActivitiesNotFound) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}/activities][%d] listProcessActivitiesNotFound ", 404)
}

func (o *ListProcessActivitiesNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewListProcessActivitiesDefault creates a ListProcessActivitiesDefault with default headers values
func NewListProcessActivitiesDefault(code int) *ListProcessActivitiesDefault {
	return &ListProcessActivitiesDefault{
		_statusCode: code,
	}
}

/*
	ListProcessActivitiesDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type ListProcessActivitiesDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the list process activities default response
func (o *ListProcessActivitiesDefault) Code() int {
	return o._statusCode
}

func (o *ListProcessActivitiesDefault) Error() string {
	return fmt.Sprintf("[GET /api/history/processes/{id}/activities][%d] listProcessActivities default  %+v", o._statusCode, o.Payload)
}
func (o *ListProcessActivitiesDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListProcessActivitiesDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
