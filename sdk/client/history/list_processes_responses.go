// Code generated by go-swagger; DO NOT EDIT.

package history

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// ListProcessesReader is a Reader for the ListProcesses structure.
type ListProcessesReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *ListProcessesReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewListProcessesOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewListProcessesBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewListProcessesUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewListProcessesNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewListProcessesDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewListProcessesOK creates a ListProcessesOK with default headers values
func NewListProcessesOK() *ListProcessesOK {
	return &ListProcessesOK{}
}

/*
	ListProcessesOK describes a response with status code 200, with default header values.

OK
*/
type ListProcessesOK struct {
	Payload []*models.ProcessInstance
}

func (o *ListProcessesOK) Error() string {
	return fmt.Sprintf("[GET /api/history/processes][%d] listProcessesOK  %+v", 200, o.Payload)
}
func (o *ListProcessesOK) GetPayload() []*models.ProcessInstance {
	return o.Payload
}

func (o *ListProcessesOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListProcessesBadRequest creates a ListProcessesBadRequest with default headers values
func NewListProcessesBadRequest() *ListProcessesBadRequest {
	return &ListProcessesBadRequest{}
}

/*
	ListProcessesBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type ListProcessesBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *ListProcessesBadRequest) Error() string {
	return fmt.Sprintf("[GET /api/history/processes][%d] listProcessesBadRequest  %+v", 400, o.Payload)
}
func (o *ListProcessesBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListProcessesBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListProcessesUnauthorized creates a ListProcessesUnauthorized with default headers values
func NewListProcessesUnauthorized() *ListProcessesUnauthorized {
	return &ListProcessesUnauthorized{}
}

/*
	ListProcessesUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type ListProcessesUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *ListProcessesUnauthorized) Error() string {
	return fmt.Sprintf("[GET /api/history/processes][%d] listProcessesUnauthorized  %+v", 401, o.Payload)
}
func (o *ListProcessesUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListProcessesUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListProcessesNotFound creates a ListProcessesNotFound with default headers values
func NewListProcessesNotFound() *ListProcessesNotFound {
	return &ListProcessesNotFound{}
}

/*
	ListProcessesNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type ListProcessesNotFound struct {
}

func (o *ListProcessesNotFound) Error() string {
	return fmt.Sprintf("[GET /api/history/processes][%d] listProcessesNotFound ", 404)
}

func (o *ListProcessesNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewListProcessesDefault creates a ListProcessesDefault with default headers values
func NewListProcessesDefault(code int) *ListProcessesDefault {
	return &ListProcessesDefault{
		_statusCode: code,
	}
}

/*
	ListProcessesDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type ListProcessesDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the list processes default response
func (o *ListProcessesDefault) Code() int {
	return o._statusCode
}

func (o *ListProcessesDefault) Error() string {
	return fmt.Sprintf("[GET /api/history/processes][%d] listProcesses default  %+v", o._statusCode, o.Payload)
}
func (o *ListProcessesDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListProcessesDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
