// Code generated by go-swagger; DO NOT EDIT.

package history

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new history API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for history API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

// ClientService is the interface for Client methods
type ClientService interface {
	GetProcess(params *GetProcessParams, opts ...ClientOption) (*GetProcessOK, error)

	ListActivities(params *ListActivitiesParams, opts ...ClientOption) (*ListActivitiesOK, error)

	ListProcessActivities(params *ListProcessActivitiesParams, opts ...ClientOption) (*ListProcessActivitiesOK, error)

	ListProcesses(params *ListProcessesParams, opts ...ClientOption) (*ListProcessesOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
GetProcess gets a process
*/
func (a *Client) GetProcess(params *GetProcessParams, opts ...ClientOption) (*GetProcessOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetProcessParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "getProcess",
		Method:             "GET",
		PathPattern:        "/api/history/processes/{id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetProcessReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetProcessOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*GetProcessDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
ListActivities lists activities
*/
func (a *Client) ListActivities(params *ListActivitiesParams, opts ...ClientOption) (*ListActivitiesOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListActivitiesParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "listActivities",
		Method:             "GET",
		PathPattern:        "/api/history/activities",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListActivitiesReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListActivitiesOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*ListActivitiesDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
ListProcessActivities lists process activities
*/
func (a *Client) ListProcessActivities(params *ListProcessActivitiesParams, opts ...ClientOption) (*ListProcessActivitiesOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListProcessActivitiesParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "listProcessActivities",
		Method:             "GET",
		PathPattern:        "/api/history/processes/{id}/activities",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListProcessActivitiesReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListProcessActivitiesOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*ListProcessActivitiesDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
ListProcesses lists processes
*/
func (a *Client) ListProcesses(params *ListProcessesParams, opts ...ClientOption) (*ListProcessesOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListProcessesParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "listProcesses",
		Method:             "GET",
		PathPattern:        "/api/history/processes",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"multipart/form-data"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListProcessesReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListProcessesOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*ListProcessesDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
