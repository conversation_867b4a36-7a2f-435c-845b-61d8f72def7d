// Code generated by go-swagger; DO NOT EDIT.

package history

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewListActivitiesParams creates a new ListActivitiesParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListActivitiesParams() *ListActivitiesParams {
	return &ListActivitiesParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListActivitiesParamsWithTimeout creates a new ListActivitiesParams object
// with the ability to set a timeout on a request.
func NewListActivitiesParamsWithTimeout(timeout time.Duration) *ListActivitiesParams {
	return &ListActivitiesParams{
		timeout: timeout,
	}
}

// NewListActivitiesParamsWithContext creates a new ListActivitiesParams object
// with the ability to set a context for a request.
func NewListActivitiesParamsWithContext(ctx context.Context) *ListActivitiesParams {
	return &ListActivitiesParams{
		Context: ctx,
	}
}

// NewListActivitiesParamsWithHTTPClient creates a new ListActivitiesParams object
// with the ability to set a custom HTTPClient for a request.
func NewListActivitiesParamsWithHTTPClient(client *http.Client) *ListActivitiesParams {
	return &ListActivitiesParams{
		HTTPClient: client,
	}
}

/*
ListActivitiesParams contains all the parameters to send to the API endpoint

	for the list activities operation.

	Typically these are written to a http.Request.
*/
type ListActivitiesParams struct {

	/* Excode.

	   external business code
	*/
	Excode *string

	/* Page.

	   page index for queries, 0 is default

	   Format: int32
	*/
	Page *int32

	/* PageSize.

	   page size for queries, 200 is default, max value 1000 is limited

	   Format: int32
	*/
	PageSize *int32

	/* ProcessInstanceID.

	   process instance id

	   Format: int64
	*/
	ProcessInstanceID *int64

	/* Status.

	   process activity status

	   Format: int32
	*/
	Status *int32

	/* Types.

	   process activity types
	*/
	Types []string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list activities params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListActivitiesParams) WithDefaults() *ListActivitiesParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list activities params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListActivitiesParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the list activities params
func (o *ListActivitiesParams) WithTimeout(timeout time.Duration) *ListActivitiesParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list activities params
func (o *ListActivitiesParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list activities params
func (o *ListActivitiesParams) WithContext(ctx context.Context) *ListActivitiesParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list activities params
func (o *ListActivitiesParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list activities params
func (o *ListActivitiesParams) WithHTTPClient(client *http.Client) *ListActivitiesParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list activities params
func (o *ListActivitiesParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithExcode adds the excode to the list activities params
func (o *ListActivitiesParams) WithExcode(excode *string) *ListActivitiesParams {
	o.SetExcode(excode)
	return o
}

// SetExcode adds the excode to the list activities params
func (o *ListActivitiesParams) SetExcode(excode *string) {
	o.Excode = excode
}

// WithPage adds the page to the list activities params
func (o *ListActivitiesParams) WithPage(page *int32) *ListActivitiesParams {
	o.SetPage(page)
	return o
}

// SetPage adds the page to the list activities params
func (o *ListActivitiesParams) SetPage(page *int32) {
	o.Page = page
}

// WithPageSize adds the pageSize to the list activities params
func (o *ListActivitiesParams) WithPageSize(pageSize *int32) *ListActivitiesParams {
	o.SetPageSize(pageSize)
	return o
}

// SetPageSize adds the pageSize to the list activities params
func (o *ListActivitiesParams) SetPageSize(pageSize *int32) {
	o.PageSize = pageSize
}

// WithProcessInstanceID adds the processInstanceID to the list activities params
func (o *ListActivitiesParams) WithProcessInstanceID(processInstanceID *int64) *ListActivitiesParams {
	o.SetProcessInstanceID(processInstanceID)
	return o
}

// SetProcessInstanceID adds the processInstanceId to the list activities params
func (o *ListActivitiesParams) SetProcessInstanceID(processInstanceID *int64) {
	o.ProcessInstanceID = processInstanceID
}

// WithStatus adds the status to the list activities params
func (o *ListActivitiesParams) WithStatus(status *int32) *ListActivitiesParams {
	o.SetStatus(status)
	return o
}

// SetStatus adds the status to the list activities params
func (o *ListActivitiesParams) SetStatus(status *int32) {
	o.Status = status
}

// WithTypes adds the types to the list activities params
func (o *ListActivitiesParams) WithTypes(types []string) *ListActivitiesParams {
	o.SetTypes(types)
	return o
}

// SetTypes adds the types to the list activities params
func (o *ListActivitiesParams) SetTypes(types []string) {
	o.Types = types
}

// WriteToRequest writes these params to a swagger request
func (o *ListActivitiesParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.Excode != nil {

		// query param excode
		var qrExcode string

		if o.Excode != nil {
			qrExcode = *o.Excode
		}
		qExcode := qrExcode
		if qExcode != "" {

			if err := r.SetQueryParam("excode", qExcode); err != nil {
				return err
			}
		}
	}

	if o.Page != nil {

		// query param page
		var qrPage int32

		if o.Page != nil {
			qrPage = *o.Page
		}
		qPage := swag.FormatInt32(qrPage)
		if qPage != "" {

			if err := r.SetQueryParam("page", qPage); err != nil {
				return err
			}
		}
	}

	if o.PageSize != nil {

		// query param page_size
		var qrPageSize int32

		if o.PageSize != nil {
			qrPageSize = *o.PageSize
		}
		qPageSize := swag.FormatInt32(qrPageSize)
		if qPageSize != "" {

			if err := r.SetQueryParam("page_size", qPageSize); err != nil {
				return err
			}
		}
	}

	if o.ProcessInstanceID != nil {

		// query param process_instance_id
		var qrProcessInstanceID int64

		if o.ProcessInstanceID != nil {
			qrProcessInstanceID = *o.ProcessInstanceID
		}
		qProcessInstanceID := swag.FormatInt64(qrProcessInstanceID)
		if qProcessInstanceID != "" {

			if err := r.SetQueryParam("process_instance_id", qProcessInstanceID); err != nil {
				return err
			}
		}
	}

	if o.Status != nil {

		// query param status
		var qrStatus int32

		if o.Status != nil {
			qrStatus = *o.Status
		}
		qStatus := swag.FormatInt32(qrStatus)
		if qStatus != "" {

			if err := r.SetQueryParam("status", qStatus); err != nil {
				return err
			}
		}
	}

	if o.Types != nil {

		// binding items for types
		joinedTypes := o.bindParamTypes(reg)

		// query array param types
		if err := r.SetQueryParam("types", joinedTypes...); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindParamListActivities binds the parameter types
func (o *ListActivitiesParams) bindParamTypes(formats strfmt.Registry) []string {
	typesIR := o.Types

	var typesIC []string
	for _, typesIIR := range typesIR { // explode []string

		typesIIV := typesIIR // string as string
		typesIC = append(typesIC, typesIIV)
	}

	// items.CollectionFormat: "multi"
	typesIS := swag.JoinByFormat(typesIC, "multi")

	return typesIS
}
