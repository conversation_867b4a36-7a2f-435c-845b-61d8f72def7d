// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// NewStartProcessParams creates a new StartProcessParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewStartProcessParams() *StartProcessParams {
	return &StartProcessParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewStartProcessParamsWithTimeout creates a new StartProcessParams object
// with the ability to set a timeout on a request.
func NewStartProcessParamsWithTimeout(timeout time.Duration) *StartProcessParams {
	return &StartProcessParams{
		timeout: timeout,
	}
}

// NewStartProcessParamsWithContext creates a new StartProcessParams object
// with the ability to set a context for a request.
func NewStartProcessParamsWithContext(ctx context.Context) *StartProcessParams {
	return &StartProcessParams{
		Context: ctx,
	}
}

// NewStartProcessParamsWithHTTPClient creates a new StartProcessParams object
// with the ability to set a custom HTTPClient for a request.
func NewStartProcessParamsWithHTTPClient(client *http.Client) *StartProcessParams {
	return &StartProcessParams{
		HTTPClient: client,
	}
}

/*
StartProcessParams contains all the parameters to send to the API endpoint

	for the start process operation.

	Typically these are written to a http.Request.
*/
type StartProcessParams struct {

	// Data.
	Data *models.StartProcessInput

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the start process params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *StartProcessParams) WithDefaults() *StartProcessParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the start process params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *StartProcessParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the start process params
func (o *StartProcessParams) WithTimeout(timeout time.Duration) *StartProcessParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the start process params
func (o *StartProcessParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the start process params
func (o *StartProcessParams) WithContext(ctx context.Context) *StartProcessParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the start process params
func (o *StartProcessParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the start process params
func (o *StartProcessParams) WithHTTPClient(client *http.Client) *StartProcessParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the start process params
func (o *StartProcessParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithData adds the data to the start process params
func (o *StartProcessParams) WithData(data *models.StartProcessInput) *StartProcessParams {
	o.SetData(data)
	return o
}

// SetData adds the data to the start process params
func (o *StartProcessParams) SetData(data *models.StartProcessInput) {
	o.Data = data
}

// WriteToRequest writes these params to a swagger request
func (o *StartProcessParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error
	if o.Data != nil {
		if err := r.SetBodyParam(o.Data); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
