// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"qiniu.io/qbpm/sdk/models"
)

// NewCancelProcessParams creates a new CancelProcessParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewCancelProcessParams() *CancelProcessParams {
	return &CancelProcessParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewCancelProcessParamsWithTimeout creates a new CancelProcessParams object
// with the ability to set a timeout on a request.
func NewCancelProcessParamsWithTimeout(timeout time.Duration) *CancelProcessParams {
	return &CancelProcessParams{
		timeout: timeout,
	}
}

// NewCancelProcessParamsWithContext creates a new CancelProcessParams object
// with the ability to set a context for a request.
func NewCancelProcessParamsWithContext(ctx context.Context) *CancelProcessParams {
	return &CancelProcessParams{
		Context: ctx,
	}
}

// NewCancelProcessParamsWithHTTPClient creates a new CancelProcessParams object
// with the ability to set a custom HTTPClient for a request.
func NewCancelProcessParamsWithHTTPClient(client *http.Client) *CancelProcessParams {
	return &CancelProcessParams{
		HTTPClient: client,
	}
}

/*
CancelProcessParams contains all the parameters to send to the API endpoint

	for the cancel process operation.

	Typically these are written to a http.Request.
*/
type CancelProcessParams struct {

	// Data.
	Data *models.CancelProcessInput

	/* ID.

	   the run execution id

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the cancel process params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *CancelProcessParams) WithDefaults() *CancelProcessParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the cancel process params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *CancelProcessParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the cancel process params
func (o *CancelProcessParams) WithTimeout(timeout time.Duration) *CancelProcessParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the cancel process params
func (o *CancelProcessParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the cancel process params
func (o *CancelProcessParams) WithContext(ctx context.Context) *CancelProcessParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the cancel process params
func (o *CancelProcessParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the cancel process params
func (o *CancelProcessParams) WithHTTPClient(client *http.Client) *CancelProcessParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the cancel process params
func (o *CancelProcessParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithData adds the data to the cancel process params
func (o *CancelProcessParams) WithData(data *models.CancelProcessInput) *CancelProcessParams {
	o.SetData(data)
	return o
}

// SetData adds the data to the cancel process params
func (o *CancelProcessParams) SetData(data *models.CancelProcessInput) {
	o.Data = data
}

// WithID adds the id to the cancel process params
func (o *CancelProcessParams) WithID(id int64) *CancelProcessParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the cancel process params
func (o *CancelProcessParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *CancelProcessParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error
	if o.Data != nil {
		if err := r.SetBodyParam(o.Data); err != nil {
			return err
		}
	}

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
