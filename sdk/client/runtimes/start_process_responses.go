// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// StartProcessReader is a Reader for the StartProcess structure.
type StartProcessReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *StartProcessReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewStartProcessOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewStartProcessBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewStartProcessUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewStartProcessNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewStartProcessDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewStartProcessOK creates a StartProcessOK with default headers values
func NewStartProcessOK() *StartProcessOK {
	return &StartProcessOK{}
}

/*
	StartProcessOK describes a response with status code 200, with default header values.

OK
*/
type StartProcessOK struct {
	Payload *models.RunExecution
}

func (o *StartProcessOK) Error() string {
	return fmt.Sprintf("[PUT /api/runtime/executions][%d] startProcessOK  %+v", 200, o.Payload)
}
func (o *StartProcessOK) GetPayload() *models.RunExecution {
	return o.Payload
}

func (o *StartProcessOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.RunExecution)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewStartProcessBadRequest creates a StartProcessBadRequest with default headers values
func NewStartProcessBadRequest() *StartProcessBadRequest {
	return &StartProcessBadRequest{}
}

/*
	StartProcessBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type StartProcessBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *StartProcessBadRequest) Error() string {
	return fmt.Sprintf("[PUT /api/runtime/executions][%d] startProcessBadRequest  %+v", 400, o.Payload)
}
func (o *StartProcessBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *StartProcessBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewStartProcessUnauthorized creates a StartProcessUnauthorized with default headers values
func NewStartProcessUnauthorized() *StartProcessUnauthorized {
	return &StartProcessUnauthorized{}
}

/*
	StartProcessUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type StartProcessUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *StartProcessUnauthorized) Error() string {
	return fmt.Sprintf("[PUT /api/runtime/executions][%d] startProcessUnauthorized  %+v", 401, o.Payload)
}
func (o *StartProcessUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *StartProcessUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewStartProcessNotFound creates a StartProcessNotFound with default headers values
func NewStartProcessNotFound() *StartProcessNotFound {
	return &StartProcessNotFound{}
}

/*
	StartProcessNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type StartProcessNotFound struct {
}

func (o *StartProcessNotFound) Error() string {
	return fmt.Sprintf("[PUT /api/runtime/executions][%d] startProcessNotFound ", 404)
}

func (o *StartProcessNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewStartProcessDefault creates a StartProcessDefault with default headers values
func NewStartProcessDefault(code int) *StartProcessDefault {
	return &StartProcessDefault{
		_statusCode: code,
	}
}

/*
	StartProcessDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type StartProcessDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the start process default response
func (o *StartProcessDefault) Code() int {
	return o._statusCode
}

func (o *StartProcessDefault) Error() string {
	return fmt.Sprintf("[PUT /api/runtime/executions][%d] startProcess default  %+v", o._statusCode, o.Payload)
}
func (o *StartProcessDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *StartProcessDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
