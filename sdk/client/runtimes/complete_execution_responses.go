// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// CompleteExecutionReader is a Reader for the CompleteExecution structure.
type CompleteExecutionReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *CompleteExecutionReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewCompleteExecutionOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewCompleteExecutionBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewCompleteExecutionUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewCompleteExecutionNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewCompleteExecutionDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewCompleteExecutionOK creates a CompleteExecutionOK with default headers values
func NewCompleteExecutionOK() *CompleteExecutionOK {
	return &CompleteExecutionOK{}
}

/*
	CompleteExecutionOK describes a response with status code 200, with default header values.

OK
*/
type CompleteExecutionOK struct {
}

func (o *CompleteExecutionOK) Error() string {
	return fmt.Sprintf("[POST /api/runtime/executions/{id}][%d] completeExecutionOK ", 200)
}

func (o *CompleteExecutionOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewCompleteExecutionBadRequest creates a CompleteExecutionBadRequest with default headers values
func NewCompleteExecutionBadRequest() *CompleteExecutionBadRequest {
	return &CompleteExecutionBadRequest{}
}

/*
	CompleteExecutionBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type CompleteExecutionBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *CompleteExecutionBadRequest) Error() string {
	return fmt.Sprintf("[POST /api/runtime/executions/{id}][%d] completeExecutionBadRequest  %+v", 400, o.Payload)
}
func (o *CompleteExecutionBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *CompleteExecutionBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCompleteExecutionUnauthorized creates a CompleteExecutionUnauthorized with default headers values
func NewCompleteExecutionUnauthorized() *CompleteExecutionUnauthorized {
	return &CompleteExecutionUnauthorized{}
}

/*
	CompleteExecutionUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type CompleteExecutionUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *CompleteExecutionUnauthorized) Error() string {
	return fmt.Sprintf("[POST /api/runtime/executions/{id}][%d] completeExecutionUnauthorized  %+v", 401, o.Payload)
}
func (o *CompleteExecutionUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *CompleteExecutionUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCompleteExecutionNotFound creates a CompleteExecutionNotFound with default headers values
func NewCompleteExecutionNotFound() *CompleteExecutionNotFound {
	return &CompleteExecutionNotFound{}
}

/*
	CompleteExecutionNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type CompleteExecutionNotFound struct {
}

func (o *CompleteExecutionNotFound) Error() string {
	return fmt.Sprintf("[POST /api/runtime/executions/{id}][%d] completeExecutionNotFound ", 404)
}

func (o *CompleteExecutionNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewCompleteExecutionDefault creates a CompleteExecutionDefault with default headers values
func NewCompleteExecutionDefault(code int) *CompleteExecutionDefault {
	return &CompleteExecutionDefault{
		_statusCode: code,
	}
}

/*
	CompleteExecutionDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type CompleteExecutionDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the complete execution default response
func (o *CompleteExecutionDefault) Code() int {
	return o._statusCode
}

func (o *CompleteExecutionDefault) Error() string {
	return fmt.Sprintf("[POST /api/runtime/executions/{id}][%d] completeExecution default  %+v", o._statusCode, o.Payload)
}
func (o *CompleteExecutionDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *CompleteExecutionDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
