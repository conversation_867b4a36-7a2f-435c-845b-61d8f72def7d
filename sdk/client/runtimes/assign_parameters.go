// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"qiniu.io/qbpm/sdk/models"
)

// NewAssignParams creates a new AssignParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewAssignParams() *AssignParams {
	return &AssignParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewAssignParamsWithTimeout creates a new AssignParams object
// with the ability to set a timeout on a request.
func NewAssignParamsWithTimeout(timeout time.Duration) *AssignParams {
	return &AssignParams{
		timeout: timeout,
	}
}

// NewAssignParamsWithContext creates a new AssignParams object
// with the ability to set a context for a request.
func NewAssignParamsWithContext(ctx context.Context) *AssignParams {
	return &AssignParams{
		Context: ctx,
	}
}

// NewAssignParamsWithHTTPClient creates a new AssignParams object
// with the ability to set a custom HTTPClient for a request.
func NewAssignParamsWithHTTPClient(client *http.Client) *AssignParams {
	return &AssignParams{
		HTTPClient: client,
	}
}

/*
AssignParams contains all the parameters to send to the API endpoint

	for the assign operation.

	Typically these are written to a http.Request.
*/
type AssignParams struct {

	// Data.
	Data *models.AssigneeInput

	/* ID.

	   the run execution id

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the assign params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *AssignParams) WithDefaults() *AssignParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the assign params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *AssignParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the assign params
func (o *AssignParams) WithTimeout(timeout time.Duration) *AssignParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the assign params
func (o *AssignParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the assign params
func (o *AssignParams) WithContext(ctx context.Context) *AssignParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the assign params
func (o *AssignParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the assign params
func (o *AssignParams) WithHTTPClient(client *http.Client) *AssignParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the assign params
func (o *AssignParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithData adds the data to the assign params
func (o *AssignParams) WithData(data *models.AssigneeInput) *AssignParams {
	o.SetData(data)
	return o
}

// SetData adds the data to the assign params
func (o *AssignParams) SetData(data *models.AssigneeInput) {
	o.Data = data
}

// WithID adds the id to the assign params
func (o *AssignParams) WithID(id int64) *AssignParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the assign params
func (o *AssignParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *AssignParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error
	if o.Data != nil {
		if err := r.SetBodyParam(o.Data); err != nil {
			return err
		}
	}

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
