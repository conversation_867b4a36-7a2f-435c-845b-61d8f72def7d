// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewListRuntimeProcessesParams creates a new ListRuntimeProcessesParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListRuntimeProcessesParams() *ListRuntimeProcessesParams {
	return &ListRuntimeProcessesParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListRuntimeProcessesParamsWithTimeout creates a new ListRuntimeProcessesParams object
// with the ability to set a timeout on a request.
func NewListRuntimeProcessesParamsWithTimeout(timeout time.Duration) *ListRuntimeProcessesParams {
	return &ListRuntimeProcessesParams{
		timeout: timeout,
	}
}

// NewListRuntimeProcessesParamsWithContext creates a new ListRuntimeProcessesParams object
// with the ability to set a context for a request.
func NewListRuntimeProcessesParamsWithContext(ctx context.Context) *ListRuntimeProcessesParams {
	return &ListRuntimeProcessesParams{
		Context: ctx,
	}
}

// NewListRuntimeProcessesParamsWithHTTPClient creates a new ListRuntimeProcessesParams object
// with the ability to set a custom HTTPClient for a request.
func NewListRuntimeProcessesParamsWithHTTPClient(client *http.Client) *ListRuntimeProcessesParams {
	return &ListRuntimeProcessesParams{
		HTTPClient: client,
	}
}

/*
ListRuntimeProcessesParams contains all the parameters to send to the API endpoint

	for the list runtime processes operation.

	Typically these are written to a http.Request.
*/
type ListRuntimeProcessesParams struct {

	/* Assignees.

	   the executions assignees
	*/
	Assignees []string

	/* Excode.

	   external business code
	*/
	Excode *string

	/* OriginalAssignees.

	   the executions original_assignees
	*/
	OriginalAssignees []string

	/* Page.

	   page index for queries, 0 is default

	   Format: int32
	*/
	Page *int32

	/* PageSize.

	   page size for queries, 200 is default, max value 1000 is limited

	   Format: int32
	*/
	PageSize *int32

	/* ProcessDefinitionID.

	   process definition id

	   Format: int64
	*/
	ProcessDefinitionID *int64

	/* ProcessDefinitionKey.

	   process definition key
	*/
	ProcessDefinitionKey *string

	/* ProcessInstanceID.

	   process instance id

	   Format: int64
	*/
	ProcessInstanceID *int64

	/* Starters.

	   the executions starters
	*/
	Starters []string

	/* Status.

	   the executions status

	   Format: int32
	*/
	Status *int32

	/* UID.

	   uid

	   Format: int64
	*/
	UID *int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list runtime processes params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListRuntimeProcessesParams) WithDefaults() *ListRuntimeProcessesParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list runtime processes params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListRuntimeProcessesParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithTimeout(timeout time.Duration) *ListRuntimeProcessesParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithContext(ctx context.Context) *ListRuntimeProcessesParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithHTTPClient(client *http.Client) *ListRuntimeProcessesParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithAssignees adds the assignees to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithAssignees(assignees []string) *ListRuntimeProcessesParams {
	o.SetAssignees(assignees)
	return o
}

// SetAssignees adds the assignees to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetAssignees(assignees []string) {
	o.Assignees = assignees
}

// WithExcode adds the excode to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithExcode(excode *string) *ListRuntimeProcessesParams {
	o.SetExcode(excode)
	return o
}

// SetExcode adds the excode to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetExcode(excode *string) {
	o.Excode = excode
}

// WithOriginalAssignees adds the originalAssignees to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithOriginalAssignees(originalAssignees []string) *ListRuntimeProcessesParams {
	o.SetOriginalAssignees(originalAssignees)
	return o
}

// SetOriginalAssignees adds the originalAssignees to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetOriginalAssignees(originalAssignees []string) {
	o.OriginalAssignees = originalAssignees
}

// WithPage adds the page to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithPage(page *int32) *ListRuntimeProcessesParams {
	o.SetPage(page)
	return o
}

// SetPage adds the page to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetPage(page *int32) {
	o.Page = page
}

// WithPageSize adds the pageSize to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithPageSize(pageSize *int32) *ListRuntimeProcessesParams {
	o.SetPageSize(pageSize)
	return o
}

// SetPageSize adds the pageSize to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetPageSize(pageSize *int32) {
	o.PageSize = pageSize
}

// WithProcessDefinitionID adds the processDefinitionID to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithProcessDefinitionID(processDefinitionID *int64) *ListRuntimeProcessesParams {
	o.SetProcessDefinitionID(processDefinitionID)
	return o
}

// SetProcessDefinitionID adds the processDefinitionId to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetProcessDefinitionID(processDefinitionID *int64) {
	o.ProcessDefinitionID = processDefinitionID
}

// WithProcessDefinitionKey adds the processDefinitionKey to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithProcessDefinitionKey(processDefinitionKey *string) *ListRuntimeProcessesParams {
	o.SetProcessDefinitionKey(processDefinitionKey)
	return o
}

// SetProcessDefinitionKey adds the processDefinitionKey to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetProcessDefinitionKey(processDefinitionKey *string) {
	o.ProcessDefinitionKey = processDefinitionKey
}

// WithProcessInstanceID adds the processInstanceID to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithProcessInstanceID(processInstanceID *int64) *ListRuntimeProcessesParams {
	o.SetProcessInstanceID(processInstanceID)
	return o
}

// SetProcessInstanceID adds the processInstanceId to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetProcessInstanceID(processInstanceID *int64) {
	o.ProcessInstanceID = processInstanceID
}

// WithStarters adds the starters to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithStarters(starters []string) *ListRuntimeProcessesParams {
	o.SetStarters(starters)
	return o
}

// SetStarters adds the starters to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetStarters(starters []string) {
	o.Starters = starters
}

// WithStatus adds the status to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithStatus(status *int32) *ListRuntimeProcessesParams {
	o.SetStatus(status)
	return o
}

// SetStatus adds the status to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetStatus(status *int32) {
	o.Status = status
}

// WithUID adds the uid to the list runtime processes params
func (o *ListRuntimeProcessesParams) WithUID(uid *int64) *ListRuntimeProcessesParams {
	o.SetUID(uid)
	return o
}

// SetUID adds the uid to the list runtime processes params
func (o *ListRuntimeProcessesParams) SetUID(uid *int64) {
	o.UID = uid
}

// WriteToRequest writes these params to a swagger request
func (o *ListRuntimeProcessesParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.Assignees != nil {

		// binding items for assignees
		joinedAssignees := o.bindParamAssignees(reg)

		// query array param assignees
		if err := r.SetQueryParam("assignees", joinedAssignees...); err != nil {
			return err
		}
	}

	if o.Excode != nil {

		// query param excode
		var qrExcode string

		if o.Excode != nil {
			qrExcode = *o.Excode
		}
		qExcode := qrExcode
		if qExcode != "" {

			if err := r.SetQueryParam("excode", qExcode); err != nil {
				return err
			}
		}
	}

	if o.OriginalAssignees != nil {

		// binding items for original_assignees
		joinedOriginalAssignees := o.bindParamOriginalAssignees(reg)

		// query array param original_assignees
		if err := r.SetQueryParam("original_assignees", joinedOriginalAssignees...); err != nil {
			return err
		}
	}

	if o.Page != nil {

		// query param page
		var qrPage int32

		if o.Page != nil {
			qrPage = *o.Page
		}
		qPage := swag.FormatInt32(qrPage)
		if qPage != "" {

			if err := r.SetQueryParam("page", qPage); err != nil {
				return err
			}
		}
	}

	if o.PageSize != nil {

		// query param page_size
		var qrPageSize int32

		if o.PageSize != nil {
			qrPageSize = *o.PageSize
		}
		qPageSize := swag.FormatInt32(qrPageSize)
		if qPageSize != "" {

			if err := r.SetQueryParam("page_size", qPageSize); err != nil {
				return err
			}
		}
	}

	if o.ProcessDefinitionID != nil {

		// query param process_definition_id
		var qrProcessDefinitionID int64

		if o.ProcessDefinitionID != nil {
			qrProcessDefinitionID = *o.ProcessDefinitionID
		}
		qProcessDefinitionID := swag.FormatInt64(qrProcessDefinitionID)
		if qProcessDefinitionID != "" {

			if err := r.SetQueryParam("process_definition_id", qProcessDefinitionID); err != nil {
				return err
			}
		}
	}

	if o.ProcessDefinitionKey != nil {

		// query param process_definition_key
		var qrProcessDefinitionKey string

		if o.ProcessDefinitionKey != nil {
			qrProcessDefinitionKey = *o.ProcessDefinitionKey
		}
		qProcessDefinitionKey := qrProcessDefinitionKey
		if qProcessDefinitionKey != "" {

			if err := r.SetQueryParam("process_definition_key", qProcessDefinitionKey); err != nil {
				return err
			}
		}
	}

	if o.ProcessInstanceID != nil {

		// query param process_instance_id
		var qrProcessInstanceID int64

		if o.ProcessInstanceID != nil {
			qrProcessInstanceID = *o.ProcessInstanceID
		}
		qProcessInstanceID := swag.FormatInt64(qrProcessInstanceID)
		if qProcessInstanceID != "" {

			if err := r.SetQueryParam("process_instance_id", qProcessInstanceID); err != nil {
				return err
			}
		}
	}

	if o.Starters != nil {

		// binding items for starters
		joinedStarters := o.bindParamStarters(reg)

		// query array param starters
		if err := r.SetQueryParam("starters", joinedStarters...); err != nil {
			return err
		}
	}

	if o.Status != nil {

		// query param status
		var qrStatus int32

		if o.Status != nil {
			qrStatus = *o.Status
		}
		qStatus := swag.FormatInt32(qrStatus)
		if qStatus != "" {

			if err := r.SetQueryParam("status", qStatus); err != nil {
				return err
			}
		}
	}

	if o.UID != nil {

		// query param uid
		var qrUID int64

		if o.UID != nil {
			qrUID = *o.UID
		}
		qUID := swag.FormatInt64(qrUID)
		if qUID != "" {

			if err := r.SetQueryParam("uid", qUID); err != nil {
				return err
			}
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindParamListRuntimeProcesses binds the parameter assignees
func (o *ListRuntimeProcessesParams) bindParamAssignees(formats strfmt.Registry) []string {
	assigneesIR := o.Assignees

	var assigneesIC []string
	for _, assigneesIIR := range assigneesIR { // explode []string

		assigneesIIV := assigneesIIR // string as string
		assigneesIC = append(assigneesIC, assigneesIIV)
	}

	// items.CollectionFormat: "multi"
	assigneesIS := swag.JoinByFormat(assigneesIC, "multi")

	return assigneesIS
}

// bindParamListRuntimeProcesses binds the parameter original_assignees
func (o *ListRuntimeProcessesParams) bindParamOriginalAssignees(formats strfmt.Registry) []string {
	originalAssigneesIR := o.OriginalAssignees

	var originalAssigneesIC []string
	for _, originalAssigneesIIR := range originalAssigneesIR { // explode []string

		originalAssigneesIIV := originalAssigneesIIR // string as string
		originalAssigneesIC = append(originalAssigneesIC, originalAssigneesIIV)
	}

	// items.CollectionFormat: "multi"
	originalAssigneesIS := swag.JoinByFormat(originalAssigneesIC, "multi")

	return originalAssigneesIS
}

// bindParamListRuntimeProcesses binds the parameter starters
func (o *ListRuntimeProcessesParams) bindParamStarters(formats strfmt.Registry) []string {
	startersIR := o.Starters

	var startersIC []string
	for _, startersIIR := range startersIR { // explode []string

		startersIIV := startersIIR // string as string
		startersIC = append(startersIC, startersIIV)
	}

	// items.CollectionFormat: "multi"
	startersIS := swag.JoinByFormat(startersIC, "multi")

	return startersIS
}
