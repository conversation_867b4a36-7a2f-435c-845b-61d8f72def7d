// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new runtimes API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for runtimes API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

// ClientService is the interface for Client methods
type ClientService interface {
	Assign(params *AssignParams, opts ...ClientOption) (*AssignOK, error)

	CancelProcess(params *CancelProcessParams, opts ...ClientOption) (*CancelProcessOK, error)

	CompleteExecution(params *CompleteExecutionParams, opts ...ClientOption) (*CompleteExecutionOK, error)

	FailExecution(params *FailExecutionParams, opts ...ClientOption) (*FailExecutionOK, error)

	GetExecution(params *GetExecutionParams, opts ...ClientOption) (*GetExecutionOK, error)

	ListExecutions(params *ListExecutionsParams, opts ...ClientOption) (*ListExecutionsOK, error)

	ListRuntimeProcesses(params *ListRuntimeProcessesParams, opts ...ClientOption) (*ListRuntimeProcessesOK, error)

	StartProcess(params *StartProcessParams, opts ...ClientOption) (*StartProcessOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
Assign assigns a run execution
*/
func (a *Client) Assign(params *AssignParams, opts ...ClientOption) (*AssignOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewAssignParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "assign",
		Method:             "PATCH",
		PathPattern:        "/api/runtime/executions/{id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &AssignReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*AssignOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*AssignDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
CancelProcess cancels a process
*/
func (a *Client) CancelProcess(params *CancelProcessParams, opts ...ClientOption) (*CancelProcessOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewCancelProcessParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "cancelProcess",
		Method:             "DELETE",
		PathPattern:        "/api/runtime/processes/{id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &CancelProcessReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*CancelProcessOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*CancelProcessDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
CompleteExecution completes a run execution
*/
func (a *Client) CompleteExecution(params *CompleteExecutionParams, opts ...ClientOption) (*CompleteExecutionOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewCompleteExecutionParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "completeExecution",
		Method:             "POST",
		PathPattern:        "/api/runtime/executions/{id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &CompleteExecutionReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*CompleteExecutionOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*CompleteExecutionDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
FailExecution fails a execution
*/
func (a *Client) FailExecution(params *FailExecutionParams, opts ...ClientOption) (*FailExecutionOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewFailExecutionParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "failExecution",
		Method:             "DELETE",
		PathPattern:        "/api/runtime/executions/{id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &FailExecutionReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*FailExecutionOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*FailExecutionDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
GetExecution gets a execution
*/
func (a *Client) GetExecution(params *GetExecutionParams, opts ...ClientOption) (*GetExecutionOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetExecutionParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "getExecution",
		Method:             "GET",
		PathPattern:        "/api/runtime/executions/{id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetExecutionReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetExecutionOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*GetExecutionDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
ListExecutions lists executions
*/
func (a *Client) ListExecutions(params *ListExecutionsParams, opts ...ClientOption) (*ListExecutionsOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListExecutionsParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "listExecutions",
		Method:             "GET",
		PathPattern:        "/api/runtime/executions",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListExecutionsReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListExecutionsOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*ListExecutionsDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
ListRuntimeProcesses lists runtime processes
*/
func (a *Client) ListRuntimeProcesses(params *ListRuntimeProcessesParams, opts ...ClientOption) (*ListRuntimeProcessesOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListRuntimeProcessesParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "listRuntimeProcesses",
		Method:             "GET",
		PathPattern:        "/api/runtime/processes",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListRuntimeProcessesReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListRuntimeProcessesOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*ListRuntimeProcessesDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
StartProcess starts a new process
*/
func (a *Client) StartProcess(params *StartProcessParams, opts ...ClientOption) (*StartProcessOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewStartProcessParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "startProcess",
		Method:             "PUT",
		PathPattern:        "/api/runtime/executions",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &StartProcessReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*StartProcessOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*StartProcessDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
