// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetExecutionParams creates a new GetExecutionParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetExecutionParams() *GetExecutionParams {
	return &GetExecutionParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetExecutionParamsWithTimeout creates a new GetExecutionParams object
// with the ability to set a timeout on a request.
func NewGetExecutionParamsWithTimeout(timeout time.Duration) *GetExecutionParams {
	return &GetExecutionParams{
		timeout: timeout,
	}
}

// NewGetExecutionParamsWithContext creates a new GetExecutionParams object
// with the ability to set a context for a request.
func NewGetExecutionParamsWithContext(ctx context.Context) *GetExecutionParams {
	return &GetExecutionParams{
		Context: ctx,
	}
}

// NewGetExecutionParamsWithHTTPClient creates a new GetExecutionParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetExecutionParamsWithHTTPClient(client *http.Client) *GetExecutionParams {
	return &GetExecutionParams{
		HTTPClient: client,
	}
}

/*
GetExecutionParams contains all the parameters to send to the API endpoint

	for the get execution operation.

	Typically these are written to a http.Request.
*/
type GetExecutionParams struct {

	/* ID.

	   the run execution id

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get execution params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetExecutionParams) WithDefaults() *GetExecutionParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get execution params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetExecutionParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get execution params
func (o *GetExecutionParams) WithTimeout(timeout time.Duration) *GetExecutionParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get execution params
func (o *GetExecutionParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get execution params
func (o *GetExecutionParams) WithContext(ctx context.Context) *GetExecutionParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get execution params
func (o *GetExecutionParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get execution params
func (o *GetExecutionParams) WithHTTPClient(client *http.Client) *GetExecutionParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get execution params
func (o *GetExecutionParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithID adds the id to the get execution params
func (o *GetExecutionParams) WithID(id int64) *GetExecutionParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the get execution params
func (o *GetExecutionParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *GetExecutionParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
