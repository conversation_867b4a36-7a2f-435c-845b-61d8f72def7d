// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// CancelProcessReader is a Reader for the CancelProcess structure.
type CancelProcessReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *CancelProcessReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewCancelProcessOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewCancelProcessBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewCancelProcessUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewCancelProcessNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewCancelProcessDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewCancelProcessOK creates a CancelProcessOK with default headers values
func NewCancelProcessOK() *CancelProcessOK {
	return &CancelProcessOK{}
}

/*
	CancelProcessOK describes a response with status code 200, with default header values.

OK
*/
type CancelProcessOK struct {
}

func (o *CancelProcessOK) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/processes/{id}][%d] cancelProcessOK ", 200)
}

func (o *CancelProcessOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewCancelProcessBadRequest creates a CancelProcessBadRequest with default headers values
func NewCancelProcessBadRequest() *CancelProcessBadRequest {
	return &CancelProcessBadRequest{}
}

/*
	CancelProcessBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type CancelProcessBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *CancelProcessBadRequest) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/processes/{id}][%d] cancelProcessBadRequest  %+v", 400, o.Payload)
}
func (o *CancelProcessBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *CancelProcessBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCancelProcessUnauthorized creates a CancelProcessUnauthorized with default headers values
func NewCancelProcessUnauthorized() *CancelProcessUnauthorized {
	return &CancelProcessUnauthorized{}
}

/*
	CancelProcessUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type CancelProcessUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *CancelProcessUnauthorized) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/processes/{id}][%d] cancelProcessUnauthorized  %+v", 401, o.Payload)
}
func (o *CancelProcessUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *CancelProcessUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCancelProcessNotFound creates a CancelProcessNotFound with default headers values
func NewCancelProcessNotFound() *CancelProcessNotFound {
	return &CancelProcessNotFound{}
}

/*
	CancelProcessNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type CancelProcessNotFound struct {
}

func (o *CancelProcessNotFound) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/processes/{id}][%d] cancelProcessNotFound ", 404)
}

func (o *CancelProcessNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewCancelProcessDefault creates a CancelProcessDefault with default headers values
func NewCancelProcessDefault(code int) *CancelProcessDefault {
	return &CancelProcessDefault{
		_statusCode: code,
	}
}

/*
	CancelProcessDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type CancelProcessDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the cancel process default response
func (o *CancelProcessDefault) Code() int {
	return o._statusCode
}

func (o *CancelProcessDefault) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/processes/{id}][%d] cancelProcess default  %+v", o._statusCode, o.Payload)
}
func (o *CancelProcessDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *CancelProcessDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
