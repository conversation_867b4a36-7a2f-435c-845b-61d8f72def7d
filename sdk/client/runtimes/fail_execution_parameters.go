// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"qiniu.io/qbpm/sdk/models"
)

// NewFailExecutionParams creates a new FailExecutionParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewFailExecutionParams() *FailExecutionParams {
	return &FailExecutionParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewFailExecutionParamsWithTimeout creates a new FailExecutionParams object
// with the ability to set a timeout on a request.
func NewFailExecutionParamsWithTimeout(timeout time.Duration) *FailExecutionParams {
	return &FailExecutionParams{
		timeout: timeout,
	}
}

// NewFailExecutionParamsWithContext creates a new FailExecutionParams object
// with the ability to set a context for a request.
func NewFailExecutionParamsWithContext(ctx context.Context) *FailExecutionParams {
	return &FailExecutionParams{
		Context: ctx,
	}
}

// NewFailExecutionParamsWithHTTPClient creates a new FailExecutionParams object
// with the ability to set a custom HTTPClient for a request.
func NewFailExecutionParamsWithHTTPClient(client *http.Client) *FailExecutionParams {
	return &FailExecutionParams{
		HTTPClient: client,
	}
}

/*
FailExecutionParams contains all the parameters to send to the API endpoint

	for the fail execution operation.

	Typically these are written to a http.Request.
*/
type FailExecutionParams struct {

	// Data.
	Data *models.OperateExecutionInput

	/* ID.

	   the run execution id

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the fail execution params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *FailExecutionParams) WithDefaults() *FailExecutionParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the fail execution params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *FailExecutionParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the fail execution params
func (o *FailExecutionParams) WithTimeout(timeout time.Duration) *FailExecutionParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the fail execution params
func (o *FailExecutionParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the fail execution params
func (o *FailExecutionParams) WithContext(ctx context.Context) *FailExecutionParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the fail execution params
func (o *FailExecutionParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the fail execution params
func (o *FailExecutionParams) WithHTTPClient(client *http.Client) *FailExecutionParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the fail execution params
func (o *FailExecutionParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithData adds the data to the fail execution params
func (o *FailExecutionParams) WithData(data *models.OperateExecutionInput) *FailExecutionParams {
	o.SetData(data)
	return o
}

// SetData adds the data to the fail execution params
func (o *FailExecutionParams) SetData(data *models.OperateExecutionInput) {
	o.Data = data
}

// WithID adds the id to the fail execution params
func (o *FailExecutionParams) WithID(id int64) *FailExecutionParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the fail execution params
func (o *FailExecutionParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *FailExecutionParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error
	if o.Data != nil {
		if err := r.SetBodyParam(o.Data); err != nil {
			return err
		}
	}

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
