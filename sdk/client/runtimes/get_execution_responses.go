// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// GetExecutionReader is a Reader for the GetExecution structure.
type GetExecutionReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetExecutionReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetExecutionOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewGetExecutionBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewGetExecutionUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewGetExecutionNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewGetExecutionDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewGetExecutionOK creates a GetExecutionOK with default headers values
func NewGetExecutionOK() *GetExecutionOK {
	return &GetExecutionOK{}
}

/*
	GetExecutionOK describes a response with status code 200, with default header values.

ok
*/
type GetExecutionOK struct {
	Payload *models.RunExecution
}

func (o *GetExecutionOK) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions/{id}][%d] getExecutionOK  %+v", 200, o.Payload)
}
func (o *GetExecutionOK) GetPayload() *models.RunExecution {
	return o.Payload
}

func (o *GetExecutionOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.RunExecution)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetExecutionBadRequest creates a GetExecutionBadRequest with default headers values
func NewGetExecutionBadRequest() *GetExecutionBadRequest {
	return &GetExecutionBadRequest{}
}

/*
	GetExecutionBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type GetExecutionBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *GetExecutionBadRequest) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions/{id}][%d] getExecutionBadRequest  %+v", 400, o.Payload)
}
func (o *GetExecutionBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *GetExecutionBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetExecutionUnauthorized creates a GetExecutionUnauthorized with default headers values
func NewGetExecutionUnauthorized() *GetExecutionUnauthorized {
	return &GetExecutionUnauthorized{}
}

/*
	GetExecutionUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type GetExecutionUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *GetExecutionUnauthorized) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions/{id}][%d] getExecutionUnauthorized  %+v", 401, o.Payload)
}
func (o *GetExecutionUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *GetExecutionUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetExecutionNotFound creates a GetExecutionNotFound with default headers values
func NewGetExecutionNotFound() *GetExecutionNotFound {
	return &GetExecutionNotFound{}
}

/*
	GetExecutionNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type GetExecutionNotFound struct {
}

func (o *GetExecutionNotFound) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions/{id}][%d] getExecutionNotFound ", 404)
}

func (o *GetExecutionNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewGetExecutionDefault creates a GetExecutionDefault with default headers values
func NewGetExecutionDefault(code int) *GetExecutionDefault {
	return &GetExecutionDefault{
		_statusCode: code,
	}
}

/*
	GetExecutionDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type GetExecutionDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the get execution default response
func (o *GetExecutionDefault) Code() int {
	return o._statusCode
}

func (o *GetExecutionDefault) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions/{id}][%d] getExecution default  %+v", o._statusCode, o.Payload)
}
func (o *GetExecutionDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *GetExecutionDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
