// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// ListRuntimeProcessesReader is a Reader for the ListRuntimeProcesses structure.
type ListRuntimeProcessesReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *ListRuntimeProcessesReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewListRuntimeProcessesOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewListRuntimeProcessesBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewListRuntimeProcessesUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewListRuntimeProcessesNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewListRuntimeProcessesDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewListRuntimeProcessesOK creates a ListRuntimeProcessesOK with default headers values
func NewListRuntimeProcessesOK() *ListRuntimeProcessesOK {
	return &ListRuntimeProcessesOK{}
}

/*
	ListRuntimeProcessesOK describes a response with status code 200, with default header values.

OK
*/
type ListRuntimeProcessesOK struct {
	Payload []*models.RunExecution
}

func (o *ListRuntimeProcessesOK) Error() string {
	return fmt.Sprintf("[GET /api/runtime/processes][%d] listRuntimeProcessesOK  %+v", 200, o.Payload)
}
func (o *ListRuntimeProcessesOK) GetPayload() []*models.RunExecution {
	return o.Payload
}

func (o *ListRuntimeProcessesOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListRuntimeProcessesBadRequest creates a ListRuntimeProcessesBadRequest with default headers values
func NewListRuntimeProcessesBadRequest() *ListRuntimeProcessesBadRequest {
	return &ListRuntimeProcessesBadRequest{}
}

/*
	ListRuntimeProcessesBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type ListRuntimeProcessesBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *ListRuntimeProcessesBadRequest) Error() string {
	return fmt.Sprintf("[GET /api/runtime/processes][%d] listRuntimeProcessesBadRequest  %+v", 400, o.Payload)
}
func (o *ListRuntimeProcessesBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListRuntimeProcessesBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListRuntimeProcessesUnauthorized creates a ListRuntimeProcessesUnauthorized with default headers values
func NewListRuntimeProcessesUnauthorized() *ListRuntimeProcessesUnauthorized {
	return &ListRuntimeProcessesUnauthorized{}
}

/*
	ListRuntimeProcessesUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type ListRuntimeProcessesUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *ListRuntimeProcessesUnauthorized) Error() string {
	return fmt.Sprintf("[GET /api/runtime/processes][%d] listRuntimeProcessesUnauthorized  %+v", 401, o.Payload)
}
func (o *ListRuntimeProcessesUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListRuntimeProcessesUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListRuntimeProcessesNotFound creates a ListRuntimeProcessesNotFound with default headers values
func NewListRuntimeProcessesNotFound() *ListRuntimeProcessesNotFound {
	return &ListRuntimeProcessesNotFound{}
}

/*
	ListRuntimeProcessesNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type ListRuntimeProcessesNotFound struct {
}

func (o *ListRuntimeProcessesNotFound) Error() string {
	return fmt.Sprintf("[GET /api/runtime/processes][%d] listRuntimeProcessesNotFound ", 404)
}

func (o *ListRuntimeProcessesNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewListRuntimeProcessesDefault creates a ListRuntimeProcessesDefault with default headers values
func NewListRuntimeProcessesDefault(code int) *ListRuntimeProcessesDefault {
	return &ListRuntimeProcessesDefault{
		_statusCode: code,
	}
}

/*
	ListRuntimeProcessesDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type ListRuntimeProcessesDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the list runtime processes default response
func (o *ListRuntimeProcessesDefault) Code() int {
	return o._statusCode
}

func (o *ListRuntimeProcessesDefault) Error() string {
	return fmt.Sprintf("[GET /api/runtime/processes][%d] listRuntimeProcesses default  %+v", o._statusCode, o.Payload)
}
func (o *ListRuntimeProcessesDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListRuntimeProcessesDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
