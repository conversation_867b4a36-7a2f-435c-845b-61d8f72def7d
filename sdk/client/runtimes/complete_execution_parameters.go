// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"qiniu.io/qbpm/sdk/models"
)

// NewCompleteExecutionParams creates a new CompleteExecutionParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewCompleteExecutionParams() *CompleteExecutionParams {
	return &CompleteExecutionParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewCompleteExecutionParamsWithTimeout creates a new CompleteExecutionParams object
// with the ability to set a timeout on a request.
func NewCompleteExecutionParamsWithTimeout(timeout time.Duration) *CompleteExecutionParams {
	return &CompleteExecutionParams{
		timeout: timeout,
	}
}

// NewCompleteExecutionParamsWithContext creates a new CompleteExecutionParams object
// with the ability to set a context for a request.
func NewCompleteExecutionParamsWithContext(ctx context.Context) *CompleteExecutionParams {
	return &CompleteExecutionParams{
		Context: ctx,
	}
}

// NewCompleteExecutionParamsWithHTTPClient creates a new CompleteExecutionParams object
// with the ability to set a custom HTTPClient for a request.
func NewCompleteExecutionParamsWithHTTPClient(client *http.Client) *CompleteExecutionParams {
	return &CompleteExecutionParams{
		HTTPClient: client,
	}
}

/*
CompleteExecutionParams contains all the parameters to send to the API endpoint

	for the complete execution operation.

	Typically these are written to a http.Request.
*/
type CompleteExecutionParams struct {

	// Data.
	Data *models.OperateExecutionInput

	/* ID.

	   the run execution id

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the complete execution params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *CompleteExecutionParams) WithDefaults() *CompleteExecutionParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the complete execution params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *CompleteExecutionParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the complete execution params
func (o *CompleteExecutionParams) WithTimeout(timeout time.Duration) *CompleteExecutionParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the complete execution params
func (o *CompleteExecutionParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the complete execution params
func (o *CompleteExecutionParams) WithContext(ctx context.Context) *CompleteExecutionParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the complete execution params
func (o *CompleteExecutionParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the complete execution params
func (o *CompleteExecutionParams) WithHTTPClient(client *http.Client) *CompleteExecutionParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the complete execution params
func (o *CompleteExecutionParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithData adds the data to the complete execution params
func (o *CompleteExecutionParams) WithData(data *models.OperateExecutionInput) *CompleteExecutionParams {
	o.SetData(data)
	return o
}

// SetData adds the data to the complete execution params
func (o *CompleteExecutionParams) SetData(data *models.OperateExecutionInput) {
	o.Data = data
}

// WithID adds the id to the complete execution params
func (o *CompleteExecutionParams) WithID(id int64) *CompleteExecutionParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the complete execution params
func (o *CompleteExecutionParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *CompleteExecutionParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error
	if o.Data != nil {
		if err := r.SetBodyParam(o.Data); err != nil {
			return err
		}
	}

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
