// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// AssignReader is a Reader for the Assign structure.
type AssignReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *AssignReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewAssignOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewAssignBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewAssignUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewAssignNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewAssignDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewAssignOK creates a AssignOK with default headers values
func NewAssignOK() *AssignOK {
	return &AssignOK{}
}

/*
	AssignOK describes a response with status code 200, with default header values.

OK
*/
type AssignOK struct {
}

func (o *AssignOK) Error() string {
	return fmt.Sprintf("[PATCH /api/runtime/executions/{id}][%d] assignOK ", 200)
}

func (o *AssignOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewAssignBadRequest creates a AssignBadRequest with default headers values
func NewAssignBadRequest() *AssignBadRequest {
	return &AssignBadRequest{}
}

/*
	AssignBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type AssignBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *AssignBadRequest) Error() string {
	return fmt.Sprintf("[PATCH /api/runtime/executions/{id}][%d] assignBadRequest  %+v", 400, o.Payload)
}
func (o *AssignBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *AssignBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewAssignUnauthorized creates a AssignUnauthorized with default headers values
func NewAssignUnauthorized() *AssignUnauthorized {
	return &AssignUnauthorized{}
}

/*
	AssignUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type AssignUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *AssignUnauthorized) Error() string {
	return fmt.Sprintf("[PATCH /api/runtime/executions/{id}][%d] assignUnauthorized  %+v", 401, o.Payload)
}
func (o *AssignUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *AssignUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewAssignNotFound creates a AssignNotFound with default headers values
func NewAssignNotFound() *AssignNotFound {
	return &AssignNotFound{}
}

/*
	AssignNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type AssignNotFound struct {
}

func (o *AssignNotFound) Error() string {
	return fmt.Sprintf("[PATCH /api/runtime/executions/{id}][%d] assignNotFound ", 404)
}

func (o *AssignNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewAssignDefault creates a AssignDefault with default headers values
func NewAssignDefault(code int) *AssignDefault {
	return &AssignDefault{
		_statusCode: code,
	}
}

/*
	AssignDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type AssignDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the assign default response
func (o *AssignDefault) Code() int {
	return o._statusCode
}

func (o *AssignDefault) Error() string {
	return fmt.Sprintf("[PATCH /api/runtime/executions/{id}][%d] assign default  %+v", o._statusCode, o.Payload)
}
func (o *AssignDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *AssignDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
