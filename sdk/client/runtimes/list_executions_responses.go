// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// ListExecutionsReader is a Reader for the ListExecutions structure.
type ListExecutionsReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *ListExecutionsReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewListExecutionsOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewListExecutionsBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewListExecutionsUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewListExecutionsNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewListExecutionsDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewListExecutionsOK creates a ListExecutionsOK with default headers values
func NewListExecutionsOK() *ListExecutionsOK {
	return &ListExecutionsOK{}
}

/*
	ListExecutionsOK describes a response with status code 200, with default header values.

OK
*/
type ListExecutionsOK struct {
	Payload []*models.RunExecution
}

func (o *ListExecutionsOK) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions][%d] listExecutionsOK  %+v", 200, o.Payload)
}
func (o *ListExecutionsOK) GetPayload() []*models.RunExecution {
	return o.Payload
}

func (o *ListExecutionsOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListExecutionsBadRequest creates a ListExecutionsBadRequest with default headers values
func NewListExecutionsBadRequest() *ListExecutionsBadRequest {
	return &ListExecutionsBadRequest{}
}

/*
	ListExecutionsBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type ListExecutionsBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *ListExecutionsBadRequest) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions][%d] listExecutionsBadRequest  %+v", 400, o.Payload)
}
func (o *ListExecutionsBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListExecutionsBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListExecutionsUnauthorized creates a ListExecutionsUnauthorized with default headers values
func NewListExecutionsUnauthorized() *ListExecutionsUnauthorized {
	return &ListExecutionsUnauthorized{}
}

/*
	ListExecutionsUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type ListExecutionsUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *ListExecutionsUnauthorized) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions][%d] listExecutionsUnauthorized  %+v", 401, o.Payload)
}
func (o *ListExecutionsUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListExecutionsUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListExecutionsNotFound creates a ListExecutionsNotFound with default headers values
func NewListExecutionsNotFound() *ListExecutionsNotFound {
	return &ListExecutionsNotFound{}
}

/*
	ListExecutionsNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type ListExecutionsNotFound struct {
}

func (o *ListExecutionsNotFound) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions][%d] listExecutionsNotFound ", 404)
}

func (o *ListExecutionsNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewListExecutionsDefault creates a ListExecutionsDefault with default headers values
func NewListExecutionsDefault(code int) *ListExecutionsDefault {
	return &ListExecutionsDefault{
		_statusCode: code,
	}
}

/*
	ListExecutionsDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type ListExecutionsDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the list executions default response
func (o *ListExecutionsDefault) Code() int {
	return o._statusCode
}

func (o *ListExecutionsDefault) Error() string {
	return fmt.Sprintf("[GET /api/runtime/executions][%d] listExecutions default  %+v", o._statusCode, o.Payload)
}
func (o *ListExecutionsDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListExecutionsDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
