// Code generated by go-swagger; DO NOT EDIT.

package runtimes

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// FailExecutionReader is a Reader for the FailExecution structure.
type FailExecutionReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *FailExecutionReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewFailExecutionOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewFailExecutionBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewFailExecutionUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewFailExecutionNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewFailExecutionDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewFailExecutionOK creates a FailExecutionOK with default headers values
func NewFailExecutionOK() *FailExecutionOK {
	return &FailExecutionOK{}
}

/*
	FailExecutionOK describes a response with status code 200, with default header values.

ok
*/
type FailExecutionOK struct {
}

func (o *FailExecutionOK) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/executions/{id}][%d] failExecutionOK ", 200)
}

func (o *FailExecutionOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewFailExecutionBadRequest creates a FailExecutionBadRequest with default headers values
func NewFailExecutionBadRequest() *FailExecutionBadRequest {
	return &FailExecutionBadRequest{}
}

/*
	FailExecutionBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type FailExecutionBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *FailExecutionBadRequest) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/executions/{id}][%d] failExecutionBadRequest  %+v", 400, o.Payload)
}
func (o *FailExecutionBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *FailExecutionBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewFailExecutionUnauthorized creates a FailExecutionUnauthorized with default headers values
func NewFailExecutionUnauthorized() *FailExecutionUnauthorized {
	return &FailExecutionUnauthorized{}
}

/*
	FailExecutionUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type FailExecutionUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *FailExecutionUnauthorized) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/executions/{id}][%d] failExecutionUnauthorized  %+v", 401, o.Payload)
}
func (o *FailExecutionUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *FailExecutionUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewFailExecutionNotFound creates a FailExecutionNotFound with default headers values
func NewFailExecutionNotFound() *FailExecutionNotFound {
	return &FailExecutionNotFound{}
}

/*
	FailExecutionNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type FailExecutionNotFound struct {
}

func (o *FailExecutionNotFound) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/executions/{id}][%d] failExecutionNotFound ", 404)
}

func (o *FailExecutionNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewFailExecutionDefault creates a FailExecutionDefault with default headers values
func NewFailExecutionDefault(code int) *FailExecutionDefault {
	return &FailExecutionDefault{
		_statusCode: code,
	}
}

/*
	FailExecutionDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type FailExecutionDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the fail execution default response
func (o *FailExecutionDefault) Code() int {
	return o._statusCode
}

func (o *FailExecutionDefault) Error() string {
	return fmt.Sprintf("[DELETE /api/runtime/executions/{id}][%d] failExecution default  %+v", o._statusCode, o.Payload)
}
func (o *FailExecutionDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *FailExecutionDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
