// Code generated by go-swagger; DO NOT EDIT.

package repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// GetProcessDefinitionReader is a Reader for the GetProcessDefinition structure.
type GetProcessDefinitionReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetProcessDefinitionReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetProcessDefinitionOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewGetProcessDefinitionBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewGetProcessDefinitionUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewGetProcessDefinitionNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewGetProcessDefinitionDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewGetProcessDefinitionOK creates a GetProcessDefinitionOK with default headers values
func NewGetProcessDefinitionOK() *GetProcessDefinitionOK {
	return &GetProcessDefinitionOK{}
}

/*
	GetProcessDefinitionOK describes a response with status code 200, with default header values.

OK
*/
type GetProcessDefinitionOK struct {
	Payload *models.ProcessDefinition
}

func (o *GetProcessDefinitionOK) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions/{id}][%d] getProcessDefinitionOK  %+v", 200, o.Payload)
}
func (o *GetProcessDefinitionOK) GetPayload() *models.ProcessDefinition {
	return o.Payload
}

func (o *GetProcessDefinitionOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ProcessDefinition)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetProcessDefinitionBadRequest creates a GetProcessDefinitionBadRequest with default headers values
func NewGetProcessDefinitionBadRequest() *GetProcessDefinitionBadRequest {
	return &GetProcessDefinitionBadRequest{}
}

/*
	GetProcessDefinitionBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type GetProcessDefinitionBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *GetProcessDefinitionBadRequest) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions/{id}][%d] getProcessDefinitionBadRequest  %+v", 400, o.Payload)
}
func (o *GetProcessDefinitionBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *GetProcessDefinitionBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetProcessDefinitionUnauthorized creates a GetProcessDefinitionUnauthorized with default headers values
func NewGetProcessDefinitionUnauthorized() *GetProcessDefinitionUnauthorized {
	return &GetProcessDefinitionUnauthorized{}
}

/*
	GetProcessDefinitionUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type GetProcessDefinitionUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *GetProcessDefinitionUnauthorized) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions/{id}][%d] getProcessDefinitionUnauthorized  %+v", 401, o.Payload)
}
func (o *GetProcessDefinitionUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *GetProcessDefinitionUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetProcessDefinitionNotFound creates a GetProcessDefinitionNotFound with default headers values
func NewGetProcessDefinitionNotFound() *GetProcessDefinitionNotFound {
	return &GetProcessDefinitionNotFound{}
}

/*
	GetProcessDefinitionNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type GetProcessDefinitionNotFound struct {
}

func (o *GetProcessDefinitionNotFound) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions/{id}][%d] getProcessDefinitionNotFound ", 404)
}

func (o *GetProcessDefinitionNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewGetProcessDefinitionDefault creates a GetProcessDefinitionDefault with default headers values
func NewGetProcessDefinitionDefault(code int) *GetProcessDefinitionDefault {
	return &GetProcessDefinitionDefault{
		_statusCode: code,
	}
}

/*
	GetProcessDefinitionDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type GetProcessDefinitionDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the get process definition default response
func (o *GetProcessDefinitionDefault) Code() int {
	return o._statusCode
}

func (o *GetProcessDefinitionDefault) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions/{id}][%d] getProcessDefinition default  %+v", o._statusCode, o.Payload)
}
func (o *GetProcessDefinitionDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *GetProcessDefinitionDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
