// Code generated by go-swagger; DO NOT EDIT.

package repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"qiniu.io/qbpm/sdk/models"
)

// AddProcessDefinitionReader is a Reader for the AddProcessDefinition structure.
type AddProcessDefinitionReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *AddProcessDefinitionReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewAddProcessDefinitionOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewAddProcessDefinitionBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewAddProcessDefinitionUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewAddProcessDefinitionNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewAddProcessDefinitionDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewAddProcessDefinitionOK creates a AddProcessDefinitionOK with default headers values
func NewAddProcessDefinitionOK() *AddProcessDefinitionOK {
	return &AddProcessDefinitionOK{}
}

/*
	AddProcessDefinitionOK describes a response with status code 200, with default header values.

OK
*/
type AddProcessDefinitionOK struct {
	Payload *AddProcessDefinitionOKBody
}

func (o *AddProcessDefinitionOK) Error() string {
	return fmt.Sprintf("[POST /api/repository/process-definitions][%d] addProcessDefinitionOK  %+v", 200, o.Payload)
}
func (o *AddProcessDefinitionOK) GetPayload() *AddProcessDefinitionOKBody {
	return o.Payload
}

func (o *AddProcessDefinitionOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(AddProcessDefinitionOKBody)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewAddProcessDefinitionBadRequest creates a AddProcessDefinitionBadRequest with default headers values
func NewAddProcessDefinitionBadRequest() *AddProcessDefinitionBadRequest {
	return &AddProcessDefinitionBadRequest{}
}

/*
	AddProcessDefinitionBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type AddProcessDefinitionBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *AddProcessDefinitionBadRequest) Error() string {
	return fmt.Sprintf("[POST /api/repository/process-definitions][%d] addProcessDefinitionBadRequest  %+v", 400, o.Payload)
}
func (o *AddProcessDefinitionBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *AddProcessDefinitionBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewAddProcessDefinitionUnauthorized creates a AddProcessDefinitionUnauthorized with default headers values
func NewAddProcessDefinitionUnauthorized() *AddProcessDefinitionUnauthorized {
	return &AddProcessDefinitionUnauthorized{}
}

/*
	AddProcessDefinitionUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type AddProcessDefinitionUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *AddProcessDefinitionUnauthorized) Error() string {
	return fmt.Sprintf("[POST /api/repository/process-definitions][%d] addProcessDefinitionUnauthorized  %+v", 401, o.Payload)
}
func (o *AddProcessDefinitionUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *AddProcessDefinitionUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewAddProcessDefinitionNotFound creates a AddProcessDefinitionNotFound with default headers values
func NewAddProcessDefinitionNotFound() *AddProcessDefinitionNotFound {
	return &AddProcessDefinitionNotFound{}
}

/*
	AddProcessDefinitionNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type AddProcessDefinitionNotFound struct {
}

func (o *AddProcessDefinitionNotFound) Error() string {
	return fmt.Sprintf("[POST /api/repository/process-definitions][%d] addProcessDefinitionNotFound ", 404)
}

func (o *AddProcessDefinitionNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewAddProcessDefinitionDefault creates a AddProcessDefinitionDefault with default headers values
func NewAddProcessDefinitionDefault(code int) *AddProcessDefinitionDefault {
	return &AddProcessDefinitionDefault{
		_statusCode: code,
	}
}

/*
	AddProcessDefinitionDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type AddProcessDefinitionDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the add process definition default response
func (o *AddProcessDefinitionDefault) Code() int {
	return o._statusCode
}

func (o *AddProcessDefinitionDefault) Error() string {
	return fmt.Sprintf("[POST /api/repository/process-definitions][%d] addProcessDefinition default  %+v", o._statusCode, o.Payload)
}
func (o *AddProcessDefinitionDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *AddProcessDefinitionDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

/*
AddProcessDefinitionOKBody add process definition o k body
swagger:model AddProcessDefinitionOKBody
*/
type AddProcessDefinitionOKBody struct {

	// the process definition int id
	ID int64 `json:"id,omitempty"`
}

// Validate validates this add process definition o k body
func (o *AddProcessDefinitionOKBody) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this add process definition o k body based on context it is used
func (o *AddProcessDefinitionOKBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (o *AddProcessDefinitionOKBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *AddProcessDefinitionOKBody) UnmarshalBinary(b []byte) error {
	var res AddProcessDefinitionOKBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}
