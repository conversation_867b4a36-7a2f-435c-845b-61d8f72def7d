// Code generated by go-swagger; DO NOT EDIT.

package repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// ListProcessDefinitionsReader is a Reader for the ListProcessDefinitions structure.
type ListProcessDefinitionsReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *ListProcessDefinitionsReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewListProcessDefinitionsOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewListProcessDefinitionsBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewListProcessDefinitionsUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewListProcessDefinitionsNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewListProcessDefinitionsDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewListProcessDefinitionsOK creates a ListProcessDefinitionsOK with default headers values
func NewListProcessDefinitionsOK() *ListProcessDefinitionsOK {
	return &ListProcessDefinitionsOK{}
}

/*
	ListProcessDefinitionsOK describes a response with status code 200, with default header values.

OK
*/
type ListProcessDefinitionsOK struct {
	Payload []*models.ProcessDefinition
}

func (o *ListProcessDefinitionsOK) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions][%d] listProcessDefinitionsOK  %+v", 200, o.Payload)
}
func (o *ListProcessDefinitionsOK) GetPayload() []*models.ProcessDefinition {
	return o.Payload
}

func (o *ListProcessDefinitionsOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListProcessDefinitionsBadRequest creates a ListProcessDefinitionsBadRequest with default headers values
func NewListProcessDefinitionsBadRequest() *ListProcessDefinitionsBadRequest {
	return &ListProcessDefinitionsBadRequest{}
}

/*
	ListProcessDefinitionsBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type ListProcessDefinitionsBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *ListProcessDefinitionsBadRequest) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions][%d] listProcessDefinitionsBadRequest  %+v", 400, o.Payload)
}
func (o *ListProcessDefinitionsBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListProcessDefinitionsBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListProcessDefinitionsUnauthorized creates a ListProcessDefinitionsUnauthorized with default headers values
func NewListProcessDefinitionsUnauthorized() *ListProcessDefinitionsUnauthorized {
	return &ListProcessDefinitionsUnauthorized{}
}

/*
	ListProcessDefinitionsUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type ListProcessDefinitionsUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *ListProcessDefinitionsUnauthorized) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions][%d] listProcessDefinitionsUnauthorized  %+v", 401, o.Payload)
}
func (o *ListProcessDefinitionsUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListProcessDefinitionsUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListProcessDefinitionsNotFound creates a ListProcessDefinitionsNotFound with default headers values
func NewListProcessDefinitionsNotFound() *ListProcessDefinitionsNotFound {
	return &ListProcessDefinitionsNotFound{}
}

/*
	ListProcessDefinitionsNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type ListProcessDefinitionsNotFound struct {
}

func (o *ListProcessDefinitionsNotFound) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions][%d] listProcessDefinitionsNotFound ", 404)
}

func (o *ListProcessDefinitionsNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewListProcessDefinitionsDefault creates a ListProcessDefinitionsDefault with default headers values
func NewListProcessDefinitionsDefault(code int) *ListProcessDefinitionsDefault {
	return &ListProcessDefinitionsDefault{
		_statusCode: code,
	}
}

/*
	ListProcessDefinitionsDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type ListProcessDefinitionsDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the list process definitions default response
func (o *ListProcessDefinitionsDefault) Code() int {
	return o._statusCode
}

func (o *ListProcessDefinitionsDefault) Error() string {
	return fmt.Sprintf("[GET /api/repository/process-definitions][%d] listProcessDefinitions default  %+v", o._statusCode, o.Payload)
}
func (o *ListProcessDefinitionsDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *ListProcessDefinitionsDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
