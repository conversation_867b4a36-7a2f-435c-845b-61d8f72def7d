// Code generated by go-swagger; DO NOT EDIT.

package repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"qiniu.io/qbpm/sdk/models"
)

// UpdateProcessDefinitionReader is a Reader for the UpdateProcessDefinition structure.
type UpdateProcessDefinitionReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *UpdateProcessDefinitionReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewUpdateProcessDefinitionOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewUpdateProcessDefinitionBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewUpdateProcessDefinitionUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewUpdateProcessDefinitionNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewUpdateProcessDefinitionDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewUpdateProcessDefinitionOK creates a UpdateProcessDefinitionOK with default headers values
func NewUpdateProcessDefinitionOK() *UpdateProcessDefinitionOK {
	return &UpdateProcessDefinitionOK{}
}

/*
	UpdateProcessDefinitionOK describes a response with status code 200, with default header values.

OK
*/
type UpdateProcessDefinitionOK struct {
}

func (o *UpdateProcessDefinitionOK) Error() string {
	return fmt.Sprintf("[PUT /api/repository/process-definitions/{id}][%d] updateProcessDefinitionOK ", 200)
}

func (o *UpdateProcessDefinitionOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewUpdateProcessDefinitionBadRequest creates a UpdateProcessDefinitionBadRequest with default headers values
func NewUpdateProcessDefinitionBadRequest() *UpdateProcessDefinitionBadRequest {
	return &UpdateProcessDefinitionBadRequest{}
}

/*
	UpdateProcessDefinitionBadRequest describes a response with status code 400, with default header values.

Bad request was submitted
*/
type UpdateProcessDefinitionBadRequest struct {
	Payload *models.ErrorResponse
}

func (o *UpdateProcessDefinitionBadRequest) Error() string {
	return fmt.Sprintf("[PUT /api/repository/process-definitions/{id}][%d] updateProcessDefinitionBadRequest  %+v", 400, o.Payload)
}
func (o *UpdateProcessDefinitionBadRequest) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *UpdateProcessDefinitionBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewUpdateProcessDefinitionUnauthorized creates a UpdateProcessDefinitionUnauthorized with default headers values
func NewUpdateProcessDefinitionUnauthorized() *UpdateProcessDefinitionUnauthorized {
	return &UpdateProcessDefinitionUnauthorized{}
}

/*
	UpdateProcessDefinitionUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type UpdateProcessDefinitionUnauthorized struct {
	Payload *models.ErrorResponse
}

func (o *UpdateProcessDefinitionUnauthorized) Error() string {
	return fmt.Sprintf("[PUT /api/repository/process-definitions/{id}][%d] updateProcessDefinitionUnauthorized  %+v", 401, o.Payload)
}
func (o *UpdateProcessDefinitionUnauthorized) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *UpdateProcessDefinitionUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewUpdateProcessDefinitionNotFound creates a UpdateProcessDefinitionNotFound with default headers values
func NewUpdateProcessDefinitionNotFound() *UpdateProcessDefinitionNotFound {
	return &UpdateProcessDefinitionNotFound{}
}

/*
	UpdateProcessDefinitionNotFound describes a response with status code 404, with default header values.

The specified resource was not found2
*/
type UpdateProcessDefinitionNotFound struct {
}

func (o *UpdateProcessDefinitionNotFound) Error() string {
	return fmt.Sprintf("[PUT /api/repository/process-definitions/{id}][%d] updateProcessDefinitionNotFound ", 404)
}

func (o *UpdateProcessDefinitionNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewUpdateProcessDefinitionDefault creates a UpdateProcessDefinitionDefault with default headers values
func NewUpdateProcessDefinitionDefault(code int) *UpdateProcessDefinitionDefault {
	return &UpdateProcessDefinitionDefault{
		_statusCode: code,
	}
}

/*
	UpdateProcessDefinitionDefault describes a response with status code -1, with default header values.

Unexpected error
*/
type UpdateProcessDefinitionDefault struct {
	_statusCode int

	Payload *models.ErrorResponse
}

// Code gets the status code for the update process definition default response
func (o *UpdateProcessDefinitionDefault) Code() int {
	return o._statusCode
}

func (o *UpdateProcessDefinitionDefault) Error() string {
	return fmt.Sprintf("[PUT /api/repository/process-definitions/{id}][%d] updateProcessDefinition default  %+v", o._statusCode, o.Payload)
}
func (o *UpdateProcessDefinitionDefault) GetPayload() *models.ErrorResponse {
	return o.Payload
}

func (o *UpdateProcessDefinitionDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.ErrorResponse)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
