// Code generated by go-swagger; DO NOT EDIT.

package repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"qiniu.io/qbpm/sdk/models"
)

// NewUpdateProcessDefinitionParams creates a new UpdateProcessDefinitionParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewUpdateProcessDefinitionParams() *UpdateProcessDefinitionParams {
	return &UpdateProcessDefinitionParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewUpdateProcessDefinitionParamsWithTimeout creates a new UpdateProcessDefinitionParams object
// with the ability to set a timeout on a request.
func NewUpdateProcessDefinitionParamsWithTimeout(timeout time.Duration) *UpdateProcessDefinitionParams {
	return &UpdateProcessDefinitionParams{
		timeout: timeout,
	}
}

// NewUpdateProcessDefinitionParamsWithContext creates a new UpdateProcessDefinitionParams object
// with the ability to set a context for a request.
func NewUpdateProcessDefinitionParamsWithContext(ctx context.Context) *UpdateProcessDefinitionParams {
	return &UpdateProcessDefinitionParams{
		Context: ctx,
	}
}

// NewUpdateProcessDefinitionParamsWithHTTPClient creates a new UpdateProcessDefinitionParams object
// with the ability to set a custom HTTPClient for a request.
func NewUpdateProcessDefinitionParamsWithHTTPClient(client *http.Client) *UpdateProcessDefinitionParams {
	return &UpdateProcessDefinitionParams{
		HTTPClient: client,
	}
}

/*
UpdateProcessDefinitionParams contains all the parameters to send to the API endpoint

	for the update process definition operation.

	Typically these are written to a http.Request.
*/
type UpdateProcessDefinitionParams struct {

	// Data.
	Data *models.UpdateProcessDefinitionInput

	/* ID.

	   the process definition id

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the update process definition params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *UpdateProcessDefinitionParams) WithDefaults() *UpdateProcessDefinitionParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the update process definition params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *UpdateProcessDefinitionParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the update process definition params
func (o *UpdateProcessDefinitionParams) WithTimeout(timeout time.Duration) *UpdateProcessDefinitionParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the update process definition params
func (o *UpdateProcessDefinitionParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the update process definition params
func (o *UpdateProcessDefinitionParams) WithContext(ctx context.Context) *UpdateProcessDefinitionParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the update process definition params
func (o *UpdateProcessDefinitionParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the update process definition params
func (o *UpdateProcessDefinitionParams) WithHTTPClient(client *http.Client) *UpdateProcessDefinitionParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the update process definition params
func (o *UpdateProcessDefinitionParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithData adds the data to the update process definition params
func (o *UpdateProcessDefinitionParams) WithData(data *models.UpdateProcessDefinitionInput) *UpdateProcessDefinitionParams {
	o.SetData(data)
	return o
}

// SetData adds the data to the update process definition params
func (o *UpdateProcessDefinitionParams) SetData(data *models.UpdateProcessDefinitionInput) {
	o.Data = data
}

// WithID adds the id to the update process definition params
func (o *UpdateProcessDefinitionParams) WithID(id int64) *UpdateProcessDefinitionParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the update process definition params
func (o *UpdateProcessDefinitionParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *UpdateProcessDefinitionParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error
	if o.Data != nil {
		if err := r.SetBodyParam(o.Data); err != nil {
			return err
		}
	}

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
