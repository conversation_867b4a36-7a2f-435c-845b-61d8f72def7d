// Code generated by go-swagger; DO NOT EDIT.

package repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetProcessDefinitionParams creates a new GetProcessDefinitionParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetProcessDefinitionParams() *GetProcessDefinitionParams {
	return &GetProcessDefinitionParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetProcessDefinitionParamsWithTimeout creates a new GetProcessDefinitionParams object
// with the ability to set a timeout on a request.
func NewGetProcessDefinitionParamsWithTimeout(timeout time.Duration) *GetProcessDefinitionParams {
	return &GetProcessDefinitionParams{
		timeout: timeout,
	}
}

// NewGetProcessDefinitionParamsWithContext creates a new GetProcessDefinitionParams object
// with the ability to set a context for a request.
func NewGetProcessDefinitionParamsWithContext(ctx context.Context) *GetProcessDefinitionParams {
	return &GetProcessDefinitionParams{
		Context: ctx,
	}
}

// NewGetProcessDefinitionParamsWithHTTPClient creates a new GetProcessDefinitionParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetProcessDefinitionParamsWithHTTPClient(client *http.Client) *GetProcessDefinitionParams {
	return &GetProcessDefinitionParams{
		HTTPClient: client,
	}
}

/*
GetProcessDefinitionParams contains all the parameters to send to the API endpoint

	for the get process definition operation.

	Typically these are written to a http.Request.
*/
type GetProcessDefinitionParams struct {

	/* ID.

	   the process definition id

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get process definition params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetProcessDefinitionParams) WithDefaults() *GetProcessDefinitionParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get process definition params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetProcessDefinitionParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get process definition params
func (o *GetProcessDefinitionParams) WithTimeout(timeout time.Duration) *GetProcessDefinitionParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get process definition params
func (o *GetProcessDefinitionParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get process definition params
func (o *GetProcessDefinitionParams) WithContext(ctx context.Context) *GetProcessDefinitionParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get process definition params
func (o *GetProcessDefinitionParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get process definition params
func (o *GetProcessDefinitionParams) WithHTTPClient(client *http.Client) *GetProcessDefinitionParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get process definition params
func (o *GetProcessDefinitionParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithID adds the id to the get process definition params
func (o *GetProcessDefinitionParams) WithID(id int64) *GetProcessDefinitionParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the get process definition params
func (o *GetProcessDefinitionParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *GetProcessDefinitionParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
