// Code generated by go-swagger; DO NOT EDIT.

package repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewAddProcessDefinitionParams creates a new AddProcessDefinitionParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewAddProcessDefinitionParams() *AddProcessDefinitionParams {
	return &AddProcessDefinitionParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewAddProcessDefinitionParamsWithTimeout creates a new AddProcessDefinitionParams object
// with the ability to set a timeout on a request.
func NewAddProcessDefinitionParamsWithTimeout(timeout time.Duration) *AddProcessDefinitionParams {
	return &AddProcessDefinitionParams{
		timeout: timeout,
	}
}

// NewAddProcessDefinitionParamsWithContext creates a new AddProcessDefinitionParams object
// with the ability to set a context for a request.
func NewAddProcessDefinitionParamsWithContext(ctx context.Context) *AddProcessDefinitionParams {
	return &AddProcessDefinitionParams{
		Context: ctx,
	}
}

// NewAddProcessDefinitionParamsWithHTTPClient creates a new AddProcessDefinitionParams object
// with the ability to set a custom HTTPClient for a request.
func NewAddProcessDefinitionParamsWithHTTPClient(client *http.Client) *AddProcessDefinitionParams {
	return &AddProcessDefinitionParams{
		HTTPClient: client,
	}
}

/*
AddProcessDefinitionParams contains all the parameters to send to the API endpoint

	for the add process definition operation.

	Typically these are written to a http.Request.
*/
type AddProcessDefinitionParams struct {

	/* Active.

	   process definition is active
	*/
	Active *bool

	/* Data.

	   xml definition file
	*/
	Data runtime.NamedReadCloser

	/* DeployAt.

	   process definition deploy at

	   Format: date-time
	*/
	DeployAt *strfmt.DateTime

	/* Description.

	   process definition description
	*/
	Description *string

	/* Key.

	   process definition key
	*/
	Key string

	/* Name.

	   process definition name
	*/
	Name *string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the add process definition params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *AddProcessDefinitionParams) WithDefaults() *AddProcessDefinitionParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the add process definition params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *AddProcessDefinitionParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the add process definition params
func (o *AddProcessDefinitionParams) WithTimeout(timeout time.Duration) *AddProcessDefinitionParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the add process definition params
func (o *AddProcessDefinitionParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the add process definition params
func (o *AddProcessDefinitionParams) WithContext(ctx context.Context) *AddProcessDefinitionParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the add process definition params
func (o *AddProcessDefinitionParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the add process definition params
func (o *AddProcessDefinitionParams) WithHTTPClient(client *http.Client) *AddProcessDefinitionParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the add process definition params
func (o *AddProcessDefinitionParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithActive adds the active to the add process definition params
func (o *AddProcessDefinitionParams) WithActive(active *bool) *AddProcessDefinitionParams {
	o.SetActive(active)
	return o
}

// SetActive adds the active to the add process definition params
func (o *AddProcessDefinitionParams) SetActive(active *bool) {
	o.Active = active
}

// WithData adds the data to the add process definition params
func (o *AddProcessDefinitionParams) WithData(data runtime.NamedReadCloser) *AddProcessDefinitionParams {
	o.SetData(data)
	return o
}

// SetData adds the data to the add process definition params
func (o *AddProcessDefinitionParams) SetData(data runtime.NamedReadCloser) {
	o.Data = data
}

// WithDeployAt adds the deployAt to the add process definition params
func (o *AddProcessDefinitionParams) WithDeployAt(deployAt *strfmt.DateTime) *AddProcessDefinitionParams {
	o.SetDeployAt(deployAt)
	return o
}

// SetDeployAt adds the deployAt to the add process definition params
func (o *AddProcessDefinitionParams) SetDeployAt(deployAt *strfmt.DateTime) {
	o.DeployAt = deployAt
}

// WithDescription adds the description to the add process definition params
func (o *AddProcessDefinitionParams) WithDescription(description *string) *AddProcessDefinitionParams {
	o.SetDescription(description)
	return o
}

// SetDescription adds the description to the add process definition params
func (o *AddProcessDefinitionParams) SetDescription(description *string) {
	o.Description = description
}

// WithKey adds the key to the add process definition params
func (o *AddProcessDefinitionParams) WithKey(key string) *AddProcessDefinitionParams {
	o.SetKey(key)
	return o
}

// SetKey adds the key to the add process definition params
func (o *AddProcessDefinitionParams) SetKey(key string) {
	o.Key = key
}

// WithName adds the name to the add process definition params
func (o *AddProcessDefinitionParams) WithName(name *string) *AddProcessDefinitionParams {
	o.SetName(name)
	return o
}

// SetName adds the name to the add process definition params
func (o *AddProcessDefinitionParams) SetName(name *string) {
	o.Name = name
}

// WriteToRequest writes these params to a swagger request
func (o *AddProcessDefinitionParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.Active != nil {

		// form param active
		var frActive bool
		if o.Active != nil {
			frActive = *o.Active
		}
		fActive := swag.FormatBool(frActive)
		if fActive != "" {
			if err := r.SetFormParam("active", fActive); err != nil {
				return err
			}
		}
	}
	// form file param data
	if err := r.SetFileParam("data", o.Data); err != nil {
		return err
	}

	if o.DeployAt != nil {

		// form param deploy_at
		var frDeployAt strfmt.DateTime
		if o.DeployAt != nil {
			frDeployAt = *o.DeployAt
		}
		fDeployAt := frDeployAt.String()
		if fDeployAt != "" {
			if err := r.SetFormParam("deploy_at", fDeployAt); err != nil {
				return err
			}
		}
	}

	if o.Description != nil {

		// form param description
		var frDescription string
		if o.Description != nil {
			frDescription = *o.Description
		}
		fDescription := frDescription
		if fDescription != "" {
			if err := r.SetFormParam("description", fDescription); err != nil {
				return err
			}
		}
	}

	// form param key
	frKey := o.Key
	fKey := frKey
	if fKey != "" {
		if err := r.SetFormParam("key", fKey); err != nil {
			return err
		}
	}

	if o.Name != nil {

		// form param name
		var frName string
		if o.Name != nil {
			frName = *o.Name
		}
		fName := frName
		if fName != "" {
			if err := r.SetFormParam("name", fName); err != nil {
				return err
			}
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
