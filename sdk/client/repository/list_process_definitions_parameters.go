// Code generated by go-swagger; DO NOT EDIT.

package repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewListProcessDefinitionsParams creates a new ListProcessDefinitionsParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListProcessDefinitionsParams() *ListProcessDefinitionsParams {
	return &ListProcessDefinitionsParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListProcessDefinitionsParamsWithTimeout creates a new ListProcessDefinitionsParams object
// with the ability to set a timeout on a request.
func NewListProcessDefinitionsParamsWithTimeout(timeout time.Duration) *ListProcessDefinitionsParams {
	return &ListProcessDefinitionsParams{
		timeout: timeout,
	}
}

// NewListProcessDefinitionsParamsWithContext creates a new ListProcessDefinitionsParams object
// with the ability to set a context for a request.
func NewListProcessDefinitionsParamsWithContext(ctx context.Context) *ListProcessDefinitionsParams {
	return &ListProcessDefinitionsParams{
		Context: ctx,
	}
}

// NewListProcessDefinitionsParamsWithHTTPClient creates a new ListProcessDefinitionsParams object
// with the ability to set a custom HTTPClient for a request.
func NewListProcessDefinitionsParamsWithHTTPClient(client *http.Client) *ListProcessDefinitionsParams {
	return &ListProcessDefinitionsParams{
		HTTPClient: client,
	}
}

/*
ListProcessDefinitionsParams contains all the parameters to send to the API endpoint

	for the list process definitions operation.

	Typically these are written to a http.Request.
*/
type ListProcessDefinitionsParams struct {

	/* Key.

	   process definition key
	*/
	Key *string

	/* Name.

	   process definition name
	*/
	Name *string

	/* Page.

	   page index for queries, 0 is default

	   Format: int32
	*/
	Page *int32

	/* PageSize.

	   page size for queries, 200 is default, max value 1000 is limited

	   Format: int32
	*/
	PageSize *int32

	/* Status.

	   process definition status, 0 is newly, 1 is active and 2 for suspended

	   Format: int32
	*/
	Status *int32

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list process definitions params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListProcessDefinitionsParams) WithDefaults() *ListProcessDefinitionsParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list process definitions params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListProcessDefinitionsParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the list process definitions params
func (o *ListProcessDefinitionsParams) WithTimeout(timeout time.Duration) *ListProcessDefinitionsParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list process definitions params
func (o *ListProcessDefinitionsParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list process definitions params
func (o *ListProcessDefinitionsParams) WithContext(ctx context.Context) *ListProcessDefinitionsParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list process definitions params
func (o *ListProcessDefinitionsParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list process definitions params
func (o *ListProcessDefinitionsParams) WithHTTPClient(client *http.Client) *ListProcessDefinitionsParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list process definitions params
func (o *ListProcessDefinitionsParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithKey adds the key to the list process definitions params
func (o *ListProcessDefinitionsParams) WithKey(key *string) *ListProcessDefinitionsParams {
	o.SetKey(key)
	return o
}

// SetKey adds the key to the list process definitions params
func (o *ListProcessDefinitionsParams) SetKey(key *string) {
	o.Key = key
}

// WithName adds the name to the list process definitions params
func (o *ListProcessDefinitionsParams) WithName(name *string) *ListProcessDefinitionsParams {
	o.SetName(name)
	return o
}

// SetName adds the name to the list process definitions params
func (o *ListProcessDefinitionsParams) SetName(name *string) {
	o.Name = name
}

// WithPage adds the page to the list process definitions params
func (o *ListProcessDefinitionsParams) WithPage(page *int32) *ListProcessDefinitionsParams {
	o.SetPage(page)
	return o
}

// SetPage adds the page to the list process definitions params
func (o *ListProcessDefinitionsParams) SetPage(page *int32) {
	o.Page = page
}

// WithPageSize adds the pageSize to the list process definitions params
func (o *ListProcessDefinitionsParams) WithPageSize(pageSize *int32) *ListProcessDefinitionsParams {
	o.SetPageSize(pageSize)
	return o
}

// SetPageSize adds the pageSize to the list process definitions params
func (o *ListProcessDefinitionsParams) SetPageSize(pageSize *int32) {
	o.PageSize = pageSize
}

// WithStatus adds the status to the list process definitions params
func (o *ListProcessDefinitionsParams) WithStatus(status *int32) *ListProcessDefinitionsParams {
	o.SetStatus(status)
	return o
}

// SetStatus adds the status to the list process definitions params
func (o *ListProcessDefinitionsParams) SetStatus(status *int32) {
	o.Status = status
}

// WriteToRequest writes these params to a swagger request
func (o *ListProcessDefinitionsParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.Key != nil {

		// query param key
		var qrKey string

		if o.Key != nil {
			qrKey = *o.Key
		}
		qKey := qrKey
		if qKey != "" {

			if err := r.SetQueryParam("key", qKey); err != nil {
				return err
			}
		}
	}

	if o.Name != nil {

		// query param name
		var qrName string

		if o.Name != nil {
			qrName = *o.Name
		}
		qName := qrName
		if qName != "" {

			if err := r.SetQueryParam("name", qName); err != nil {
				return err
			}
		}
	}

	if o.Page != nil {

		// query param page
		var qrPage int32

		if o.Page != nil {
			qrPage = *o.Page
		}
		qPage := swag.FormatInt32(qrPage)
		if qPage != "" {

			if err := r.SetQueryParam("page", qPage); err != nil {
				return err
			}
		}
	}

	if o.PageSize != nil {

		// query param page_size
		var qrPageSize int32

		if o.PageSize != nil {
			qrPageSize = *o.PageSize
		}
		qPageSize := swag.FormatInt32(qrPageSize)
		if qPageSize != "" {

			if err := r.SetQueryParam("page_size", qPageSize); err != nil {
				return err
			}
		}
	}

	if o.Status != nil {

		// query param status
		var qrStatus int32

		if o.Status != nil {
			qrStatus = *o.Status
		}
		qStatus := swag.FormatInt32(qrStatus)
		if qStatus != "" {

			if err := r.SetQueryParam("status", qStatus); err != nil {
				return err
			}
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
