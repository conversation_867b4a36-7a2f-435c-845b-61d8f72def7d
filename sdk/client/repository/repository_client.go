// Code generated by go-swagger; DO NOT EDIT.

package repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new repository API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for repository API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

// ClientService is the interface for Client methods
type ClientService interface {
	AddProcessDefinition(params *AddProcessDefinitionParams, opts ...ClientOption) (*AddProcessDefinitionOK, error)

	GetProcessDefinition(params *GetProcessDefinitionParams, opts ...ClientOption) (*GetProcessDefinitionOK, error)

	ListProcessDefinitions(params *ListProcessDefinitionsParams, opts ...ClientOption) (*ListProcessDefinitionsOK, error)

	UpdateProcessDefinition(params *UpdateProcessDefinitionParams, opts ...ClientOption) (*UpdateProcessDefinitionOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
AddProcessDefinition adds a new process definition to the store
*/
func (a *Client) AddProcessDefinition(params *AddProcessDefinitionParams, opts ...ClientOption) (*AddProcessDefinitionOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewAddProcessDefinitionParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "addProcessDefinition",
		Method:             "POST",
		PathPattern:        "/api/repository/process-definitions",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"multipart/form-data"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &AddProcessDefinitionReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*AddProcessDefinitionOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*AddProcessDefinitionDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
GetProcessDefinition gets a process definition from the store
*/
func (a *Client) GetProcessDefinition(params *GetProcessDefinitionParams, opts ...ClientOption) (*GetProcessDefinitionOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetProcessDefinitionParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "getProcessDefinition",
		Method:             "GET",
		PathPattern:        "/api/repository/process-definitions/{id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetProcessDefinitionReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetProcessDefinitionOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*GetProcessDefinitionDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
ListProcessDefinitions lists process definitions
*/
func (a *Client) ListProcessDefinitions(params *ListProcessDefinitionsParams, opts ...ClientOption) (*ListProcessDefinitionsOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListProcessDefinitionsParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "listProcessDefinitions",
		Method:             "GET",
		PathPattern:        "/api/repository/process-definitions",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListProcessDefinitionsReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListProcessDefinitionsOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*ListProcessDefinitionsDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

/*
UpdateProcessDefinition updates a process definition to the store
*/
func (a *Client) UpdateProcessDefinition(params *UpdateProcessDefinitionParams, opts ...ClientOption) (*UpdateProcessDefinitionOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewUpdateProcessDefinitionParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "updateProcessDefinition",
		Method:             "PUT",
		PathPattern:        "/api/repository/process-definitions/{id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &UpdateProcessDefinitionReader{formats: a.formats},
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*UpdateProcessDefinitionOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	unexpectedSuccess := result.(*UpdateProcessDefinitionDefault)
	return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
