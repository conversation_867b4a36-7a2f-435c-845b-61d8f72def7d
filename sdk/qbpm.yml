swagger: "2.0"
info:
  description: qbpm API doc
  version: "1.0.0"
  title: qbpm api
tags:
- name: repository
  description: process definition store
- name: runtimes
  description: everything about runtime execution
- name: history
  description: about execute history
schemes:
- http
- https
paths:
  /api/repository/process-definitions:
    post:
      tags:
      - repository
      summary: Add a new process definition to the store
      operationId: addProcessDefinition
      consumes:
      - multipart/form-data
      produces:
      - application/json
      parameters:
        - in: formData
          name: data
          description: xml definition file
          required: true
          type: file
        - in: formData
          name: name
          description: process definition name
          type: string
        - in: formData
          name: key
          description: process definition key
          type: string
          required: true
        - in: formData
          name: deploy_at
          description: process definition deploy at
          type: string
          format: date-time
        - in: formData
          name: active
          description: process definition is active
          type: boolean
        - in: formData
          name: description
          description: process definition description
          type: string
      responses:
        200:
          description: OK
          schema:
            type: object
            properties:
              id:
                type: integer
                format: int64
                description: the process definition int id
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

    get:
      tags:
        - repository
      summary: list process definitions
      operationId: listProcessDefinitions
      produces:
        - application/json
      parameters:
        - in: query
          name: key
          description: process definition key
          type: string
        - in: query
          name: name
          description: process definition name
          type: string
        - in: query
          name: status
          description: process definition status, 0 is newly, 1 is active and 2 for suspended
          type: integer
          format: int32
          minimum: 1
          maximum: 3
        - in: query
          name: page
          description: page index for queries, 0 is default
          type: integer
          format: int32
          minimum: 0
        - in: query
          name: page_size
          description: page size for queries, 200 is default, max value 1000 is limited
          type: integer
          format: int32
          minimum: 0
      responses:
        200:
          description: OK
          schema:
            type: array
            items:
              $ref: "#/definitions/ProcessDefinition"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

  /api/repository/process-definitions/{id}:
    get:
      tags:
        - repository
      summary: Get a process definition from the store
      operationId: getProcessDefinition
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: path
          name: id
          type: integer
          format: int64
          required: true
          description: the process definition id
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/ProcessDefinition"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

    put:
      tags:
      - repository
      summary: Update a process definition to the store
      operationId: updateProcessDefinition
      consumes:
      - application/json
      produces:
      - application/json
      parameters:
        - in: path
          name: id
          type: integer
          format: int64
          required: true
          description: the process definition id
        - in: body
          name: data
          required: true
          schema:
            $ref: "#/definitions/UpdateProcessDefinitionInput"
      responses:
        200:
          description: OK
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

  /api/runtime/executions:
    put:
      tags:
        - runtimes
      summary: Start a new process
      operationId: startProcess
      consumes:
      - application/json
      produces:
      - application/json
      parameters:
        - in: body
          name: data
          required: true
          schema:
            $ref: "#/definitions/StartProcessInput"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/RunExecution"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

    get:
      tags:
        - runtimes
      summary: List executions
      operationId: listExecutions
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: query
          name: process_definition_id
          description: process definition id
          type: integer
          format: int64
        - in: query
          name: process_definition_key
          description: process definition key
          type: string
        - in: query
          name: process_instance_id
          description: process instance id
          type: integer
          format: int64
        - in: query
          name: activity_define_key
          description: activity definition key
          type: string
        - in: query
          name: excode
          description: external business code
          type: string
        - in: query
          name: uid
          description: uid
          type: integer
          format: int64
        - in: query
          name: status
          description: the executions status
          type: integer
          format: int32
          minimum: 1
          maximum: 6
        - in: query
          name: original_assignees
          description: the executions original_assignees
          type: array
          collectionFormat: multi
          items:
            type: string
        - in: query
          name: assignees
          description: the executions assignees
          type: array
          collectionFormat: multi
          items:
            type: string
        - in: query
          name: starters
          description: the executions starters
          type: array
          collectionFormat: multi
          items:
            type: string
        - in: query
          name: page
          description: page index for queries, 0 is default
          type: integer
          format: int32
          minimum: 0
        - in: query
          name: page_size
          description: page size for queries, 200 is default, max value 1000 is limited
          type: integer
          format: int32
          minimum: 0
      responses:
        200:
          description: OK
          schema:
            type: array
            items:
              $ref: "#/definitions/RunExecution"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

  /api/runtime/executions/{id}:
    get:
      tags:
        - "runtimes"
      summary: "get a execution"
      operationId: "getExecution"
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "path"
        description: "the run execution id"
        required: true
        type: "integer"
        format: "int64"
        x-exportParamName: "Id"
      responses:
        200:
          description: "ok"
          schema:
            $ref: "#/definitions/RunExecution"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

    post:
      tags:
      - runtimes
      summary: complete a run execution
      operationId: completeExecution
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: path
          name: id
          description: the run execution id
          type: integer
          format: int64
          required: true
        - in: body
          name: data
          required: true
          schema:
            $ref: "#/definitions/OperateExecutionInput"
      responses:
        200:
          description: OK
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

    delete:
      tags:
        - runtimes
      summary: fail a execution
      operationId: failExecution
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: path
          name: id
          description: the run execution id
          type: integer
          format: int64
          required: true
        - in: body
          name: data
          required: true
          schema:
            $ref: "#/definitions/OperateExecutionInput"
      responses:
        200:
          description: ok
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

    patch:
      tags:
        - runtimes
      summary: Assign a run execution
      operationId: assign
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: path
          name: id
          description: the run execution id
          type: integer
          format: int64
          required: true
        - in: body
          name: data
          required: true
          schema:
            $ref: "#/definitions/AssigneeInput"
      responses:
        200:
          description: OK
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

  /api/runtime/processes:
    get:
      tags:
        - runtimes
      summary: List runtime processes
      operationId: listRuntimeProcesses
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: query
          name: process_definition_id
          description: process definition id
          type: integer
          format: int64
        - in: query
          name: process_definition_key
          description: process definition key
          type: string
        - in: query
          name: process_instance_id
          description: process instance id
          type: integer
          format: int64
        - in: query
          name: excode
          description: external business code
          type: string
        - in: query
          name: uid
          description: uid
          type: integer
          format: int64
        - in: query
          name: status
          description: the executions status
          type: integer
          format: int32
          minimum: 1
          maximum: 6
        - in: query
          name: original_assignees
          description: the executions original_assignees
          type: array
          collectionFormat: multi
          items:
            type: string
        - in: query
          name: assignees
          description: the executions assignees
          type: array
          collectionFormat: multi
          items:
            type: string
        - in: query
          name: starters
          description: the executions starters
          type: array
          collectionFormat: multi
          items:
            type: string
        - in: query
          name: page
          description: page index for queries, 0 is default
          type: integer
          format: int32
          minimum: 0
        - in: query
          name: page_size
          description: page size for queries, 200 is default, max value 1000 is limited
          type: integer
          format: int32
          minimum: 0
      responses:
        200:
          description: OK
          schema:
            type: array
            items:
              $ref: "#/definitions/RunExecution"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

  /api/runtime/processes/{id}:
    delete:
      tags:
        - runtimes
      summary: Cancel a process
      operationId: cancelProcess
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: path
          name: id
          description: the run execution id
          type: integer
          format: int64
          required: true
        - in: body
          name: data
          required: true
          schema:
            $ref: "#/definitions/CancelProcessInput"
      responses:
        200:
          description: OK
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

  /api/history/processes:
    get:
      tags:
        - history
      summary: List processes
      operationId: listProcesses
      consumes:
        - multipart/form-data
      produces:
        - application/json
      parameters:
        - in: query
          name: process_definition_id
          description: process definition id
          type: integer
          format: int64
        - in: query
          name: process_definition_key
          description: process definition key
          type: string
        - in: query
          name: process_instance_id
          description: process instance id
          type: integer
          format: int64
        - in: query
          name: excode
          description: external business code
          type: string
        - in: query
          name: uid
          description: uid
          type: integer
          format: int64
        - in: query
          name: assignees
          description: the executions assignees
          type: array
          collectionFormat: multi
          items:
            type: string
        - in: query
          name: status
          description: the process instance status
          type: integer
          format: int32
          minimum: 1
          maximum: 5
        - in: query
          name: starters
          description: the executions starters
          type: array
          collectionFormat: multi
          items:
            type: string
        - in: query
          name: page
          description: page index for queries, 0 is default
          type: integer
          format: int32
          minimum: 0
        - in: query
          name: page_size
          description: page size for queries, 200 is default, max value 1000 is limited
          type: integer
          format: int32
          minimum: 0
      responses:
        200:
          description: OK
          schema:
            type: array
            items:
              $ref: "#/definitions/ProcessInstance"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

  /api/history/processes/{id}:
    get:
      tags:
        - history
      summary: Get a process
      operationId: getProcess
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: path
          name: id
          description: the process id
          type: integer
          format: int64
          required: true
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/ProcessInstance"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

  /api/history/processes/{id}/activities:
    get:
      tags:
      - history
      summary: List process activities
      operationId: listProcessActivities
      consumes:
      - application/json
      produces:
      - application/json
      parameters:
        - in: path
          name: id
          description: the process id
          type: integer
          format: int64
          required: true
      responses:
        200:
          description: OK
          schema:
            type: array
            items:
              $ref: "#/definitions/ActivityInstance"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'

  /api/history/activities:
    get:
      tags:
      - history
      summary: List activities
      operationId: listActivities
      consumes:
      - application/json
      produces:
      - application/json
      parameters:
        - in: query
          name: process_instance_id
          description: process instance id
          type: integer
          format: int64
        - in: query
          name: excode
          type: string
          description: external business code
        - in: query
          name: status
          description: process activity status
          type: integer
          format: int32
          minimum: 1
          maximum: 5
        - in: query
          name: types
          description: process activity types
          type: array
          collectionFormat: multi
          items:
            type: string
        - in: query
          name: page
          description: page index for queries, 0 is default
          type: integer
          format: int32
          minimum: 0
        - in: query
          name: page_size
          description: page size for queries, 200 is default, max value 1000 is limited
          type: integer
          format: int32
          minimum: 0
      responses:
        200:
          description: OK
          schema:
            type: array
            items:
              $ref: "#/definitions/ActivityInstance"
        400:
          $ref: '#/responses/BadRequest'
        401:
          $ref: '#/responses/Unauthorized'
        404:
          $ref: '#/responses/NotFound'
        default:
          $ref: '#/responses/UnexpectedError'
          
# Descriptions of common responses
responses:
  BadRequest:
    description: Bad request was submitted
    schema:
      $ref: '#/definitions/ErrorResponse'
  NotFound:
    description: The specified resource was not found2
    # schema:
    #   $ref: '#/definitions/ErrorResponse'
  Unauthorized:
    description: Unauthorized
    schema:
      $ref: '#/definitions/ErrorResponse'
  UnexpectedError:
    description: Unexpected error
    schema:
      $ref: '#/definitions/ErrorResponse'

definitions:
  ErrorResponse:
    type: object
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string
  ProcessDefinition:
    type: object
    properties:
      id:
        type: integer
        format: int64
      created_at:
        type: string
        format: date-time
      updated_at:
        type: string
        format: date-time
      key:
        type: string
      version:
        type: integer
        format: int32
      name:
        type: string
      status:
        type: integer
        format: int32
      description:
        type: string
      xml_data:
        type: string
      deploy_at:
        type: string
        format: date-time
  ProcessInstance:
    type: object
    properties:
      id:
        type: integer
        format: int64
      created_at:
        type: string
        format: date-time
      updated_at:
        type: string
        format: date-time
      process_definition_id:
        type: integer
        format: int64
      super_process_instance_id:
        type: integer
        format: int64
      name:
        type: string
      excode:
        type: string
      uid:
        type: integer
        format: int64
      params:
        type: string
      start_by_id:
        type: string
      start_at:
        type: string
        format: date-time
      end_at:
        type: string
        format: date-time
      status:
        type: integer
        format: int32
  RunExecution:
    type: object
    properties:
      id:
        type: integer
        format: int64
      created_at:
        type: string
        format: date-time
      updated_at:
        type: string
        format: date-time
      process_definition_id:
        type: integer
        format: int64
      process_instance_id:
        type: integer
        format: int64
      activity_define_key:
        type: string
      name:
        type: string
      excode:
        type: string
      uid:
        type: integer
        format: int64
      params:
        type: string
      start_by_id:
        type: string
      start_at:
        type: string
        format: date-time
      status:
        type: integer
        format: int32
      original_assignee:
        type: string
      assignee:
        type: string
      memo:
        type: string
  ActivityInstance:
    type: object
    properties:
      id:
        type: integer
        format: int64
      created_at:
        type: string
        format: date-time
      updated_at:
        type: string
        format: date-time
      process_instance_id:
        type: integer
        format: int64
      execution_id:
        type: integer
        format: int64
      call_process_instance_id:
        type: integer
        format: int64
      activity_define_key:
        type: string
      name:
        type: string
      type:
        type: string
      original_actor:
        type: string
      actor:
        type: string
      params:
        type: string
      start_at:
        type: string
        format: date-time
      end_at:
        type: string
        format: date-time
      due_at:
        type: string
        format: date-time
      due_action:
        type: integer
        format: int32
      perform_type:
        type: integer
        format: int32
      perform_condition:
        type: string
      fail_action:
        type: integer
        format: int32
      status:
        type: integer
        format: int32
      memo:
        type: string
  StartProcessInput:
    type: object
    properties:
      key:
        description: the process definition key
        type: string
      uid:
        type: integer
        format: int64
      params:
        description: params to be used in process
        type: string
      excode:
        description: external business code
        type: string
      operator:
        description: external business code
        type: string
  OperateExecutionInput:
    type: object
    properties:
      params:
        type: string
      operator:
        type: string
      memo:
        type: string
    example:
      params: params
      operator: operator
      memo: memo
  UpdateProcessDefinitionInput:
      type: object
      properties:
        type:
          type: string
        action:
          type: string
        include_instances:
            type: boolean
  AssigneeInput:
      type: object
      properties:
        assignee:
          type: string
  CancelProcessInput:
      type: object
      properties:
        operator:
          type: string
        memo:
          type: string
