package cache

import (
	"context"

	"github.com/go-redis/redis/v8"

	"github.com/qbox/bo-base/v3/dao"
	redisTrace "github.com/qbox/sonic/sdk/trace/redis"
)

// Init instances a redis client
func Init(cfg *dao.CacheConfig) (client redis.UniversalClient, err error) {
	opts := &redis.UniversalOptions{
		MasterName: cfg.RedisConfig.MasterName,
		Addrs:      cfg.RedisConfig.Addrs,
		Password:   cfg.RedisConfig.Password,
		DB:         cfg.RedisConfig.DB,
	}
	client = redisTrace.Trace(redis.NewUniversalClient(opts))

	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return client, err
	}

	return client, nil
}
