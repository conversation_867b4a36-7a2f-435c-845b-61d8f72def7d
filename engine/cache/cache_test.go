package cache_test

import (
	"github.com/go-redis/redis/v8"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/qbox/bo-base/v3/dao"
	"qiniu.io/qbpm/engine/cache"
)

var _ = Describe("cache", func() {

	Context("cache", func() {
		It("test cache", func() {
			// NewUniversalClient 默认 addr := "127.0.0.1:6379"
			_, err := cache.Init(&dao.CacheConfig{
				RedisConfig: redis.UniversalOptions{
					Addrs: []string{"127.0.0.1:6379"},
					DB:    6,
				},
			})
			Expect(err).NotTo(HaveOccurred())
		})
	})
})
