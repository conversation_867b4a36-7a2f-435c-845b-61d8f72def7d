package ctx

import (
	"qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/bpmn/models/enums"
	pModel "qiniu.io/qbpm/engine/models"
	pEnums "qiniu.io/qbpm/engine/models/enums"
)

// NodeStatus defines status of execution node
type NodeStatus int

// enums node status
const (
	NodeStatusPending NodeStatus = iota + 1
	NodeStatusCompleted
	NodeStatusFailed
)

type nodeStatusCheck func(task models.Task, finishCnt, failCnt, total int) (status NodeStatus)

func checkMultiAnyDecision(task models.Task, finishCnt, failCnt, total int) (status NodeStatus) {
	if finishCnt > 0 {
		status = NodeStatusCompleted
	} else if failCnt > 0 {
		// first one makes decisions
		status = NodeStatusFailed
	}
	return
}

func checkMultiAllDecision(task models.Task, finishCnt, failCnt, total int) (status NodeStatus) {
	if finishCnt == total {
		status = NodeStatusCompleted
	} else if failCnt > 0 {
		status = NodeStatusFailed
	}
	return
}

func checkMultiRateDecision(task models.Task, finishCnt, failCnt, total int) (status NodeStatus) {
	rate := task.GetMultiDecisionRate()
	if rate <= 0 || rate > 1 {
		rate = 1
	}

	if float32(finishCnt)/float32(total) >= task.GetMultiDecisionRate() {
		status = NodeStatusCompleted
	} else if float32(failCnt)/float32(total) > task.GetMultiDecisionRate() {
		status = NodeStatusFailed
	}
	return
}

func checkMultiThresholdDecision(task models.Task, finishCnt, failCnt, total int) (status NodeStatus) {
	threshold := task.GetMultiDecisionThreshold()
	if threshold <= 0 || threshold > total {
		threshold = total
	}

	if finishCnt >= task.GetMultiDecisionThreshold() {
		status = NodeStatusCompleted
	} else if failCnt > (total - threshold) {
		status = NodeStatusFailed
	}
	return
}

// CheckNodeStatus checks status of specific key node
func (ctx *Context) CheckNodeStatus(executions []*pModel.RunExecution) (status NodeStatus, err error) {
	if len(executions) == 0 {
		return
	}

	// 批量查询所有execution对应的ActivityInstance
	executionIds := make([]uint64, len(executions))
	for i, exec := range executions {
		executionIds[i] = exec.ID
	}

	var activityInstances []*pModel.ActivityInstance
	err = ctx.Mgr.Model(&pModel.ActivityInstance{}).Where("execution_id in (?)", executionIds).Find(&activityInstances).Error
	if err != nil {
		return 0, err
	}

	// 建立execution_id到ActivityInstance的映射
	activityMap := make(map[uint64]*pModel.ActivityInstance)
	for _, activity := range activityInstances {
		activityMap[activity.ExecutionID] = activity
	}

	var nodeFinishedCnt, nodeFailedCnt, total int
	for _, exec := range executions {
		var isFinished, isFailed bool

		if exec.ID == ctx.RunExecution.ID {
			// 当前节点：使用RunExecutionStatus判断
			isFinished = exec.Status == pEnums.RunExecutionStatusFinished
			isFailed = exec.Status == pEnums.RunExecutionStatusFailed
		} else {
			// 兄弟节点：使用ActivityStatus判断
			if activity, exists := activityMap[exec.ID]; exists {
				isFinished = activity.Status == pEnums.ActivityStatusCompleted
				isFailed = activity.Status == pEnums.ActivityStatusFailed
			} else {
				// 如果没有找到ActivityInstance，跳过
				total++
				continue
			}
		}

		if isFinished {
			nodeFinishedCnt++
		} else if isFailed {
			node, err := ctx.ProcessDefinition.GetExecutionNode(exec)
			if err != nil {
				return 0, err
			}

			if _, ok := node.(*models.SuggestTask); ok {
				nodeFinishedCnt++
			} else {
				nodeFailedCnt++
			}
		}

		total++
	}

	// 调用拆分出来的状态计算函数
	return ctx.CalculateNodeStatusFromCounts(executions, nodeFinishedCnt, nodeFailedCnt, total)
}

// CalculateNodeStatusFromCounts 根据完成/失败计数和执行列表计算节点状态
func (ctx *Context) CalculateNodeStatusFromCounts(executions []*pModel.RunExecution, nodeFinishedCnt, nodeFailedCnt, total int) (status NodeStatus, err error) {
	if len(executions) == 0 {
		return
	}

	var statusChckFn nodeStatusCheck = checkMultiAllDecision
	if executions[0].CounterSignType.Valid() {
		// 加签节点默认为任意通过即可
		status = checkMultiAnyDecision(nil, nodeFinishedCnt, nodeFailedCnt, total)
	} else {
		node, err := ctx.ProcessDefinition.GetExecutionNode(executions[0])
		if err != nil {
			return 0, err
		}

		task, ok := node.(models.Task)
		if ok {
			switch task.GetMultiDecision() {
			case enums.MultiDecisionAny:
				statusChckFn = checkMultiAnyDecision
			case enums.MultiDecisionAll:
				statusChckFn = checkMultiAllDecision
			case enums.MultiDecisionRate:
				statusChckFn = checkMultiRateDecision
			case enums.MultiDecisionThreshold:
				statusChckFn = checkMultiThresholdDecision
			default:
				if task.GetAssigneeNode().AssigneeType == enums.AssigneeTypeLabel {
					statusChckFn = checkMultiAnyDecision
				}
			}
		}

		status = statusChckFn(task, nodeFinishedCnt, nodeFailedCnt, total)
	}

	if status == 0 {
		status = NodeStatusPending
	}

	return
}
