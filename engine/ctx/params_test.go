package ctx_test

import (
	"fmt"
	"strings"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("Params", func() {
	var activityInstance *models.ActivityInstance
	var process *models.ProcessInstance
	var c *ctx.Context
	BeforeEach(func() {
		base = test.Init()

		processDef := models.NewProcessDefinition()
		processDef.Name = "testProcessDef1"
		processDef.Key = processDef.Name
		base.Create(processDef)

		process = &models.ProcessInstance{
			ProcessDefinitionID: processDef.ID,
			Name:                "test",
			Excode:              "test",
		}
		base.Create(process)

		execution := &models.RunExecution{
			Base: models.Base{ID: 123},
		}
		base.Create(execution)

		activityInstance = &models.ActivityInstance{
			ExecutionID:       execution.ID,
			ProcessInstanceID: process.ID,
			ActivityDefineKey: "test",
		}

		c = ctx.BuildContext(execution, processDef, base, nil, nil, nil, nil, false)

		base.Create(activityInstance)
	})

	It("test empty params", func() {
		params, err := c.ConstructParams(activityInstance)
		Expect(err).To(BeNil())
		Expect(strings.Contains(params, fmt.Sprintf(`"id":"%v"`, process.ID))).To(BeTrue())
	})

	It("test exist params", func() {
		activityInstance.Params = `{"data":{"url":"localhost"},"flow":{"status":0}}`

		params, err := c.ConstructParams(activityInstance)
		Expect(err).To(BeNil())

		Expect(strings.Contains(params, fmt.Sprintf(`"id":"%v"`, process.ID))).To(BeTrue())
	})

	It("test fail params", func() {
		activityInstance.ProcessInstanceID = activityInstance.ProcessInstanceID + 1

		_, err := c.ConstructParams(activityInstance)
		Expect(err).ToNot(BeNil())
	})
})
