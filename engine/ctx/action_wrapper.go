package ctx

import (
	"encoding/json"
	"errors"
	"strings"

	sofaSDK "github.com/qbox/pay-sdk/sofa/client/user"

	"qiniu.io/qbpm/engine/bpmn/models/enums"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/services/system"
	"qiniu.io/qbpm/engine/util/exp"
	"qiniu.io/qbpm/engine/util/params"
)

var (
	ErrNotFound = errors.New("not found")
)

// DoAction wrapper action behavior
func (c *Context) DoAction(actionRef string, ctxModel interface{}, canStore bool) (interface{}, error) {
	action, err := system.GetAction(actionRef)
	if err != nil {
		return nil, err
	}

	if ctxModel == nil {
		ctxModel = c.RunExecution
	}

	param, err := c.ConstructParams(ctxModel)
	if err != nil {
		return nil, err
	}

	actionRes, err := action.Do(param)
	if actionRes == nil {
		return nil, err
	}

	var (
		parseResult string
		storeResult interface{}
	)

	if action.ParseKey == "" {
		if actionResultStr, ok := actionRes.(string); ok {
			parseResult = actionResultStr
		} else {
			actionResultBytes, err := json.Marshal(actionRes)
			if err != nil {
				return nil, err
			}
			parseResult = string(actionResultBytes)
		}
	} else {
		parseResult, err = exp.EvalSpread(action.ParseKey, actionRes)
		if err != nil {
			return nil, err
		}
	}

	if canStore && action.StoreKey != "" {
		if action.ParseFirst {
			storeResult = parseResult
		} else {
			storeResult = actionRes
		}

		storeKey := strings.TrimPrefix(action.StoreKey, "params.")
		if param, err = params.SetParam(c.RunExecution.Params, storeKey, storeResult); err != nil {
			c.Log.WithError(err).Error("set param failed")
			return nil, err
		}

		if err = mgr.NewRunExecutionMgr(*c.Mgr).UpdateParams(c.RunExecution, param); err != nil {
			c.Log.WithError(err).Error("update model failed")
			return nil, err
		}
	}

	return parseResult, nil
}

// GetAssignee parse assignee value with params or actions
func (c *Context) GetAssignee(assigneeType enums.AssigneeType, assignee string) (string, error) {
	if assigneeType != enums.AssigneeTypeManager && assignee == "" {
		c.Log.Warn("assignee label not specified")
		return "", nil
	}

	switch assigneeType {
	case enums.AssigneeTypeDynamic:
		dynamicAssignee, err := c.DoAction(assignee, nil, true)
		if err != nil {
			return "", err
		}

		return dynamicAssignee.(string), nil
	case enums.AssigneeTypeLabel:
		if c.GaeaService == nil {
			c.Log.Warn("gaeaService not initialized")
			return "", errors.New("nil gaea service")
		}

		users, err := c.GaeaService.ListUsersOfLabel(assignee)
		if err != nil {
			return "", err
		}

		emails := make([]string, len(users))
		for i, user := range users {
			emails[i] = user.Email
		}

		return strings.Join(emails, ","), nil
	case enums.AssigneeTypeManager:
		response, err := c.SofaService.GetUserManager(sofaSDK.NewGetUserManagerParams().WithEmail(&assignee))
		if err != nil {
			var getUserManagerNotFound *sofaSDK.GetUserManagerNotFound
			if errors.As(err, &getUserManagerNotFound) {
				return "", nil
			}
			return "", err
		}
		if response.GetPayload() == nil || response.GetPayload().Data == nil {
			return "", errors.New("nil response data")
		}
		manager := response.GetPayload().Data.Email
		return manager, nil
	default:
		//TODO: use ConstructParams(runExecution, mgr) if required
		return exp.EvalSpread(assignee, c.RunExecution.Params)
	}

}
