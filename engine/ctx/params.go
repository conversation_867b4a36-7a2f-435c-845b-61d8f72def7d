package ctx

import (
	"encoding/json"
	"errors"
	"reflect"
	"time"

	bpmnEnum "qiniu.io/qbpm/engine/bpmn/models/enums"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/util/params"
)

type (
	ExecuteParam struct {
		ID                  uint64                   `json:"id,string"`
		ProcessDefinitionID uint64                   `json:"process_definition_id,string"`
		ProcessInstanceID   uint64                   `json:"process_instance_id,string"`
		ActivityDefineKey   string                   `json:"activity_define_key"`
		Name                string                   `json:"name"`
		Excode              string                   `json:"excode"`
		UID                 uint64                   `json:"uid"`
		Params              string                   `json:"params"`
		StartByID           string                   `json:"start_by_id"`
		StartAt             time.Time                `json:"start_at"`
		Status              enums.RunExecutionStatus `json:"status"`
		OriginalAssignee    string                   `json:"original_assignee"`
		Assignee            string                   `json:"assignee"`
		CounterSignType     bpmnEnum.CounterSignType `json:"counter_sign_type"`
		CounterSigner       string                   `json:"counter_signer"`
		ParentID            uint64                   `json:"parent_id,string"`
		ParentKey           string                   `json:"parent_key"`
		Memo                string                   `json:"memo"`
		CreatedAt           time.Time                `json:"created_at"`
		UpdatedAt           time.Time                `json:"updated_at"`
	}
	ProcessParam struct {
		ID                     uint64                      `json:"id,string"`
		ProcessDefinitionID    uint64                      `json:"process_definition_id,string"`
		SuperProcessInstanceID uint64                      `json:"super_process_instance_id,string"`
		Name                   string                      `json:"name"`
		Excode                 string                      `json:"excode"`
		UID                    uint64                      `json:"uid"`
		Params                 string                      `json:"params"`
		StartByID              string                      `json:"start_by_id"`
		StartAt                time.Time                   `json:"start_at"`
		EndAt                  time.Time                   `json:"end_at"`
		Status                 enums.ProcessInstanceStatus `json:"status"`
		CreatedAt              time.Time                   `json:"created_at"`
		UpdatedAt              time.Time                   `json:"updated_at"`
	}
	ProcessDefinitionParam struct {
		ID          uint64                 `json:"id,string"`
		Key         string                 `json:"key"`
		Version     int                    `json:"version"`
		Name        string                 `json:"name"`
		Status      enums.ProcessDefStatus `json:"status"`
		Description string                 `json:"description"`
		DeployAt    time.Time              `json:"deploy_at"`
		CreatedAt   time.Time              `json:"created_at"`
		UpdatedAt   time.Time              `json:"updated_at"`
	}
)

// ConstructParams construct params with activity and process
func (c *Context) ConstructParams(source interface{}) (param string, err error) {
	var (
		processInstanceID uint64
		executeParam      ExecuteParam
	)

	if reflect.ValueOf(source).IsNil() {
		return "", nil
	}

	if activity, ok := source.(*models.ActivityInstance); ok {
		param = activity.Params
		processInstanceID = activity.ProcessInstanceID

		executeParam = ExecuteParam{
			ID:                activity.ExecutionID,
			ProcessInstanceID: activity.ProcessInstanceID,
			ActivityDefineKey: activity.ActivityDefineKey,
			Name:              activity.Name,
			Params:            activity.Params,
			Status:            activity.Status.ExecutionStatus(),
			OriginalAssignee:  activity.OriginalActor,
			Assignee:          activity.Actor,
			CounterSignType:   activity.CounterSignType,
			CounterSigner:     activity.CounterSigner,
			ParentID:          activity.ParentExecutionID,
			ParentKey:         activity.ParentKey,
			Memo:              activity.Memo,
			CreatedAt:         activity.CreatedAt,
			UpdatedAt:         activity.UpdatedAt,
		}
	}

	if execute, ok := source.(*models.RunExecution); ok {
		param = execute.Params
		processInstanceID = execute.ProcessInstanceID

		executeParam = ExecuteParam{
			ID:                execute.ID,
			ProcessInstanceID: execute.ProcessInstanceID,
			ActivityDefineKey: execute.ActivityDefineKey,
			Name:              execute.Name,
			Params:            execute.Params,
			Status:            execute.Status,
			OriginalAssignee:  execute.OriginalAssignee,
			Assignee:          execute.Assignee,
			CounterSignType:   execute.CounterSignType,
			CounterSigner:     execute.CounterSigner,
			ParentID:          execute.ParentID,
			ParentKey:         execute.ParentKey,
			Memo:              execute.Memo,
			CreatedAt:         execute.CreatedAt,
			UpdatedAt:         execute.UpdatedAt,
		}
	}
	if param == "" && processInstanceID == 0 {
		return "", errors.New("source is not ActivityInstance and RunExecution")
	}

	var processInstance models.ProcessInstance
	err = c.Mgr.First(&processInstance, processInstanceID).Error
	if err != nil {
		return
	}

	executeParam.UID = processInstance.UID
	executeParam.Excode = processInstance.Excode
	executeParam.StartByID = processInstance.StartByID
	executeParam.StartAt = processInstance.StartAt
	executeParam.ProcessDefinitionID = processInstance.ProcessDefinitionID

	param, err = setParam(param, "execute", executeParam)
	if err != nil {
		return
	}

	param, err = setParam(param, "process", ProcessParam{
		ID:                     processInstance.ID,
		ProcessDefinitionID:    processInstance.ProcessDefinitionID,
		SuperProcessInstanceID: processInstance.SuperProcessInstanceID,
		Name:                   processInstance.Name,
		Excode:                 processInstance.Excode,
		UID:                    processInstance.UID,
		Params:                 processInstance.Params,
		StartByID:              processInstance.StartByID,
		StartAt:                processInstance.StartAt,
		EndAt:                  processInstance.EndAt,
		Status:                 processInstance.Status,
		CreatedAt:              processInstance.CreatedAt,
		UpdatedAt:              processInstance.UpdatedAt,
	})
	if err != nil {
		return
	}

	param, err = setParam(param, "process_definition", ProcessDefinitionParam{
		ID:          c.ProcessDefinition.ID,
		Key:         c.ProcessDefinition.Key,
		Version:     c.ProcessDefinition.Version,
		Name:        c.ProcessDefinition.Name,
		Status:      c.ProcessDefinition.Status,
		Description: c.ProcessDefinition.Description,
		DeployAt:    c.ProcessDefinition.DeployAt,
		CreatedAt:   c.ProcessDefinition.CreatedAt,
		UpdatedAt:   c.ProcessDefinition.UpdatedAt,
	})
	if err != nil {
		return
	}

	return param, nil
}

func setParam(param, path string, obj interface{}) (string, error) {
	jsonStr, err := json.Marshal(obj)
	if err != nil {
		return "", err
	}

	return params.SetParam(param, path, string(jsonStr))
}
