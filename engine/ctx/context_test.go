package ctx_test

import (
	"fmt"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/engine/bpmn/parser"
	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("Context", func() {
	var (
		base *mgr.Base
	)

	BeforeEach(func() {
		base = test.Init()
	})

	Describe("GetInstanceOutgoings", func() {
		var processDef *models.ProcessDefinition
		var instance *models.ProcessInstance

		JustBeforeEach(func() {
			processDef = models.NewProcessDefinition()
			processDef.Name = "testProcessDef1"
			processDef.Key = processDef.Name
			processDef.XMLData = []byte(`<?xml version="1.0" encoding="UTF-8"?>
				<definitions id="definitions"
				  xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
				  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
				 
					<process id="modify_user_price" name="用户改价流程">
						<startEvent id="theStart"/>
				 
						<sequenceFlow id="toProfileCheck" sourceRef="theStart" targetRef="profileCheck"/>
				 
						<exclusiveGateway id="profileCheck" name="角色判断" />
						<sequenceFlow id="toProfileCheck" sourceRef="profileCheck" targetRef="subManager">
							<conditionExpression xsi:type="tFormalExpression">data.managerProfile == "区域销售经理"</conditionExpression>
						</sequenceFlow>
						<sequenceFlow id="toManager" sourceRef="profileCheck" targetRef="manager">
							<conditionExpression xsi:type="tFormalExpression">data.managerProfile == "区域销售总监"</conditionExpression>
						</sequenceFlow>
						<sequenceFlow id="toFinance" sourceRef="profileCheck" targetRef="finance">
							<conditionExpression xsi:type="tFormalExpression">data.managerProfile == "销售总裁"</conditionExpression>
						</sequenceFlow>
				 
						<userTask id="subManager" name="经理审批" assignee="" assignType="user" performType="all" succAction="next" failAction="final_fail"/>
						<sequenceFlow id="subManagerToManager" sourceRef="subManager" targetRef="manager"/>
				 
						<userTask id="manager" name="总监审批"/>
						<sequenceFlow id="managerToFinance" sourceRef="manager" targetRef="finance"/>
				 
				 
						<userTask id="finance" name="财务审批"/>
						<sequenceFlow id="financeToMeasureCheck" sourceRef="finance" targetRef="measureCheck"/>
						 
						<exclusiveGateway id="measureCheck" name="测算结果判断"/>
						<sequenceFlow id="measureToEnd" sourceRef="measureCheck" targetRef="theEnd">
							<conditionExpression xsi:type="tFormalExpression">data.measureResult.length > 0</conditionExpression>
						</sequenceFlow>
						<sequenceFlow id="measureToFork" sourceRef="measureCheck" targetRef="fork">
							<conditionExpression xsi:type="tFormalExpression">data.measureResult.length == 0</conditionExpression>
						</sequenceFlow>
						 
						<parallelGateway id="fork"/>
						<sequenceFlow id="forkToProduct" sourceRef="fork" targetRef="product" />
						<sequenceFlow id="forkToVp" sourceRef="fork" targetRef="vp" />
						 
						<userTask id="product" name="产品线审批"/>
						<sequenceFlow id="productToJoin" sourceRef="product" targetRef="join"/>
				 
						<userTask id="vp" name="销售总裁审批"/>
						<sequenceFlow id="vpToJoin" sourceRef="vp" targetRef="join"/>
						 
						<parallelGateway id="join" />
						<sequenceFlow sourceRef="join" targetRef="theEnd" />
				 
						<endEvent id="theEnd"/>
					</process>
					 
				</definitions>
			`)
			base.Create(processDef)

			instance = &models.ProcessInstance{
				ProcessDefinitionID: processDef.ID,
				Name:                "test",
				Excode:              "test",
			}
			base.Create(instance)
		})

		It("should return model not found", func() {
			execution := &models.RunExecution{
				Base:              models.Base{ID: 123},
				ProcessInstanceID: instance.ID,
			}
			base.Create(execution)

			c := ctx.BuildContext(execution, processDef, base, nil, nil, nil, nil, false)
			_, err := c.CurrentNode()
			Expect(err).To(Equal(fmt.Errorf("not found")))
		})

		It("should return node key not found", func() {
			execution := &models.RunExecution{
				Base:                models.Base{ID: 123},
				ProcessDefinitionID: processDef.ID,
				ProcessInstanceID:   instance.ID,
			}
			base.Create(execution)

			c := ctx.BuildContext(execution, processDef, base, nil, nil, nil, nil, false)
			_, err := c.CurrentNode()
			Expect(err).To(Equal(fmt.Errorf("not found")))
		})

		It("should return node not found", func() {
			execution := &models.RunExecution{
				Base:                models.Base{ID: 123},
				ProcessDefinitionID: processDef.ID,
				ProcessInstanceID:   instance.ID,
				ActivityDefineKey:   "join",
			}
			base.Create(execution)

			c := ctx.BuildContext(execution, processDef, base, nil, nil, nil, nil, false)
			_, err := c.CurrentNode()
			Expect(err).To(Equal(fmt.Errorf("node not found")))
		})

		It("should get 1 node", func() {
			execution := &models.RunExecution{
				Base:                models.Base{ID: 123},
				ProcessDefinitionID: processDef.ID,
				ProcessInstanceID:   instance.ID,
				ActivityDefineKey:   "join",
			}
			base.Create(execution)

			var err error
			processDef.ProcessModel, err = parser.ParseBpmn(processDef.XMLData)
			Expect(err).To(BeNil())

			c := ctx.BuildContext(execution, processDef, base, nil, nil, nil, nil, false)
			nodes, err := c.GetInstanceOutgoings()
			Expect(err).To(BeNil())
			Expect(len(nodes)).To(Equal(1))
		})
	})
})
