package ctx

import (
	"fmt"
	"net/http"

	"github.com/sirupsen/logrus"

	sofaSDK "github.com/qbox/pay-sdk/sofa/client/user"
	bpmn "qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/lib/gaea"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
)

// Context Execution context
type Context struct {
	RunExecution           *models.RunExecution
	ProcessDefinition      *models.ProcessDefinition
	Log                    logrus.FieldLogger
	Mgr                    *mgr.Base
	AdminTransport         http.RoundTripper
	SofaService            sofaSDK.ClientService
	GaeaService            gaea.GaeaAdminService
	IsCounterSignTypeAfter bool
}

// BuildContext Build context with runtime execution
func BuildContext(
	execution *models.RunExecution,
	processDef *models.ProcessDefinition,
	mgr *mgr.Base,
	adminTransport http.RoundTripper,
	sofaService sofaSDK.ClientService,
	gaeaService gaea.GaeaAdminService,
	l logrus.FieldLogger,
	isCounterSignTypeAfter bool,
) (context *Context) {
	context = &Context{
		RunExecution:           execution,
		ProcessDefinition:      processDef,
		Mgr:                    mgr,
		AdminTransport:         adminTransport,
		SofaService:            sofaService,
		GaeaService:            gaeaService,
		Log:                    l,
		IsCounterSignTypeAfter: isCounterSignTypeAfter,
	}

	if context.Log == nil {
		context.Log = logrus.WithFields(logrus.Fields{
			"processID":         processDef.ID,
			"processKey":        processDef.Key,
			"processInstanceID": execution.ProcessInstanceID,
			"executionID":       execution.ID,
			"activityDefineKey": execution.ActivityDefineKey,
		})
	} else {
		context.Log = context.Log.WithFields(logrus.Fields{
			"processID":         processDef.ID,
			"processKey":        processDef.Key,
			"processInstanceID": execution.ProcessInstanceID,
			"executionID":       execution.ID,
			"activityDefineKey": execution.ActivityDefineKey,
		})
	}

	return
}

// CurrentNode Get current execution node
func (c *Context) CurrentNode() (bpmn.INode, error) {
	return GetExecutionNode(c.RunExecution, c.ProcessDefinition)
}

// GetExecutionNode Get Execution node of specific runExecution
func GetExecutionNode(runExecution *models.RunExecution, processDef *models.ProcessDefinition) (node bpmn.INode, err error) {
	if runExecution == nil || processDef == nil || runExecution.ActivityDefineKey == "" {
		return nil, fmt.Errorf("not found")
	}
	return processDef.GetExecutionNode(runExecution)
}
