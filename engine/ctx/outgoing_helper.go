package ctx

import (
	"fmt"

	"qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/util/exp"
)

// GetInstanceOutgoings Get outgoings for specific instance node
func (ctx *Context) GetInstanceOutgoings() ([]*models.SequenceFlow, error) {
	node, err := ctx.CurrentNode()
	if err != nil {
		return nil, err
	}

	return ctx.GetNodeOutgoings(node)
}

// GetNodeOutgoings Get outgoings for specific node
func (ctx *Context) GetNodeOutgoings(node models.INode) ([]*models.SequenceFlow, error) {
	if len(node.GetOutgoings()) == 0 {
		if node.GetType() != "EndEvent" {
			return nil, fmt.Errorf("no outgoings found")
		}
		return nil, nil
	}

	if node.GetType() != "ExclusiveGateway" && node.GetType() != "InclusiveGateway" {
		return node.GetOutgoings(), nil
	}

	defaultFlow := findDefaultFlow(node)
	outgoings := make([]*models.SequenceFlow, 0)
	for _, flow := range node.GetOutgoings() {
		if flow.Condition != nil && flow.Condition.Expression != "" {
			//TODO: use jobs.ConstructParams(activityInstance, mgr) if required
			validate, err := exp.EvalBool(flow.Condition.Expression, ctx.RunExecution.Params)
			if err != nil {
				return nil, err
			}

			if validate {
				outgoings = append(outgoings, flow)
				if node.GetType() == "ExclusiveGateway" {
					break
				}
			}
		} else if defaultFlow == nil || defaultFlow.ID != flow.ID {
			// defaultFlow 分支不作为自动允许分支加入
			outgoings = append(outgoings, flow)
		}
	}

	outgoingCount := len(outgoings)
	if outgoingCount > 1 && node.GetType() == "ExclusiveGateway" {
		return nil, fmt.Errorf("more than 1 outgoing found for exclusive gateway")
	}

	// if no outgoings found, try use default flow if specified
	if outgoingCount == 0 {
		if defaultFlow == nil {
			return nil, fmt.Errorf("no outgoings meet conditions")
		}

		outgoings = append(outgoings, defaultFlow)
	}

	return outgoings, nil
}

func findDefaultFlow(node models.INode) *models.SequenceFlow {
	var defaultFlow string

	if gateway, ok := node.(*models.ExclusiveGateway); ok {
		defaultFlow = gateway.DefaultFlow
	} else if gateway, ok := node.(*models.InclusiveGateway); ok {
		defaultFlow = gateway.DefaultFlow
	}

	if defaultFlow != "" {
		for _, flow := range node.GetOutgoings() {
			if flow.ID == defaultFlow {
				return flow
			}
		}
	}

	return nil

}
