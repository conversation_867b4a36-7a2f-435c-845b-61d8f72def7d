package models

import (
	"time"

	"qiniu.io/qbpm/engine/models/enums"
)

// ProcessInstance Process instance history record
type ProcessInstance struct {
	Base
	ProcessDefinitionID    uint64                      `gorm:"INDEX;NOT NULL" json:"process_definition_id"`
	SuperProcessInstanceID uint64                      `gorm:"INDEX" json:"super_process_instance_id"`
	Name                   string                      `gorm:"type:varchar(255)" json:"name"`
	Excode                 string                      `gorm:"type:varchar(64);INDEX;NOT NULL" json:"excode"`
	UID                    uint64                      `gorm:"INDEX;" json:"uid"`
	Params                 string                      `gorm:"type:text" json:"params"`
	StartByID              string                      `gorm:"type:varchar(255)" json:"start_by_id"`
	StartAt                time.Time                   `gorm:"type:datetime" json:"start_at"`
	EndAt                  time.Time                   `gorm:"type:datetime" json:"end_at"`
	Status                 enums.ProcessInstanceStatus `gorm:"type:int;default:1" json:"status"` // Completed、Failed、Removed、Pending
}
