package enums

import (
	"fmt"
)

// ActivityStatus Status of activity node
type ActivityStatus int

const (
	// ActivityStatusPending Activity is pending to be completed
	ActivityStatusPending ActivityStatus = iota + 1
	// ActivityStatusCompleted Activity has been completed
	ActivityStatusCompleted
	// ActivityStatusRemoved Activity has been removed
	ActivityStatusRemoved
	// ActivityStatusFailed Activity has been failed
	ActivityStatusFailed
	// ActivityStatusReassigned Activity has been reassigned
	ActivityStatusReassigned
	// ActivityStatusNoResponse Activity has been completed by others
	ActivityStatusNoResponse
	// ActivityStatusSuspend suspend by other nodes
	ActivityStatusSuspend
)

func (s ActivityStatus) String() string {
	switch s {
	case ActivityStatusPending:
		return "pending"
	case ActivityStatusCompleted:
		return "completed"
	case ActivityStatusRemoved:
		return "removed"
	case ActivityStatusFailed:
		return "failed"
	case ActivityStatusReassigned:
		return "reassigned"
	case ActivityStatusNoResponse:
		return "no response"
	case ActivityStatusSuspend:
		return "suspend"
	default:
		return fmt.Sprintf("unknown status: %d", s)
	}
}

// ExecutionStatus get execution status from activity status
func (s ActivityStatus) ExecutionStatus() RunExecutionStatus {
	switch s {
	case ActivityStatusCompleted:
		return RunExecutionStatusFinished
	case ActivityStatusNoResponse:
		return RunExecutionStatusNoResponse
	case ActivityStatusFailed:
		return RunExecutionStatusFailed
	case ActivityStatusRemoved:
		return RunExecutionStatusRemoved
	case ActivityStatusSuspend:
		return RunExecutionStatusSuspend
	}
	return RunExecutionStatusPending
}
