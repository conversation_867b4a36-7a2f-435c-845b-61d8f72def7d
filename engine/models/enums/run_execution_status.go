package enums

import "fmt"

// RunExecutionStatus defines enum of runExecution status
type RunExecutionStatus int

func (s RunExecutionStatus) String() string {
	switch s {
	case RunExecutionStatusRunning:
		return "running"
	case RunExecutionStatusSuspend:
		return "suspend"
	case RunExecutionStatusPending:
		return "pending"
	case RunExecutionStatusFinished:
		return "finished"
	case RunExecutionStatusFailed:
		return "failed"
	case RunExecutionStatusRemoved:
		return "removed"
	case RunExecutionStatusNoResponse:
		return "no response"
	}
	return fmt.Sprintf("unknown status: %d", s)
}

// ActivityStatus get activity status from exectution status
func (s RunExecutionStatus) ActivityStatus() ActivityStatus {
	switch s {
	case RunExecutionStatusFinished:
		return ActivityStatusCompleted
	case RunExecutionStatusFailed:
		return ActivityStatusFailed
	case RunExecutionStatusRemoved:
		return ActivityStatusRemoved
	case RunExecutionStatusNoResponse:
		return ActivityStatusNoResponse
	case RunExecutionStatusSuspend:
		return ActivityStatusSuspend
	}
	return ActivityStatusPending
}

// IsEnd indicates if this status is end symbol
func (s RunExecutionStatus) IsEnd() bool {
	switch s {
	case RunExecutionStatusFinished,
		RunExecutionStatusFailed,
		RunExecutionStatusRemoved,
		RunExecutionStatusNoResponse:
		return true
	}
	return false
}

const (
	// RunExecutionStatusRunning 正在执行
	RunExecutionStatusRunning RunExecutionStatus = 1

	// RunExecutionStatusPending 等待先决条件满足后执行
	RunExecutionStatusPending RunExecutionStatus = 2

	// RunExecutionStatusFinished 已完成待流转
	RunExecutionStatusFinished RunExecutionStatus = 3

	// RunExecutionStatusFailed 失败待流转
	RunExecutionStatusFailed RunExecutionStatus = 4

	// RunExecutionStatusSuspend 被外部原因暂停，不受调度器控制流转
	RunExecutionStatusSuspend RunExecutionStatus = 5

	// RunExecutionStatusRemoved 被取消
	RunExecutionStatusRemoved RunExecutionStatus = 6

	// RunExecutionStatusNoResponse 无需处理
	RunExecutionStatusNoResponse RunExecutionStatus = 7
)
