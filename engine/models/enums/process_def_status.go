package enums

import "fmt"

// ProcessDefStatus Status of Process definition
type ProcessDefStatus int

const (
	// ProcessDefStatusNew new
	ProcessDefStatusNew ProcessDefStatus = iota + 1
	// ProcessDefStatusActive active
	ProcessDefStatusActive
	// ProcessDefStatusSuspended suspended
	ProcessDefStatusSuspended
)

func (s ProcessDefStatus) String() string {
	switch s {
	case ProcessDefStatusNew:
		return "new"
	case ProcessDefStatusActive:
		return "active"
	case ProcessDefStatusSuspended:
		return "suspended"
	default:
		return fmt.Sprintf("unknown status: %d", s)
	}
}
