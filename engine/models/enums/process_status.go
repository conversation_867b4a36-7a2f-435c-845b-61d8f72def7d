package enums

import "fmt"

// ProcessInstanceStatus Status type of process instance
type ProcessInstanceStatus int

const (
	// ProcessStatusPending Process is pending to be completed
	ProcessStatusPending ProcessInstanceStatus = iota + 1
	// ProcessStatusRemoved Process has been removed
	ProcessStatusRemoved
	// ProcessStatusCompleted Process has been completed
	ProcessStatusCompleted
	// ProcessStatusFailed Process has been failed
	ProcessStatusFailed
	// ProcessStatusSuspended Process has been suspend, only pending status can be suspended
	ProcessStatusSuspended
)

func (s ProcessInstanceStatus) String() string {
	switch s {
	case ProcessStatusPending:
		return "pending"
	case ProcessStatusRemoved:
		return "removed"
	case ProcessStatusCompleted:
		return "completed"
	case ProcessStatusFailed:
		return "failed"
	case ProcessStatusSuspended:
		return "suspended"
	}
	return fmt.Sprintf("unknown status: %d", s)
}

func (s ProcessInstanceStatus) Humanize() string {
	switch s {
	case ProcessStatusPending:
		return "审核中"
	case ProcessStatusRemoved:
		return "已撤回"
	case ProcessStatusCompleted:
		return "审核通过"
	case ProcessStatusFailed:
		return "已驳回"
	case ProcessStatusSuspended:
		return "已挂起"
	}
	return fmt.Sprintf("未知状态: %d", s)
}
