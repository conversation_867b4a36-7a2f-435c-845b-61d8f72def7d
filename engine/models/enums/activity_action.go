package enums

import (
	"fmt"
)

// FailAction Action type when node task fails
type FailAction int

const (
	// FailActionFinal Final fail, will end current process
	FailActionFinal FailAction = iota + 1
	// FailActionPrev Jump to previous node, first task node equals FailActionFinal
	FailActionPrev
)

func (s FailAction) String() string {
	switch s {
	case FailActionFinal:
		return "final"
	case FailActionPrev:
		return "prev"
	default:
		return fmt.Sprintf("unknown action: %d", s)
	}
}

// DueAction Action type when task expires
type DueAction int

const (
	// DueActionFail Fail this task
	DueActionFail DueAction = iota + 1
	// DueActionComplete Complete this task
	DueActionComplete
)

func (s DueAction) String() string {
	switch s {
	case DueActionFail:
		return "fail"
	case DueActionComplete:
		return "complete"
	default:
		return fmt.Sprintf("unknown action: %d", s)
	}
}

// PerformType Perform type when node is multiple instance
type PerformType int

const (
	// PerformTypeParallel Perform task concurrently
	PerformTypeParallel PerformType = iota + 1
	// PerformTypeSequential Perform task sequentially
	PerformTypeSequential
)

func (s PerformType) String() string {
	switch s {
	case PerformTypeParallel:
		return "parallel"
	case PerformTypeSequential:
		return "sequential"
	default:
		return fmt.Sprintf("unknown type: %d", s)
	}
}
