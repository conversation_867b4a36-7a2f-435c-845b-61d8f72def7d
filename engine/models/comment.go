package models

import (
	"errors"
	"regexp"
	"strings"
)

type Comment struct {
	Base
	ProcessInstanceID       uint64 `gorm:"index;not null" json:"process_instance_id"`
	Content                 string `gorm:"type:text;not null" json:"content"`
	Author                  string `gorm:"not null" json:"author"`
	ParentCommentID         uint64 `gorm:"not null" json:"parent_comment_id"` // 回复的评论 ID，如果没有则为0
	EditedBy                string `gorm:"not null" json:"edited_by"`         // 编辑人：用户/管理员
	IsDeleted               bool   `gorm:"not null" json:"is_deleted"`
	AttachmentFilename      string `gorm:"column:attachment_filename;not null" json:"attachment_filename"`
	AttachmentContentBase64 string `gorm:"-" json:"content_base64,omitempty"`
	AttachmentFileURL       string `gorm:"-" json:"attachment_file_url"`
}

type CommentResponse struct {
	Comment
	ReplyTo string `json:"reply_to"`
}

func (c Comment) Valid() error {
	if strings.TrimSpace(c.Content) == "" {
		return errors.New("content cannot be empty")
	}
	if c.ProcessInstanceID == 0 {
		return errors.New("process_instance_id cannot be empty")
	}

	if len(c.Content) > 1000 {
		return errors.New("content length exceeds maximum limit")
	}

	return nil
}

func (c Comment) ParseMentionedUsers() []string {
	content := c.Content
	regex := regexp.MustCompile(`@([a-zA-Z0-9_]+\x20)`)
	mentioned := regex.FindAllString(content, len(content))
	result := make([]string, 0)

	for _, m := range mentioned {
		t := strings.TrimSpace(strings.TrimPrefix(m, "@"))
		if t != "" {
			result = append(result, t)
		}
	}
	return result
}
