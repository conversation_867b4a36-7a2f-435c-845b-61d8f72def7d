package models

type SignalExecutionRelation struct {
	Base
	SignalRef         string `gorm:"index:signal_id;NOT NULL" json:"signalRef"`
	Business          string `gorm:"index:signal_id" json:"business"`
	ExecutionId       uint64 `gorm:"unique_index" json:"execution_id"`
	Completed         bool   `json:"completed"`
	ProcessInstanceID uint64 `gorm:"index:process,signal_id; not null" json:"process_instance_id"`
	ActivityDefineKey string `gorm:"index:process" json:"activity_define_key"` // 父节点的id
	SignalDefineKey   string `json:"signal_define_key"`                        //signalEventDefinition的id
}
