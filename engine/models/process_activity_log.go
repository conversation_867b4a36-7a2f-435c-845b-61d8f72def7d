package models

import (
	"time"

	"qiniu.io/qbpm/engine/models/enums"
)

type ProcessActivityLog struct {
	ID                uint64                `gorm:"PRIMARY_KEY;AUTO_INCREMENT" json:"id"`
	ProcessInstanceID uint64                `gorm:"INDEX;NOT NULL" json:"process_instance_id"`
	ActivityType      enums.ActivityLogType `gorm:"type:varchar(50);NOT NULL" json:"activity_type"`
	Operator          string                `gorm:"type:varchar(100);NOT NULL" json:"operator"`
	Content           string                `gorm:"type:text" json:"content"`
	CreatedAt         time.Time             `gorm:"type:datetime;NOT NULL" json:"created_at"`
}

type ProcessActivityLogMention struct {
	ActivityID uint64 `gorm:"PRIMARY_KEY" json:"activity_id"`
	UserID     string `gorm:"type:varchar(100);NOT NULL" json:"user_id"`
}

func (ProcessActivityLog) TableName() string {
	return "process_activity_log"
}
