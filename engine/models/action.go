package models

import "qiniu.io/qbpm/engine/models/enums"

type Action struct {
	Base
	Code           string         `gorm:"unique_index" json:"code"` // action code，used for external configs, unique required
	Name           string         `json:"name"`                     // action name
	Service        uint64         `json:"service"`                  // service config
	UseServiceAuth bool           `json:"use_service_auth"`         // use service auth as default
	CustomAuth     enums.AuthType `json:"custom_auth"`              // custom auth, will ignore service auth
	URL            string         `json:"url"`                      // request url path
	Method         string         `json:"method"`                   // http request method
	Header         string         `json:"header"`                   // request header
	Body           string         `json:"body"`                     // request body
	StoreKey       string         `json:"store_key"`                // key of execution params to save values
	ParseKey       string         `json:"parse_key"`                // key to parse specified values
	ParseFirst     bool           `json:"parse_first"`              // parse fields before store values if parse<PERSON>ey and StoreKey both defined
	MaxRetry       int            `json:"max_retry"`                // max retry times when action is failed
}
