package models

import (
	"time"

	bpmnEnum "qiniu.io/qbpm/engine/bpmn/models/enums"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/util/snowflake"
)

// ActivityInstance Activity instance history record
type ActivityInstance struct {
	Base
	ProcessInstanceID     uint64                   `gorm:"index;not null" json:"process_instance_id"`
	ExecutionID           uint64                   `gorm:"index;not null" json:"execution_id"`
	CallProcessInstanceID uint64                   `gorm:"index" json:"call_process_instance_id"`
	ActivityDefineKey     string                   `gorm:"type:varchar(255)" json:"activity_define_key"`
	Name                  string                   `gorm:"type:varchar(255)" json:"name"`
	Type                  string                   `gorm:"type:varchar(64)" json:"type"`
	OriginalActor         string                   `gorm:"type:varchar(255)" json:"original_actor"`
	Actor                 string                   `gorm:"type:varchar(255)" json:"actor"`
	Params                string                   `gorm:"type:text" json:"params"`
	StartAt               time.Time                `gorm:"type:datetime" json:"start_at"`
	EndAt                 time.Time                `gorm:"type:datetime" json:"end_at"`
	DueAt                 time.Time                `gorm:"type:datetime" json:"due_at"`
	DueAction             enums.DueAction          `gorm:"type:int" json:"due_action"`
	PerformType           enums.PerformType        `gorm:"type:int" json:"perform_type"`
	PerformCondition      string                   `gorm:"type:varchar(255)" json:"perform_condition"`
	FailAction            enums.FailAction         `gorm:"type:int" json:"fail_action"`
	Status                enums.ActivityStatus     `gorm:"type:int;default:1" json:"status"`
	CounterSignType       bpmnEnum.CounterSignType `json:"counter_sign_type"`
	CounterSigner         string                   `json:"counter_signer"`
	ParentExecutionID     uint64                   `json:"parent_execution_id"`
	ParentKey             string                   `json:"parent_key"`
	Memo                  string                   `gorm:"type:varchar(255)" json:"memo"`
}

// NewActivityInstance Generate an activity instance using runExecution
func NewActivityInstance(runExecution *RunExecution, typ string) *ActivityInstance {
	return &ActivityInstance{
		Base:              Base{ID: snowflake.Generator.ID()},
		ProcessInstanceID: runExecution.ProcessInstanceID,
		ExecutionID:       runExecution.ID,
		ActivityDefineKey: runExecution.ActivityDefineKey,
		Name:              runExecution.Name,
		Type:              typ,
		Params:            runExecution.Params,
		StartAt:           runExecution.StartAt,
		Status:            runExecution.Status.ActivityStatus(),
		OriginalActor:     runExecution.OriginalAssignee,
		Actor:             runExecution.Assignee,
		CounterSignType:   runExecution.CounterSignType,
		CounterSigner:     runExecution.CounterSigner,
		ParentExecutionID: runExecution.ParentID,
		ParentKey:         runExecution.ParentKey,
		Memo:              runExecution.Memo,
	}
}
