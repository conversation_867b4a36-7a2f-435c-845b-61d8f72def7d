package models

import "time"

// ListenerStatus defines enum of listener status
type ListenerStatus int16

const (
	// BeginListener means newly created listener action
	BeginListener ListenerStatus = iota
	// CompleteListener means listener action completed
	CompleteListener
	// FailListener means listener action failed
	FailListener
)

// Listener is history to record listener actions
type Listener struct {
	ExecutionID         uint64         `gorm:"PRIMARY_KEY;not null" json:"execution_id"`
	ListenerKey         string         `gorm:"PRIMARY_KEY;type:varchar(64)" json:"listener_key"`
	ActionKey           string         `gorm:"PRIMARY_KEY;type:varchar(64)" json:"action_key"`
	NodeKey             string         `gorm:"type:varchar(64)" json:"node_key"`
	Fire                string         `gorm:"type:varchar(20)" json:"fire"`
	Status              ListenerStatus `json:"status"`
	Errors              string         `json:"errors"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	ProcessInstanceID   uint64         `gorm:"index" json:"process_instance_id"`
	ProcessDefinitionID uint64         `gorm:"INDEX:process_definition_id;NOT NULL" json:"process_definition_id"`
}
