package models

import (
	"errors"
	"strings"
	"time"

	"qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/util/snowflake"
)

// ErrNodeNotFound defines process node not found
var ErrNodeNotFound = errors.New("node not found")

// ProcessDefinition Process definition model
type ProcessDefinition struct {
	Base
	Key          string                 `gorm:"type:varchar(64);unique_index:key_version" json:"key"`
	Version      int                    `gorm:"unique_index:key_version;default:1" json:"version"`
	Name         string                 `json:"name"`
	Status       enums.ProcessDefStatus `gorm:"default:1" json:"status"`
	Description  string                 `gorm:"type:varchar(512)" json:"description"`
	XMLData      []byte                 `gorm:"type:mediumblob" json:"xml_data"`
	DeployAt     time.Time              `gorm:"type:datetime" json:"deploy_at"`
	ProcessModel *models.BpmnModel      `gorm:"-" json:"process_model"`
}

// NewProcessDefinition Inititalize a process definition instance
func NewProcessDefinition() *ProcessDefinition {
	return &ProcessDefinition{
		Base: Base{
			ID: snowflake.Generator.ID(),
		},
	}
}

// GetNodeMap get process node map, key is node id
func (m *ProcessDefinition) GetNodeMap() map[string]models.INode {
	return m.ProcessModel.Process.NodeMap
}

// GetExecutionNode get node from process definition by execution
func (m *ProcessDefinition) GetExecutionNode(runExecution *RunExecution) (node models.INode, err error) {
	activityDefineKey := strings.Split(runExecution.ActivityDefineKey, "-")[0]
	return m.GetNode(activityDefineKey)
}

// GetNode get node from process definition by node id
func (m *ProcessDefinition) GetNode(key string) (node models.INode, err error) {
	if m.ProcessModel != nil && m.ProcessModel.Process != nil {
		if node, ok := m.ProcessModel.Process.NodeMap[key]; ok {
			return node, nil
		}
	}
	return nil, ErrNodeNotFound
}
