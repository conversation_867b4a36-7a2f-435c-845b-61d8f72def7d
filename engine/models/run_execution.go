package models

import (
	"errors"
	"fmt"
	"time"

	"qiniu.io/qbpm/engine/bpmn/models"
	bpmnEnum "qiniu.io/qbpm/engine/bpmn/models/enums"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/util"
	"qiniu.io/qbpm/engine/util/snowflake"
)

var (
	// ErrStartEventNotFound defines start event node not found
	ErrStartEventNotFound = errors.New("start event not found")
)

// RunExecution stores running execution info which would be deleted after node exits
type RunExecution struct {
	Base
	ProcessDefinitionID uint64                   `gorm:"INDEX;NOT NULL" json:"process_definition_id"`
	ProcessInstanceID   uint64                   `gorm:"INDEX;NOT NULL" json:"process_instance_id"`
	ActivityDefineKey   string                   `gorm:"type:varchar(64);INDEX" json:"activity_define_key"`
	Name                string                   `gorm:"type:varchar(255)" json:"name"`
	Excode              string                   `gorm:"type:varchar(64);INDEX;NOT NULL" json:"excode"`
	UID                 uint64                   `gorm:"INDEX;" json:"uid"`
	Params              string                   `gorm:"type:text" json:"params"`
	StartByID           string                   `gorm:"type:varchar(255)" json:"start_by_id"`
	StartAt             time.Time                `gorm:"type:datetime" json:"start_at"`
	Status              enums.RunExecutionStatus `gorm:"type:int" json:"status"`
	OriginalAssignee    string                   `gorm:"INDEX" json:"original_assignee"`
	Assignee            string                   `gorm:"INDEX" json:"assignee"`
	AssigneeType        bpmnEnum.AssigneeType    `gorm:"type:varchar(64)" json:"assignee_type"`
	LeftAssignees       string                   `gorm:"left_assignees" json:"-"` // 当前节点待分派的剩余审批人
	CounterSignType     bpmnEnum.CounterSignType `json:"counter_sign_type"`
	CounterSigner       string                   `json:"counter_signer"`
	ParentID            uint64                   `json:"parent_id"`
	ParentKey           string                   `json:"parent_key"`
	Memo                string                   `gorm:"type:varchar(255)" json:"memo"`
	DDL                 int64                    `gorm:"type:int" json:"ddl"` // 到期自动驳回时间点
}

// NewRunExecution creates a running execution when process started
func NewRunExecution(
	definition ProcessDefinition,
	params string,
	excode string,
	startByID string,
	uid uint64,
	ddl int64,
) (runExecution *RunExecution, err error) {
	id := snowflake.Generator.ID()

	if len(definition.ProcessModel.Process.StartEvents) == 0 {
		err = ErrStartEventNotFound
		return
	}

	node := definition.ProcessModel.Process.StartEvents[0]

	runExecution = &RunExecution{
		Base:                Base{ID: id},
		ProcessDefinitionID: definition.ID,
		ProcessInstanceID:   id,
		ActivityDefineKey:   node.ID,
		Name:                node.GetName(),
		Excode:              excode,
		UID:                 uid,
		Params:              params,
		StartByID:           startByID,
		StartAt:             time.Now(),
		OriginalAssignee:    startByID,
		Assignee:            startByID,
		Status:              enums.RunExecutionStatusRunning,
		DDL:                 ddl,
	}
	return
}

// NextRunExecution creates a running execution when enters next node
func NextRunExecution(execution *RunExecution, node models.INode) *RunExecution {

	status := enums.RunExecutionStatusRunning

	return &RunExecution{
		Base:                Base{ID: snowflake.Generator.ID()},
		ProcessDefinitionID: execution.ProcessDefinitionID,
		ProcessInstanceID:   execution.ProcessInstanceID,
		ActivityDefineKey:   node.GetKey(),
		Name:                node.GetName(),
		ParentID:            execution.ID,
		ParentKey:           execution.ActivityDefineKey,
		Excode:              execution.Excode,
		UID:                 execution.UID,
		Params:              execution.Params,
		StartByID:           execution.StartByID,
		StartAt:             time.Now(),
		Status:              status,
		DDL:                 execution.DDL,
	}
}

// CounterSignRunExecution counter-signs a runExecution
func CounterSignRunExecution(
	execution *RunExecution,
	counterSignType bpmnEnum.CounterSignType,
	key string,
	name string,
	operator string,
) *RunExecution {
	if name != "" {
		name = "-" + name
	}

	return &RunExecution{
		Base:                Base{ID: snowflake.Generator.ID()},
		ProcessDefinitionID: execution.ProcessDefinitionID,
		ProcessInstanceID:   execution.ProcessInstanceID,
		ActivityDefineKey:   fmt.Sprintf("%s-%s", execution.ActivityDefineKey, key),
		Name:                fmt.Sprintf("%s-%s%s", execution.Name, counterSignType.Humanize(), name),
		CounterSignType:     counterSignType,
		CounterSigner:       operator,
		ParentID:            execution.ID,
		ParentKey:           execution.ActivityDefineKey,
		Excode:              execution.Excode,
		UID:                 execution.UID,
		Params:              execution.Params,
		StartByID:           execution.StartByID,
		StartAt:             time.Now(),
		Status:              enums.RunExecutionStatusPending,
		DDL:                 execution.DDL,
	}
}

// AssignRunExecution creates a running execution when reassigns a task
func AssignRunExecution(execution *RunExecution, assignee string) *RunExecution {
	result := &RunExecution{
		Base:                Base{ID: snowflake.Generator.ID()},
		ProcessDefinitionID: execution.ProcessDefinitionID,
		ProcessInstanceID:   execution.ProcessInstanceID,
		ActivityDefineKey:   execution.ActivityDefineKey,
		Name:                execution.Name,
		ParentID:            execution.ID,
		ParentKey:           execution.ActivityDefineKey,
		Excode:              execution.Excode,
		UID:                 execution.UID,
		Params:              execution.Params,
		StartByID:           execution.StartByID,
		StartAt:             execution.StartAt,
		Status:              enums.RunExecutionStatusPending,
		OriginalAssignee:    assignee,
		Assignee:            assignee,
		AssigneeType:        bpmnEnum.AssigneeTypeManual,
		DDL:                 execution.DDL,
	}

	// 如果是加签节点，需要将加签信息带上，防止找不到节点信息
	if execution.CounterSignType.Valid() {
		result.CounterSignType = execution.CounterSignType
		result.CounterSigner = execution.CounterSigner
		result.ParentKey = execution.ParentKey
		//这里先用 ParentKey 而非 ActivityDefineKey，为了避免 key 过长
		result.ActivityDefineKey = fmt.Sprintf("%s-%s", execution.ParentKey, util.RandomStringr(4))
	}

	return result
}
