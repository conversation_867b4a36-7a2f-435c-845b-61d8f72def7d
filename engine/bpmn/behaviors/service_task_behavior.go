package behaviors

import (
	"errors"

	"qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/ctx"
)

// ServiceTaskBehavior is behavior for service task
type ServiceTaskBehavior struct {
	NodeBehavior
}

// Execute implements interface to perform behavior
func (b *ServiceTaskBehavior) Execute(context *ctx.Context) error {
	var (
		serviceTaskModel *models.ServiceTask
		ok               bool
	)

	node, err := context.CurrentNode()
	if err != nil {
		return err
	}

	if serviceTaskModel, ok = node.(*models.ServiceTask); !ok {
		err := errors.New("node assert ServiceTask fail")
		context.Log.Error(err.Error())
		return err
	}

	_, err = context.DoAction(serviceTaskModel.ActionRef, nil, true)
	if err != nil {
		context.Log.Errorf(
			"service task rest fail: %s=%s",
			serviceTaskModel.ActionRef, err,
		)
		return err
	}

	return nil
}
