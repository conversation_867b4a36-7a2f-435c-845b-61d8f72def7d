package behaviors

import (
	"qiniu.io/qbpm/engine/ctx"
)

// UserTaskBehavior Behavior for user task
type UserTaskBehavior struct {
	NodeBehavior
}

// Enter Override enter function
func (b *UserTaskBehavior) Enter(ctx *ctx.Context) error {
	// notify enter listeners
	// notify assign task listeners
	return b.Enter(ctx)
}

// Execute Override execute function
func (b *UserTaskBehavior) Execute(ctx *ctx.Context) error {
	return nil
}
