package behaviors

import "qiniu.io/qbpm/engine/ctx"

// BaseBehavior Behavior base
type BaseBehavior interface {
	Enter(ctx *ctx.Context) error
	Execute(ctx *ctx.Context) error
}

// NodeBehavior Define behavior of node
type NodeBehavior struct {
}

// Enter Default behavior
func (b *NodeBehavior) Enter(ctx *ctx.Context) error {
	return nil
}

// Execute Default behavior
func (b *NodeBehavior) Execute(ctx *ctx.Context) error {
	return nil
}

func (b *NodeBehavior) leave() {

}
