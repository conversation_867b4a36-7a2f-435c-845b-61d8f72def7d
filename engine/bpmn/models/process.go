package models

import (
	"fmt"
)

type (
	// BpmnModel bpmn model of a process
	BpmnModel struct {
		BaseElement
		Process     *Process            `xml:"process" json:"process"`
		Auths       []*Auth             `xml:"auth" json:"-"`
		AuthMap     map[string]*Auth    `xml:"-" json:"-"`
		RestActions []*RestAction       `xml:"restAction" json:"-"`
		ActionMap   map[string]Actioner `xml:"-" json:"-"`
	}

	// Process concrete definition of a process
	Process struct {
		FlowElement
		StartEvents       []*StartEvent             `xml:"startEvent" json:"start_events,omitempty"`
		EndEvents         []*EndEvent               `xml:"endEvent" json:"end_events,omitempty"`
		ParallelGateways  []*ParallelGateway        `xml:"parallelGateway" json:"parallel_gateways,omitempty"`
		InclusiveGateways []*InclusiveGateway       `xml:"inclusiveGateway" json:"inclusive_gateways,omitempty"`
		ExclusiveGateways []*ExclusiveGateway       `xml:"exclusiveGateway" json:"exclusive_gateways,omitempty"`
		UserTasks         []*UserTask               `xml:"userTask" json:"user_tasks,omitempty"`
		SuggestTasks      []*SuggestTask            `xml:"suggestTask" json:"suggest_tasks,omitempty"`
		SequenceFlows     []*SequenceFlow           `xml:"sequenceFlow" json:"-"`
		ServiceTask       []*ServiceTask            `xml:"serviceTask" json:"service_tasks,omitempty"`
		NodeMap           map[string]INode          `xml:"-" json:"-"`
		CatchEvents       []*IntermediateCatchEvent `xml:"intermediateCatchEvent" json:"catch_events,omitempty"`
	}
)

// GetActions Get actions by action id
func (m *BpmnModel) GetActions(actions ...string) ([]Actioner, error) {
	length := len(actions)

	actioners := make([]Actioner, length)
	for i, action := range actions {
		if actioner, ok := m.ActionMap[action]; ok {
			actioners[i] = actioner
		} else {
			return nil, fmt.Errorf("action not found by action id: %s", action)
		}
	}
	return actioners, nil
}
