package models

import (
	"qiniu.io/qbpm/engine/bpmn/models/enums"
)

type (
	// Event Specifies event node
	Event struct {
		FlowNode
	}

	// StartEvent Specifies start event
	StartEvent struct {
		Event
	}

	// EndEvent Specifies end event
	EndEvent struct {
		Event
	}

	IntermediateCatchEvent struct {
		TaskNode
		SignalEvents []*SignalEventDefinition `xml:"signalEventDefinition"`
	}

	IntermediateThrowEvent struct {
		FlowElement
		SignalEvent *SignalEventDefinition `xml:"signalEventDefinition"`
	}

	SignalEventDefinition struct {
		FlowElement
		SignalRef string               `xml:"signalRef,attr"`
		Business  string               `xml:"business,attr"`
		Payload   string               `xml:"payload,attr"`
		Condition *ConditionExpression `xml:"conditionExpression"`
	}
)

// GetType Get type of StartEvent
func (f *StartEvent) GetType() string {
	return "StartEvent"
}

// GetType Get type of EndEvent
func (f *EndEvent) GetType() string {
	return "EndEvent"
}

func (f *IntermediateCatchEvent) GetType() string {
	return "IntermediateCatchEvent"
}

// GetMultiDecision returns MultiDecision
func (f *IntermediateCatchEvent) GetMultiDecision() enums.MultiDecisionType {
	return f.MultiDecision
}

// GetMultiDecisionRate returns MultiDecisionRate
func (f *IntermediateCatchEvent) GetMultiDecisionRate() float32 {
	return f.MultiDecisionRate
}

// GetMultiDecisionThreshold returns MultiDecisionThreshold
func (f *IntermediateCatchEvent) GetMultiDecisionThreshold() int {
	return f.MultiDecisionThreshold
}
