package models

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"reflect"

	log "github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/util/exp"
)

var (
	// AuthCookieType defines type that uses cookie to authorize
	AuthCookieType = "Cookie"
	// AuthOauthType defines type that uses oauth to authorize
	AuthOauthType = "Oauth"
	// AuthBasicType defines type that uses basic to authorize
	AuthBasicType = "Basic"
)

// Auth defines auth model to authorize
type Auth struct {
	BaseElement
	Type         string      `xml:"type,attr"`
	CommonHeader string      `xml:"commonHeader,attr"`
	RestAction   *RestAction `xml:"restAction"`
	Prefix       string      `xml:"prefix,attr"`

	// basic auth
	UserName string `xml:"userName,attr"`
	PassWord string `xml:"passWord,attr"`

	// cookie
	Value   string `xml:"value,attr"`
	Payload string `xml:"payload,attr"`

	// <PERSON><PERSON>h
	<PERSON>    string `xml:"token,attr"`
	Token<PERSON>ey string `xml:"tokenKey,attr"`

	// cache the acture token
	actualToken string
	Actioner    Actioner
}

// BasicAuth generates token using basic auth
func (auth *Auth) BasicAuth() (string, error) {
	if auth.UserName == "" || auth.PassWord == "" {
		return "", errors.New("username or password is empty")
	}

	token := base64.StdEncoding.EncodeToString([]byte(auth.UserName + ":" + auth.PassWord))
	return token, nil
}

// Oauth generates token using oauth
func (auth *Auth) Oauth(params string) (string, error) {
	if auth.Token != "" {
		return auth.Token, nil
	}

	if auth.Actioner == nil {
		auth.Actioner = auth.RestAction
	}
	var (
		result interface{}
		err    error
	)
	if result, err = auth.Actioner.Do(nil, params); err != nil {
		return "", err
	}
	return exp.EvalSpread(auth.TokenKey, result)
}

// RefushToken refreshes token
func (auth *Auth) RefushToken(params string) error {
	var err error
	switch auth.Type {
	case AuthOauthType:
		auth.actualToken, err = auth.Oauth(params)
	case AuthBasicType:
		auth.actualToken, err = auth.BasicAuth()
	default:
		log.WithFields(log.Fields{
			"authKey": auth.ID,
		}).Errorf("not support auth type")
	}

	if auth.Prefix != "" {
		auth.actualToken = fmt.Sprintf("%s %s", auth.Prefix, auth.actualToken)
	}
	if err != nil {
		log.WithFields(log.Fields{
			"authKey":  auth.ID,
			"params":   params,
			"authType": auth.Type,
		}).Errorf("auth refush token err: %s", err.Error())
	}
	return err
}

// GetToken get token
// TODO: to be inner packaged
func (auth *Auth) GetToken(params string) (string, error) {
	if auth.actualToken != "" {
		return auth.actualToken, nil
	}

	err := auth.RefushToken(params)
	return auth.actualToken, err
}

// SetBody set body for request
// TODO: set common body
func (auth *Auth) SetBody(body string) string {
	return body
}

// SetCommonHeader set common headers for request
func (auth *Auth) SetCommonHeader(request *http.Request, params string) error {
	if auth.CommonHeader != "" {
		var header, paramsObj map[string]interface{}

		json.Unmarshal([]byte(params), &paramsObj)
		commonHeader, _ := exp.EvalSpread(auth.CommonHeader, paramsObj)
		if err := json.Unmarshal([]byte(commonHeader), &header); err != nil {
			return err
		}

		for key, value := range header {
			if reflect.TypeOf(value).Kind() == reflect.String {
				request.Header.Set(key, value.(string))
			} else {
				if str, err := json.Marshal(value); err != nil {
					request.Header.Set(key, string(str))
				}
			}
		}
	}

	if auth.Type == AuthCookieType && auth.Payload != "" {
		return nil
	}

	if auth.Type == AuthBasicType || auth.Type == AuthOauthType {

		token, err := auth.GetToken(params)
		if err != nil {
			return err
		}
		request.Header.Set("Authorization", token)
	}
	return nil
}
