package models

type (
	// SequenceFlow Transition line from one node to another
	SequenceFlow struct {
		FlowElement
		SourceRef  string               `xml:"sourceRef,attr" json:"source_ref,omitempty"`
		TargetRef  string               `xml:"targetRef,attr" json:"target_ref,omitempty"`
		SourceNode INode                `xml:"-" json:"-"`
		TargetNode INode                `xml:"-" json:"-"`
		Condition  *ConditionExpression `xml:"conditionExpression" json:"-"`
		IsDefault  bool                 `xml:"-" json:"-"`
	}

	// ConditionExpression Condition expression of a specific sequenceFlow
	ConditionExpression struct {
		Type       string `xml:"xsi:type,attr"`
		Expression string `xml:",cdata"`
	}
)
