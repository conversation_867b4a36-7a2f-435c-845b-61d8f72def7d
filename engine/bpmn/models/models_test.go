package models_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"encoding/json"
	"fmt"
	"net/http"

	gomock "github.com/golang/mock/gomock"
	. "qiniu.io/qbpm/engine/bpmn/models"
	mock "qiniu.io/qbpm/engine/bpmn/models/mock"
	. "qiniu.io/qbpm/engine/bpmn/parser"
)

var _ = Describe("Models", func() {

	var (
		model *BpmnModel
	)

	BeforeEach(func() {
		xml := `<?xml version="1.0" encoding="UTF-8"?>
				<definitions id="definitions"
				  xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
				  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
					<auth id="BasicAuth" type="Basic" useName="xtb" passWord="pwd" commonHeader='{"hello":"world"}'/> 
					<auth id="OauthAuth" type="Oauth" tokenKey="params.access_token" prefix="Bearer" commonHeader='{"hello":"world", "p2":"params.params.p2"}'>
						<restAction url="http://acc.jfcs.qiniu.io/oauth2/token" method="post" body='{"grant_type":"password", "username":"root", "password":"root"}'/>
					</auth>
					<restAction id="testRest" url="http://acc.jfcs.qiniu.io/oauth2/token" method="post" body='{"grant_type":"password", "username":"root", "password":"root"}'/>
					<process id="modify_user_price" name="用户改价流程">
						<startEvent id="theStart">
							<listener id="listener" type="rest" fire="begin" actionRefs="testRest">
							</listener>
						</startEvent>
						<endEvent id="theEnd"/>
					</process>
				</definitions>
				`
		data := []byte(xml)
		model, _ = ParseBpmn(data)
	})

	Describe("bpmn models", func() {
		Context("listener action", func() {
			It("listener", func() {
				actions, err := model.GetActions(model.Process.StartEvents[0].Listeners[0].ActionRefs...)
				Expect(err).To(BeNil())
				if len(actions) != 1 {
					Fail(fmt.Sprintf("Actioners: %d", len(actions)))
				}
			})

			It("auth do", func() {
				Oauth := model.AuthMap["OauthAuth"]
				response := `{
							  "access_token": "access_token1",
							  "expires_in": 3600,
							  "refresh_token": "refresh_token"
							}`
				var res map[string]interface{}
				json.Unmarshal([]byte(response), &res)
				var t mock.GinkgoTestReporter
				mockCtl := gomock.NewController(t)
				actioner := mock.NewMockActioner(mockCtl)
				actioner.EXPECT().Do(nil, gomock.Any()).Return(res, nil)

				Oauth.Actioner = actioner

				token, _ := Oauth.GetToken("")
				if token != "Bearer access_token1" {
					Fail(fmt.Sprintf("get token fail: %s", token))
				}

			})

			It("auth SetCommonHeader", func() {
				Oauth := model.AuthMap["OauthAuth"]
				request, _ := http.NewRequest(http.MethodGet, "http://www.baidu.com", nil)
				Oauth.SetCommonHeader(request, `{"params":{"p2":"hello world"}}`)
				if request.Header.Get("hello") != "world" {
					Fail(fmt.Sprintf("hello = %s", request.Header.Get("hello")))
				}
				if request.Header.Get("p2") != "hello world" {
					Fail(fmt.Sprintf("header eval fail: %s", request.Header.Get("p2")))
				}
			})
		})
	})
})
