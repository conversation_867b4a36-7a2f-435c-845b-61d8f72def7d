package models

type (
	// Gateway Specifies gateway node
	Gateway struct {
		FlowNode
		DefaultFlow string `xml:"defaultFlow,attr" json:"default_flow,omitempty"`
	}

	// ParallelGateway Specifies parallel gateway
	ParallelGateway struct {
		Gateway
	}

	// InclusiveGateway Specifies inclusive gateway
	InclusiveGateway struct {
		Gateway
	}

	// ExclusiveGateway Specifies exclusive gateway
	ExclusiveGateway struct {
		Gateway
	}
)

// GetType Specifies activity node
func (f *ParallelGateway) GetType() string {
	return "ParallelGateway"
}

// GetType Specifies activity node
func (f *InclusiveGateway) GetType() string {
	return "InclusiveGateway"
}

// GetType Specifies activity node
func (f *ExclusiveGateway) GetType() string {
	return "ExclusiveGateway"
}
