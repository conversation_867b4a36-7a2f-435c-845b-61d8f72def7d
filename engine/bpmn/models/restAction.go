package models

import (
	"encoding/json"
	"regexp"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/util/exp"
	"qiniu.io/qbpm/engine/util/logger"
	"qiniu.io/qbpm/engine/util/rest"
)

// RestAction defines model to perform API request
type RestAction struct {
	BaseElement
	URL        string `xml:"url,attr"`
	Method     string `xml:"method,attr"`
	Header     string `xml:"header,attr"`
	Body       string `xml:"body,attr"`
	StoreKey   string `xml:"storeKey,attr"`   // key of execution params to save values
	Parse<PERSON>ey   string `xml:"parseKey,attr"`   // key to parse specified values
	ParseFirst bool   `xml:"parseFirst,attr"` // parse fields before store values if parse<PERSON><PERSON> and <PERSON><PERSON><PERSON> both defined
	AuthRef    string `xml:"authRef,attr"`
	MaxRetry   int    `xml:"maxRetry,attr"` // max retry times when action is failed
}

func (ra *RestAction) rebuildValues(value string, params map[string]interface{}, log logrus.FieldLogger) string {
	r, err := regexp.Compile(`\{\{(.*?)\}\}`)
	if err != nil {
		log.WithError(err).Warn("try compile regexp failed")
		return value
	}

	matches := r.FindAllString(value, -1)

	for _, m := range matches {
		key := strings.Replace(m, "{{", "", 1)
		key = strings.Replace(key, "}}", "", 1)

		if key == "" {
			continue
		}

		parsedValue, err := exp.EvalSpread(key, params)
		if err != nil {
			log.WithError(err).Warnf("parse param failed for key [%s]", key)
			continue
		}

		value = strings.Replace(value, m, parsedValue, 1)
	}
	return value
}

// Do impletements interface to perform actions
func (ra *RestAction) Do(auth *Auth, params string) (interface{}, error) {
	var (
		result    interface{}
		err       error
		paramsObj map[string]interface{}
	)

	err = json.Unmarshal([]byte(params), &paramsObj)
	if err != nil {
		return nil, err
	}

	log := logrus.WithFields(logrus.Fields{
		logger.FieldKeyPrefix: "RestAction",
		"actionKey":           ra.ID,
		"method":              ra.Method,
		"url":                 ra.URL,
	})

	url := ra.rebuildValues(ra.URL, paramsObj, log)
	log = log.WithField("url", ra.URL)

	for index := 0; index < ra.GetMaxRetry(); index++ {
		body := ra.Body
		if auth != nil {
			body = auth.SetBody(ra.Body)
		}

		body = ra.rebuildValues(body, paramsObj, log)
		header := ra.rebuildValues(ra.Header, paramsObj, log)

		req := rest.NewRest(url, ra.Method, header, body)
		if auth != nil {
			if err := auth.SetCommonHeader(req.Request, params); err != nil {
				log.WithField("auth", auth.ID).
					Warnf("SetCommonHeader err: %s", err.Error())
			}
		}

		log = log.WithFields(logrus.Fields{
			"header": req.Request.Header,
			"body":   body,
		})

		result, err = req.Do()
		if req.Response != nil && (req.Response.StatusCode == 401 || req.Response.StatusCode == 403) {
			auth.RefushToken(params)
		}
		if err != nil {
			log.WithField("retryOn", index).
				Warnf("restAction err: %s", err.Error())
			time.Sleep((time.Duration(index) + 1) * time.Second * 3)
			continue
		}
		break
	}
	return result, err
}

// GetAuthRef implements methods to get auth info
func (ra *RestAction) GetAuthRef() string {
	return ra.AuthRef
}

// GetID implements interface to get node key
func (ra *RestAction) GetID() string {
	return ra.ID
}

// GetParseKey implements interface to get parser key
// parser key can be used to get specific key value from returned json values
func (ra *RestAction) GetParseKey() string {
	return ra.ParseKey
}

// GetStoreKey implements interface to get store key
// parser key can be used to store specific key value from returned json values
func (ra *RestAction) GetStoreKey() string {
	return ra.StoreKey
}

// GetParseFirst implements interface to check if parse first or not
// if specifies true, only stores parsed key value, otherwise, stores entire values.
func (ra *RestAction) GetParseFirst() bool {
	return ra.ParseFirst
}

// GetMaxRetry implements interface to get maxRetry times
// if maxRetry not set, default value 3 is returned
func (ra *RestAction) GetMaxRetry() int {
	if ra.MaxRetry <= 0 {
		return 3
	}
	return ra.MaxRetry
}
