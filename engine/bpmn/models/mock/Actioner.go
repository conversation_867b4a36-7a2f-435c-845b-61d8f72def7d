// Code generated by MockGen. DO NOT EDIT.
// Source: qiniu.io/qbpm/engine/bpmn/models (interfaces: Actioner)

// Package mock_models is a generated GoMock package.
package mock

import (
	"fmt"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	ginkgo "github.com/onsi/ginkgo/v2"
	models "qiniu.io/qbpm/engine/bpmn/models"
)

// MockActioner is a mock of Actioner interface.
type MockActioner struct {
	ctrl     *gomock.Controller
	recorder *MockActionerMockRecorder
}

// MockActionerMockRecorder is the mock recorder for MockActioner.
type MockActionerMockRecorder struct {
	mock *MockActioner
}

// NewMockActioner creates a new mock instance.
func NewMockActioner(ctrl *gomock.Controller) *MockActioner {
	mock := &MockActioner{ctrl: ctrl}
	mock.recorder = &MockActionerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockActioner) EXPECT() *MockActionerMockRecorder {
	return m.recorder
}

// Do mocks base method.
func (m *MockActioner) Do(arg0 *models.Auth, arg1 string) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Do", arg0, arg1)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Do indicates an expected call of Do.
func (mr *MockActionerMockRecorder) Do(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Do", reflect.TypeOf((*MockActioner)(nil).Do), arg0, arg1)
}

// GetAuthRef mocks base method.
func (m *MockActioner) GetAuthRef() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthRef")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAuthRef indicates an expected call of GetAuthRef.
func (mr *MockActionerMockRecorder) GetAuthRef() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthRef", reflect.TypeOf((*MockActioner)(nil).GetAuthRef))
}

// GetID mocks base method.
func (m *MockActioner) GetID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetID")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetID indicates an expected call of GetID.
func (mr *MockActionerMockRecorder) GetID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetID", reflect.TypeOf((*MockActioner)(nil).GetID))
}

// GetMaxRetry mocks base method.
func (m *MockActioner) GetMaxRetry() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxRetry")
	ret0, _ := ret[0].(int)
	return ret0
}

// GetMaxRetry indicates an expected call of GetMaxRetry.
func (mr *MockActionerMockRecorder) GetMaxRetry() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxRetry", reflect.TypeOf((*MockActioner)(nil).GetMaxRetry))
}

// GetParseFirst mocks base method.
func (m *MockActioner) GetParseFirst() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParseFirst")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetParseFirst indicates an expected call of GetParseFirst.
func (mr *MockActionerMockRecorder) GetParseFirst() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParseFirst", reflect.TypeOf((*MockActioner)(nil).GetParseFirst))
}

// GetParseKey mocks base method.
func (m *MockActioner) GetParseKey() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParseKey")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetParseKey indicates an expected call of GetParseKey.
func (mr *MockActionerMockRecorder) GetParseKey() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParseKey", reflect.TypeOf((*MockActioner)(nil).GetParseKey))
}

// GetStoreKey mocks base method.
func (m *MockActioner) GetStoreKey() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStoreKey")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetStoreKey indicates an expected call of GetStoreKey.
func (mr *MockActionerMockRecorder) GetStoreKey() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoreKey", reflect.TypeOf((*MockActioner)(nil).GetStoreKey))
}

type GinkgoTestReporter struct{}

func (g GinkgoTestReporter) Errorf(format string, args ...interface{}) {
	ginkgo.Fail(fmt.Sprintf(format, args...))
}

func (g GinkgoTestReporter) Fatalf(format string, args ...interface{}) {
	ginkgo.Fail(fmt.Sprintf(format, args...))
}
