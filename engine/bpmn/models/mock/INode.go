// Code generated by MockGen. DO NOT EDIT.
// Source: qiniu.io/qbpm/engine/bpmn/models (interfaces: INode)

// Package qiniu.io/qbpm/engine/bpmn/models/mocks is a generated GoMock package.
package mock

import (
	gomock "github.com/golang/mock/gomock"
	models "qiniu.io/qbpm/engine/bpmn/models"
	reflect "reflect"
)

// MockINode is a mock of INode interface.
type MockINode struct {
	ctrl     *gomock.Controller
	recorder *MockINodeMockRecorder
}

// MockINodeMockRecorder is the mock recorder for MockINode.
type MockINodeMockRecorder struct {
	mock *MockINode
}

// NewMockINode creates a new mock instance.
func NewMockINode(ctrl *gomock.Controller) *MockINode {
	mock := &MockINode{ctrl: ctrl}
	mock.recorder = &MockINodeMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockINode) EXPECT() *MockINodeMockRecorder {
	return m.recorder
}

// AddIncomings mocks base method.
func (m *MockINode) AddIncomings(arg0 *models.SequenceFlow) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddIncomings", arg0)
}

// AddIncomings indicates an expected call of AddIncomings.
func (mr *MockINodeMockRecorder) AddIncomings(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddIncomings", reflect.TypeOf((*MockINode)(nil).AddIncomings), arg0)
}

// AddListener mocks base method.
func (m *MockINode) AddListener(arg0 *models.Listener) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddListener", arg0)
}

// AddListener indicates an expected call of AddListener.
func (mr *MockINodeMockRecorder) AddListener(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddListener", reflect.TypeOf((*MockINode)(nil).AddListener), arg0)
}

// AddOutgoings mocks base method.
func (m *MockINode) AddOutgoings(arg0 *models.SequenceFlow) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddOutgoings", arg0)
}

// AddOutgoings indicates an expected call of AddOutgoings.
func (mr *MockINodeMockRecorder) AddOutgoings(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOutgoings", reflect.TypeOf((*MockINode)(nil).AddOutgoings), arg0)
}

// GetBehavior mocks base method.
func (m *MockINode) GetBehavior() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBehavior")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// GetBehavior indicates an expected call of GetBehavior.
func (mr *MockINodeMockRecorder) GetBehavior() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBehavior", reflect.TypeOf((*MockINode)(nil).GetBehavior))
}

// GetIncomings mocks base method.
func (m *MockINode) GetIncomings() []*models.SequenceFlow {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIncomings")
	ret0, _ := ret[0].([]*models.SequenceFlow)
	return ret0
}

// GetIncomings indicates an expected call of GetIncomings.
func (mr *MockINodeMockRecorder) GetIncomings() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncomings", reflect.TypeOf((*MockINode)(nil).GetIncomings))
}

// GetKey mocks base method.
func (m *MockINode) GetKey() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKey")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetKey indicates an expected call of GetKey.
func (mr *MockINodeMockRecorder) GetKey() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKey", reflect.TypeOf((*MockINode)(nil).GetKey))
}

// GetListenerMap mocks base method.
func (m *MockINode) GetListenerMap() map[string][]*models.Listener {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetListenerMap")
	ret0, _ := ret[0].(map[string][]*models.Listener)
	return ret0
}

// GetListenerMap indicates an expected call of GetListenerMap.
func (mr *MockINodeMockRecorder) GetListenerMap() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetListenerMap", reflect.TypeOf((*MockINode)(nil).GetListenerMap))
}

// GetListeners mocks base method.
func (m *MockINode) GetListeners() []*models.Listener {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetListeners")
	ret0, _ := ret[0].([]*models.Listener)
	return ret0
}

// GetListeners indicates an expected call of GetListeners.
func (mr *MockINodeMockRecorder) GetListeners() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetListeners", reflect.TypeOf((*MockINode)(nil).GetListeners))
}

// GetName mocks base method.
func (m *MockINode) GetName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetName")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetName indicates an expected call of GetName.
func (mr *MockINodeMockRecorder) GetName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetName", reflect.TypeOf((*MockINode)(nil).GetName))
}

// GetOutgoings mocks base method.
func (m *MockINode) GetOutgoings() []*models.SequenceFlow {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOutgoings")
	ret0, _ := ret[0].([]*models.SequenceFlow)
	return ret0
}

// GetOutgoings indicates an expected call of GetOutgoings.
func (mr *MockINodeMockRecorder) GetOutgoings() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOutgoings", reflect.TypeOf((*MockINode)(nil).GetOutgoings))
}

// GetThrowEvents mocks base method.
func (m *MockINode) GetThrowEvents() []*models.IntermediateThrowEvent {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetThrowEvents")
	ret0, _ := ret[0].([]*models.IntermediateThrowEvent)
	return ret0
}

// GetThrowEvents indicates an expected call of GetThrowEvents.
func (mr *MockINodeMockRecorder) GetThrowEvents() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetThrowEvents", reflect.TypeOf((*MockINode)(nil).GetThrowEvents))
}

// GetType mocks base method.
func (m *MockINode) GetType() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetType")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetType indicates an expected call of GetType.
func (mr *MockINodeMockRecorder) GetType() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetType", reflect.TypeOf((*MockINode)(nil).GetType))
}

// IsNotifyDisabled mocks base method.
func (m *MockINode) IsNotifyDisabled() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsNotifyDisabled")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsNotifyDisabled indicates an expected call of IsNotifyDisabled.
func (mr *MockINodeMockRecorder) IsNotifyDisabled() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsNotifyDisabled", reflect.TypeOf((*MockINode)(nil).IsNotifyDisabled))
}

// SetBehavior mocks base method.
func (m *MockINode) SetBehavior(arg0 interface{}) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetBehavior", arg0)
}

// SetBehavior indicates an expected call of SetBehavior.
func (mr *MockINodeMockRecorder) SetBehavior(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBehavior", reflect.TypeOf((*MockINode)(nil).SetBehavior), arg0)
}
