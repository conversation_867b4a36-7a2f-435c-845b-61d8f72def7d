package enums

// MultiDecisionType defines how to decide a node task has finished when it has multi instances
type MultiDecisionType string

const (
	// MultiDecisionAll all tasks should be finished
	MultiDecisionAll MultiDecisionType = "all"
	// MultiDecisionAny first one decides the result
	MultiDecisionAny = "any"
	// MultiDecisionRate first one decides the result
	// the value of MultiDecisionRate should between 0 and 1
	MultiDecisionRate = "rate"
	// MultiDecisionThreshold first one decides the result
	// the value of MultiDecisionThreshold should greater than 0 and less than number of muti instances
	MultiDecisionThreshold = "threshold"
)
