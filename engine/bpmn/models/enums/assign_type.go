package enums

// AssigneeType specifies how task owner should be assigneed
type AssigneeType string

const (
	// AssigneeTypeManual set assignees manually
	AssigneeTypeManual AssigneeType = "manual"
	// AssigneeTypeStatic set assignees using xml defined
	AssigneeTypeStatic = "static"
	// AssigneeTypeDynamic set assignees dynamically, rest action performed
	AssigneeTypeDynamic = "dynamic"
	// AssigneeTypeManager set assignees to manager
	AssigneeTypeManager = "manager"
	// AssigneeTypeLabel set assignees to label
	AssigneeTypeLabel = "label"
)
