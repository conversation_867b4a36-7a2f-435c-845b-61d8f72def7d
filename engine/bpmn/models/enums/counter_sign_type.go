package enums

// CounterSignType 加签类型
type CounterSignType int

const (
	_CounterSignTypeMin CounterSignType = iota
	// CounterSignTypeBefore 前加签
	CounterSignTypeBefore
	// CounterSignTypeAfter 后加签
	CounterSignTypeAfter
	// CounterSignTypeAnd 并加签
	CounterSignTypeAnd
	_CounterSignTypeMax
)

// Valid 检查加签类型是否有效
func (t CounterSignType) Valid() bool {
	return t > _CounterSignTypeMin && t < _CounterSignTypeMax
}

// Humanize 加签类型名称
func (t CounterSignType) Humanize() string {
	switch t {
	case CounterSignTypeBefore:
		return "前加签"
	case CounterSignTypeAfter:
		return "后加签"
	case CounterSignTypeAnd:
		return "并加签"
	}

	return "未知加签"
}
