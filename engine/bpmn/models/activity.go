package models

import (
	"qiniu.io/qbpm/engine/bpmn/models/enums"
)

type (
	// Activity Specifies activity node
	Activity struct {
		FlowNode
	}

	// Task wraps method for task node
	Task interface {
		GetIsSequential() bool
		GetMultiDecision() enums.MultiDecisionType
		GetMultiDecisionRate() float32
		GetMultiDecisionThreshold() int
		GetAssigneeNode() AssigneeNode
	}

	// TaskNode Specifies task node info
	TaskNode struct {
		Activity
		AssigneeNode
		IsSequential           bool                    `xml:"isSequential,attr" json:"is_sequential,omitempty"` // 多实例场景是否串行执行
		MultiDecision          enums.MultiDecisionType `xml:"multiDecision,attr" json:"multi_decision,omitempty"`
		MultiDecisionRate      float32                 `xml:"multiDecisionRate,attr" json:"multi_decision_rate,omitempty"`
		MultiDecisionThreshold int                     `xml:"multiDecisionThreshold,attr" json:"multi_decision_threshold,omitempty"`
		ExpiresIn              string                  `xml:"expiresIn,attr" json:"expiresin,omitempty"`
	}

	// UserTask Specifies user task
	UserTask struct {
		TaskNode
		CounterSign
	}

	// SuggestTask specifies suggest task
	SuggestTask struct {
		TaskNode
	}

	// CounterSign counter sign node
	CounterSign struct {
		AllowCounterSign bool                    `xml:"allowCounterSign,attr" json:"allow_counter_sign"`
		CounterSignTypes []enums.CounterSignType `xml:"counterSignTypes>counterSignType" json:"counter_sign_types,omitempty"`
		CounterSigns     []*AssigneeNode         `xml:"counterSigns>counterSign" json:"counter_signs,omitempty"`
	}

	// AssigneeNode task assignee
	AssigneeNode struct {
		AssigneeType enums.AssigneeType `xml:"assigneeType,attr" json:"assignee_type,omitempty"`
		Assignee     string             `xml:"assignee,attr" json:"assignee,omitempty"`
		DisplayName  string             `xml:"displayName,attr" json:"display_name,omitempty"`
	}

	// ServiceTask specifies service task
	ServiceTask struct {
		TaskNode
		ActionRef string `xml:"actionRef,attr" json:"action_ref,omitempty"`
	}
)

// GetType Get type of UserTask
func (f *UserTask) GetType() string {
	return "UserTask"
}

// GetType implements interface to get current task type
func (f *ServiceTask) GetType() string {
	return "ServiceTask"
}

// GetType implements interface to get current task type
func (f *SuggestTask) GetType() string {
	return "SuggestTask"
}

// GetIsSequential returns IsSequential
func (f *TaskNode) GetIsSequential() bool {
	return f.IsSequential
}

// GetMultiDecision returns MultiDecision
func (f *TaskNode) GetMultiDecision() enums.MultiDecisionType {
	return f.MultiDecision
}

// GetMultiDecisionRate returns MultiDecisionRate
func (f *TaskNode) GetMultiDecisionRate() float32 {
	return f.MultiDecisionRate
}

// GetMultiDecisionThreshold returns MultiDecisionThreshold
func (f *TaskNode) GetMultiDecisionThreshold() int {
	return f.MultiDecisionThreshold
}

// GetAssigneeNode returns assignee node
func (f *TaskNode) GetAssigneeNode() AssigneeNode {
	return f.AssigneeNode
}
