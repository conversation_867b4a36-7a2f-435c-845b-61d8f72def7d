package models

import (
	"fmt"
)

type (
	// BaseElement Specifies activity node
	BaseElement struct {
		ID string `xml:"id,attr" json:"id,omitempty"`
	}

	// FlowElement Specifies activity node
	FlowElement struct {
		BaseElement
		Name          string `xml:"name,attr" json:"name,omitempty"`
		Documentation string `xml:"documentation,attr" json:"documentation,omitempty"`
	}

	// FlowNode Specifies activity node
	FlowNode struct {
		FlowElement

		Behavior      interface{}               `xml:"-" json:"-"`
		IsAsync       bool                      `xml:"-" json:"-"`
		IsExclusive   bool                      `xml:"-" json:"-"`
		DisableNotify bool                      `xml:"disableNotify,attr" json:"disable_notify"`
		Incomings     []*SequenceFlow           `xml:"-" json:"-"`
		Outgoings     []*SequenceFlow           `xml:"-" json:"outgoings,omitempty"`
		Listeners     []*Listener               `xml:"listener" json:"-"`
		ThrowEvents   []*IntermediateThrowEvent `xml:"intermediateThrowEvent" json:"-"`
	}

	// INode Specifies activity node
	INode interface {
		GetKey() string
		GetName() string
		GetType() string
		IsNotifyDisabled() bool
		GetIncomings() []*SequenceFlow
		GetOutgoings() []*SequenceFlow
		AddIncomings(sequenceFlow *SequenceFlow)
		AddOutgoings(sequenceFlow *SequenceFlow)
		GetBehavior() interface{}
		SetBehavior(interface{})
		GetListenerMap() map[string][]*Listener
		GetListeners() []*Listener
		AddListener(*Listener)
		GetThrowEvents() []*IntermediateThrowEvent
	}

	// Actioner wraps methods to perform listener actions
	Actioner interface {
		Do(auth *Auth, params string) (interface{}, error)
		GetAuthRef() string
		GetID() string
		GetParseKey() string
		GetStoreKey() string
		GetParseFirst() bool
		GetMaxRetry() int
	}
)

func (f *FlowNode) GetThrowEvents() []*IntermediateThrowEvent {
	return f.ThrowEvents
}

// GetKey Get key of node
func (f *FlowNode) GetKey() string {
	return f.ID
}

// GetName Get name of node
func (f *FlowNode) GetName() string {
	if f.Name == "" {
		return fmt.Sprintf("%s-%s", f.GetType(), f.GetKey())
	}
	return f.Name
}

// GetType Get type of node
func (f *FlowNode) GetType() string {
	return ""
}

func (f *FlowNode) IsNotifyDisabled() bool {
	return f.DisableNotify
}

// GetIncomings get node's incoming flows
func (f *FlowNode) GetIncomings() []*SequenceFlow {
	return f.Incomings
}

// GetOutgoings get node's outgoing flows
func (f *FlowNode) GetOutgoings() []*SequenceFlow {
	return f.Outgoings
}

// AddIncomings add incoming flows for node
func (f *FlowNode) AddIncomings(sequenceFlow *SequenceFlow) {
	if f.Incomings == nil {
		f.Incomings = make([]*SequenceFlow, 0)
	}
	f.Incomings = append(f.Incomings, sequenceFlow)
}

// AddOutgoings add outgoing flows for node
func (f *FlowNode) AddOutgoings(sequenceFlow *SequenceFlow) {
	if f.Outgoings == nil {
		f.Outgoings = make([]*SequenceFlow, 0)
	}
	f.Outgoings = append(f.Outgoings, sequenceFlow)
}

// GetBehavior Get behavior of node
func (f *FlowNode) GetBehavior() interface{} {
	return f.Behavior
}

// SetBehavior Set behavior for node
func (f *FlowNode) SetBehavior(behavior interface{}) {
	f.Behavior = behavior
}

// GetListenerMap implements interface to get listener map
func (f *FlowNode) GetListenerMap() map[string][]*Listener {
	var listenerMap = make(map[string][]*Listener)
	length := len(f.Listeners)
	for index := 0; index < length; index++ {
		listener := f.Listeners[index]
		listenerMap[listener.Fire] = append(listenerMap[listener.Fire], listener)
	}

	return listenerMap
}

func (f *FlowNode) AddListener(listener *Listener) {
	f.Listeners = append(f.Listeners, listener)
}

func (f *FlowNode) GetListeners() []*Listener {
	return f.Listeners
}
