package parser_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/engine/bpmn/models"
	. "qiniu.io/qbpm/engine/bpmn/parser"
	"qiniu.io/qbpm/engine/jobs"
	ormModel "qiniu.io/qbpm/engine/models"
)

var _ = Describe("parseBpmn", func() {

	It("should create 2 ProcessDefinition models with diff versions", func() {
		xml := `<?xml version="1.0" encoding="UTF-8"?>
				<definitions id="definitions"
				  xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
				  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
					 
				</definitions>
				`
		data := []byte(xml)
		_, err := ParseBpmn(data)
		Expect(err).NotTo(BeNil())
	})

	It("should create 2 ProcessDefinition models with diff versions", func() {
		xml := `<?xml version="1.0" encoding="UTF-8"?>
				<definitions id="definitions"
				  xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
				  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
				 
					<process id="modify_user_price" name="用户改价流程">
						<startEvent id="theStart"/>
						<sequenceFlow id="toProfileCheck1" sourceRef="theStart" targetRef="catchEvent"/>
						<intermediateCatchEvent id="catchEvent" name="事件捕获" >
							<signalEventDefinition signalRef=""/>
						</intermediateCatchEvent>
				 
						<sequenceFlow id="toProfileCheck" sourceRef="catchEvent" targetRef="profileCheck"/>
				 
						<exclusiveGateway id="profileCheck" name="角色判断" />
						<sequenceFlow id="toProfileCheck" sourceRef="profileCheck" targetRef="subManager">
							<conditionExpression xsi:type="tFormalExpression">${managerProfile == "区域销售经理"}</conditionExpression>
						</sequenceFlow>
						<sequenceFlow id="toManager" sourceRef="profileCheck" targetRef="manager">
							<conditionExpression xsi:type="tFormalExpression">${managerProfile == "区域销售总监"}</conditionExpression>
						</sequenceFlow>
						<sequenceFlow id="toFinance" sourceRef="profileCheck" targetRef="finance">
							<conditionExpression xsi:type="tFormalExpression">${managerProfile == "销售总裁"}</conditionExpression>
						</sequenceFlow>
				 
						<userTask id="subManager" name="经理审批" assignee="" assignType="user" performType="all" succAction="next" failAction="final_fail"/>
						<sequenceFlow id="subManagerToManager" sourceRef="subManager" targetRef="manager"/>
				 
						<userTask id="manager" name="总监审批"/>
						<sequenceFlow id="managerToFinance" sourceRef="manager" targetRef="finance"/>
				 
				 
						<userTask id="finance" name="财务审批"/>
						<sequenceFlow id="financeToMeasureCheck" sourceRef="finance" targetRef="measureCheck"/>
						 
						<exclusiveGateway id="measureCheck" name="测算结果判断"/>
						<sequenceFlow id="measureToEnd" sourceRef="measureCheck" targetRef="theEnd">
							<conditionExpression xsi:type="tFormalExpression">${measureResult.size == true}</conditionExpression>
						</sequenceFlow>
						<sequenceFlow id="measureToFork" sourceRef="measureCheck" targetRef="fork">
							<conditionExpression xsi:type="tFormalExpression">${measureResult.size == false}</conditionExpression>
						</sequenceFlow>
						 
						<parallelGateway id="fork"/>
						<sequenceFlow id="forkToProduct" sourceRef="fork" targetRef="product" />
						<sequenceFlow id="forkToVp" sourceRef="fork" targetRef="vp" />
						 
						<userTask id="product" name="产品线审批"/>
						<sequenceFlow id="productToJoin" sourceRef="product" targetRef="join"/>
				 
						<userTask id="vp" name="销售总裁审批"/>
						<sequenceFlow id="vpToJoin" sourceRef="vp" targetRef="join"/>
						 
						<parallelGateway id="join" />
						<sequenceFlow sourceRef="join" targetRef="theEnd" />
				 
						<endEvent id="theEnd"/>
					</process>
					 
				</definitions>
				`
		data := []byte(xml)
		model, err := ParseBpmn(data)
		Expect(err).To(BeNil())
		Expect(len(model.Process.NodeMap)).To(Equal(12))
	})

	Context("", func() {
		var model *models.BpmnModel

		BeforeEach(func() {
			xml := `<?xml version="1.0" encoding="UTF-8"?>
	   			<definitions id="definitions"
	   			  xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
	   			  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	   				<auth id="BasicAuth" type="Basic" useName="xtb" passWord="pwd" commonHeader='{"hello":"world"}'/> 
					<auth id="OauthAuth" type="Oauth" grant_type="password" tokenKey="access_token" prefix="Bearer" commonHeader='{"hello":"world"}'>
	   					<restAction url="http://acc.jfcs.qiniu.io/oauth2/token" method="post" body='{"grant_type":"password", "username":"root", "password":"root"}'/>
	   				</auth>
	   				<process id="modify_user_price" name="用户改价流程">
	   					<startEvent id="theStart">
	   						<listener id="listener" type="rest" fire="begin">
							<restAction url="http://127.0.0.1:5000/" method="post" authRef="OauthAuth"/>
	   						</listener>
	   					</startEvent>
	   					<endEvent id="theEnd"/>
	   				</process>
					<restAction id='default'/>
					<restAction id='custom' maxRetry='6'/>
	   			</definitions>
	   			`
			data := []byte(xml)
			model, _ = ParseBpmn(data)
		})

		It("Auth", func() {
			if model.AuthMap == nil || model.AuthMap["BasicAuth"] == nil || model.AuthMap["BasicAuth"].ID != "BasicAuth" {
				Fail("BasicAuth analysis fail")
			}

			if model.AuthMap["OauthAuth"] == nil || model.AuthMap["OauthAuth"].ID != "OauthAuth" {
				Fail("OauthAuth analysis fail")
			}

			listenerMap := model.Process.NodeMap["theStart"].GetListenerMap()
			if len(listenerMap["begin"]) != 1 || listenerMap["begin"][0].ID != "listener" {
				Fail("listener analysis fail")
			}

			if false {
				var (
					err error
				)
				if _, err = model.AuthMap["OauthAuth"].GetToken(""); err != nil {
					Fail(err.Error())
				}

				startNode := model.Process.NodeMap["theStart"]
				defModel := &ormModel.ProcessDefinition{
					ProcessModel: model,
				}
				job_listener := jobs.NewListener(startNode, defModel, 0, nil, nil)
				err = job_listener.FireAction("begin")
				if err != nil {
					Fail(err.Error())
				}
			}
		})
		It("Node:GetListenerMap", func() {
			startNode := model.Process.NodeMap["theStart"]
			listenerMap := startNode.GetListenerMap()
			if len(listenerMap["begin"]) != 1 || listenerMap["begin"][0].ID != "listener" {
				Fail("INode: listener map fail")
			}
		})
		It("RestAction", func() {
			if model.RestActions == nil || len(model.ActionMap) != 2 {
				Fail("RestAction analysis fail")
			}
			if model.ActionMap["default"].GetMaxRetry() != 3 {
				Fail("RestAction analysis fail")
			}
			if model.ActionMap["custom"].GetMaxRetry() != 6 {
				Fail("RestAction analysis fail")
			}
		})
	})

})
