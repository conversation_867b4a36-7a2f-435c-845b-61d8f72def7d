package parser

import (
	"encoding/xml"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"github.com/fatih/structs"
	"qiniu.io/qbpm/engine/bpmn/behaviors"
	"qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/util"
)

// ParseBpmn Parse bpmn config file as a model
func ParseBpmn(bytes []byte) (model *models.BpmnModel, err error) {
	err = xml.Unmarshal(bytes, &model)
	if err != nil {
		return
	}

	if err = parseAuths(model); err != nil {
		return
	}

	if err = parseActions(model); err != nil {
		return
	}

	if err = parseNodes(model); err != nil {
		return
	}

	if err = validateAction(model); err != nil {
		return
	}

	setDefaultListener(model)
	setBehaviors(model)
	return
}

func parseNodes(model *models.BpmnModel) (err error) {
	if model.Process == nil {
		return fmt.Errorf("no process defined")
	}

	model.Process.NodeMap = make(map[string]models.INode, 0)

	process := structs.New(model.Process)
	processValues := process.Values()
	for _, fieldValue := range processValues {
		processField := reflect.ValueOf(fieldValue)
		if processField.Kind() != reflect.Slice {
			continue
		}

		for i := 0; i < processField.Len(); i++ {
			if node, ok := processField.Index(i).Interface().(models.INode); ok {
				if node.GetKey() == "" {
					return fmt.Errorf("empty node id")
				} else if _, ok := model.Process.NodeMap[node.GetKey()]; ok {
					return fmt.Errorf("duplicate node id: %s", node.GetKey())
				}
				model.Process.NodeMap[node.GetKey()] = node
			} else {
				break
			}
		}
	}

	for _, fieldValue := range processValues {
		if seqFlows, ok := fieldValue.([]*models.SequenceFlow); ok {
			for _, f := range seqFlows {
				if src, ok := model.Process.NodeMap[f.SourceRef]; ok {
					src.AddOutgoings(f)
					f.SourceNode = src
				} else {
					return fmt.Errorf("src node not found: %s", f.SourceRef)
				}

				if dst, ok := model.Process.NodeMap[f.TargetRef]; ok {
					dst.AddIncomings(f)
					f.TargetNode = dst
				} else {
					return fmt.Errorf("dst node not found: %s", f.TargetRef)
				}
			}
		}
	}

	return
}

func setBehaviors(model *models.BpmnModel) {
	for _, node := range model.Process.NodeMap {
		switch node.GetType() {
		case "UserTask", "SuggestTask":
			node.SetBehavior(&behaviors.UserTaskBehavior{})
		case "StartEvent":
			node.SetBehavior(&behaviors.StartEventBehavior{})
		case "EndEvent":
			node.SetBehavior(&behaviors.EndEventBehavior{})
		case "ParallelGateway":
			node.SetBehavior(&behaviors.ExclusiveGatewayBehavior{})
		case "ExclusiveGateway":
			node.SetBehavior(&behaviors.ExclusiveGatewayBehavior{})
		case "InclusiveGateway":
			node.SetBehavior(&behaviors.InclusiveGatewayBehavior{})
		case "ServiceTask":
			node.SetBehavior(&behaviors.ServiceTaskBehavior{})
		case "IntermediateCatchEvent":
			node.SetBehavior(&behaviors.CatchEventBehavior{})
		default:
			node.SetBehavior(&behaviors.NodeBehavior{})
		}
	}
}

func parseAuths(model *models.BpmnModel) error {
	if model.Auths == nil {
		return nil
	}

	model.AuthMap = make(map[string]*models.Auth)
	for _, auth := range model.Auths {
		if _, ok := model.AuthMap[auth.ID]; ok {
			return fmt.Errorf("duplicate auth id %s", auth.ID)
		}
		model.AuthMap[auth.ID] = auth
	}

	return nil
}

func parseActions(model *models.BpmnModel) error {
	if model.RestActions == nil {
		return nil
	}

	model.ActionMap = make(map[string]models.Actioner, 0)
	for _, action := range model.RestActions {
		if action.GetID() == "" {
			continue
		}

		if _, ok := model.ActionMap[action.GetID()]; ok {
			return fmt.Errorf("duplicate action id %s", action.GetID())
		}
		model.ActionMap[action.GetID()] = action
	}

	return nil
}

func checkStoreKey(storeKey string) bool {
	if storeKey == "" {
		return true
	}

	if !strings.HasPrefix(storeKey, "params.") {
		return false
	}

	if strings.HasPrefix(storeKey, "params.flow") ||
		strings.HasPrefix(storeKey, "params.process") ||
		strings.HasPrefix(storeKey, "params.execute") {
		return false
	}

	return true
}

func validateAction(model *models.BpmnModel) error {
	for _, action := range model.ActionMap {
		if !checkStoreKey(action.GetStoreKey()) {
			return errors.New("invalidate storekey")
		}
	}
	return nil
}

func validateExclusiveGateway(model *models.BpmnModel) error {
	for _, gateway := range model.Process.ExclusiveGateways {
		defaultCnt := 0

		for _, sequence := range gateway.GetOutgoings() {
			if sequence.Condition != nil && sequence.ID != gateway.DefaultFlow {
				continue
			}

			defaultCnt++
			if defaultCnt > 1 {
				return errors.New("more than 1 default flow found for exclusive gateway")
			}
		}
	}
	return nil
}

func setDefaultListener(model *models.BpmnModel) {
	for _, node := range model.Process.NodeMap {
		if node.IsNotifyDisabled() {
			continue
		}

		switch node.GetType() {
		case "UserTask", "SuggestTask":
			if !hasNotifyListener(node) {
				node.AddListener(newListener("assign", "assignNotify"))
				node.AddListener(newListener("assign", "assignWechatNotify"))
			}
		case "EndEvent":
			if !hasNotifyListener(node) {
				node.AddListener(newListener("process.end", "completeNotify"))
				node.AddListener(newListener("process.end", "completeWechatNotify"))
			}
		}
	}
}

func hasNotifyListener(node models.INode) bool {
	for _, l := range node.GetListeners() {
		for _, action := range l.ActionRefs {
			if strings.HasSuffix(strings.ToLower(action), "notify") {
				return true
			}
		}
	}
	return false
}

func newListener(fire string, action string) *models.Listener {
	return &models.Listener{
		BaseElement: models.BaseElement{
			ID: util.RandomStringr(8),
		},
		Fire:       fire,
		ActionRefs: []string{action},
	}
}
