package runtime

import (
	"fmt"
	"strings"
	"time"

	"github.com/qbox/bo-base/v3/lock"
	"github.com/xen0n/go-workwx"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
)

const (
	workItemWechatTemplate = `**[审批待办提醒]**
>Hi,
>当前您还有 ` + "`%d`" + ` 个审批任务待处理，请尽快处理。
><font color="comment">审批地址：</font>%s/approval/list
>[点此去处理](%s/approval/list)
>
`
	workItemStarterWechatTemplate = `**[审批跟进提醒]**
>Hi,
>当前您还有 ` + "`%d`" + ` 个审批任务在审核中，请及时跟进并推进审批。
><font color="comment">审批地址：</font>%s/approval/list?view_type=1
>[点此去处理](%s/approval/list?view_type=1)
>
`
)

var jobFuncs = map[string]func(s *runtime){
	"work_item_wechat_notify":         workItemsNotify,
	"work_item_starter_wechat_notify": workItemsStarterNotify,
	"work_item_auto_refuse_ddl":       workItemsAutoRefuseDDL,
}

type UserTaskCount struct {
	Assignee string
	Cnt      int
}

func (s *runtime) startJob(jobSpecs map[string]string) {
	if len(jobSpecs) == 0 {
		return
	}

	for job, specs := range jobSpecs {
		if specs == "" {
			continue
		}

		if jobFunc, ok := jobFuncs[job]; !ok {
			continue
		} else {
			_, err := s.cronJob.AddFunc(specs, func() {
				// 这里其实限制了 job 最快的频率不能高于 次/20s, 否则前一个任务执行时间超过此时间时，下一个任务必定 get lock failed
				expiry := time.Second * 20
				l := lock.NewRedisLocker(
					s.RedisCli,
					lock.WithExpiry(expiry),
					lock.WithSpinTimes(1),
					lock.WithAutoRenewal(true),
				)
				if !l.Lock(job) {
					s.Logger.Warnf("start work %s job failed, cannot get job lock", job)
					return
				}
				defer l.Unlock(job)
				jobFunc(s)
			})
			if err != nil {
				s.Logger.WithError(err).Warnf("add job func failed, job: %s, specs: %s", job, specs)
				continue
			}
		}
	}

	s.cronJob.Start()
}

func workItemsNotify(s *runtime) {
	var userTaskCnt = make([]*UserTaskCount, 0)
	err := s.RunExecutionMgr.Model(&models.RunExecution{}).
		Select("assignee, count(1) as cnt").
		Where("status = ?", enums.RunExecutionStatusPending).
		Group("assignee").
		Scan(&userTaskCnt).Error
	if err != nil {
		s.Logger.
			WithError(err).
			Error("<RuntimeService.workItemsNotify> group execution by assignee failed")
		return
	}

	for _, record := range userTaskCnt {
		err := s.workItemWechatNotify(record.Assignee, record.Cnt, workItemWechatTemplate)
		if err != nil {
			s.Logger.WithError(err).WithField("assignee", record.Assignee).Warn("work item wechat notify failed")
		}
	}
}

type SubmittedTaskCount struct {
	StartByID string
	Cnt       int
}

func workItemsStarterNotify(s *runtime) {
	var taskCnt = make([]*SubmittedTaskCount, 0)
	err := s.RunExecutionMgr.Model(&models.RunExecution{}).
		Select("start_by_id, count(distinct process_instance_id) as cnt").
		Where("status = ?", enums.RunExecutionStatusPending).
		Group("start_by_id").
		Scan(&taskCnt).Error
	if err != nil {
		s.Logger.
			WithError(err).
			Error("<RuntimeService.workItemsStarterNotify> group execution by start_by_id failed")
		return
	}

	for _, record := range taskCnt {
		err := s.workItemWechatNotify(record.StartByID, record.Cnt, workItemStarterWechatTemplate)
		if err != nil {
			s.Logger.WithError(err).WithField("start_by_id", record.StartByID).Warn("work item starter wechat notify failed")
		}
	}
}

func (s *runtime) workItemWechatNotify(userEmail string, count int, template string) error {
	if s.workwx == nil || userEmail == "" || count == 0 {
		return nil
	}

	// 非生产环境由于企业微信 IP 白名单限制等原因，无法发送
	// 直接返回避免产生大量报错污染测试环境日志
	if !s.cfg.Mode.IsProd() {
		return nil
	}

	if !strings.Contains(userEmail, "@qiniu.com") {
		return nil
	}
	userID := strings.TrimSuffix(userEmail, "@qiniu.com")
	content := fmt.Sprintf(template,
		count, s.cfg.Services.PortalIOHost, s.cfg.Services.PortalIOHost)
	return s.workwx.SendMarkdownMessage(&workwx.Recipient{
		UserIDs: []string{userID},
	}, content, false)
}

func workItemsAutoRefuseDDL(s *runtime) {
	var tasks = make([]*models.RunExecution, 0)
	err := s.RunExecutionMgr.Model(&models.RunExecution{}).
		Where("status = ?", enums.RunExecutionStatusPending).
		Where("ddl > 0 AND ddl <= ?", time.Now().Unix()).
		Scan(&tasks).Error
	if err != nil {
		s.Logger.
			WithError(err).
			Error("<RuntimeService.workItemsAutoRefuseDDL> list expired ddl failed")
		return
	}
	for _, task := range tasks {
		err = s.FailExecution(task.ID, task.Params, "", "审批单过期，系统自动驳回", enums.CallerSystem)
		if err != nil {
			s.Logger.WithError(err).
				WithField("execution_id", task.ID).
				Warn("work item auto refuse failed")
		}
	}
}
