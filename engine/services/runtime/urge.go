package runtime

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/xen0n/go-workwx"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
)

const UrgeWechatTemplate = `**[审批催办]**
>Hi,
>您有一份来自 %s 的审批催办请求。
>
>**【审批信息】**
><font color="comment">UID： </font>%d
><font color="comment">审批业务：</font>%s
><font color="comment">审批地址：</font>%s
>[点此去处理](%s)
`
const cachePrefixUrge = "urge:%d"

func (s *runtime) UrgeByExecution(executionID uint64, operator, memo string) error {
	var (
		runExecutionMgr *mgr.RunExecutionMgr = mgr.NewRunExecutionMgr(*s.Mgr)
		executions      []*models.RunExecution
	)

	if err := runExecutionMgr.Where("id = ?", executionID).Find(&executions).Error; err != nil {
		s.Logger.
			WithField("id", executionID).
			WithError(err).
			Error("<RuntimeService.UrgeByExecution> runExecutionMgr.Model.First")
		return err
	}

	return s.urge(executions, executionID, operator, memo)
}

func (s *runtime) UrgeByProcess(processID uint64, operator, memo string) error {
	var (
		runExecutionMgr *mgr.RunExecutionMgr = mgr.NewRunExecutionMgr(*s.Mgr)
		executions      []*models.RunExecution
	)

	if err := runExecutionMgr.Where("process_instance_id = ?", processID).Find(&executions).Error; err != nil {
		s.Logger.
			WithField("id", processID).
			WithError(err).
			Error("<RuntimeService.UrgeByProcess> runExecutionMgr.Model.First")
		return err
	}

	return s.urge(executions, processID, operator, memo)
}

func (s *runtime) urge(executions []*models.RunExecution, bizID uint64, operator, memo string) error {
	if len(executions) == 0 {
		return nil
	}

	var (
		processInstance models.ProcessInstance
		processMgr      *mgr.ProcessInstanceMgr = &mgr.ProcessInstanceMgr{Base: *s.Mgr}
	)

	key := fmt.Sprintf(cachePrefixUrge, bizID)
	ctx := context.Background()
	val := s.RedisCli.Get(ctx, key).Val()
	if val == fmt.Sprint(bizID) {
		t := s.RedisCli.TTL(ctx, key).Val()
		return fmt.Errorf("您已经催办过，请 %.1f 分钟后再尝试", t.Minutes())
	}

	processID := executions[0].ProcessInstanceID

	if err := processMgr.First(&processInstance, processID).Error; err != nil {
		s.Logger.
			WithField("id", processID).
			WithError(err).
			Error("<RuntimeService.Urge> processMgr.Model.First")
		return err
	}

	processDefinition, err := s.Repository.GetProcessByID(processInstance.ProcessDefinitionID)
	if err != nil {
		s.Logger.
			WithField("id", processInstance.ProcessDefinitionID).
			WithError(err).
			Error("<RuntimeService.Urge> Repository.GetProcessByID")
		return err
	}

	urgeMgr := mgr.UrgeTaskMgr{Base: *s.Mgr}
	for _, exec := range executions {
		if exec.Status.IsEnd() {
			continue
		}

		err = s.urgeWorkwxNotify(exec, &processInstance, &processDefinition, operator, memo)
		if err != nil {
			return err
		}

		_, err = urgeMgr.Create(&models.UrgeTask{
			ExecutionID: exec.ID,
			Assignee:    exec.Assignee,
			UrgeBy:      operator,
			Memo:        memo,
		})
		if err != nil {
			// NOTE: 这里记录只是方便统计数据，不影响核心业务，故此处不返回错误
			s.Logger.
				WithField("execution_id", exec.ID).
				WithField("operator", operator).
				WithError(err).
				Warn("<RuntimeService.Urge> save urge task failed")
		}
	}

	s.RedisCli.Set(ctx, key, fmt.Sprint(bizID), time.Hour)
	return nil
}

func (s *runtime) urgeWorkwxNotify(
	execution *models.RunExecution,
	processInstance *models.ProcessInstance,
	processDefinition *models.ProcessDefinition,
	operator string,
	memo string,
) error {
	if s.workwx == nil || execution == nil || execution.Assignee == "" || processInstance == nil {
		return nil
	}

	// 非生产环境由于企业微信 IP 白名单限制等原因，无法发送
	// 直接返回避免产生大量报错污染测试环境日志
	if !s.cfg.Mode.IsProd() {
		return nil
	}
	if !strings.Contains(execution.Assignee, "@qiniu.com") {
		return nil
	}

	assignee := strings.TrimSuffix(execution.Assignee, "@qiniu.com")
	detailURL := s.approvalDetailURL(
		processDefinition.Key,
		processInstance.Excode,
		processInstance.UID,
		processInstance.ID,
	)
	content := fmt.Sprintf(UrgeWechatTemplate,
		operator, execution.UID, processInstance.Name,
		detailURL, detailURL,
	)
	return s.workwx.SendMarkdownMessage(&workwx.Recipient{
		UserIDs: []string{assignee},
	}, content, false)
}

func (s *runtime) approvalDetailURL(
	definitionKey string,
	excode string,
	uid uint64,
	processInstanceID uint64,
) string {
	return fmt.Sprintf(
		"%s/approval/%s/%s?uid=%d&process_instance_id=%d",
		s.cfg.Services.PortalIOHost, definitionKey, excode, uid, processInstanceID,
	)
}

// 检查流程是否有被催办
func (s *runtime) HasUrgeTasksByProcessIDs(processInstanceIDs []uint64) (map[uint64]bool, error) {
	if len(processInstanceIDs) == 0 {
		return make(map[uint64]bool), nil
	}

	// 初始化结果映射，默认所有流程都没有催办记录
	results := make(map[uint64]bool, len(processInstanceIDs))
	for _, id := range processInstanceIDs {
		results[id] = false
	}

	// 查询有催办记录的流程实例ID
	var urgedProcessIDs []uint64
	db := mgr.RunExecutionMgr{Base: *s.Mgr}.DB.Table("run_execution").
		Select("DISTINCT run_execution.process_instance_id").
		Joins("JOIN urge_task ON urge_task.execution_id = run_execution.id").
		Where("run_execution.process_instance_id IN (?)", processInstanceIDs)

	if err := db.Pluck("run_execution.process_instance_id", &urgedProcessIDs).Error; err != nil {
		s.Logger.
			WithField("process_instance_ids", processInstanceIDs).
			WithError(err).
			Error("<RuntimeService.HasUrgeTasksByProcessIDs> failed to query urge tasks")
		return nil, err
	}

	// 更新有催办记录的流程实例状态
	for _, id := range urgedProcessIDs {
		results[id] = true
	}

	return results, nil
}
