package runtime_test

import (
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	sofaSDK "github.com/qbox/pay-sdk/sofa/client/user"
	"qiniu.io/qbpm/engine/conf"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/mock"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/services/history"
	"qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/services/runtime"
	"qiniu.io/qbpm/engine/services/scheduler"
	"qiniu.io/qbpm/engine/services/system"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("execute job", func() {

	var (
		base              *mgr.Base
		runtimeService    runtime.Service
		repositoryService repository.Service
		schedulerService  scheduler.Service
		sofaService       sofaSDK.ClientService
		historyService    history.Service
	)

	BeforeEach(func() {
		base = test.Init()
		redis := test.InitRedis()
		sofaService = mock.NewMockSofaService()
		servBase := &services.Base{Mgr: base, RedisCli: redis}

		_ = system.NewSystemService(&services.Base{Mgr: base}, nil)
		repositoryService = repository.NewRepositoryService(servBase)
		schedulerService = scheduler.NewService(redis)
		historyService = history.NewHistoryService(servBase, repositoryService, nil)
		runtimeService = runtime.NewService(servBase, repositoryService, schedulerService, historyService, sofaService, nil, nil, &conf.Config{})
	})

	Describe("RuntimeController", func() {

		JustBeforeEach(func() {
			model := &models.ProcessDefinition{
				DeployAt: time.Now(),
				Name:     "testManagerProcess",
				Key:      "testManagerProcess",
				XMLData: []byte(
					`<?xml version="1.0" encoding="UTF-8"?>
							<definitions id="definitions"
	  						xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
	  							xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

								<process id="flow-test" name="flow-test">
									<startEvent id="theStart"/>
									<sequenceFlow id="startManager1" sourceRef="theStart" targetRef="manager1"/>

									<userTask id="manager1" assigneeType="manager"/>
									<sequenceFlow id="toParallel" sourceRef="manager1" targetRef="parallel"/>

									<parallelGateway id="parallel"/>
									<sequenceFlow id="parallel1" sourceRef="parallel" targetRef="static1"/>
									<sequenceFlow id="parallel2" sourceRef="parallel" targetRef="manager2"/>

									<userTask id="static1" assigneeType="static" assignee="<EMAIL>"/>
									<sequenceFlow id="static1Join" sourceRef="static1" targetRef="join"/>
									<userTask id="manager2" assigneeType="manager"/>
									<sequenceFlow id="manager2Join" sourceRef="manager2" targetRef="join"/>
									
									<parallelGateway id="join"/>
									<sequenceFlow id="joinEnd" sourceRef="join" targetRef="theEnd"/>

									<endEvent id="theEnd"/>
								</process>

							</definitions>`),
			}
			err := repositoryService.Create(model)
			Expect(err).To(BeNil())
		})

		It("should finish a process with manager", func() {
			// 发起流程到上级审批
			exec, _ := runtimeService.StartProcess("testManagerProcess", "", "001", "<EMAIL>", 0, 0)
			time.Sleep(time.Second)
			execs, _, _ := runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			Expect(execs).To(HaveLen(1))
			Expect(execs[0].Assignee).To(Equal("<EMAIL>"))

			// 上级审批完成到总监审批
			runtimeService.CompleteExecution(execs[0].ID, "", "", "")
			time.Sleep(time.Second)

			execs, _, _ = runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			Expect(execs).To(HaveLen(1))
			Expect(execs[0].Assignee).To(Equal("<EMAIL>"))

			// 总监审批完成到并行节点审批
			runtimeService.CompleteExecution(execs[0].ID, "", "", "")
			time.Sleep(time.Second)

			execs, _, _ = runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			Expect(execs).To(HaveLen(2))

			levelApproval := execs[0]
			staticApproval := execs[1]

			if execs[0].Assignee != "<EMAIL>" {
				levelApproval = execs[1]
				staticApproval = execs[0]
			}

			Expect(levelApproval.Assignee, "<EMAIL>")
			Expect(staticApproval.Assignee, "<EMAIL>")

			// 上级审批
			runtimeService.CompleteExecution(levelApproval.ID, "", "", "")
			time.Sleep(time.Second)

			execs, _, _ = runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			Expect(execs).To(HaveLen(2))

			// 静态审批
			runtimeService.CompleteExecution(staticApproval.ID, "", "", "")
			time.Sleep(time.Second)

			execs, _, _ = runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			Expect(execs).To(HaveLen(2))

			processInstance, err := historyService.GetProcessByID(exec.ProcessInstanceID)
			Expect(err).To(BeNil())
			Expect(processInstance.Status).To(Equal(enums.ProcessStatusPending))

			levelApproval = execs[0]
			if execs[0].Assignee != "<EMAIL>" {
				levelApproval = execs[1]
			}

			// 总监审批，流程结束
			runtimeService.CompleteExecution(levelApproval.ID, "", "", "")
			time.Sleep(time.Second)

			schedulerService.Shutdown()

			execs, _, _ = runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			Expect(execs).To(HaveLen(0))

			processInstance, err = historyService.GetProcessByID(exec.ProcessInstanceID)
			Expect(err).To(BeNil())
			Expect(processInstance.Status).To(Equal(enums.ProcessStatusCompleted))
		})

		It("should finish a process without manager", func() {
			model := &models.ProcessDefinition{
				DeployAt: time.Now(),
				Name:     "testManagerProcess",
				Key:      "testManagerProcess",
				XMLData: []byte(
					`<?xml version="1.0" encoding="UTF-8"?>
							<definitions id="definitions"
	  						xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
	  							xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

								<process id="flow-test" name="flow-test">
									<startEvent id="theStart"/>
									<sequenceFlow id="startManager1" sourceRef="theStart" targetRef="manager1"/>

									<userTask id="manager1" assigneeType="manager"/>
									<sequenceFlow id="toParallel" sourceRef="manager1" targetRef="parallel"/>

									<parallelGateway id="parallel"/>
									<sequenceFlow id="parallel1" sourceRef="parallel" targetRef="static1"/>
									<sequenceFlow id="parallel2" sourceRef="parallel" targetRef="manager2"/>

									<userTask id="static1" assigneeType="static" assignee="<EMAIL>"/>
									<sequenceFlow id="static1Join" sourceRef="static1" targetRef="join"/>
									<userTask id="manager2" assigneeType="manager"/>
									<sequenceFlow id="manager2Join" sourceRef="manager2" targetRef="join"/>
									
									<parallelGateway id="join"/>
									<sequenceFlow id="joinEnd" sourceRef="join" targetRef="theEnd"/>

									<endEvent id="theEnd"/>
								</process>

							</definitions>`),
			}
			err := repositoryService.Create(model)
			Expect(err).To(BeNil())

			// 发起流程到并行审批
			exec, _ := runtimeService.StartProcess("testManagerProcess", "", "002", "<EMAIL>", 0, 0)
			time.Sleep(time.Second)
			execs, _, _ := runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			Expect(execs).To(HaveLen(2))

			processInstance, err := historyService.GetProcessByID(exec.ProcessInstanceID)
			Expect(err).To(BeNil())
			Expect(processInstance.Status).To(Equal(enums.ProcessStatusPending))

			staticApproval := execs[0]
			if staticApproval.OriginalAssignee == "" {
				staticApproval = execs[1]
			}

			// 静态节点审批
			runtimeService.CompleteExecution(staticApproval.ID, "", "", "")
			time.Sleep(time.Second)

			schedulerService.Shutdown()

			execs, _, _ = runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			Expect(execs).To(HaveLen(0))

			processInstance, err = historyService.GetProcessByID(exec.ProcessInstanceID)
			Expect(err).To(BeNil())
			Expect(processInstance.Status).To(Equal(enums.ProcessStatusCompleted))
		})
	})
})
