package runtime

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/qbox/bo-base/v3/base"
	"github.com/qbox/bo-base/v3/lock"
	"github.com/qbox/bo-base/v3/sync/resultgroup"
	sofaSDK "github.com/qbox/pay-sdk/sofa/client/user"
	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"
	"github.com/xen0n/go-workwx"
	bpmnModel "qiniu.io/qbpm/engine/bpmn/models"
	bpmnEnum "qiniu.io/qbpm/engine/bpmn/models/enums"
	"qiniu.io/qbpm/engine/conf"
	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/jobs"
	"qiniu.io/qbpm/engine/lib/gaea"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/services/history"
	"qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/services/scheduler"
	"qiniu.io/qbpm/engine/util"
	"qiniu.io/qbpm/engine/util/logger"
)

type runtime struct {
	*services.Base
	Logger          logrus.FieldLogger
	RunExecutionMgr mgr.RunExecutionMgr
	Repository      repository.Service
	Scheduler       scheduler.Service
	HistoryService  history.Service
	AdminTransport  http.RoundTripper
	sofaService     sofaSDK.ClientService
	gaeaService     gaea.GaeaAdminService
	cfg             *conf.Config
	workwx          *workwx.WorkwxApp
	cronJob         *cron.Cron
}

// NewService instances a runtime service to handle runtime actions
func NewService(
	srvBase *services.Base,
	repositoryService repository.Service,
	scheduler scheduler.Service,
	historyService history.Service,
	sofaService sofaSDK.ClientService,
	gaeaService gaea.GaeaAdminService,
	adminTransport http.RoundTripper,
	cfg *conf.Config,
) Service {

	workwx := workwx.New(cfg.Services.Workwx.CorpID)
	workwxApp := workwx.WithApp(cfg.Services.Workwx.CorpSecret, cfg.Services.Workwx.AgentID)
	workwxApp.SpawnAccessTokenRefresher()

	r := &runtime{
		Logger:          logrus.WithField(logger.FieldKeyPrefix, "RuntimeService"),
		Base:            srvBase,
		RunExecutionMgr: mgr.RunExecutionMgr{Base: *srvBase.Mgr},
		Repository:      repositoryService,
		Scheduler:       scheduler,
		sofaService:     sofaService,
		HistoryService:  historyService,
		gaeaService:     gaeaService,
		AdminTransport:  adminTransport,
		cfg:             cfg,
		workwx:          workwxApp,
		cronJob: cron.New(
			cron.WithLocation(base.CST),
			cron.WithSeconds(),
		),
	}

	go func() {
		r.startJob(cfg.JobSpecs)
	}()

	return r
}

// StartProcess starts a new process by pre-defined process definition key
func (s *runtime) StartProcess(
	key string,
	params string,
	excode string,
	operator string,
	uid uint64,
	ddl int64,
) (
	runExecution *models.RunExecution, err error,
) {
	lockKey := fmt.Sprintf("qbpm:run:%s:%s", key, excode)
	lock := lock.NewRedisLocker(
		s.RedisCli,
		lock.WithAutoRenewal(true),
		lock.WithExpiry(time.Second*30),
		lock.WithSpinTimes(1000),
	)

	if !lock.Lock(lockKey) {
		return nil, errors.New("duplicate operation")
	}

	defer lock.Unlock(lockKey)

	// look for process definition
	processDefinition, err := s.Repository.GetProcessByKey(key)
	if err != nil {
		return
	}

	var existCount int
	err = mgr.NewRunExecutionMgr(*s.Mgr).Model(&models.RunExecution{}).
		Where(models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			Excode:              excode,
		}).Count(&existCount).Error
	if err != nil {
		return nil, err
	} else if existCount > 0 {
		return nil, errors.New("already in process")
	}

	// excode should exists if contains userTasks
	if excode == "" && len(processDefinition.ProcessModel.Process.UserTasks) > 0 {
		err = errors.New("excode required")
		return
	}

	if params != "" {
		params = fmt.Sprintf(`{"data":%s}`, params)
	}

	// new process instance
	runExecution, err = models.NewRunExecution(
		processDefinition,
		params,
		excode,
		operator,
		uid,
		ddl,
	)
	if err != nil {
		return
	}

	processInstance := &models.ProcessInstance{
		Base: models.Base{
			ID: runExecution.ProcessInstanceID,
		},
		ProcessDefinitionID:    runExecution.ProcessDefinitionID,
		SuperProcessInstanceID: runExecution.ProcessInstanceID,
		Name:                   processDefinition.Name,
		Excode:                 runExecution.Excode,
		UID:                    runExecution.UID,
		Params:                 runExecution.Params,
		StartByID:              runExecution.StartByID,
		StartAt:                runExecution.StartAt,
		Status:                 enums.ProcessStatusPending,
	}

	// save
	s.RunExecutionMgr.PerformTransaction(func(db *mgr.Base) (err error) {
		// create process
		if err = db.Create(processInstance).Error; err != nil {
			s.Logger.
				WithField("processInstance", processInstance).
				WithError(err).
				Errorf("<RuntimeService.StartProcess> create process instance with error: %s", err)
			return
		}

		// create execution
		if err = db.Create(runExecution).Error; err != nil {
			s.Logger.
				WithField("runExecution", runExecution).
				WithError(err).
				Errorf("<RuntimeService.StartProcess> create execution with error: %s", err)
			return
		}

		if err = db.Create(models.NewActivityInstance(runExecution, "StartEvent")).Error; err != nil {
			return
		}

		return
	})

	// put into scheduler

	s.Scheduler.Put(jobs.NewExecute(ctx.BuildContext(
		runExecution,
		&processDefinition,
		s.Mgr,
		s.AdminTransport,
		s.sofaService,
		s.gaeaService,
		s.Logger,
		false,
	)))

	return
}

// CompleteExecution completes a execution task
func (s *runtime) CompleteExecution(executionID uint64, params, operator, memo string) (err error) {
	var (
		runExecution       models.RunExecution
		processInstance    models.ProcessInstance
		runExecutionMgr    *mgr.RunExecutionMgr
		processInstanceMgr mgr.ProcessInstanceMgr
	)
	runExecutionMgr = mgr.NewRunExecutionMgr(*s.Mgr)
	if err = runExecutionMgr.First(&runExecution, executionID).Error; err != nil {
		s.Logger.
			WithField("id", executionID).
			WithError(err).
			Error("<RuntimeService.CompleteExecution> runExecutionMgr.Model.First")
		return
	}

	processInstanceMgr = mgr.NewProcessInstanceMgr(*s.Mgr)
	err = processInstanceMgr.Model(&models.ProcessInstance{}).
		Where("id=?", runExecution.ProcessInstanceID).
		Find(&processInstance).
		Error
	if err != nil {
		s.Logger.
			WithField("execution_id", executionID).
			WithField("process_instance_id", runExecution.ProcessInstanceID).
			WithError(err).
			Error("<RuntimeService.CompleteExecution> processInstanceMgr.Model.ProcessInstanceID")
		return
	}

	// 不能对已挂起的流程操作
	if processInstance.Status == enums.ProcessStatusSuspended {
		s.Logger.
			WithField("process_instance_id", runExecution.ProcessInstanceID).
			Warn("<RuntimeService.CompleteExecution>processInstance currently suspended")
		return errors.New("process is currently suspended")
	}

	processDefinition, err := s.Repository.GetProcessByID(runExecution.ProcessDefinitionID)
	if err != nil {
		s.Logger.
			WithField("id", runExecution.ProcessDefinitionID).
			WithError(err).
			Error("<RuntimeService.CompleteExecution> s.Repository.GetProcessByID")
		return
	}

	// save params by scope
	// runExecution.Params = params

	runExecution.Status = enums.RunExecutionStatusRunning
	runExecution.Assignee = operator
	runExecution.Memo = memo

	if err = runExecutionMgr.Save(runExecution).Error; err != nil {
		s.Logger.
			WithField("runExecution", runExecution).
			WithError(err).
			Error("<RuntimeService.CompleteExecution> runExecutionMgr.Save")
		return
	}

	s.Scheduler.Put(jobs.NewExecute(ctx.BuildContext(
		&runExecution,
		&processDefinition,
		s.Mgr,
		s.AdminTransport,
		s.sofaService,
		s.gaeaService,
		s.Logger,
		false)))

	return
}

// ExecutionOperateParam 节点操作请求参数
type ExecutionOperateParam struct {
	ExecutionID uint64 `json:"execution_id,omitempty"`
	Params      string `json:"params,omitempty"`
	Operator    string `json:"operator,omitempty"`
	Memo        string `json:"memo,omitempty"`
}

// AssignParam 分配人
type AssignParam struct {
	Assignee     string                `json:"assignee,omitempty"`
	AssigneeType bpmnEnum.AssigneeType `json:"assignee_type,omitempty"`
	DisplayName  string                `json:"display_name,omitempty"`
}

// CounterSignExecutionParam 加签请求参数
type CounterSignExecutionParam struct {
	ExecutionParam   ExecutionOperateParam    `json:"execution_param,omitempty"`
	CounterSignNodes []AssignParam            `json:"counter_sign_nodes,omitempty"`
	CounterSignType  bpmnEnum.CounterSignType `json:"counter_sign_type,omitempty"`
}

// CounterSignExecution counter-sign a execution task
func (s *runtime) CounterSignExecution(param CounterSignExecutionParam) (err error) {
	var (
		runExecution       models.RunExecution
		processInstance    models.ProcessInstance
		runExecutionMgr    *mgr.RunExecutionMgr
		processInstanceMgr mgr.ProcessInstanceMgr
	)

	runExecutionMgr = mgr.NewRunExecutionMgr(*s.Mgr)
	if err = runExecutionMgr.First(&runExecution, param.ExecutionParam.ExecutionID).Error; err != nil {
		s.Logger.
			WithField("id", param.ExecutionParam.ExecutionID).
			WithError(err).
			Error("<RuntimeService.CounterSignExecution> runExecutionMgr.Model.First")
		return
	}

	processInstanceMgr = mgr.NewProcessInstanceMgr(*s.Mgr)
	err = processInstanceMgr.Model(&models.ProcessInstance{}).
		Where("id=?", runExecution.ProcessInstanceID).
		Find(&processInstance).
		Error
	if err != nil {
		s.Logger.
			WithFields(logrus.Fields{
				"execution id":        param.ExecutionParam.ExecutionID,
				"process_instance_id": runExecution.ProcessInstanceID,
			}).
			WithError(err).
			Error("<RuntimeService.CompleteExecution> processInstanceMgr.Model.ProcessInstanceID")
		return
	}

	// 不能对已挂起的流程操作
	if processInstance.Status == enums.ProcessStatusSuspended {
		s.Logger.
			WithField("process_instance_id", runExecution.ProcessInstanceID).
			Warn("<RuntimeService.CompleteExecution>processInstance currently suspended")
		return errors.New("process is currently suspended")
	}

	processDefinition, err := s.Repository.GetProcessByID(runExecution.ProcessDefinitionID)
	if err != nil {
		s.Logger.
			WithField("id", runExecution.ProcessDefinitionID).
			WithError(err).
			Error("<RuntimeService.CounterSignExecution> s.Repository.GetProcessByID")
		return
	}

	node, _ := processDefinition.GetExecutionNode(&runExecution)
	task, ok := node.(*bpmnModel.UserTask)
	if !ok || !task.AllowCounterSign || len(task.CounterSign.CounterSigns) == 0 {
		return errors.New("counter-sign forbidden")
	}

	var signTypeForbidden bool
	if len(task.CounterSignTypes) > 0 {
		signTypeForbidden = true
		for _, t := range task.CounterSignTypes {
			if t == param.CounterSignType {
				signTypeForbidden = false
				break
			}
		}
	}

	if signTypeForbidden || !param.CounterSignType.Valid() {
		return fmt.Errorf("sign type %d forbidden", param.CounterSignType)
	}

	if len(param.CounterSignNodes) == 0 {
		return errors.New("counter sign nodes required")
	}

	var execs []*models.RunExecution
	var activities []*models.ActivityInstance

	{
		// 构造加签节点
		jobCtx := ctx.BuildContext(
			&runExecution,
			&processDefinition,
			s.Mgr,
			s.AdminTransport,
			s.sofaService,
			s.gaeaService,
			s.Logger,
			false)
		for _, assign := range param.CounterSignNodes {
			// 获取加签人
			a := assign.Assignee
			if assign.AssigneeType == bpmnEnum.AssigneeTypeManager {
				a = runExecution.StartByID
			}
			assignees, err := jobCtx.GetAssignee(assign.AssigneeType, a)
			if err != nil {
				s.Logger.WithError(err).
					Errorf("<RuntimeService.CounterSignExecution> ctx.GetAssignee(%s, %s)",
						assign.AssigneeType, assign.Assignee)
				return err
			}

			key := util.RandomStringr(4)
			for _, assignee := range strings.Split(assignees, ",") {
				exec := models.CounterSignRunExecution(
					&runExecution,
					param.CounterSignType,
					key,
					assign.DisplayName,
					param.ExecutionParam.Operator,
				)

				exec.Assignee = assignee
				exec.OriginalAssignee = assignee
				exec.AssigneeType = assign.AssigneeType

				if assignees == "" && assign.AssigneeType == bpmnEnum.AssigneeTypeManager {
					exec.Status = enums.RunExecutionStatusRunning
					exec.Memo = "系统自动通过"
				}

				activities = append(activities, models.NewActivityInstance(exec, "UserTask"))
				execs = append(execs, exec)
			}
		}
	}

	var suspendNodeKey string

	switch param.CounterSignType {
	case bpmnEnum.CounterSignTypeBefore:
		// 前加签将当前节点置为 suspend 状态
		suspendNodeKey = runExecution.ActivityDefineKey
	case bpmnEnum.CounterSignTypeAnd:
		// 并加签当前节点不做处理
		break
	default:
		runExecution.Status = enums.RunExecutionStatusRunning
		runExecution.Assignee = param.ExecutionParam.Operator
		runExecution.Memo = param.ExecutionParam.Memo
		execs = append(execs, &runExecution)
	}

	// suspend 当前节点并保存新增节点
	err = s.Mgr.PerformTransaction(func(db *mgr.Base) (err error) {
		if suspendNodeKey != "" {
			if err = db.Model(&models.RunExecution{}).
				Where("process_instance_id = ? and activity_define_key = ?", runExecution.ProcessInstanceID, suspendNodeKey).
				Update("status", enums.RunExecutionStatusSuspend).Error; err != nil {
				return
			}
			if err = db.Model(&models.ActivityInstance{}).
				Where("process_instance_id = ? and activity_define_key = ?", runExecution.ProcessInstanceID, suspendNodeKey).
				Update("status", enums.ActivityStatusSuspend).Error; err != nil {
				return
			}
		}

		for _, exec := range execs {
			if err = db.Save(exec).Error; err != nil {
				return
			}
		}

		if len(activities) > 0 {
			for _, act := range activities {
				if err = db.Create(act).Error; err != nil {
					return
				}
			}
		}

		return nil
	})

	if err != nil {
		s.Logger.
			WithField("runExecution", execs).
			WithError(err).
			Error("<RuntimeService.CounterSignExecution> db.PerformTransaction")
		return
	}

	// 加入调度并触发 assign 事件
	for _, exec := range execs {
		if exec.CounterSignType.Valid() && exec.Status == enums.RunExecutionStatusPending {
			go jobs.NewListener(node, &processDefinition, exec.ID, s.Mgr, s.AdminTransport).FireAction("assign")
		}
		s.Scheduler.Put(jobs.NewExecute(
			ctx.BuildContext(
				exec, &processDefinition, s.Mgr, s.AdminTransport,
				s.sofaService, s.gaeaService, s.Logger,
				param.CounterSignType == bpmnEnum.CounterSignTypeAfter,
			),
		))
	}

	return
}

// FailExecution fails a execution task
func (s *runtime) FailExecution(executionID uint64, params, operator, memo string, caller enums.Caller) (err error) {
	var (
		runExecution       models.RunExecution
		runExecutionMgr    *mgr.RunExecutionMgr
		processInstance    models.ProcessInstance
		processInstanceMgr mgr.ProcessInstanceMgr
	)
	runExecutionMgr = mgr.NewRunExecutionMgr(*s.Mgr)
	if err = runExecutionMgr.First(&runExecution, executionID).Error; err != nil {
		s.Logger.
			WithField("id", executionID).
			WithError(err).
			Error("<RuntimeService.FailExecution> runExecutionMgr.Model.First")
		return
	}

	processInstanceMgr = mgr.NewProcessInstanceMgr(*s.Mgr)
	err = processInstanceMgr.Model(&models.ProcessInstance{}).
		Where("id=?", runExecution.ProcessInstanceID).
		Find(&processInstance).
		Error
	if err != nil {
		s.Logger.
			WithField("id", executionID).
			WithError(err).
			Error("<RuntimeService.FailExecution> processInstanceMgr.Model.ProcessInstanceID")
		return
	}

	// 不能对已挂起的流程操作（仅限用户操作）
	if processInstance.Status == enums.ProcessStatusSuspended && caller.FromUser() {
		s.Logger.
			WithField("process_instance_id", runExecution.ProcessInstanceID).
			Warn("<RuntimeService.FailExecution>processInstance currently suspended")
		return errors.New("process is currently suspended")
	}

	processDefinition, err := s.Repository.GetProcessByID(runExecution.ProcessDefinitionID)
	if err != nil {
		s.Logger.
			WithField("id", runExecution.ProcessDefinitionID).
			Error("<RuntimeService.FailExecution> s.Repository.GetProcessByID")
		return
	}

	// save params by scope
	// runExecution.Params = params

	runExecution.Status = enums.RunExecutionStatusFailed
	runExecution.Assignee = operator
	runExecution.Memo = memo

	if err = runExecutionMgr.Save(runExecution).Error; err != nil {
		s.Logger.
			WithField("runExecution", runExecution).
			WithError(err).
			Error("<RuntimeService.FailExecution> runExecutionMgr.Save")
		return
	}

	s.Scheduler.Put(jobs.NewFlow(ctx.BuildContext(
		&runExecution,
		&processDefinition,
		s.Mgr,
		s.AdminTransport,
		s.sofaService,
		s.gaeaService,
		s.Logger,
		false)))

	if caller.FromUser() {
		// 记录驳回事件
		err = s.HistoryService.CreateProcessActivityLog(&models.ProcessActivityLog{
			ProcessInstanceID: runExecution.ProcessInstanceID,
			ActivityType:      enums.ActivityLogTypeRejected,
			Operator:          operator,
			Content:           memo,
		})

		if err != nil {
			s.Logger.
				WithField("process_instance_id", runExecution.ProcessInstanceID).
				WithError(err).
				Error("<RuntimeService.FailProcessesByExcodes> create process activity log failed")
		}
	}
	return
}

// FailExecutionsByExcodes fail executions with specified excodes
func (s *runtime) FailExecutionsByExcodes(
	excodes []string,
	operator, memo string,
	caller enums.Caller,
) error {
	var (
		runExecution       models.RunExecution
		runExecutions      []models.RunExecution
		runExecutionMgr    *mgr.RunExecutionMgr
		processInstances   []models.ProcessInstance
		processInstanceMgr mgr.ProcessInstanceMgr
	)
	runExecutionMgr = mgr.NewRunExecutionMgr(*s.Mgr)
	runExecutions, err := runExecutionMgr.ListExecutionsByExcodes(excodes)
	if err != nil {
		s.Logger.WithFields(logrus.Fields{
			"excodes":  excodes,
			"operator": operator,
			"memo":     memo,
		}).WithError(err).Error("list executions by excodes failed")
		return err
	}

	processInstanceIDs := make([]uint64, 0)
	for _, execution := range runExecutions {
		processInstanceIDs = append(processInstanceIDs, execution.ProcessInstanceID)
	}

	processInstanceMgr = mgr.NewProcessInstanceMgr(*s.Mgr)
	err = processInstanceMgr.Model(&models.ProcessInstance{}).
		Where("id IN (?)", processInstanceIDs).Find(&processInstances).Error
	if err != nil {
		s.Logger.
			WithField("runExecution", runExecution).
			WithError(err).
			Error("<RuntimeService.FailExecutionByExcodes> list process instance by id failed")
		return err
	}

	validExecutions := make([]models.RunExecution, 0)
	// 已挂起的流程（如果是用户调用）不能驳回
	for _, execution := range runExecutions {
		for _, instance := range processInstances {
			isSuspendedProcess := instance.Status == enums.ProcessStatusSuspended && caller.FromUser()
			if execution.ProcessInstanceID == instance.ID && !isSuspendedProcess {
				validExecutions = append(validExecutions, execution)
				break
			}
		}
	}

	type temp struct {
		ProcessDefinitionID uint64
		ProcessDefinition   models.ProcessDefinition
	}

	resultGroup := resultgroup.Group[uint64, temp]{
		MaxConcurrency: len(excodes),
	}

	executionDefinitionMap := make(map[models.RunExecution]models.ProcessDefinition)
	processDefinitionIDSet := make(map[uint64]struct{})
	for _, execution := range validExecutions {
		processDefinitionIDSet[execution.ProcessDefinitionID] = struct{}{}
	}

	for processDefinitionID := range processDefinitionIDSet {
		resultGroup.Queue(processDefinitionID)
	}

	oks, errs := resultGroup.Do(func(processDefinitionID uint64) (temp, error) {
		processDefinition, err := s.Repository.GetProcessByID(processDefinitionID)
		return temp{
			ProcessDefinitionID: processDefinitionID,
			ProcessDefinition:   processDefinition,
		}, err
	})
	for _, e := range errs {
		if e != nil {
			s.Logger.
				WithField("runExecution", validExecutions).
				WithError(e).
				Error("<RuntimeService.FailExecutionByExcodes> Repository.GetProcessByID")
			return e
		}
	}
	for _, ok := range oks {
		for _, execution := range validExecutions {
			if execution.ProcessDefinitionID == ok.ProcessDefinitionID {
				executionDefinitionMap[execution] = ok.ProcessDefinition
			}
		}
	}

	runExecution.Status = enums.RunExecutionStatusFailed
	runExecution.Assignee = operator
	runExecution.Memo = memo
	err = runExecutionMgr.UpdateByBatch(validExecutions, runExecution)
	if err != nil {
		s.Logger.
			WithField("runExecution", validExecutions).
			WithError(err).
			Error("<RuntimeService.FailExecutionByExcodes> runExecutionMgr.Save")
		return err
	}
	for execution, processDefinition := range executionDefinitionMap {
		execution.Status = enums.RunExecutionStatusFailed
		execution.Assignee = operator
		execution.Memo = memo
		s.Scheduler.Put(jobs.NewFlow(ctx.BuildContext(
			&execution,
			&processDefinition,
			s.Mgr,
			s.AdminTransport,
			s.sofaService,
			s.gaeaService,
			s.Logger,
			false)))

		if caller.FromUser() {
			// 记录驳回事件
			err := s.HistoryService.CreateProcessActivityLog(&models.ProcessActivityLog{
				ProcessInstanceID: execution.ProcessInstanceID,
				ActivityType:      enums.ActivityLogTypeRejected,
				Content:           memo,
				Operator:          operator,
			})

			if err != nil {
				s.Logger.
					WithField("process_instance_id", execution.ProcessInstanceID).
					WithError(err).
					Error("<RuntimeService.FailProcessesByExcodes> create process activity log failed")
			}
		}
	}
	return nil
}

// FailProcessesByExcodes 根据 excodes 批量驳回流程
// 即使审批当前节点是建议节点，或者流程已被挂起，流程也会被驳回
func (s *runtime) FailProcessesByExcodes(excodes []string, operator, memo string) error {
	var (
		runExecution    models.RunExecution
		runExecutions   []models.RunExecution
		runExecutionMgr *mgr.RunExecutionMgr
	)
	runExecutionMgr = mgr.NewRunExecutionMgr(*s.Mgr)
	runExecutions, err := runExecutionMgr.ListExecutionsByExcodes(excodes)
	if err != nil {
		s.Logger.WithFields(logrus.Fields{
			"excodes":  excodes,
			"operator": operator,
			"memo":     memo,
		}).WithError(err).Error("list executions by excodes failed")
		return err
	}

	type temp struct {
		ProcessDefinitionID uint64
		ProcessDefinition   models.ProcessDefinition
	}

	resultGroup := resultgroup.Group[uint64, temp]{
		MaxConcurrency: len(excodes),
	}

	executionDefinitionMap := make(map[models.RunExecution]models.ProcessDefinition)
	processDefinitionIDSet := make(map[uint64]struct{})
	for _, execution := range runExecutions {
		processDefinitionIDSet[execution.ProcessDefinitionID] = struct{}{}
	}

	for processDefinitionID := range processDefinitionIDSet {
		resultGroup.Queue(processDefinitionID)
	}

	oks, errs := resultGroup.Do(func(processDefinitionID uint64) (temp, error) {
		processDefinition, err := s.Repository.GetProcessByID(processDefinitionID)
		return temp{
			ProcessDefinitionID: processDefinitionID,
			ProcessDefinition:   processDefinition,
		}, err
	})
	for _, e := range errs {
		if e != nil {
			s.Logger.
				WithField("runExecution", runExecutions).
				WithError(e).
				Error("<RuntimeService.FailExecutionByExcodes> Repository.GetProcessByID")
			return e
		}
	}
	for _, ok := range oks {
		for _, execution := range runExecutions {
			if execution.ProcessDefinitionID == ok.ProcessDefinitionID {
				executionDefinitionMap[execution] = ok.ProcessDefinition
			}
		}
	}

	runExecution.Status = enums.RunExecutionStatusFailed
	runExecution.Assignee = operator
	runExecution.Memo = memo
	err = runExecutionMgr.UpdateByBatch(runExecutions, runExecution)
	if err != nil {
		s.Logger.
			WithField("runExecution", runExecutionMgr).
			WithError(err).
			Error("<RuntimeService.FailExecutionByExcodes> runExecutionMgr.Save")
		return err
	}
	for execution, processDefinition := range executionDefinitionMap {
		execution.Status = enums.RunExecutionStatusFailed
		execution.Assignee = operator
		execution.Memo = memo
		s.Scheduler.Put(jobs.NewFailProcess(ctx.BuildContext(
			&execution,
			&processDefinition,
			s.Mgr,
			s.AdminTransport,
			s.sofaService,
			s.gaeaService,
			s.Logger,
			false), operator, memo))
	}
	return nil
}

// SuspendOrResumeProcess suspend or resume process instance by id
func (s *runtime) SuspendOrResumeProcess(processInstanceID uint64, suspend bool) error {
	processInstanceMgr := mgr.NewProcessInstanceMgr(*s.Mgr)
	processInstanceStatus := enums.ProcessStatusPending
	if suspend {
		processInstanceStatus = enums.ProcessStatusSuspended
	}

	var processInstance models.ProcessInstance
	err := processInstanceMgr.Model(&processInstance).
		Where("id=?", processInstanceID).
		Find(&processInstance).
		Error
	if err != nil {
		s.Logger.WithFields(
			logrus.Fields{
				"processInstanceID": processInstanceID,
				"status":            processInstanceStatus,
			}).
			WithError(err).
			Error("<RuntimeService.SuspendOrResumeProcess>get process by id failed")
		return err
	}

	// 这些状态的 process instance 不能挂起或恢复
	invalidStatus := []enums.ProcessInstanceStatus{
		enums.ProcessStatusFailed, enums.ProcessStatusCompleted, enums.ProcessStatusRemoved,
	}

	for _, status := range invalidStatus {
		if processInstance.Status == status {
			return fmt.Errorf("process status cannot be set %s", processInstanceStatus.String())
		}
	}

	if processInstance.Status == processInstanceStatus {
		return fmt.Errorf("process status is already %s", processInstanceStatus.String())
	}

	err = processInstanceMgr.Model(&processInstance).
		Where("id=?", processInstanceID).
		Update("status", processInstanceStatus).
		Error
	if err != nil {
		s.Logger.WithFields(
			logrus.Fields{
				"processInstanceID": processInstanceID,
				"status":            processInstanceStatus,
			}).
			WithError(err).
			Error("<RuntimeService.SuspendOrResumeProcess>update process status failed")
		return err
	}
	return nil
}

// RunExecutionQuery defines query params to list runtime executions
type RunExecutionQuery struct {
	ProcessDefinitionID  *uint64
	ProcessDefinitionKey *string
	ProcessInstanceID    *uint64
	ActivityDefineKey    *string
	Excode               *string
	UID                  *uint64
	Status               *enums.RunExecutionStatus
	OriginalAssignees    []string
	Assignees            []string
	Starters             []string
	ListByProcess        bool
	Page                 int
	PageSize             int
}

type SearchExecutionResult struct {
	models.RunExecution
	ProcessDefinitionKey string `gorm:"process_definition.key" json:"process_definition_key"`
	HasUrge              bool   `gorm:"has_urge" json:"has_urge"`
}

// SearchExecutions searches runtime executions
func (s *runtime) SearchExecutions(query RunExecutionQuery) (executions []SearchExecutionResult, count int, err error) {
	tbName := "run_execution"
	db := s.RunExecutionMgr.Table(tbName)

	fields := s.RunExecutionMgr.Fields(models.RunExecution{}, tbName)

	if query.ListByProcess {
		fields = strings.Replace(fields, fmt.Sprintf("%s.process_instance_id,", tbName), "", 1)
		fields = fmt.Sprintf("distinct %s.process_instance_id,%s", tbName, fields)
	}

	fields = fmt.Sprintf("%s, process_definition.key as process_definition_key, CASE WHEN urge_task.id IS NULL THEN false ELSE true END as has_urge", fields)

	db = db.Select(fields)
	db = db.Joins("join process_definition on process_definition.id = run_execution.process_definition_id")
	db = db.Joins("left join urge_task on urge_task.execution_id = run_execution.id")

	if query.ProcessDefinitionID != nil && *query.ProcessDefinitionID != 0 {
		db = db.Where("run_execution.process_definition_id = ?", *query.ProcessDefinitionID)
	}

	if query.ProcessInstanceID != nil && *query.ProcessInstanceID != 0 {
		db = db.Where("run_execution.process_instance_id = ?", *query.ProcessInstanceID)
	}

	if query.ActivityDefineKey != nil && *query.ActivityDefineKey != "" {
		db = db.Where("run_execution.activity_define_key = ?", *query.ActivityDefineKey)
	}

	if query.Excode != nil && *query.Excode != "" {
		db = db.Where("run_execution.excode = ?", *query.Excode)
	}

	if query.UID != nil && *query.UID != 0 {
		db = db.Where("run_execution.uid = ?", *query.UID)
	}

	if query.Status != nil && *query.Status != 0 {
		db = db.Where("run_execution.status = ?", *query.Status)
	}

	sliceLen := len(query.OriginalAssignees)
	if sliceLen == 1 {
		db = db.Where("run_execution.original_assignee = ?", query.OriginalAssignees[0])
	} else if sliceLen > 1 {
		db = db.Where("run_execution.original_assignee in (?)", query.OriginalAssignees)
	}

	sliceLen = len(query.Assignees)
	if sliceLen == 1 {
		db = db.Where("run_execution.assignee = ?", query.Assignees[0])
	} else if sliceLen > 1 {
		db = db.Where("run_execution.assignee in (?)", query.Assignees)
	}

	sliceLen = len(query.Starters)
	if sliceLen == 1 {
		db = db.Where("run_execution.start_by_id = ?", query.Starters[0])
	} else if sliceLen > 1 {
		db = db.Where("run_execution.start_by_id in (?)", query.Starters)
	}

	if query.ProcessDefinitionKey != nil && *query.ProcessDefinitionKey != "" {
		db = db.Where("process_definition.key = ?", *query.ProcessDefinitionKey)
	}

	if query.ListByProcess {
		countDb := db.Select("COUNT(DISTINCT run_execution.process_instance_id)")
		err = countDb.Count(&count).Error
	} else {
		err = db.Count(&count).Error
	}

	if err != nil {
		return
	}

	db = mgr.Paging(db, query.Page, query.PageSize)
	err = db.Order("has_urge desc, run_execution.updated_at desc").Find(&executions).Error
	return
}

type ReassignParam struct {
	Assignee string `json:"assignee"`
	Operator string `json:"operator"`
	Memo     string `json:"memo"`
}

// Assign reassigns a task to another person
func (s *runtime) Assign(executionID uint64, param *ReassignParam) (err error) {
	if param == nil || param.Assignee == "" || param.Operator == "" {
		return errors.New("invalid params")
	}

	var (
		runExecution models.RunExecution
	)

	if err = s.RunExecutionMgr.First(&runExecution, executionID).Error; err != nil {
		s.Logger.
			WithField("id", executionID).
			WithError(err).
			Error("<RuntimeService.Assign> runExecutionMgr.Model.First")
		return
	}

	processDef, err := s.Repository.GetProcessByID(runExecution.ProcessDefinitionID)
	if err != nil {
		return
	}

	node, err := processDef.GetExecutionNode(&runExecution)
	if err != nil {
		return
	}

	exec := models.AssignRunExecution(&runExecution, param.Assignee)

	err = s.RunExecutionMgr.PerformTransaction(func(db *mgr.Base) (err error) {
		if err = db.Delete(runExecution).Error; err != nil {
			return
		}

		if err = db.Model(models.ActivityInstance{}).
			Where("execution_id=?", runExecution.ID).
			Updates(models.ActivityInstance{
				Status: enums.ActivityStatusReassigned,
				Actor:  param.Operator,
				Memo:   param.Memo,
				EndAt:  time.Now(),
			}).Error; err != nil {
			return
		}

		if err = db.Create(exec).Error; err != nil {
			return
		}

		return db.Create(models.NewActivityInstance(exec, node.GetType())).Error
	})

	if err == nil {
		go jobs.NewListener(node, &processDef, exec.ID, s.Mgr, s.AdminTransport).FireAction("assign")
	}
	return err
}

// Cancel cancels a running process
func (s *runtime) Cancel(processInstanceID uint64, operator string, memo string) (err error) {

	var runExecution models.RunExecution
	err = s.RunExecutionMgr.
		Model(&models.RunExecution{}).
		Where("process_instance_id = ?", processInstanceID).
		First(&runExecution).Error

	if err != nil {
		return
	}

	model, err := s.Repository.GetProcessByID(runExecution.ProcessDefinitionID)
	if err != nil {
		return
	}

	return s.Scheduler.Run(jobs.NewCancel(
		ctx.BuildContext(
			&runExecution,
			&model,
			s.Mgr,
			s.AdminTransport,
			s.sofaService,
			s.gaeaService,
			s.Logger,
			false),
		operator, memo,
	))
}

// GetExecution get a execution by executionID
func (s *runtime) GetExecution(executionID uint64) (runExecution models.RunExecution, err error) {
	if err = s.RunExecutionMgr.First(&runExecution, executionID).Error; err != nil {
		s.Logger.
			WithField("id", executionID).
			WithError(err).
			Error("<RuntimeService.GetExecution> runExecutionMgr.Model.First")
		return
	}
	return
}

func (s *runtime) GetCounterSignConfig(executionID uint64) (*bpmnModel.CounterSign, error) {
	execution, err := s.GetExecution(executionID)
	if err != nil {
		s.Logger.WithField("executionID", executionID).
			WithError(err).
			Error("<RuntimeService.GetCounterSignConfig>get execution by id failed")
		return nil, err
	}

	// 只有处在待审核状态下才能加签
	if execution.Status != enums.RunExecutionStatusPending {
		return &bpmnModel.CounterSign{
			AllowCounterSign: false,
		}, nil
	}

	processInstanceMgr := mgr.NewProcessInstanceMgr(*s.Mgr)
	var processInstance models.ProcessInstance
	err = processInstanceMgr.Model(&processInstance).
		Where("id=?", execution.ProcessInstanceID).
		Find(&processInstance).
		Error
	if err != nil {
		s.Logger.WithField("processInstanceID", execution.ProcessInstanceID).
			WithError(err).
			Error("<RuntimeService.GetCounterSignConfig>get process by id failed")
		return nil, err
	}

	// 流程已挂起，不能加签
	if processInstance.Status == enums.ProcessStatusSuspended {
		return &bpmnModel.CounterSign{
			AllowCounterSign: false,
		}, nil
	}

	processDef, err := s.Repository.GetProcessByID(execution.ProcessDefinitionID)
	if err != nil {
		s.Logger.WithField("processDefinitionID", execution.ProcessInstanceID).
			WithError(err).
			Error("<RuntimeService.GetCounterSignConfig>get process definition by id failed")
		return nil, err
	}

	node, err := processDef.GetExecutionNode(&execution)
	if err != nil {
		s.Logger.WithField("execution", execution).
			WithError(err).
			Error("<RuntimeService.GetCounterSignConfig>get execution node failed")
		return nil, err
	}

	// 如果 node 节点不是 UserTask，不允许加签
	if node.GetType() != "UserTask" {
		return &bpmnModel.CounterSign{
			AllowCounterSign: false,
		}, nil
	}

	// 如果 node 是 UserTask 且 AllowCounterSign 为真，允许加签
	userTask := node.(*bpmnModel.UserTask)
	if userTask.AllowCounterSign {
		return &userTask.CounterSign, nil
	}
	return &bpmnModel.CounterSign{
		AllowCounterSign: false,
	}, nil
}
