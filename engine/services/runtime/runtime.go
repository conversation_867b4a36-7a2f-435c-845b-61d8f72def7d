package runtime

import (
	bpmnModel "qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
)

// Service Runtime service to handle processes
type Service interface {
	StartProcess(key, params, excode, operator string, uid uint64, ddl int64) (*models.RunExecution, error)
	CompleteExecution(executionID uint64, params, operator, memo string) error
	CounterSignExecution(param CounterSignExecutionParam) error
	FailExecution(executionID uint64, params, operator, memo string, caller enums.Caller) error
	FailExecutionsByExcodes(excodes []string, operator, memo string, caller enums.Caller) error
	FailProcessesByExcodes(excodes []string, operator, memo string) error
	SuspendOrResumeProcess(processInstanceID uint64, suspend bool) error
	SearchExecutions(query RunExecutionQuery) ([]SearchExecutionResult, int, error)
	Assign(executionID uint64, param *ReassignParam) error
	Cancel(processInstanceID uint64, operator, memo string) error
	GetExecution(executionID uint64) (runExecution models.RunExecution, err error)
	GetCounterSignConfig(executionID uint64) (*bpmnModel.CounterSign, error)
	UrgeByExecution(executionID uint64, operator, memo string) error
	UrgeByProcess(processID uint64, operator, memo string) error
	AssignNotify(executionID uint64) error
	CompleteNotify(processInstanceID uint64) error
	HasUrgeTasksByProcessIDs(processInstanceIDs []uint64) (map[uint64]bool, error)
}
