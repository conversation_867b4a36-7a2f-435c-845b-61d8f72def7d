package runtime

import (
	"fmt"
	"strings"

	"github.com/xen0n/go-workwx"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
)

const assignWechatTemplate = `**[审批待办通知]**
>Hi,
>您有一份来自 ` + "`%s`" + ` 的审批请求。
>
>**【审批信息】**
><font color="comment">UID： </font>%d
><font color="comment">审批业务：</font>%s
><font color="comment">审批地址：</font>%s
>[点此去处理](%s)
`

const completeWechatTemplate = `**[审批完成通知]**
>Hi,
>您发起的 ` + "`%s`" + ` 审批已完成，审批结果为 ` + "`%s`" + `。
>
>**【审批信息】**
><font color="comment">UID： </font>%d
><font color="comment">用户名： </font>%s
><font color="comment">审批地址：</font>%s
>[点此查看](%s)
`

func (s *runtime) AssignNotify(executionID uint64) error {

	var (
		runExecution    models.RunExecution
		processInstance models.ProcessInstance
		runExecutionMgr *mgr.RunExecutionMgr    = mgr.NewRunExecutionMgr(*s.Mgr)
		processMgr      *mgr.ProcessInstanceMgr = &mgr.ProcessInstanceMgr{Base: *s.Mgr}
	)

	if err := runExecutionMgr.First(&runExecution, executionID).Error; err != nil {
		s.Logger.
			WithField("id", executionID).
			WithError(err).
			Error("<RuntimeService.AssignNotify> runExecutionMgr.Model.First")
		return err
	}

	if runExecution.Status.IsEnd() {
		return nil
	}

	if err := processMgr.First(&processInstance, runExecution.ProcessInstanceID).Error; err != nil {
		s.Logger.
			WithField("id", runExecution.ProcessInstanceID).
			WithError(err).
			Error("<RuntimeService.AssignNotify> processMgr.Model.First")
		return err
	}

	processDefinition, err := s.Repository.GetProcessByID(processInstance.ProcessDefinitionID)
	if err != nil {
		s.Logger.
			WithField("id", processInstance.ProcessDefinitionID).
			WithError(err).
			Error("<RuntimeService.AssignNotify> Repository.GetProcessByID")
		return err
	}

	node, err := processDefinition.GetNode(runExecution.ActivityDefineKey)
	if err != nil {
		return err
	}

	if node.GetType() != "UserTask" && node.GetType() != "SuggestTask" {
		s.Logger.WithField("id", executionID).Warnf("current node is %s, not task node", node.GetType())
		return nil
	}

	err = s.assignWorkwxNotify(&runExecution, &processInstance, &processDefinition)
	if err != nil {
		return err
	}

	return nil
}

func (s *runtime) assignWorkwxNotify(
	execution *models.RunExecution,
	processInstance *models.ProcessInstance,
	processDefinition *models.ProcessDefinition,
) error {
	if s.workwx == nil || execution == nil || execution.Assignee == "" || processInstance == nil {
		return nil
	}

	// 非生产环境由于企业微信 IP 白名单限制等原因，无法发送
	// 直接返回避免产生大量报错污染测试环境日志
	if !s.cfg.Mode.IsProd() {
		return nil
	}
	if !strings.Contains(execution.Assignee, "@qiniu.com") {
		return nil
	}

	assignee := strings.TrimSuffix(execution.Assignee, "@qiniu.com")
	detailURL := s.approvalDetailURL(
		processDefinition.Key,
		processInstance.Excode,
		processInstance.UID,
		processInstance.ID,
	)
	content := fmt.Sprintf(assignWechatTemplate,
		execution.StartByID, execution.UID, processInstance.Name,
		detailURL, detailURL)

	return s.workwx.SendMarkdownMessage(&workwx.Recipient{
		UserIDs: []string{assignee},
	}, content, false)
}

func (s *runtime) CompleteNotify(processInstanceID uint64) error {

	var (
		processInstance models.ProcessInstance
		processMgr      *mgr.ProcessInstanceMgr = &mgr.ProcessInstanceMgr{Base: *s.Mgr}
	)

	if err := processMgr.First(&processInstance, processInstanceID).Error; err != nil {
		s.Logger.
			WithField("id", processInstanceID).
			WithError(err).
			Error("<RuntimeService.CompleteNotify> processMgr.Model.First")
		return err
	}

	processDefinition, err := s.Repository.GetProcessByID(processInstance.ProcessDefinitionID)
	if err != nil {
		s.Logger.
			WithField("id", processInstance.ProcessDefinitionID).
			WithError(err).
			Error("<RuntimeService.CompleteNotify> Repository.GetProcessByID")
		return err
	}

	err = s.completeWorkwxNotify(&processInstance, &processDefinition)
	if err != nil {
		return err
	}

	return nil
}

func (s *runtime) completeWorkwxNotify(
	processInstance *models.ProcessInstance,
	processDefinition *models.ProcessDefinition,
) error {
	if s.workwx == nil || processInstance == nil {
		return nil
	}

	// 非生产环境由于企业微信 IP 白名单限制等原因，无法发送
	// 直接返回避免产生大量报错污染测试环境日志
	if !s.cfg.Mode.IsProd() {
		return nil
	}
	if !strings.Contains(processInstance.StartByID, "@qiniu.com") {
		return nil
	}

	username := ""
	developerOverview, _ := s.gaeaService.GetDeveloperOverview(processInstance.UID)
	if developerOverview != nil {
		username = developerOverview.FullName
	}

	starter := strings.TrimSuffix(processInstance.StartByID, "@qiniu.com")
	detailURL := s.approvalDetailURL(
		processDefinition.Key,
		processInstance.Excode,
		processInstance.UID,
		processInstance.ID,
	)
	content := fmt.Sprintf(completeWechatTemplate,
		processInstance.Name, processInstance.Status.Humanize(), processInstance.UID, username,
		detailURL, detailURL)

	return s.workwx.SendMarkdownMessage(&workwx.Recipient{
		UserIDs: []string{starter},
	}, content, false)
}
