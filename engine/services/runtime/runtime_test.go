package runtime_test

import (
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"qiniu.io/qbpm/engine/bpmn/models/enums"
	"qiniu.io/qbpm/engine/conf"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	pEnums "qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/services/history"
	"qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/services/runtime"
	"qiniu.io/qbpm/engine/services/scheduler"
	"qiniu.io/qbpm/engine/services/system"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("runtime", func() {

	var (
		base              *mgr.Base
		runtimeService    runtime.Service
		repositoryService repository.Service
		schedulerService  scheduler.Service
	)

	BeforeEach(func() {
		base = test.Init()
		redis := test.InitRedis()
		servBase := &services.Base{Mgr: base, RedisCli: redis}

		repositoryService = repository.NewRepositoryService(servBase)
		schedulerService = scheduler.NewService(redis)
		historyService := history.NewHistoryService(
			servBase,
			repositoryService,
			nil,
		)
		runtimeService = runtime.NewService(servBase, repositoryService, schedulerService, historyService, nil, nil, nil, &conf.Config{})
		_ = system.NewSystemService(&services.Base{Mgr: base}, nil)
	})

	Describe("RuntimeController", func() {

		JustBeforeEach(func() {
			model := &models.ProcessDefinition{
				DeployAt: time.Now(),
				Name:     "testRepo",
				Key:      "testRepo",
				XMLData: []byte(
					`<definitions id="definitions"
				  xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
				  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
				 
					<process id="modify_user_price" name="用户改价流程">
						<startEvent id="theStart"/>
				 
						<sequenceFlow id="toProfileCheck" sourceRef="theStart" targetRef="finance"/>
				 
						<userTask id="finance" name="财务审批" allowCounterSign="true">
							<counterSigns>
								<counterSign assigneeType="static" assignee="<EMAIL>"/>
							</counterSigns>
						</userTask>

						<sequenceFlow id="financeToEnd" sourceRef="finance" targetRef="theEnd"/>
						 
						<endEvent id="theEnd"/>
					</process>
					 
				</definitions>`),
			}
			err := repositoryService.Create(model)
			Expect(err).To(BeNil())
		})

		It("should finish a process", func() {
			exec, _ := runtimeService.StartProcess("testRepo", "", "001", "tester", 0, 0)
			time.Sleep(time.Second)
			execs, _, _ := runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			runtimeService.CompleteExecution(execs[0].ID, "", "", "")
			time.Sleep(time.Second)
			schedulerService.Shutdown()
			Expect(base.Where("process_instance_id = ?", exec.ProcessInstanceID).First(&models.RunExecution{}).Error).To(HaveOccurred())
		})

		It("should post-counter-sign a process", func() {
			// 发起流程审批
			exec, _ := runtimeService.StartProcess("testRepo", "", "001", "tester", 0, 0)
			time.Sleep(time.Second)
			execs, _, _ := runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			// 后加签一人参与审批
			runtimeService.CounterSignExecution(runtime.CounterSignExecutionParam{
				ExecutionParam: runtime.ExecutionOperateParam{
					ExecutionID: execs[0].ID,
					Params:      "",
					Operator:    "tester",
					Memo:        "",
				},
				CounterSignType: enums.CounterSignTypeAfter,
				CounterSignNodes: []runtime.AssignParam{
					{
						Assignee:     "<EMAIL>",
						AssigneeType: enums.AssigneeTypeStatic,
					},
				},
			})
			time.Sleep(time.Second)

			// 加签后 runExecution 表中应该有两条数据，一条为当前加签人处理过的已完成的记录，一条为被加签人待处理数据
			var out []models.RunExecution
			err := base.Where("process_instance_id = ?", exec.ProcessInstanceID).Find(&out).Order("id").Error
			Expect(err).NotTo(HaveOccurred())
			Expect(out).To(HaveLen(2))
			Expect(out[0].Status).To(Equal(pEnums.RunExecutionStatusFinished))
			Expect(out[1].CounterSigner).To(Equal("tester"))

			// 被加签人审批通过后整个流程应该全部结束
			runtimeService.CompleteExecution(out[1].ID, "", "tester", "approved")
			time.Sleep(time.Second)
			err = base.Where("process_instance_id = ?", exec.ProcessInstanceID).Find(&out).Order("id").Error
			Expect(err).To(BeNil())
			Expect(out).To(HaveLen(0))

			schedulerService.Shutdown()
		})

		It("should pre-counter-sign a process", func() {
			// 发起流程审批
			exec, _ := runtimeService.StartProcess("testRepo", "", "001", "tester", 0, 0)
			time.Sleep(time.Second)
			execs, _, _ := runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})

			// 前加签一人参与审批
			runtimeService.CounterSignExecution(runtime.CounterSignExecutionParam{
				ExecutionParam: runtime.ExecutionOperateParam{
					ExecutionID: execs[0].ID,
					Params:      "",
					Operator:    "tester",
					Memo:        "",
				},
				CounterSignType: enums.CounterSignTypeBefore,
				CounterSignNodes: []runtime.AssignParam{
					{
						Assignee:     "<EMAIL>",
						AssigneeType: enums.AssigneeTypeStatic,
					},
				},
			})
			time.Sleep(time.Second)

			// 加签后 runExecution 表中应该有两条数据，一条为当前加签人状态为挂起的记录，一条为被加签人待处理数据
			var out []models.RunExecution
			err := base.Where("process_instance_id = ?", exec.ProcessInstanceID).Order("id").Find(&out).Error
			Expect(err).NotTo(HaveOccurred())
			Expect(out).To(HaveLen(2))
			Expect(out[0].Assignee).To(BeEmpty())
			Expect(out[0].Status).To(Equal(pEnums.RunExecutionStatusSuspend))
			Expect(out[1].CounterSigner).To(Equal("tester"))

			// 被加签人审批通过后流转向原加签操作人，状态为待处理
			runtimeService.CompleteExecution(out[1].ID, "", "tester", "approved")
			time.Sleep(time.Second)
			err = base.Where("process_instance_id = ?", exec.ProcessInstanceID).Find(&out).Order("id").Error
			Expect(err).To(BeNil())
			Expect(out).To(HaveLen(2))
			Expect(out[0].Assignee).To(BeEmpty())
			Expect(out[0].Status).To(Equal(pEnums.RunExecutionStatusPending))
			Expect(out[1].Status).To(Equal(pEnums.RunExecutionStatusFinished))

			// 加签人审批通过后整个流程结束
			runtimeService.CompleteExecution(out[0].ID, "", "approver", "approved")
			time.Sleep(time.Second)
			err = base.Where("process_instance_id = ?", exec.ProcessInstanceID).Find(&out).Order("id").Error
			Expect(err).To(BeNil())
			Expect(out).To(HaveLen(0))

			schedulerService.Shutdown()
		})

		It("should fail a process", func() {
			exec, _ := runtimeService.StartProcess("testRepo", "", "001", "tester", 0, 0)
			time.Sleep(time.Second)
			execs, _, _ := runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			runtimeService.FailExecution(execs[0].ID, "", "", "", pEnums.CallerUser)
			time.Sleep(time.Second)
			schedulerService.Shutdown()
			Expect(base.Where("process_instance_id = ?", exec.ProcessInstanceID).First(&models.RunExecution{}).Error).To(HaveOccurred())
		})

		It("should reassign a runExecution", func() {
			exec, _ := runtimeService.StartProcess("testRepo", "", "001", "tester", 0, 0)
			time.Sleep(time.Second)
			schedulerService.Shutdown()
			execs, _, _ := runtimeService.SearchExecutions(runtime.RunExecutionQuery{ProcessInstanceID: &exec.ProcessInstanceID})
			runtimeService.Assign(execs[0].ID, &runtime.ReassignParam{Assignee: "<EMAIL>", Operator: "tester"})
			Expect(base.First(&models.RunExecution{}, exec.ID).Error).To(HaveOccurred())
			Expect(base.Where("assignee = ?", "<EMAIL>").First(&models.RunExecution{}).Error).NotTo(HaveOccurred())
		})

		It("should cancel a process", func() {
			exec, _ := runtimeService.StartProcess("testRepo", "", "001", "tester", 0, 0)
			time.Sleep(time.Second)
			runtimeService.Cancel(exec.ProcessInstanceID, "<EMAIL>", "")
			time.Sleep(time.Second)
			Expect(base.Where("process_instance_id = ?", exec.ProcessInstanceID).First(&models.RunExecution{}).Error).To(HaveOccurred())
		})

	})

})
