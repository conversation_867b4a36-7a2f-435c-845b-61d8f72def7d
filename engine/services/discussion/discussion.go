package discussion

import (
	"errors"
	"fmt"

	"github.com/sirupsen/logrus"
	"github.com/xen0n/go-workwx"

	"qiniu.io/qbpm/engine/conf"
	"qiniu.io/qbpm/engine/lib/gaea"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/util/kodo"
	"qiniu.io/qbpm/engine/util/logger"
)

type Service struct {
	*services.Base
	logger      logrus.FieldLogger
	GaeaService gaea.GaeaAdminService
	workwx      *workwx.WorkwxApp
	kodoClient  *kodo.Client
	cfg         *conf.Config
}

func NewDiscussionService(
	base *services.Base,
	gaeaService gaea.GaeaAdminService,
	cfg *conf.Config) *Service {
	workwx := workwx.New(cfg.Services.Workwx.CorpID)
	workwxApp := workwx.WithApp(cfg.Services.Workwx.CorpSecret, cfg.Services.Workwx.AgentID)
	workwxApp.SpawnAccessTokenRefresher()

	kodoClient := kodo.NewClient(
		cfg.Services.DiscussionAttachment.AccessKey,
		cfg.Services.DiscussionAttachment.SecretKey,
		cfg.Services.DiscussionAttachment.Domain,
		cfg.Services.DiscussionAttachment.Bucket,
	)

	return &Service{
		Base:        base,
		logger:      logrus.WithField(logger.FieldKeyPrefix, "DiscussionService"),
		GaeaService: gaeaService,
		workwx:      workwxApp,
		kodoClient:  kodoClient,
		cfg:         cfg,
	}
}

const (
	mentionTemplate = `**[审批留言通知]**
>Hi, 有人在一条审批中留言@了你。
>Ta 说: %s
>[点击去查看完整留言](%s)
`
	replyTemplate = `**[审批留言被回复]**
>Hi, 你在审批的留言被人回复了，赶紧去看看吧。
>Ta 说: %s
>[点击去查看完整留言](%s)
`
)

func (s *Service) CreateComment(comment models.Comment) error {
	if err := comment.Valid(); err != nil {
		s.logger.WithField("comment", comment).
			WithError(err).Error("<DiscussionService.CreateComment>comment invalid")
		return err
	}

	if len(comment.AttachmentFilename) != 0 && len(comment.AttachmentContentBase64) == 0 ||
		len(comment.AttachmentFilename) == 0 && len(comment.AttachmentContentBase64) != 0 {
		err := errors.New("attachment cannot be empty")
		s.logger.WithField("comment", comment).
			WithError(err).Error("<DiscussionService.CreateComment>attachment cannot be empty")
		return err
	}

	type temp struct {
		Key    string
		UID    uint64
		Excode string
	}

	var queryResult temp

	err := s.Mgr.Model(&models.ProcessInstance{}).
		Joins("left join process_definition on process_instance.process_definition_id = process_definition.id").
		Where("process_instance.id=?", comment.ProcessInstanceID).
		Select("process_definition.key, process_instance.uid, process_instance.excode").
		Scan(&queryResult).Error
	if err != nil {
		s.logger.WithField("comment", comment).
			WithError(err).Error("<DiscussionService.CreateComment>find process-instance failed")
		return err
	}

	err = s.Mgr.PerformTransaction(func(base *mgr.Base) error {
		if comment.ParentCommentID != 0 {
			var c models.Comment
			e := base.Model(&c).
				Where("id = ?", comment.ParentCommentID).
				First(&c).Error
			if e != nil {
				return e
			}

			if c.ProcessInstanceID != comment.ProcessInstanceID {
				return errors.New("invalid parent_comment_id")
			}
		}

		if comment.AttachmentFilename != "" {
			e := s.kodoClient.UploadFile(kodo.UploadFileReq{
				Directory:     fmt.Sprintf("%s/discussion", queryResult.Key),
				Prefix:        fmt.Sprintf("%d_", comment.ProcessInstanceID),
				Filename:      comment.AttachmentFilename,
				ContentBase64: comment.AttachmentContentBase64,
			})
			if e != nil {
				return e
			}
		}

		return base.Model(&models.Comment{}).Create(&comment).Error
	})
	if err != nil {
		s.logger.WithField("comment", comment).
			WithError(err).Error("<DiscussionService.CreateComment> create comment failed")
		return err
	}

	go func() error {
		// 解析 评论中是否有 @人
		mentionedUsers := comment.ParseMentionedUsers()
		adminUsers, e := s.GaeaService.ListUserByNames(mentionedUsers)
		if e != nil {
			s.logger.WithField("comment", comment).
				WithError(e).Error("<DiscussionService.CreateComment>list user by names failed")
			return e
		}

		availableMentionedUserNames := make([]string, 0)
		for _, user := range adminUsers {
			availableMentionedUserNames = append(availableMentionedUserNames, user.Name)
		}

		replyingToUser := ""
		if comment.ParentCommentID != 0 {
			parentComment, err1 := s.GetCommentByID(comment.ParentCommentID)
			if err1 != nil {
				s.logger.WithField("comment", comment).
					WithError(err1).
					Error("<DiscussionService.CreateComment>get parent comment failed")
				return err1
			}
			replyingToUser = parentComment.Author
		}

		// 发送回复企业微信
		applicationURL := fmt.Sprintf("%s/approval/%s/%s?uid=%d", s.cfg.Services.PortalIOHost,
			queryResult.Key, queryResult.Excode, queryResult.UID)
		content := comment.Content
		if len(content) > 30 {
			content = fmt.Sprintf("%s...", content[:30])
		}
		if replyingToUser != "" {
			c := fmt.Sprintf(replyTemplate, content, applicationURL)
			e = s.workwx.SendMarkdownMessage(&workwx.Recipient{
				UserIDs: []string{replyingToUser},
			}, c, false)
			if e != nil {
				s.logger.WithField("replyingToUser", replyingToUser).
					WithError(e).Error("<DiscussionService.CreateComment>send wechat message failed")
			}
			// 如果 @ 的人包含了回复的人，那就不要重复发送消息了
			for i := len(availableMentionedUserNames) - 1; i >= 0; i-- {
				if availableMentionedUserNames[i] == replyingToUser {
					availableMentionedUserNames = append(availableMentionedUserNames[:i], availableMentionedUserNames[i+1:]...)
				}
			}
		}

		if len(availableMentionedUserNames) == 0 {
			return nil
		}

		// 发送 @ 企业微信
		content = fmt.Sprintf(mentionTemplate, content, applicationURL)
		e = s.workwx.SendMarkdownMessage(&workwx.Recipient{
			UserIDs: availableMentionedUserNames,
		}, content, false)
		if e != nil {
			s.logger.WithField("comment", comment).
				WithError(e).Error("<DiscussionService.CreateComment>send wechat message failed")
			return e
		}
		return nil
	}()

	return nil
}

func (s *Service) ListComment(processInstanceID uint64, offset int, limit int) ([]*models.CommentResponse, error) {
	result := make([]*models.CommentResponse, 0)
	err := s.Mgr.Model(&models.Comment{}).
		Joins("left join comment c1 on comment.parent_comment_id = c1.id").
		Where("comment.process_instance_id = ?", processInstanceID).
		Order("created_at").
		Offset(offset).
		Limit(limit).
		Select("comment.*, c1.author as reply_to").
		Scan(&result).Error
	if err != nil {
		s.logger.WithField("process_instance_id", processInstanceID).
			WithError(err).
			Error("<DiscussionService.ListComment>list comments failed")
		return nil, err
	}

	type temp struct {
		Key string
	}

	var queryResult temp
	err = s.Mgr.Model(&models.ProcessInstance{}).
		Joins("left join process_definition on process_instance.process_definition_id = process_definition.id").
		Where("process_instance.id=?", processInstanceID).
		Select("process_definition.key").
		Scan(&queryResult).Error
	if err != nil {
		s.logger.WithField("process_instance_id", processInstanceID).
			WithError(err).Error("<DiscussionService.ListComment> find process-instance failed")
		return nil, err
	}

	for _, comment := range result {
		if comment.IsDeleted {
			comment.Content = "[该评论已被删除]"
			comment.AttachmentFilename = ""
		} else {
			if comment.AttachmentFilename != "" {
				prefix := fmt.Sprintf("%d_", processInstanceID)
				directory := fmt.Sprintf("%s/discussion", queryResult.Key)
				comment.AttachmentFileURL, _ = s.kodoClient.GetFile(directory, prefix, comment.AttachmentFilename)
			}
		}
	}

	return result, nil
}

func (s *Service) CountComment(processInstanceID uint64) (int64, error) {
	var count int64
	err := s.Mgr.Model(&models.Comment{}).
		Where("process_instance_id = ?", processInstanceID).
		Count(&count).Error
	if err != nil {
		s.logger.WithField("process_instance_id", processInstanceID).
			WithError(err).
			Error("<DiscussionService.CountComment>count comments failed")
		return 0, err
	}
	return count, nil
}

func (s *Service) GetCommentByID(id uint64) (*models.Comment, error) {
	var result models.Comment
	err := s.Mgr.Model(&models.Comment{}).Where("id = ?", id).First(&result).Error
	return &result, err
}

func (s *Service) DeleteComment(comment models.Comment) error {
	err := s.Mgr.Model(&models.Comment{}).
		Where("id = ?", comment.ID).
		Updates(models.Comment{
			IsDeleted: true,
			EditedBy:  comment.EditedBy,
		}).Error
	if err != nil {
		s.logger.WithField("comment", comment).WithError(err).Error("<DiscussionService.DeleteComment> delete comment failed")
		return err
	}
	return nil
}
