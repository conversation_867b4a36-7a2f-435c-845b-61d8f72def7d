package scheduler

import (
	"context"
	"errors"
	"reflect"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/util/logger"
)

// Scheduler default scheduler service
type Scheduler struct {
	Redis        redis.UniversalClient
	Log          logrus.FieldLogger
	IsShutdown   bool
	RunningGroup sync.WaitGroup
}

// ErrIsShuttingDown indicates current scheduler has been shut down
var ErrIsShuttingDown = errors.New("scheduler is shutting down now")

// NewService instances a scheduler service
func NewService(redis redis.UniversalClient) Service {
	return &Scheduler{
		Log:   logrus.WithField(logger.FieldKeyPrefix, "Scheduler"),
		Redis: redis,
	}
}

// Put puts a job to current scheduler and runs it
func (s *Scheduler) Put(job Job) (err error) {
	if s.IsShutdown {
		err = ErrIsShuttingDown
		return
	}
	go s.Run(job)
	return
}

// Run starts to run a job
func (s *Scheduler) Run(job Job) (err error) {
	log := s.Log.WithFields(logrus.Fields{
		"job_name": getName(job),
		"job_key":  job.Key(),
		"status":   job.Status(),
	})

	var (
		ok       bool
		tryTimes int
	)
	ctx := context.Background()
	tryTimes = 3
	for {
		tryTimes--
		if ok, err = s.Redis.SetNX(ctx, job.Key(), 1, job.LockTime()).Result(); err != nil {
			log.WithError(err).Error("<Scheduler.Run> s.Redis.SetNX failed")
			return
		} else if !ok {
			if tryTimes <= 0 {
				log.Warn("<Scheduler.Run> job is locked")
				return
			}
			time.Sleep(time.Second * 1)
			continue
		}
		break
	}

	s.RunningGroup.Add(1)

	startTime := time.Now()

	log.Info("<Scheduler.Run> job start")

	var nextJobs []Job
	if nextJobs, err = job.Run(); err != nil {
		log.WithError(err).Error("<Scheduler.Run> job.Run failed")
	}

	log.WithField("status", job.Status()).
		WithField("duration", time.Since(startTime).Seconds()).
		Info("<Scheduler.Run> job end")

	s.RunningGroup.Done()
	if err = s.Redis.Del(ctx, job.Key()).Err(); err != nil {
		log.WithField("status", job.Status()).
			WithError(err).
			Error("<Scheduler.Run> s.Redis.Del failed")
	}

	for _, job := range nextJobs {
		if err = s.Put(job); err != nil {
			log.WithField("status", job.Status()).
				WithError(err).
				Warn("<Scheduler.Run> s.Put failed")
		}
	}

	return
}

// Shutdown stops current scheduler
func (s *Scheduler) Shutdown() {
	s.IsShutdown = true
	s.Log.Info("scheduler is shutting down...")
	time.Sleep(time.Second)

	s.RunningGroup.Wait()
	s.Log.Info("scheduler is shutted down...")
	return
}

func getName(val interface{}) string {
	t := reflect.TypeOf(val)
	if t.Kind() == reflect.Ptr {
		return t.Elem().Name()
	}
	return t.Name()
}
