package scheduler_test

import (
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"qiniu.io/qbpm/engine/services/scheduler"
	"qiniu.io/qbpm/engine/test"
)

type Job struct {
	A chan int
	B int
	C string
}

func (j *Job) Key() string {
	return j.C
}

func (j *Job) Status() string {
	return "status"
}

func (j *Job) Run() ([]scheduler.Job, error) {
	j.A <- 1
	j.B = 1
	return nil, nil
}

func (j *Job) LockTime() time.Duration {
	return time.Second
}

var sch scheduler.Service

var _ = Describe("Scheduler", func() {
	BeforeEach(func() {
		sch = scheduler.NewService(test.InitRedis())
	})
	It("should run job", func() {
		job1 := Job{
			A: make(chan int),
			C: "1",
		}
		go func() { Expect(<-job1.A).To(Equal(1)) }()
		sch.Run(&job1)

		job2 := Job{A: make(chan int), C: "2"}
		go func() { Expect(<-job2.A).To(Equal(1)) }()
		Expect(sch.Put(&job2)).NotTo(HaveOccurred())

		sch.Shutdown()

		job3 := Job{A: make(chan int), C: "3"}
		Expect(sch.Put(&job3)).To(HaveOccurred())
	})
})
