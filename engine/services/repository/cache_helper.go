package repository

import (
	"qiniu.io/qbpm/engine/bpmn/parser"
	"qiniu.io/qbpm/engine/models"
)

func (r *repositoryService) doCache(model *models.ProcessDefinition) (err error) {
	if model.ProcessModel == nil && model.XMLData != nil {
		if model.ProcessModel, err = parser.ParseBpmn(model.XMLData); err != nil {
			return
		}
	}

	r.processMap[model.ID] = model
	return
}

func (r *repositoryService) cacheModel(model *models.ProcessDefinition) (err error) {
	r.Lock()
	defer r.Unlock()

	return r.doCache(model)
}

func (r *repositoryService) cacheModels(models []models.ProcessDefinition) (err error) {
	r.Lock()
	defer r.Unlock()

	for i := range models {
		if err = r.doCache(&models[i]); err != nil {
			return
		}
	}

	return
}

func (r *repositoryService) GetCache(id uint64) *models.ProcessDefinition {
	r.RLock()
	defer r.RUnlock()

	return r.processMap[id]
}
