package repository_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	. "qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/test"
)

var (
	repoService Service
	processMgr  *mgr.ProcessDefinitionMgr
)

var _ = Describe("repository", func() {

	BeforeEach(func() {
		mgrBase := test.Init()
		processMgr = &mgr.ProcessDefinitionMgr{*mgrBase}
		servBase := &services.Base{Mgr: mgrBase}
		repoService = NewRepositoryService(servBase)
	})

	Describe("RepositoryService", func() {

		Context("with two record in table", func() {

			var (
				key1 = "testRepo1"
				key2 = "testRepo2"
				m1   = &models.ProcessDefinition{
					Name: key1,
					Key:  key1,
				}
				m2 = &models.ProcessDefinition{
					Status: enums.ProcessDefStatusActive,
					Name:   key2,
					Key:    key2,
				}
				err error
			)

			JustBeforeEach(func() {
				err = processMgr.Create(m1)
				Expect(err).To(BeNil())

				err = processMgr.Create(m2)
				Expect(err).To(BeNil())
			})

			It("should create 1 ProcessDefinition model with diff versions", func() {
				m := &models.ProcessDefinition{
					Name: key1,
					Key:  key1,
					XMLData: []byte(
						`<definitions>
						<process>
						</process>
						</definitions>`),
				}
				err := repoService.Create(m)
				Expect(err).To(BeNil())
				Expect(m.Version).ToNot(Equal(m1.Version))
			})

			It("should activate and then suspend 1 ProcessDefinition model", func() {
				err := repoService.ActivateProcessByKey(key1, false)
				Expect(err).To(BeNil())
				m1, err := repoService.GetProcessByKey(key1)
				Expect(err).To(BeNil())
				Expect(m1.Status).To(Equal(enums.ProcessDefStatusActive))

				err = repoService.SuspendProcessByKey(key1, false)
				Expect(err).To(BeNil())
				m1, err = repoService.GetProcessByKey(key1)
				Expect(err).To(BeNil())
				Expect(m1.Status).To(Equal(enums.ProcessDefStatusSuspended))
			})

			It("should suspend and then activate 1 ProcessDefinition model", func() {
				err := repoService.SuspendProcessByKey(key2, false)
				Expect(err).To(BeNil())
				m2, err := repoService.GetProcessByKey(key2)
				Expect(err).To(BeNil())
				Expect(m2.Status).To(Equal(enums.ProcessDefStatusSuspended))

				err = repoService.ActivateProcessByKey(key2, false)
				Expect(err).To(BeNil())
				m2, err = repoService.GetProcessByKey(key2)
				Expect(err).To(BeNil())
				Expect(m2.Status).To(Equal(enums.ProcessDefStatusActive))

			})
		})

	})

})
