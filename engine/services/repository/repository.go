package repository

import (
	"regexp"
	"sync"

	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/bpmn/parser"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/util/logger"
)

// Service Repository service to manage process definitions
type Service interface {
	Init() error
	Create(*models.ProcessDefinition) error
	GetProcessByKey(key string) (model models.ProcessDefinition, err error)
	GetProcessByID(id uint64) (model models.ProcessDefinition, err error)
	ListProcess(param ProcessDefList) (models []models.ProcessDefinition, err error)
	SearchProcessByName(param SearchProcessByNameParam) ([]models.ProcessDefinition, error)
	CountProcessByName(name string) (int64, error)
	SuspendProcessByKey(key string, suspendInstances bool) error
	SuspendProcessByID(id uint64, suspendInstances bool) error
	ActivateProcessByKey(key string, activateInstances bool) error
	ActivateProcessByID(id uint64, activateInstances bool) error
}

type repositoryService struct {
	*services.Base
	logger               logrus.FieldLogger
	processDefinitionMgr mgr.ProcessDefinitionMgr
	processMap           map[uint64]*models.ProcessDefinition
	sync.RWMutex
}

// NewRepositoryService construct a repository service
func NewRepositoryService(base *services.Base) (service Service) {
	service = &repositoryService{
		Base:                 base,
		logger:               logrus.WithField(logger.FieldKeyPrefix, "RepositoryService"),
		processDefinitionMgr: mgr.ProcessDefinitionMgr{*base.Mgr},
		processMap:           make(map[uint64]*models.ProcessDefinition, 0),
	}

	return
}

// TODO: init load existed active process
func (r *repositoryService) Init() (err error) {
	_, err = r.ListProcess(ProcessDefList{})
	return err
}

func formatKey(key string) string {
	reg := regexp.MustCompile("//s+")
	return reg.ReplaceAllString(key, "")
}

func (r *repositoryService) Create(model *models.ProcessDefinition) (err error) {
	// parse node
	if model.XMLData != nil {
		model.ProcessModel, err = parser.ParseBpmn(model.XMLData)
		if err != nil {
			r.logger.WithField("data", string(model.XMLData)).
				WithError(err).
				Error("<repositoryService.Create> parse bpmn model with error")
			return
		}
	}

	// insert model
	err = r.processDefinitionMgr.Create(model)
	if err != nil {
		r.logger.WithField("name", model.Name).
			WithField("key", model.Key).
			WithError(err).
			Error("<repositoryService.Create> save process-definition with error")
		return
	}

	// ignore error
	r.cacheModel(model)
	return
}

func (r *repositoryService) GetProcessByKey(key string) (model models.ProcessDefinition, err error) {
	model, err = r.processDefinitionMgr.GetProcessByKey(key)
	if err != nil {
		r.logger.WithField("key", key).
			WithError(err).
			Error("<repositoryService.GetProcessByKey> find process-definition by key with error")
		return
	}

	err = r.cacheModel(&model)
	return
}

func (r *repositoryService) GetProcessByID(id uint64) (model models.ProcessDefinition, err error) {
	if m := r.GetCache(id); m != nil {
		return *m, nil
	}

	model, err = r.processDefinitionMgr.GetProcessByID(id)
	if err != nil {
		r.logger.WithField("id", id).
			WithError(err).
			Error("<repositoryService.GetProcessByID> find process-definition by id with error")
		return
	}

	err = r.cacheModel(&model)

	return
}

type SearchProcessByNameParam struct {
	Name   string
	Limit  int
	Offset int
}

func (r *repositoryService) SearchProcessByName(param SearchProcessByNameParam) ([]models.ProcessDefinition, error) {
	processes, err := r.processDefinitionMgr.SearchProcessByName(param.Name, param.Offset, param.Limit)
	if err != nil {
		r.logger.WithFields(logrus.Fields{
			"name":   param.Name,
			"limit":  param.Limit,
			"offset": param.Offset,
		}).WithError(err).Error("<repositoryService.SearchProcessByName> search latest process-definitions with error")
		return nil, err
	}
	return processes, nil
}

func (r *repositoryService) CountProcessByName(name string) (int64, error) {
	count, err := r.processDefinitionMgr.CountProcessByName(name)
	if err != nil {
		r.logger.WithField("name", name).
			WithError(err).
			Error("<repositoryService.CountProcessByName> count latest process-definitions by name with error")
		return 0, err
	}
	return count, nil
}

// ProcessDefList List param for process definition
type ProcessDefList struct {
	Key      string
	Name     string
	Status   *enums.ProcessDefStatus
	Page     int
	PageSize int
}

func (r *repositoryService) ListProcess(param ProcessDefList) (processDefs []models.ProcessDefinition, err error) {
	db := r.processDefinitionMgr.Model(&models.ProcessDefinition{})
	db = mgr.Paging(db, param.Page, param.PageSize)
	if param.Key != "" {
		db = db.Where("process_definition.key = ?", param.Key)
	}
	if param.Name != "" {
		db = db.Where("process_definition.name = ?", param.Name)
	}
	if param.Status != nil && *param.Status != 0 {
		db = db.Where("process_definition.status = ?", *param.Status)
	}

	err = db.Order("process_definition.updated_at desc").Find(&processDefs).Error
	if err != nil {
		r.logger.WithField("params", param).
			WithError(err).
			Error("<repositoryService.ListProcess> list process-definitions with error")
		return
	}

	err = r.cacheModels(processDefs)
	return
}

func (r *repositoryService) SuspendProcessByKey(key string, suspendInstances bool) error {
	// TODO: suspend all instances related
	if suspendInstances {
		return nil
	}
	return r.processDefinitionMgr.SuspendProcessByKey(key)
}

func (r *repositoryService) SuspendProcessByID(id uint64, suspendInstances bool) error {
	return r.processDefinitionMgr.SuspendProcessByID(id)
}

func (r *repositoryService) ActivateProcessByKey(key string, activateInstances bool) error {
	// TODO: activate all instances related
	if activateInstances {
		return nil
	}
	return r.processDefinitionMgr.ActivateProcessByKey(key)
}

func (r *repositoryService) ActivateProcessByID(id uint64, activateInstances bool) error {
	return r.processDefinitionMgr.ActivateProcessByID(id)
}
