package history

import (
	"net/http"

	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/util/logger"
)

// Service History service to query history infos of process
type Service interface {
	CreateProcess(*models.ProcessInstance) error
	GetProcessByID(id uint64) (models.ProcessInstance, error)
	ListProcess(param ProcessListParam) ([]ProcessListResult, int, error)
	SaveProcess(*models.ProcessInstance) error

	ListActivity(param ActivityListParam) ([]models.ActivityInstance, error)
	CreateActivity(*models.ActivityInstance) error
	CreateActivities([]*models.ActivityInstance) error

	ListListeners(param *ListListenerParam) ([]models.Listener, error)
	RunListener(param *RunListenerParam) error
	UpdateListenerStatus(param *UpdateListenerStatusParam) error
	UpdateListenerStatusByExcode(param *UpdateListenerStatusByExcodeParam) error

	// 统计相关接口
	CountProcessInstance(param CountProcessInstanceParam) (int, error)
	AverageDuration(param AverageDurationParam) (int, error)
	CountOverTimeProcessInstance(param CountOverTimeProcessInstanceParam) (int, error)
	CountUrge(param CountUrgeProcessInstanceParam) (int, error)
	CountRejectProcessInstance(param CountRejectProcessInstanceParam) (int, error)
	GlobalAvgCountProcessInstance(param GlobalAvgCountProcessInstanceParam) (int, error)
	GlobalAvgAverageDuration(param GlobalAvgAverageDurationParam) (int, error)
	GlobalAvgCountOverTimeProcessInstance(param GlobalAvgCountOverTimeProcessInstanceParam) (int, error)
	GlobalAvgCountUrge(param GlobalAvgCountUrgeParam) (int, error)
	GlobalAvgCountReject(param GlobalAvgCountRejectParam) (int, error)
	SortApprovalTypeByCount(param SortApprovalTypeByCountParam) ([]string, error)

	CreateProcessActivityLog(*models.ProcessActivityLog) error
	CreateProcessActivityLogWithMentions(log *models.ProcessActivityLog, mentions []*models.ProcessActivityLogMention) error
	ListProcessActivityLogByUser(param ListProcessActivityLogByUserParam) ([]ListProcessActivityLogByUserResp, int, error)
}

type historyService struct {
	*services.Base
	logger                       logrus.FieldLogger
	Repository                   repository.Service
	processInstanceMgr           mgr.ProcessInstanceMgr
	activityInstanceMgr          mgr.ActivityInstanceMgr
	adminTransport               http.RoundTripper
	listenerMgr                  mgr.ListenerMgr
	processActivityLogMgr        mgr.ProcessActivityLogMgr
	processActivityLogMentionMgr mgr.ProcessActivityLogMentionMgr
}

// NewHistoryService construct a history service
func NewHistoryService(base *services.Base, repositoryService repository.Service, adminTransport http.RoundTripper) (service Service) {
	service = &historyService{
		Base:                         base,
		logger:                       logrus.WithField(logger.FieldKeyPrefix, "HistoryService"),
		processInstanceMgr:           mgr.ProcessInstanceMgr{Base: *base.Mgr},
		activityInstanceMgr:          mgr.ActivityInstanceMgr{Base: *base.Mgr},
		listenerMgr:                  mgr.ListenerMgr{Base: *base.Mgr},
		Repository:                   repositoryService,
		adminTransport:               adminTransport,
		processActivityLogMgr:        mgr.ProcessActivityLogMgr{Base: *base.Mgr},
		processActivityLogMentionMgr: mgr.ProcessActivityLogMentionMgr{Base: *base.Mgr},
	}

	return
}

func (s *historyService) CreateProcess(m *models.ProcessInstance) error {
	return s.processInstanceMgr.Create(m)
}

func (s *historyService) GetProcessByID(id uint64) (m models.ProcessInstance, err error) {
	err = s.processInstanceMgr.DB.First(&m, "id = ?", id).Error
	return
}

func (s *historyService) ListProcess(param ProcessListParam) (m []ProcessListResult, count int, err error) {
	return s.listProcess(param)
}

func (s *historyService) SaveProcess(m *models.ProcessInstance) error {
	return s.processInstanceMgr.Save(m).Error
}

func (s *historyService) ListActivity(param ActivityListParam) (m []models.ActivityInstance, err error) {
	return s.listActivity(param)
}

func (s *historyService) CreateActivity(m *models.ActivityInstance) error {
	return s.activityInstanceMgr.Create(m)
}

func (s *historyService) CreateActivities(m []*models.ActivityInstance) error {
	return s.activityInstanceMgr.CreateBatch(m)
}

func (s *historyService) SortApprovalTypeByCount(param SortApprovalTypeByCountParam) ([]string, error) {
	return s.sortApprovalTypeByCount(param)
}

func (s *historyService) CreateProcessActivityLog(m *models.ProcessActivityLog) error {
	return s.processActivityLogMgr.Create(m)
}

func (s *historyService) CreateProcessActivityLogWithMentions(log *models.ProcessActivityLog, mentions []*models.ProcessActivityLogMention) error {
	return s.processActivityLogMgr.CreateWithMentions(log, mentions)
}

func (s *historyService) ListProcessActivityLogByUser(param ListProcessActivityLogByUserParam) ([]ListProcessActivityLogByUserResp, int, error) {
	return s.listProcessActivityLogByUser(param)
}
