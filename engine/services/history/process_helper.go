package history

import (
	"fmt"
	"strings"

	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
)

// ProcessListParam Params for list process instance
type ProcessListParam struct {
	ProcessDefinitionID  *uint64
	ProcessDefinitionKey *string
	ProcessInstanceID    *string
	Excode               *string
	UID                  *uint64
	Assignees            []string
	Status               *enums.ProcessInstanceStatus
	Starters             []string
	Page                 int
	PageSize             int
}

type ProcessListResult struct {
	models.ProcessInstance
	ProcessDefinitionKey string `gorm:"process_definition.key" json:"process_definition_key"`
	HasUrge              bool   `gorm:"has_urge" json:"has_urge"`
}

func (s *historyService) listProcess(param ProcessListParam) (m []ProcessListResult, count int, err error) {
	prefix := "process_instance"
	fields := s.Base.Mgr.Fields(models.ProcessInstance{}, prefix)
	fields = strings.Replace(fields, fmt.Sprintf("%s.id", prefix), fmt.Sprintf("DISTINCT %s.id", prefix), 1)
	fields = fmt.Sprintf("%s, process_definition.key as process_definition_key", fields)

	db := s.processInstanceMgr.Table("process_instance")

	if param.Status != nil && *param.Status == enums.ProcessStatusPending {
		fields = fmt.Sprintf("%s, CASE WHEN urge_task.id IS NULL THEN false ELSE true END as has_urge", fields)
		db = db.Joins("left join run_execution on run_execution.process_instance_id = process_instance.id")
		db = db.Joins("left join urge_task on urge_task.execution_id = run_execution.id")
	}

	db = db.Joins("join process_definition on process_definition.id = process_instance.process_definition_id")

	if param.Status != nil && *param.Status != 0 {
		db = db.Where("process_instance.status = ?", *param.Status)
	}

	if param.ProcessInstanceID != nil && *param.ProcessInstanceID != "" {
		db = db.Where("process_instance.id = ?", *param.ProcessInstanceID)
	}

	if param.Excode != nil && *param.Excode != "" {
		db = db.Where("process_instance.excode = ?", *param.Excode)
	}

	if param.UID != nil && *param.UID != 0 {
		db = db.Where("process_instance.uid = ?", *param.UID)
	}

	starterSize := len(param.Starters)
	if starterSize == 1 {
		db = db.Where("process_instance.start_by_id = ?", param.Starters[0])
	} else if starterSize > 1 {
		db = db.Where("process_instance.start_by_id in (?)", param.Starters)
	}

	if param.ProcessDefinitionKey != nil && *param.ProcessDefinitionKey != "" {
		db = db.Where("process_definition.key = ?", *param.ProcessDefinitionKey)
	}

	if param.ProcessDefinitionID != nil && *param.ProcessDefinitionID != 0 {
		db = db.Where("process_instance.process_definition_id = ?", *param.ProcessDefinitionID)
	}

	if len(param.Assignees) > 0 {
		db = db.Joins("join activity_instance act on act.process_instance_id = process_instance.id and type in ('UserTask', 'SuggestTask') and act.actor in (?)", param.Assignees)
	}

	countDb := db.Select("COUNT(DISTINCT process_instance.id)")
	err = countDb.Count(&count).Error

	if err != nil {
		return
	}

	db = mgr.Paging(db, param.Page, param.PageSize)

	db = db.Select(fields)

	if param.Status != nil && *param.Status == enums.ProcessStatusPending {
		db = db.Order("has_urge desc, process_instance.updated_at desc")
	} else {
		db = db.Order("process_instance.updated_at desc")
	}
	err = db.Find(&m).Error
	return
}

type SortApprovalTypeByCountParam struct {
	UserId string
	Days   int
	Asc    bool
}

// 审批类型按发起次数排序
func (s *historyService) sortApprovalTypeByCount(params SortApprovalTypeByCountParam) (sortedList []string, err error) {
	db := s.processInstanceMgr.Model(&models.ProcessInstance{})

	if params.UserId != "" {
		db = db.Where("start_by_id = ?", params.UserId)
	}

	if params.Days > 0 {
		db = db.Where("start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", params.Days)
	}

	orderDirection := "DESC"
	if params.Asc {
		orderDirection = "ASC"
	}

	db = db.Joins("JOIN process_definition ON process_definition.id = process_instance.process_definition_id").
		Select("process_definition.key, COUNT(*) as count").
		Group("process_definition.key").
		Order(fmt.Sprintf("Count(*) %s", orderDirection))

	err = db.Pluck("process_definition.key", &sortedList).Error

	if sortedList == nil {
		sortedList = []string{}
	}

	return
}
