package history

import (
	"errors"

	"qiniu.io/qbpm/engine/jobs"
	"qiniu.io/qbpm/engine/models"
)

type ListListenerParam struct {
	ProcessInstanceID uint64 `schema:"process_instance_id,string"`
	ExecutionID       uint64 `schema:"execution_id,string"`
	ActionKey         string `schema:"action_key"`
	NodeKey           string `schema:"node_key"`
}

func (i ListListenerParam) Valid() bool {
	return i.ProcessInstanceID != 0 || i.ExecutionID != 0
}

type RunListenerParam struct {
	ExecutionID uint64 `json:"execution_id,string"`
	ListenerKey string `json:"listener_key,omitempty"`
	ActionKey   string `json:"action_key,omitempty"`
}

func (p RunListenerParam) Valid() bool {
	return p.ExecutionID != 0 && p.ListenerKey != "" && p.ActionKey != ""
}

func (s *historyService) ListListeners(param *ListListenerParam) (result []models.Listener, err error) {
	db := s.listenerMgr.Model(&models.Listener{})

	if param.ProcessInstanceID != 0 {
		db = db.Where("process_instance_id = ?", param.ProcessInstanceID)
	}
	if param.ExecutionID != 0 {
		db = db.Where("execution_id = ?", param.ExecutionID)
	}
	if param.ActionKey != "" {
		db = db.Where("action_key = ?", param.ActionKey)
	}
	if param.NodeKey != "" {
		db = db.Where("node_key = ?", param.NodeKey)
	}

	err = db.Find(&result).Order("created_at").Error
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (s *historyService) RunListener(param *RunListenerParam) error {
	listener, err := s.listenerMgr.Get(&models.Listener{
		ExecutionID: param.ExecutionID,
		ListenerKey: param.ListenerKey,
		ActionKey:   param.ActionKey,
	})
	if err != nil {
		return err
	}

	if listener.Status == models.CompleteListener {
		return errors.New("重复操作")
	}

	definition, err := s.Repository.GetProcessByID(listener.ProcessDefinitionID)
	if err != nil {
		return err
	}

	node, err := definition.GetNode(listener.NodeKey)
	if err != nil {
		s.logger.Errorf("load listener jobs with error: %s", err)
		return err
	}

	err = jobs.NewListener(node, &definition, listener.ExecutionID, s.Mgr, s.adminTransport).FireAction(listener.Fire, listener.NodeKey, listener.ActionKey)
	if err != nil {
		s.logger.Errorf("run listener job(executionID: %d, listener_key: %s, fire: %s, action: %d) with error: %s",
			listener.ExecutionID, listener.ListenerKey, listener.Fire, listener.ActionKey, err)
		return err
	}

	return nil
}

type UpdateListenerStatusByExcodeParam struct {
	ProcessDefinitionKey string `json:"process_definition_key"`
	Excode               string `json:"excode"`
	ActionKey            string `json:"action_key"`
	Status               int    `json:"status"`
	Errors               string `json:"errors"`
}

func (p UpdateListenerStatusByExcodeParam) Valid() bool {
	if p.ProcessDefinitionKey == "" || p.Excode == "" || p.ActionKey == "" {
		return false
	}

	return p.Status >= 0 && p.Status <= 2
}

func (s *historyService) UpdateListenerStatusByExcode(param *UpdateListenerStatusByExcodeParam) error {
	if !param.Valid() {
		return errors.New("invalid params")
	}

	processes, _, err := s.listProcess(ProcessListParam{
		ProcessDefinitionKey: &param.ProcessDefinitionKey,
		Excode:               &param.Excode,
	})
	if err != nil {
		return err
	}

	if len(processes) == 0 {
		return errors.New("process not found")
	}

	listeners, err := s.listenerMgr.ListByProcessInstance(processes[0].ID)
	if err != nil {
		return err
	}

	var listener *models.Listener
	for _, l := range listeners {
		if l.ActionKey == param.ActionKey {
			listener = l
		}
	}

	listener.Status = models.ListenerStatus(param.Status)
	listener.Errors = param.Errors

	return s.listenerMgr.UpdateListener(listener)
}

type UpdateListenerStatusParam struct {
	ExecutionID uint64 `json:"execution_id,string"`
	ListenerKey string `json:"listener_key"`
	ActionKey   string `json:"action_key"`
	Status      int    `json:"status"`
	Errors      string `json:"errors"`
}

func (p UpdateListenerStatusParam) Valid() bool {
	if p.ListenerKey == "" || p.ActionKey == "" || p.ExecutionID <= 0 {
		return false
	}

	return p.Status >= 0 && p.Status <= 2
}

func (s *historyService) UpdateListenerStatus(param *UpdateListenerStatusParam) error {
	if !param.Valid() {
		return errors.New("invalid params")
	}

	listener, err := s.listenerMgr.Get(&models.Listener{
		ExecutionID: param.ExecutionID,
		ListenerKey: param.ListenerKey,
		ActionKey:   param.ActionKey,
	})
	if err != nil {
		return err
	}

	listener.Status = models.ListenerStatus(param.Status)
	listener.Errors = param.Errors

	return s.listenerMgr.UpdateListener(listener)
}
