package history

import (
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
)

// ActivityListParam Params for list activity instance
type ActivityListParam struct {
	ProcessInstanceID *uint64
	Excode            *string
	Status            *enums.ActivityStatus
	Types             []string
	Page              int
	PageSize          int
}

func (s *historyService) listActivity(param ActivityListParam) (m []models.ActivityInstance, err error) {
	db := s.activityInstanceMgr.Model(&models.ActivityInstance{})
	db = mgr.Paging(db, param.Page, param.PageSize)

	if param.ProcessInstanceID != nil && *param.ProcessInstanceID != 0 {
		db = db.Where("activity_instance.process_instance_id = ?", *param.ProcessInstanceID)
	}

	if param.Status != nil && *param.Status != 0 {
		db = db.Where("activity_instance.status = ?", *param.Status)
	}

	typeSize := len(param.Types)
	if typeSize == 1 {
		db = db.Where("activity_instance.type = ?", param.Types[0])
	} else if typeSize > 1 {
		db = db.Where("activity_instance.type in (?)", param.Types)
	}

	if param.Excode != nil && *param.Excode != "" {
		db = db.Joins("join process_instance on activity_instance.process_instance_id = process_instance.id and process_instance.excode = ?", *param.Excode)
	}

	err = db.Order("activity_instance.id desc").Find(&m).Error

	return
}
