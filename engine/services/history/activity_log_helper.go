package history

import (
	"time"

	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
)

type ListProcessActivityLogByUserParam struct {
	User      string     `schema:"user"`
	Page      int        `schema:"page"`
	PageSize  int        `schema:"page_size"`
	StartTime *time.Time `schema:"start_time"`
	EndTime   *time.Time `schema:"end_time"`
}

type ListProcessActivityLogByUserResp struct {
	models.ProcessActivityLog
	ProcessDefinitionKey  string `json:"process_definition_key" gorm:"column:process_definition_key"`
	ProcessInstanceExcode string `json:"process_instance_excode" gorm:"column:process_instance_excode"`
	Uid                   uint64 `json:"uid" gorm:"uid"`
	AccountName           string `json:"account_name"`
}

func (s *historyService) listProcessActivityLogByUser(param ListProcessActivityLogByUserParam) (logs []ListProcessActivityLogByUserResp, count int, err error) {
	db := s.processActivityLogMgr.Model(&models.ProcessActivityLog{})
	db = mgr.Paging(db, param.Page, param.PageSize)

	if param.User == "" {
		return []ListProcessActivityLogByUserResp{}, 0, nil
	}

	db = db.Joins("join process_instance on process_instance.id = process_activity_log.process_instance_id").
		Joins("join process_definition on process_definition.id = process_instance.process_definition_id").
		Joins("left join process_activity_log_mention on process_activity_log_mention.activity_id = process_activity_log.id").
		Select("process_activity_log.*, process_definition.key as process_definition_key, process_instance.uid as uid, process_instance.excode as process_instance_excode").
		Where("(process_instance.start_by_id = ? and process_activity_log.activity_type in (?, ?) or process_activity_log_mention.user_id = ?)", param.User, enums.ActivityLogTypeRejected, enums.ActivityLogTypeSuspended, param.User)

	if param.StartTime != nil {
		db = db.Where("process_activity_log.created_at >= ?", *param.StartTime)
	}

	if param.EndTime != nil {
		db = db.Where("process_activity_log.created_at <= ?", *param.EndTime)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	err = db.Order("process_activity_log.created_at desc").Find(&logs).Error

	return
}
