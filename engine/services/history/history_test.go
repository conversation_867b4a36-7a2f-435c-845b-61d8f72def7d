package history_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/services"
	. "qiniu.io/qbpm/engine/services/history"
	"qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/test"
)

var (
	historyService Service
	mgrBase        *mgr.Base
)

var _ = Describe("History", func() {
	BeforeEach(func() {
		mgrBase = test.Init()
		servBase := &services.Base{Mgr: mgrBase}
		repositoryService := repository.NewRepositoryService(servBase)
		historyService = NewHistoryService(servBase, repositoryService, nil)
	})

	Describe("HistoryService", func() {
		Context("with some data in db", func() {
			var (
				err error

				key        = "testRepo1"
				processDef = &models.ProcessDefinition{
					Name: key,
					Key:  key,
				}

				instance = &models.ProcessInstance{Name: "test"}

				execution *models.RunExecution
			)

			JustBeforeEach(func() {
				err = mgrBase.Create(processDef).Error
				Expect(err).To(BeNil())

				instance.ProcessDefinitionID = processDef.ID
				err = mgrBase.Create(instance).Error
				Expect(err).To(BeNil())

				execution = &models.RunExecution{
					Base: models.Base{ID: 123},
				}
				execution.ProcessDefinitionID = processDef.ID
				execution.ProcessInstanceID = instance.ID
				err = mgrBase.Create(execution).Error
				Expect(err).To(BeNil())
			})

			It("should create 1 process instance", func() {
				m := &models.ProcessInstance{
					ProcessDefinitionID: processDef.ID,
				}
				err := historyService.CreateProcess(m)
				Expect(err).To(BeNil())
			})

			It("should get 1 process instance", func() {
				process, err := historyService.GetProcessByID(instance.ID)
				Expect(err).To(BeNil())
				Expect(process.Name).To(Equal("test"))
			})

			It("should list 1 process instance", func() {
				processes, count, err := historyService.ListProcess(ProcessListParam{})
				Expect(err).To(BeNil())
				Expect(len(processes)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should create 1 activity instance", func() {
				m := &models.ActivityInstance{
					ProcessInstanceID: instance.ID,
					ExecutionID:       execution.ID,
				}
				err := historyService.CreateActivity(m)
				Expect(err).To(BeNil())
			})

			It("should create 2 activity instances", func() {
				instances := make([]*models.ActivityInstance, 2)
				instances[0] = &models.ActivityInstance{
					ProcessInstanceID: instance.ID,
					ExecutionID:       execution.ID,
				}
				instances[1] = &models.ActivityInstance{
					ProcessInstanceID: instance.ID,
					ExecutionID:       execution.ID,
				}

				err := historyService.CreateActivities(instances)
				Expect(err).To(BeNil())
			})

			It("should list specific activity instances", func() {
				instances := make([]*models.ActivityInstance, 2)
				instances[0] = &models.ActivityInstance{
					ProcessInstanceID: instance.ID,
					ExecutionID:       execution.ID,
				}
				instances[1] = &models.ActivityInstance{
					ProcessInstanceID: instance.ID,
					ExecutionID:       execution.ID,
				}

				err := historyService.CreateActivities(instances)
				Expect(err).To(BeNil())

				processes, err := historyService.ListActivity(ActivityListParam{})
				Expect(err).To(BeNil())
				Expect(len(processes)).To(Equal(2))

				processes, _ = historyService.ListActivity(ActivityListParam{PageSize: 1})
				Expect(len(processes)).To(Equal(1))
			})

			It("should list 1 activity instance", func() {
				m := &models.ActivityInstance{
					ProcessInstanceID: instance.ID,
					ExecutionID:       execution.ID,
				}
				err := historyService.CreateActivity(m)
				Expect(err).To(BeNil())

				processes, err := historyService.ListActivity(ActivityListParam{})
				Expect(err).To(BeNil())
				Expect(len(processes)).To(Equal(1))
			})
		})
	})
})
