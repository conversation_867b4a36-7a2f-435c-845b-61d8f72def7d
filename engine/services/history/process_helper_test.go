package history_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	. "qiniu.io/qbpm/engine/services/history"
	"qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("ProcessHelper", func() {
	BeforeEach(func() {
		mgrBase = test.Init()
		servBase := &services.Base{Mgr: mgrBase}
		repositoryService := repository.NewRepositoryService(servBase)
		historyService = NewHistoryService(servBase, repositoryService, nil)
	})

	Describe("HistoryService", func() {
		Context("with some data in db", func() {
			var (
				err        error
				key        = "testRepo"
				processDef = &models.ProcessDefinition{
					Name: key,
					Key:  key,
				}
				instance  *models.ProcessInstance
				execution *models.RunExecution
				activity1 *models.ActivityInstance
				activity2 *models.ActivityInstance
			)

			JustBeforeEach(func() {
				// create definition
				err = mgrBase.Create(processDef).Error
				Expect(err).To(BeNil())

				// create instance
				instance = &models.ProcessInstance{
					ProcessDefinitionID: processDef.ID,
					Name:                processDef.Name,
					StartByID:           "user",
					Excode:              "001",
				}
				err = mgrBase.Create(instance).Error
				Expect(err).To(BeNil())

				// create execution
				execution = &models.RunExecution{
					Base: models.Base{ID: 123},
				}
				execution.ProcessDefinitionID = processDef.ID
				execution.ProcessInstanceID = instance.ID
				err = mgrBase.Create(execution).Error
				Expect(err).To(BeNil())

				// create activity
				activity1 = &models.ActivityInstance{
					Base:              models.Base{ID: 123},
					ProcessInstanceID: instance.ID,
					ExecutionID:       execution.ID,
					ActivityDefineKey: "test",
					Name:              "test",
					Type:              "UserTask",
					OriginalActor:     "hr",
					Actor:             "admin",
				}
				err = mgrBase.Create(activity1).Error
				Expect(err).To(BeNil())

				activity2 = &models.ActivityInstance{
					Base:              models.Base{ID: 1234},
					ProcessInstanceID: instance.ID,
					ExecutionID:       execution.ID,
					ActivityDefineKey: "test",
					Name:              "test",
					Type:              "UserTask",
					OriginalActor:     "hr",
					Actor:             "hr",
				}
				err = mgrBase.Create(activity2).Error
				Expect(err).To(BeNil())
			})

			It("should list 1 process instance", func() {
				models, count, err := historyService.ListProcess(ProcessListParam{})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should list 1 process instance", func() {
				models, count, err := historyService.ListProcess(ProcessListParam{
					ProcessDefinitionID: &processDef.ID,
				})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should list 1 process instance", func() {
				var key = "testRepo"
				models, count, err := historyService.ListProcess(ProcessListParam{
					ProcessDefinitionKey: &key,
				})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should list 1 process instance", func() {
				var excode = "001"
				models, count, err := historyService.ListProcess(ProcessListParam{
					Excode: &excode,
				})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should list 1 process instance", func() {
				var status = enums.ProcessStatusPending
				models, count, err := historyService.ListProcess(ProcessListParam{
					Status: &status,
				})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should list 1 process instance", func() {
				var starters = []string{"user"}
				models, count, err := historyService.ListProcess(ProcessListParam{
					Starters: starters,
				})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should list 1 process instance", func() {
				var starters = []string{"user", "test"}
				models, count, err := historyService.ListProcess(ProcessListParam{
					Starters: starters,
				})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should list 1 process instance", func() {
				var assignees = []string{"admin"}
				models, count, err := historyService.ListProcess(ProcessListParam{
					Assignees: assignees,
				})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should list 1 process instance", func() {
				var assignees = []string{"admin", "hr"}
				models, count, err := historyService.ListProcess(ProcessListParam{
					Assignees: assignees,
				})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(1))
				Expect(count).To(Equal(1))
			})

			It("should list 0 process instance", func() {
				var assignees = []string{"test"}
				models, count, err := historyService.ListProcess(ProcessListParam{
					Assignees: assignees,
				})
				Expect(err).To(BeNil())
				Expect(len(models)).To(Equal(0))
				Expect(count).To(Equal(0))
			})

		})
	})
})
