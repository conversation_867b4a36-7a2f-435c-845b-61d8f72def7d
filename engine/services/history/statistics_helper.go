package history

import (
	"database/sql"
	"fmt"
	"math"
	"time"

	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
)

type CountProcessInstanceParam struct {
	enums.ViewType
	Days   int
	UserId string
}

// 统计审批数量
func (s *historyService) CountProcessInstance(param CountProcessInstanceParam) (count int, err error) {
	db := s.processInstanceMgr.Model(&models.ProcessInstance{})

	// 我审批的
	if param.ViewType == enums.Assignee && param.UserId != "" {
		db = db.Select("COUNT(DISTINCT process_instance.id)").
			Joins("JOIN activity_instance ON activity_instance.process_instance_id = process_instance.id").
			Where("activity_instance.actor = ? AND activity_instance.type = ?", param.UserId, enums.ActivityTypeUserTask)
	}

	// 我发起的
	if param.ViewType == enums.Starter && param.UserId != "" {
		db = db.Where("start_by_id =?", param.UserId)
	}

	// 查询指定天数
	if param.Days > 0 {
		if param.ViewType == enums.Assignee {
			db = db.Where("process_instance.start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
		} else {
			db = db.Where("start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
		}
	}

	if param.ViewType == enums.Assignee {
		err = db.Row().Scan(&count)
	} else {
		err = db.Count(&count).Error
	}

	return
}

type AverageDurationParam struct {
	enums.ViewType
	Days   int
	UserId string
}

// 统计平均审批时长，审批开始时间在指定天数内
func (s *historyService) AverageDuration(param AverageDurationParam) (avgDuration int, err error) {
	processDb := s.processInstanceMgr.Model(&models.ProcessInstance{})
	activityDb := s.activityInstanceMgr.Model(&models.ActivityInstance{})

	var nullableAvgDuration sql.NullFloat64

	if param.ViewType == enums.Assignee && param.UserId != "" {
		// 第一层子查询：计算每个流程的总耗时
		innerSubQuery := activityDb.Select("activity_instance.process_instance_id, SUM(TIMESTAMPDIFF(SECOND, activity_instance.start_at, activity_instance.end_at)) as total_duration").
			Joins("JOIN process_instance ON process_instance.id = activity_instance.process_instance_id").
			Where("activity_instance.actor = ? AND activity_instance.status IN (?,?) AND activity_instance.type = ?",
				param.UserId, enums.ActivityStatusCompleted, enums.ActivityStatusFailed, enums.ActivityTypeUserTask)

		if param.Days > 0 {
			innerSubQuery = innerSubQuery.Where("process_instance.start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
		}

		innerSubQuery = innerSubQuery.Group("activity_instance.process_instance_id")

		// 使用子查询计算平均值
		err = activityDb.Raw("SELECT AVG(total_duration) FROM (?) as t", innerSubQuery.SubQuery()).
			Row().Scan(&nullableAvgDuration)
	}

	if param.ViewType == enums.Starter && param.UserId != "" {
		processDb = processDb.Where("start_by_id = ? AND status IN (?,?)", param.UserId, enums.ProcessStatusCompleted, enums.ProcessStatusFailed)

		if param.Days > 0 {
			processDb = processDb.Where("start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
		}

		err = processDb.Select("AVG(TIMESTAMPDIFF(SECOND, start_at, end_at))").Row().Scan(&nullableAvgDuration)
	}

	// 处理可能为NULL的情况
	if err == nil && nullableAvgDuration.Valid {
		avgDuration = int(nullableAvgDuration.Float64)
	} else {
		avgDuration = 0
	}

	return
}

type CountOverTimeProcessInstanceParam struct {
	enums.ViewType
	OvertimeDays int
	UserId       string
}

// 统计审批节点等待超过n天的数量(历史全部)
func (s *historyService) CountOverTimeProcessInstance(param CountOverTimeProcessInstanceParam) (count int, err error) {
	db := s.processInstanceMgr.Model(&models.ProcessInstance{})
	db = db.Joins("JOIN activity_instance ON activity_instance.process_instance_id = process_instance.id").
		Where("process_instance.status = ?", enums.ProcessStatusPending)

	if param.ViewType == enums.Assignee && param.UserId != "" {
		db = db.Where("activity_instance.actor = ? AND activity_instance.type = ?", param.UserId, enums.ActivityTypeUserTask)
	}

	if param.ViewType == enums.Starter && param.UserId != "" {
		db = db.Where("process_instance.start_by_id = ?", param.UserId)
	}

	if param.OvertimeDays > 0 {
		db = db.Where("activity_instance.start_at < DATE_SUB(CURDATE(), INTERVAL ? DAY) AND activity_instance.status = ?", param.OvertimeDays, enums.ActivityStatusPending)
	}

	// 使用DISTINCT避免重复计数
	err = db.Select("COUNT(DISTINCT process_instance.id)").Row().Scan(&count)

	return
}

type CountUrgeProcessInstanceParam struct {
	Days   int
	UserId string
}

// 统计n天内被催次数
func (s *historyService) CountUrge(param CountUrgeProcessInstanceParam) (count int, err error) {
	urgeMgr := mgr.UrgeTaskMgr{Base: *s.Mgr}
	db := urgeMgr.Model(&models.UrgeTask{})

	if param.UserId == "" {
		return 0, nil
	}

	db = db.Where("assignee = ?", param.UserId)

	if param.Days > 0 {
		db = db.Where("created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
	}

	err = db.Count(&count).Error
	return
}

type CountRejectProcessInstanceParam struct {
	Days   int
	UserId string
}

// 统计n天内被驳回的审批数量
func (s *historyService) CountRejectProcessInstance(param CountRejectProcessInstanceParam) (count int, err error) {
	db := s.processInstanceMgr.Model(&models.ProcessInstance{})

	if param.UserId == "" {
		return 0, nil
	}

	db = db.Where("start_by_id = ?", param.UserId)

	if param.Days > 0 {
		db = db.Where("start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
	}

	db = db.Where("status = ?", enums.ProcessStatusFailed)

	err = db.Count(&count).Error
	return
}

// 计算到次日0点的时间差作为过期时间
func (s *historyService) getExpirationToNextDay() time.Duration {
	now := time.Now()
	tomorrow := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
	return tomorrow.Sub(now)
}

type GlobalAvgCountProcessInstanceParam struct {
	enums.ViewType
	Days int
}

// 统计全部用户审批数量平均值
func (s *historyService) GlobalAvgCountProcessInstance(param GlobalAvgCountProcessInstanceParam) (avgCount int, err error) {

	// 查询缓存
	cacheKey := fmt.Sprintf("global_avg_count_process_instance:%s:%d", param.ViewType, param.Days)
	val, err := s.RedisCli.Get(s.RedisCli.Context(), cacheKey).Int()
	if err == nil {
		return val, nil
	}

	processDb := s.processInstanceMgr.Model(&models.ProcessInstance{})
	activityDb := s.activityInstanceMgr.Model(&models.ActivityInstance{})
	var nullableAvgCount sql.NullFloat64

	if param.ViewType == enums.Assignee {
		// 创建子查询
		subQuery := activityDb.Select("actor, COUNT(DISTINCT process_instance_id) as count").
			Where("type = ?", enums.ActivityTypeUserTask)

		if param.Days > 0 {
			subQuery = subQuery.Where("start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
		}

		subQuery = subQuery.Group("actor")

		// 使用子查询计算平均值
		err = activityDb.Raw("SELECT AVG(count) FROM (?) as t", subQuery.SubQuery()).
			Row().Scan(&nullableAvgCount)
	}

	if param.ViewType == enums.Starter {
		// 创建子查询
		subQuery := processDb.Select("start_by_id, COUNT(*) as count")

		if param.Days > 0 {
			subQuery = subQuery.Where("start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
		}

		subQuery = subQuery.Group("start_by_id")

		// 使用子查询计算平均值
		err = processDb.Raw("SELECT AVG(count) FROM (?) as t", subQuery.SubQuery()).
			Row().Scan(&nullableAvgCount)
	}

	// 处理可能为NULL的情况
	if err == nil && nullableAvgCount.Valid {
		avgCount = int(math.Round(nullableAvgCount.Float64))
	} else {
		avgCount = 0
	}

	// 将结果存入Redis缓存，设置过期时间为当天结束（次日0点）
	if err == nil {
		expiration := s.getExpirationToNextDay()
		s.RedisCli.Set(s.RedisCli.Context(), cacheKey, avgCount, expiration)
	}

	return
}

type GlobalAvgAverageDurationParam struct {
	enums.ViewType
	Days int
}

// 统计全部用户审批平均耗时
func (s *historyService) GlobalAvgAverageDuration(param GlobalAvgAverageDurationParam) (avgDuration int, err error) {
	// 查询缓存
	cacheKey := fmt.Sprintf("global_avg_average_duration:%s:%d", param.ViewType, param.Days)
	val, err := s.RedisCli.Get(s.RedisCli.Context(), cacheKey).Int()
	if err == nil {
		return val, nil
	}

	processDb := s.processInstanceMgr.Model(&models.ProcessInstance{})
	activityDb := s.activityInstanceMgr.Model(&models.ActivityInstance{})
	var nullableAvgDuration sql.NullFloat64

	if param.ViewType == enums.Assignee {
		// 第一层子查询：按actor和process_instance_id分组，计算每个流程的总耗时
		innerSubQuery := activityDb.Select("actor, activity_instance.process_instance_id, SUM(TIMESTAMPDIFF(SECOND, activity_instance.start_at, activity_instance.end_at)) as total_duration").
			Joins("JOIN process_instance ON process_instance.id = activity_instance.process_instance_id").
			Where("activity_instance.status IN (?,?) AND type = ?", enums.ActivityStatusCompleted, enums.ActivityStatusFailed, enums.ActivityTypeUserTask)

		if param.Days > 0 {
			innerSubQuery = innerSubQuery.Where("process_instance.start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
		}

		innerSubQuery = innerSubQuery.Group("actor, activity_instance.process_instance_id")

		// 第二层子查询：按actor分组，计算每个用户的平均耗时
		outerSubQuery := activityDb.Raw("SELECT actor, AVG(total_duration) as avg_duration FROM (?) as t1 GROUP BY actor",
			innerSubQuery.SubQuery())

		// 最终查询：计算所有用户平均耗时的平均值
		err = activityDb.Raw("SELECT AVG(avg_duration) FROM (?) as t2", outerSubQuery.SubQuery()).
			Row().Scan(&nullableAvgDuration)
	}

	if param.ViewType == enums.Starter {
		// 创建子查询：按start_by_id分组，计算每个发起人的平均耗时
		subQuery := processDb.Select("start_by_id, AVG(TIMESTAMPDIFF(SECOND, start_at, end_at)) as avg_duration").
			Where("status IN (?,?)", enums.ProcessStatusCompleted, enums.ProcessStatusFailed)

		if param.Days > 0 {
			subQuery = subQuery.Where("start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
		}

		subQuery = subQuery.Group("start_by_id")

		// 使用子查询计算所有发起人平均耗时的平均值
		err = processDb.Raw("SELECT AVG(avg_duration) FROM (?) as t", subQuery.SubQuery()).
			Row().Scan(&nullableAvgDuration)
	}

	// 处理可能为NULL的情况
	if err == nil && nullableAvgDuration.Valid {
		avgDuration = int(math.Round(nullableAvgDuration.Float64))
	} else {
		avgDuration = 0
	}

	// 将结果存入Redis缓存，设置过期时间为当天结束（次日0点）
	if err == nil {
		expiration := s.getExpirationToNextDay()
		s.RedisCli.Set(s.RedisCli.Context(), cacheKey, avgDuration, expiration)
	}

	return
}

type GlobalAvgCountOverTimeProcessInstanceParam struct {
	enums.ViewType
	OvertimeDays int
}

// 统计全部用户审批节点等待超过n天的平均数量
func (s *historyService) GlobalAvgCountOverTimeProcessInstance(param GlobalAvgCountOverTimeProcessInstanceParam) (avgCount int, err error) {
	// 查询缓存
	cacheKey := fmt.Sprintf("global_avg_count_overtime_process_instance:%s:%d", param.ViewType, param.OvertimeDays)
	val, err := s.RedisCli.Get(s.RedisCli.Context(), cacheKey).Int()
	if err == nil {
		return val, nil
	}

	processDb := s.processInstanceMgr.Model(&models.ProcessInstance{})
	activityDb := s.activityInstanceMgr.Model(&models.ActivityInstance{})
	var nullableAvgCount sql.NullFloat64

	if param.ViewType == enums.Assignee {
		// 创建子查询：按actor分组，计算每个用户的超时审批数
		subQuery := activityDb.Select("actor, COUNT(DISTINCT process_instance_id) as count").
			Where("status = ? AND type = ?", enums.ActivityStatusPending, enums.ActivityTypeUserTask)

		if param.OvertimeDays > 0 {
			subQuery = subQuery.Where("start_at < DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.OvertimeDays)
		}

		subQuery = subQuery.Group("actor")

		// 使用子查询计算平均值
		err = activityDb.Raw("SELECT AVG(count) FROM (?) as t", subQuery.SubQuery()).
			Row().Scan(&nullableAvgCount)
	}

	if param.ViewType == enums.Starter {
		// 创建子查询：联表查询，按start_by_id分组，计算每个发起人的超时审批数
		subQuery := processDb.Select("process_instance.start_by_id, COUNT(DISTINCT process_instance.id) as count").
			Joins("JOIN activity_instance ON activity_instance.process_instance_id = process_instance.id").
			Where("activity_instance.status = ?", enums.ActivityStatusPending)

		if param.OvertimeDays > 0 {
			subQuery = subQuery.Where("activity_instance.start_at < DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.OvertimeDays)
		}

		subQuery = subQuery.Group("process_instance.start_by_id")

		// 使用子查询计算平均值
		err = processDb.Raw("SELECT AVG(count) FROM (?) as t", subQuery.SubQuery()).
			Row().Scan(&nullableAvgCount)
	}

	// 处理可能为NULL的情况
	if err == nil && nullableAvgCount.Valid {
		avgCount = int(math.Round(nullableAvgCount.Float64))
	} else {
		avgCount = 0
	}

	// 将结果存入Redis缓存，设置过期时间为当天结束（次日0点）
	if err == nil {
		expiration := s.getExpirationToNextDay()
		s.RedisCli.Set(s.RedisCli.Context(), cacheKey, avgCount, expiration)
	}

	return
}

type GlobalAvgCountUrgeParam struct {
	Days int
}

// 统计全部用户审批节点被催次数平均值
func (s *historyService) GlobalAvgCountUrge(param GlobalAvgCountUrgeParam) (avgCount int, err error) {
	// 查询缓存
	cacheKey := fmt.Sprintf("global_avg_count_urge:%d", param.Days)
	val, err := s.RedisCli.Get(s.RedisCli.Context(), cacheKey).Int()
	if err == nil {
		return val, nil
	}

	urgeMgr := mgr.UrgeTaskMgr{Base: *s.Mgr}
	db := urgeMgr.Model(&models.UrgeTask{})
	var nullableAvgCount sql.NullFloat64

	// 创建子查询：按assignee分组，计算每个用户被催的次数
	subQuery := db.Select("assignee, COUNT(*) as count")

	if param.Days > 0 {
		subQuery = subQuery.Where("created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
	}

	subQuery = subQuery.Group("assignee")

	// 使用子查询计算平均值
	err = db.Raw("SELECT AVG(count) FROM (?) as t", subQuery.SubQuery()).
		Row().Scan(&nullableAvgCount)

	// 处理可能为NULL的情况
	if err == nil && nullableAvgCount.Valid {
		avgCount = int(math.Round(nullableAvgCount.Float64))
	} else {
		avgCount = 0
	}

	// 将结果存入Redis缓存，设置过期时间为当天结束（次日0点）
	if err == nil {
		expiration := s.getExpirationToNextDay()
		s.RedisCli.Set(s.RedisCli.Context(), cacheKey, avgCount, expiration)
	}

	return
}

type GlobalAvgCountRejectParam struct {
	Days int
}

// 统计全部用户审批被驳回次数平均值
func (s *historyService) GlobalAvgCountReject(param GlobalAvgCountRejectParam) (avgCount int, err error) {
	// 查询缓存
	cacheKey := fmt.Sprintf("global_avg_count_process_reject:%d", param.Days)
	val, err := s.RedisCli.Get(s.RedisCli.Context(), cacheKey).Int()
	if err == nil {
		return val, nil
	}

	processDb := s.processInstanceMgr.Model(&models.ProcessInstance{})
	var nullableAvgCount sql.NullFloat64

	// 创建子查询：按start_by_id分组，计算每个用户被驳回的次数
	subQuery := processDb.Select("start_by_id, COUNT(*) as count").
		Where("status = ?", enums.ProcessStatusFailed)

	if param.Days > 0 {
		subQuery = subQuery.Where("start_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)", param.Days)
	}

	subQuery = subQuery.Group("start_by_id")

	// 使用子查询计算平均值
	err = processDb.Raw("SELECT AVG(count) FROM (?) as t", subQuery.SubQuery()).
		Row().Scan(&nullableAvgCount)

	// 处理可能为NULL的情况
	if err == nil && nullableAvgCount.Valid {
		avgCount = int(math.Round(nullableAvgCount.Float64))
	} else {
		avgCount = 0
	}

	// 将结果存入Redis缓存，设置过期时间为当天结束（次日0点）
	if err == nil {
		expiration := s.getExpirationToNextDay()
		s.RedisCli.Set(s.RedisCli.Context(), cacheKey, avgCount, expiration)
	}

	return
}
