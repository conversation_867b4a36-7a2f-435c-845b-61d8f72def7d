package system

import (
	"net/http"

	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/util/logger"
)

type systemService struct {
	*services.Base
	logger         logrus.FieldLogger
	actionMgr      mgr.ActionMgr
	serviceMgr     mgr.ServiceMgr
	adminTransport http.RoundTripper
}

// NewSystemService construct a system service
func NewSystemService(base *services.Base, adminTransport http.RoundTripper) Service {
	service := &systemService{
		Base:           base,
		logger:         logrus.WithField(logger.FieldKeyPrefix, "SystemService"),
		actionMgr:      mgr.ActionMgr{Base: *base.Mgr},
		serviceMgr:     mgr.ServiceMgr{Base: *base.Mgr},
		adminTransport: adminTransport,
	}

	initPool(service)
	return service
}

func (s *systemService) ListServices() ([]*models.Service, error) {
	return s.serviceMgr.ListServices()
}

type CreateServiceParam struct {
	Code string         `json:"code"`
	Name string         `json:"name"`
	Host string         `json:"host"`
	Auth enums.AuthType `json:"auth"`
}

func (p CreateServiceParam) Valid() bool {
	if p.Code == "" ||
		p.Name == "" ||
		p.Host == "" ||
		!p.Auth.Valid() {
		return false
	}

	return true
}

func (s *systemService) CreateService(param *CreateServiceParam) (*models.Service, error) {
	data := &models.Service{
		Code: param.Code,
		Name: param.Name,
		Host: param.Host,
		Auth: param.Auth,
	}
	_, err := s.serviceMgr.Save(data)
	if err != nil {
		return nil, err
	}

	actionPoolInst.loadServices()

	return data, nil
}

type UpdateServiceParam struct {
	Name string         `json:"name"`
	Host string         `json:"host"`
	Auth enums.AuthType `json:"auth"`
}

func (p UpdateServiceParam) Valid() bool {
	if p.Name == "" ||
		p.Host == "" ||
		!p.Auth.Valid() {
		return false
	}

	return true
}

func (s *systemService) UpdateService(id uint64, param *UpdateServiceParam) error {
	data := &models.Service{
		Base: models.Base{
			ID: id,
		},
		Name: param.Name,
		Host: param.Host,
		Auth: param.Auth,
	}
	_, err := s.serviceMgr.Save(data)
	if err != nil {
		return err
	}

	actionPoolInst.loadServices()

	return nil
}

func (s *systemService) ListActions() ([]*models.Action, error) {
	return s.actionMgr.ListActions()
}

type QueryActionParam struct {
	Service uint64 `form:"service"`
}

func (s *systemService) QueryActions(param *QueryActionParam) ([]*models.Action, error) {
	if param == nil {
		return s.actionMgr.ListActions()
	}

	return s.actionMgr.FindActions(param.Service)
}

type CreateActionParam struct {
	Code           string         `json:"code"`
	Name           string         `json:"name"`
	Service        uint64         `json:"service,string"`
	UseServiceAuth bool           `json:"use_service_auth"`
	CustomAuth     enums.AuthType `json:"custom_auth"`
	URL            string         `json:"url"`
	Method         string         `json:"method"`
	Header         string         `json:"header"`
	Body           string         `json:"body"`
	StoreKey       string         `json:"store_key"`
	ParseKey       string         `json:"parse_key"`
	ParseFirst     bool           `json:"parse_first"`
	MaxRetry       int            `json:"max_retry"`
}

func (p CreateActionParam) Valid() bool {
	if p.Code == "" ||
		p.Name == "" ||
		p.Service == 0 ||
		p.URL == "" ||
		p.Method == "" ||
		!p.CustomAuth.Valid() {
		return false
	}

	return true
}

func (s *systemService) CreateAction(param *CreateActionParam) (*models.Action, error) {
	data := &models.Action{
		Code:           param.Code,
		Name:           param.Name,
		Service:        param.Service,
		UseServiceAuth: param.UseServiceAuth,
		CustomAuth:     param.CustomAuth,
		URL:            param.URL,
		Method:         param.Method,
		Header:         param.Header,
		Body:           param.Body,
		StoreKey:       param.StoreKey,
		ParseKey:       param.ParseKey,
		ParseFirst:     param.ParseFirst,
		MaxRetry:       param.MaxRetry,
	}
	_, err := s.actionMgr.Save(data)
	if err != nil {
		return nil, err
	}

	actionPoolInst.loadActions()

	return data, nil
}

type UpdateActionParam struct {
	Name           string         `json:"name"`
	Service        uint64         `json:"service,string"`
	UseServiceAuth bool           `json:"use_service_auth"`
	CustomAuth     enums.AuthType `json:"custom_auth"`
	URL            string         `json:"url"`
	Method         string         `json:"method"`
	Header         string         `json:"header"`
	Body           string         `json:"body"`
	StoreKey       string         `json:"store_key"`
	ParseKey       string         `json:"parse_key"`
	ParseFirst     bool           `json:"parse_first"`
	MaxRetry       int            `json:"max_retry"`
}

func (p UpdateActionParam) Valid() bool {
	if p.Name == "" ||
		p.Service == 0 ||
		p.URL == "" ||
		p.Method == "" ||
		!p.CustomAuth.Valid() {
		return false
	}

	return true
}

func (s *systemService) UpdateAction(id uint64, param *UpdateActionParam) error {
	data := &models.Action{
		Base: models.Base{
			ID: id,
		},
		Name:           param.Name,
		Service:        param.Service,
		UseServiceAuth: param.UseServiceAuth,
		CustomAuth:     param.CustomAuth,
		URL:            param.URL,
		Method:         param.Method,
		Header:         param.Header,
		Body:           param.Body,
		StoreKey:       param.StoreKey,
		ParseKey:       param.ParseKey,
		ParseFirst:     param.ParseFirst,
		MaxRetry:       param.MaxRetry,
	}
	_, err := s.actionMgr.Save(data)
	if err != nil {
		return err
	}

	actionPoolInst.loadActions()

	return nil
}
