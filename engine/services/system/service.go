package system

import "qiniu.io/qbpm/engine/models"

// Service defines system service api
type Service interface {
	ListServices() ([]*models.Service, error)
	CreateService(param *CreateServiceParam) (*models.Service, error)
	UpdateService(id uint64, param *UpdateServiceParam) error
	ListActions() ([]*models.Action, error)
	QueryActions(param *QueryActionParam) ([]*models.Action, error)
	CreateAction(param *CreateActionParam) (*models.Action, error)
	UpdateAction(id uint64, param *UpdateActionParam) error
}
