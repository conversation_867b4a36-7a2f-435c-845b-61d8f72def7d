package system

import (
	"encoding/json"
	"net/http"
	"regexp"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/util/exp"
	"qiniu.io/qbpm/engine/util/logger"
	"qiniu.io/qbpm/engine/util/rest"
)

var actionPoolInst *actionPool
var once sync.Once

type actionPool struct {
	sync.RWMutex
	Mgr        *mgr.Base
	actionMap  map[string]Action
	serviceMap map[uint64]models.Service
	authMap    map[enums.AuthType]http.RoundTripper
}

func initPool(s *systemService) {
	once.Do(func() {
		actionPoolInst = &actionPool{
			Mgr:        s.Mgr,
			actionMap:  make(map[string]Action),
			serviceMap: make(map[uint64]models.Service),
			authMap:    make(map[enums.AuthType]http.RoundTripper),
		}
		err := actionPoolInst.initActionAndServices(s)
		if err != nil {
			logrus.WithError(err).Error("init action and service failed")
		}
	})
}

var (
	ErrNotFound = errors.New("not found")
)

type Action models.Action

func (ra *Action) rebuildValues(value string, params map[string]interface{}, log logrus.FieldLogger) string {
	if value == "" {
		return ""
	}

	r, err := regexp.Compile(`\{\{(.*?)\}\}`)
	if err != nil {
		log.WithError(err).Warn("try compile regexp failed")
		return value
	}

	matches := r.FindAllString(value, -1)

	for _, m := range matches {
		key := strings.Replace(m, "{{", "", 1)
		key = strings.Replace(key, "}}", "", 1)

		if key == "" {
			continue
		}

		parsedValue, err := exp.EvalSpread(key, params)
		if err != nil {
			log.WithError(err).Warnf("parse param failed for key [%s]", key)
			continue
		}

		value = strings.Replace(value, m, parsedValue, 1)
	}
	return value
}

func GetActions(codes ...string) ([]*Action, error) {
	actions := make([]*Action, 0)
	for _, code := range codes {
		action, err := GetAction(code)
		if err != nil {
			return nil, err
		}
		actions = append(actions, action)
	}

	return actions, nil
}

func GetAction(code string, stop ...bool) (*Action, error) {
	actionPoolInst.RLock()
	action, ok := actionPoolInst.actionMap[code]
	actionPoolInst.RUnlock()

	if !ok {
		if len(stop) > 0 && stop[0] {
			return nil, errors.Wrapf(ErrNotFound, "action %s not found", code)
		}

		actionPoolInst.loadActions()
		return GetAction(code, true)
	}

	return &action, nil
}

func (ra *Action) getService(stop ...bool) (*models.Service, error) {
	actionPoolInst.RLock()
	service, ok := actionPoolInst.serviceMap[ra.Service]
	actionPoolInst.RUnlock()

	if !ok {
		if len(stop) > 0 && stop[0] {
			return nil, errors.Wrapf(ErrNotFound, "service %d not found", ra.Service)
		}

		actionPoolInst.loadActions()
		return ra.getService(true)
	}

	return &service, nil
}

func (ra *Action) getAuthTransport() (http.RoundTripper, error) {

	var auth enums.AuthType
	if !ra.UseServiceAuth && ra.CustomAuth != "" {
		auth = ra.CustomAuth
	} else {
		service, err := ra.getService()
		if err != nil {
			return nil, err
		}

		auth = service.Auth
	}

	actionPoolInst.RLock()
	defer actionPoolInst.RUnlock()

	if auth != "" {
		if tr, ok := actionPoolInst.authMap[auth]; ok {
			return tr, nil
		}
		return nil, errors.Wrapf(ErrNotFound, "auth type %s not found", auth)
	}

	return nil, nil
}

// Do implements interface to perform actions
func (ra *Action) Do(params string) (interface{}, error) {
	var (
		result    interface{}
		err       error
		paramsObj map[string]interface{}
	)

	err = json.Unmarshal([]byte(params), &paramsObj)
	if err != nil {
		return nil, err
	}

	log := logrus.WithFields(logrus.Fields{
		logger.FieldKeyPrefix: "RestAction",
		"actionKey":           ra.ID,
		"method":              ra.Method,
		"url":                 ra.URL,
	})

	service, err := ra.getService()
	if err != nil {
		return nil, err
	}

	ra.URL = service.Host + ra.URL
	url := ra.rebuildValues(ra.URL, paramsObj, log)
	log = log.WithField("url", url)

	if ra.MaxRetry == 0 {
		ra.MaxRetry = 1
	}

	var authTransport http.RoundTripper
	for index := 0; index < ra.MaxRetry; index++ {
		body := ra.Body
		body = ra.rebuildValues(body, paramsObj, log)
		header := ra.rebuildValues(ra.Header, paramsObj, log)

		authTransport, err = ra.getAuthTransport()
		if err != nil {
			return nil, err
		}

		req := rest.NewRest(url, ra.Method, header, body, authTransport)
		log = log.WithFields(logrus.Fields{
			"header": req.Request.Header,
			"body":   body,
		})

		log.Info("action do")
		result, err = req.Do()
		if err != nil {
			log.WithField("retryOn", index).
				Warnf("restAction err: %s", err.Error())
			continue
		}
		break
	}
	return result, err
}

func (p *actionPool) loadActions() error {
	p.Lock()
	defer p.Unlock()

	actionMgr := mgr.ActionMgr{Base: *p.Mgr}
	actions, err := actionMgr.ListActions()
	if err != nil {
		return err
	}

	for _, action := range actions {
		p.actionMap[action.Code] = Action(*action)
	}

	return nil
}

func (p *actionPool) loadServices() error {
	p.Lock()
	defer p.Unlock()

	serviceMgr := mgr.ServiceMgr{Base: *p.Mgr}
	services, err := serviceMgr.ListServices()
	if err != nil {
		return err
	}

	for _, srv := range services {
		p.serviceMap[srv.ID] = *srv
	}

	return nil
}

func (p *actionPool) initAuths(s *systemService) error {
	p.Lock()
	defer p.Unlock()

	if s.adminTransport != nil {
		p.authMap[enums.AuthTypeQBoxBearer] = s.adminTransport
	}

	return nil
}

func (p *actionPool) initActionAndServices(s *systemService) error {
	if err := p.initAuths(s); err != nil {
		return err
	}

	if err := p.loadServices(); err != nil {
		return err
	}

	if err := p.loadActions(); err != nil {
		return err
	}

	return nil
}
