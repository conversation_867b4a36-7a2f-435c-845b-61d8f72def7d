package mgr_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/test"
)

var (
	machineMgr *mgr.MachineMgr
)

var _ = Describe("machine", func() {

	BeforeEach(func() {
		base = test.Init()
		machineMgr = mgr.NewMachineMgr(*base)
	})

	Describe("machine", func() {
		It("should get a int id", func() {
			id, _ := machineMgr.NodeID("abc")
			Expect(id > 0).To(BeTrue())
		})
	})

})
