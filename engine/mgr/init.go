package mgr

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/util/logger"
)

// DBConfig configuration for db connection
type DBConfig struct {
	Driver      string        `yaml:"driver"`
	Host        string        `yaml:"host"`
	Port        int           `yaml:"port"`
	Username    string        `yaml:"username"`
	Password    string        `yaml:"password"`
	DB          string        `yaml:"db"`
	Charset     string        `yaml:"charset"`
	Collation   string        `yaml:"collation"`
	ParseTime   bool          `yaml:"parse_time"`
	Loc         string        `yaml:"loc"`
	MaxOpenConn int           `yaml:"max_open_conn"`
	MaxIdleConn int           `yaml:"max_idle_conn"`
	MaxLifeTime time.Duration `yaml:"max_life_time"`
}

// DSN Generate data source name
func (c *DBConfig) DSN() string {
	var hostPort string
	if c.Port == 0 {
		// zero value of port is not valid, just use host
		hostPort = c.Host
	} else {
		hostPort = fmt.Sprintf("%s:%d", c.Host, c.Port)
	}
	return fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=%s&collation=%s&parseTime=%t&loc=%s",
		c.Username, c.Password, hostPort, c.DB, c.Charset, c.Collation, c.ParseTime, c.Loc)
}

// InitDB initialize and return current db connection
func InitDB(cfg *DBConfig, debug bool) (base *Base, err error) {
	db, err := gorm.Open(cfg.Driver, cfg.DSN())
	if err != nil {
		return
	}

	base = &Base{db}

	base.SingularTable(true)
	base.DB.DB().SetMaxOpenConns(cfg.MaxOpenConn)
	base.DB.DB().SetMaxIdleConns(cfg.MaxIdleConn)
	base.DB.DB().SetConnMaxLifetime(cfg.MaxLifeTime)
	base.LogMode(debug)
	base.SetLogger(logrus.WithField(logger.FieldKeyPrefix, "Gorm"))

	return
}
