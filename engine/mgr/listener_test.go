package mgr_test

import (
	. "github.com/onsi/ginkgo/v2"

	"fmt"

	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("Listener", func() {

	var (
		listenerMgr *mgr.ListenerMgr
		listener    models.Listener
	)
	BeforeEach(func() {
		base := test.Init()
		listenerMgr = &mgr.ListenerMgr{*base}

		listener = models.Listener{
			ExecutionID: 1,
			ActionKey:   "ActionKey",
			Listener<PERSON><PERSON>: "ListenerKey",
			NodeKey:     "NodeKey",
			Fire:        "begin",
			Status:      models.BeginListener,
		}
	})

	Describe("listener", func() {
		It("Insert and get", func() {
			if err := listenerMgr.Create(&listener).Error; err != nil {
				Fail(fmt.Sprintf("create fail: %s", err.Error()))
			}

			l, err := listenerMgr.Get(&listener)
			if err != nil {
				Fail(fmt.Sprintf("get fail: %s", err.Error()))
			}

			if l.Status != models.BeginListener {
				Fail(fmt.Sprintf("get wrong value: %v", l))
			}

		})
	})

	It("update", func() {
		if err := listenerMgr.Create(&listener).Error; err != nil {
			By(err.Error())
		}
		query := models.Listener{
			ExecutionID: 1,
			ActionKey:   "ActionKey",
			ListenerKey: "ListenerKey",
		}
		l, err := listenerMgr.Get(&query)
		if err != nil {
			By(err.Error())
		}

		l.Status = models.FailListener

		err = listenerMgr.Upsert(l)
		if err != nil {
			Fail(fmt.Sprintf("update err: %s", err.Error()))
		}

		l, _ = listenerMgr.Get(&query)

		if l.Status != models.FailListener {
			Fail("update not work")
		}
	})

})
