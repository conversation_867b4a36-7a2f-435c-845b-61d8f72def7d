package mgr_test

import (
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/test"
)

var (
	runExecutionMgr *mgr.RunExecutionMgr
)

var _ = Describe("runExecution", func() {

	BeforeEach(func() {
		base = test.Init()
		runExecutionMgr = mgr.NewRunExecutionMgr(*base)
	})

	Describe("RunExecution", func() {

		Context("with two record in table", func() {

			var m1 *models.RunExecution
			var m2 *models.RunExecution

			JustBeforeEach(func() {
				// insert test data
				m1 = &models.RunExecution{
					Base:                models.Base{ID: 1},
					ProcessDefinitionID: 1,
					ProcessInstanceID:   1,
					ActivityDefineKey:   "",
					Name:                "",
					Excode:              "",
					Params:              "",
					StartByID:           "",
					StartAt:             time.Now(),
					Status:              enums.RunExecutionStatusRunning,
				}
				m2 = &models.RunExecution{
					Base:                models.Base{ID: 2},
					ProcessDefinitionID: 2,
					ProcessInstanceID:   2,
					ActivityDefineKey:   "",
					Name:                "",
					Excode:              "",
					Params:              "",
					StartByID:           "",
					StartAt:             time.Now(),
					Status:              enums.RunExecutionStatusRunning,
				}

				base.Create(&m1)
				base.Create(&m2)
			})

			It("should update 1 RunExecution model's status", func() {
				runExecutionMgr.UpdateStatus(m1, enums.RunExecutionStatusRunning)

				var finded models.RunExecution
				base.Model(&models.RunExecution{}).First(&finded, m1.ID)

				Expect(finded.Status).To(Equal(enums.RunExecutionStatusRunning))
			})

			It("should delete 1 RunExecution and create 1 RunExecution", func() {
				m3 := &models.RunExecution{
					Base:                models.Base{ID: 3},
					ProcessDefinitionID: 3,
					ProcessInstanceID:   3,
					ActivityDefineKey:   "",
					Name:                "",
					Excode:              "",
					Params:              "",
					StartByID:           "",
					StartAt:             time.Now(),
					Status:              enums.RunExecutionStatusRunning,
				}

				runExecutionMgr.DeleteAndCreate([]*models.RunExecution{m1}, []*models.RunExecution{m3})

				Expect(
					base.
						First(&models.RunExecution{}, m1.ID).
						RecordNotFound(),
				).To(BeTrue())

				Expect(
					base.
						First(&models.RunExecution{}, m3.ID).
						RecordNotFound(),
				).To(BeFalse())
			})

		})

	})

})
