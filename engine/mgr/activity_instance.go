package mgr

import (
	"errors"

	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/util/snowflake"
)

// ActivityInstanceMgr Management of activity instnace
type ActivityInstanceMgr struct {
	Base
}

// Create Create a activity instance
func (mgr *ActivityInstanceMgr) Create(m *models.ActivityInstance) error {
	if m.ID == 0 {
		m.ID = snowflake.Generator.ID()
	}

	return mgr.DB.Create(m).Error
}

// CreateBatch Batch create activity instances
func (mgr *ActivityInstanceMgr) CreateBatch(models []*models.ActivityInstance) error {

	for _, m := range models {
		if m.ID == 0 {
			m.ID = snowflake.Generator.ID()
		}
	}

	return mgr.PerformTransaction(func(db *Base) error {
		for _, m := range models {
			err := db.Create(m).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// Get get activity instance by id or execution id
func (mgr *ActivityInstanceMgr) Get(activity *models.ActivityInstance) (*models.ActivityInstance, error) {
	if activity == nil || (activity.ID == 0 && activity.ExecutionID == 0) {
		return nil, errors.New("ID and ExecutionID is null")
	}

	db := mgr.DB
	if activity.ID != 0 {
		db = db.Where("id=?", activity.ID)
	} else if activity.ExecutionID != 0 {
		db = db.Where("execution_id=?", activity.ExecutionID)
	}

	var ins models.ActivityInstance
	err := db.First(&ins).Error
	if ins.ID == 0 {
		return nil, err
	}
	return &ins, nil
}
