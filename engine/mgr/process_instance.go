package mgr

import (
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/util/snowflake"
)

// ProcessInstanceMgr Management of process instance
type ProcessInstanceMgr struct {
	Base
}

func NewProcessInstanceMgr(base Base) ProcessInstanceMgr {
	return ProcessInstanceMgr{
		Base: base,
	}
}

// Create Create a process instance
func (mgr *ProcessInstanceMgr) Create(m *models.ProcessInstance) error {
	if m.ID == 0 {
		m.ID = snowflake.Generator.ID()
	}

	return mgr.DB.Create(m).Error
}
