package mgr

import (
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/util/snowflake"
)

type ActionMgr struct {
	Base
}

func (mgr *ActionMgr) Save(model *models.Action) (id uint64, err error) {
	var isInsert bool
	if model.ID == 0 {
		model.ID = snowflake.Generator.ID()
		isInsert = true
	}

	if isInsert {
		err = mgr.DB.Model(&models.Action{}).Save(model).Error
	} else {
		err = mgr.DB.Model(&models.Action{}).Updates(model).Error
	}

	if err == nil {
		id = model.ID
	}

	return
}

func (mgr *ActionMgr) ListActions() (records []*models.Action, err error) {
	err = mgr.DB.Find(&records).Order("code asc").Error
	return
}

func (mgr *ActionMgr) FindActions(service uint64) (records []*models.Action, err error) {
	db := mgr.DB.Model(&models.Action{})
	if service > 0 {
		db = db.Where("service = ?", service)
	}

	err = db.Find(&records).Order("code asc").Error
	return
}
