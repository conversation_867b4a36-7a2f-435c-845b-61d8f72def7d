package mgr_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/test"
)

var (
	signalMgr *mgr.SignalMgr
)

var _ = Describe("signal", func() {
	BeforeEach(func() {
		base = test.Init()
		signalMgr = &mgr.SignalMgr{*base}
	})

	Context("mget", func() {
		var twoIds, threeIds []uint64

		JustBeforeEach(func() {
			signal := models.SignalInfo{
				SignalRef: "signal",
				Business:  "business",
			}
			id, err := signalMgr.Create(&signal)
			Expect(err).To(BeNil())
			twoIds = append(twoIds, id)
			threeIds = append(threeIds, id)
			signal.ID = 0

			signal.Business = "*"
			id, err = signalMgr.Create(&signal)
			Expect(err).To(BeNil())
			twoIds = append(twoIds, id)
			threeIds = append(threeIds, id)
			signal.ID = 0

			signal.Business = "other"
			id, err = signalMgr.Create(&signal)
			Expect(err).To(BeNil())
			threeIds = append(threeIds, id)
			signal.ID = 0
		})

		It("mget", func() {
			signals, err := signalMgr.MgetSignal("signal", "business")
			Expect(err).To(BeNil())
			Expect(len(signals)).To(Equal(len(twoIds)))
			for _, signal := range signals {
				Expect(twoIds).To(ContainElement(signal.ID))
			}

			signals, err = signalMgr.MgetSignal("signal", "*")
			Expect(err).To(BeNil())
			Expect(len(signals)).To(Equal(len(threeIds)))
			for _, signal := range signals {
				Expect(threeIds).To(ContainElement(signal.ID))
			}
		})
	})
})
