package mgr

import (
	"errors"
	"fmt"

	"github.com/go-sql-driver/mysql"
	"qiniu.io/qbpm/engine/models"
)

// ListenerMgr DML for listener model
type ListenerMgr struct {
	Base
}

// Upsert update or insert model by primary key(execution_id、listener_key、action_key)
func (mgr *ListenerMgr) Upsert(m *models.Listener) error {
	if err := mgr.checkID(m); err != nil {
		return err
	}

	var mysqlErr *mysql.MySQLError
	if err := mgr.Create(m).Error; err != nil {
		if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 {
			return mgr.Model(&models.Listener{}).Updates(m).Error
		}

		return err
	}

	return nil
}

// Get get listener by primary key(execution_id、listener_key、action_key)
func (mgr *ListenerMgr) Get(m *models.Listener) (*models.Listener, error) {
	if err := mgr.checkID(m); err != nil {
		return nil, err
	}

	var listener models.Listener
	err := mgr.Where("execution_id= ? AND action_key= ? AND listener_key = ?",
		m.ExecutionID, m.<PERSON>ey, m.ListenerKey).First(&listener).Error
	if listener.ExecutionID == 0 {
		return nil, err
	}
	return &listener, nil
}

func (mgr *ListenerMgr) checkID(m *models.Listener) error {
	if m.ExecutionID == 0 || m.ActionKey == "" || m.ListenerKey == "" {
		return fmt.Errorf("listener%+v primary key is empty", m)
	}
	return nil
}

// ListByProcessInstance list listener by process instance id
func (mgr *ListenerMgr) ListByProcessInstance(id uint64) ([]*models.Listener, error) {
	var listeners []*models.Listener
	err := mgr.Where("process_instance_id = ?", id).Order("created_at desc").Find(&listeners).Error
	if err != nil {
		return nil, err
	}

	return listeners, nil
}

func (mgr *ListenerMgr) UpdateListener(m *models.Listener) error {
	if err := mgr.checkID(m); err != nil {
		return err
	}

	return mgr.Model(&models.Listener{}).Updates(m).Error
}
