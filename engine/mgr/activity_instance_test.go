package mgr_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"fmt"

	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/test"
	"qiniu.io/qbpm/engine/util/snowflake"
)

var _ = Describe("ActivityInstance", func() {
	var (
		base                *mgr.Base
		activityInstanceMgr mgr.ActivityInstanceMgr
	)

	BeforeEach(func() {
		base = test.Init()
		activityInstanceMgr = mgr.ActivityInstanceMgr{*base}
	})

	Describe("ActivityInstance", func() {

		Context("with process definition and instance in table", func() {

			var processDef *models.ProcessDefinition
			var instance *models.ProcessInstance
			var execution *models.RunExecution

			JustBeforeEach(func() {
				processDef = models.NewProcessDefinition()
				processDef.Name = "testProcessDef1"
				processDef.Key = processDef.Name
				base.Create(processDef)

				instance = &models.ProcessInstance{
					ProcessDefinitionID: processDef.ID,
					Name:                "test",
					Excode:              "test",
				}
				base.Create(instance)

				execution = &models.RunExecution{
					Base: models.Base{ID: 123},
				}
				base.Create(execution)
			})

			It("should create 1 ActivityInstance model", func() {
				activityInstance := &models.ActivityInstance{
					ExecutionID:       execution.ID,
					ProcessInstanceID: instance.ID,
					ActivityDefineKey: "test",
				}

				err := activityInstanceMgr.Create(activityInstance)
				Expect(err).To(BeNil())
				Expect(activityInstance.Status).To(Equal(enums.ActivityStatusPending))
			})

			It("should create 1 ActivityInstance model with specific ID", func() {
				id := snowflake.Generator.ID()
				activityInstance := &models.ActivityInstance{
					ExecutionID:       execution.ID,
					ProcessInstanceID: instance.ID,
					ActivityDefineKey: "test",
					Base:              models.Base{ID: id},
				}

				err := activityInstanceMgr.Create(activityInstance)
				Expect(err).To(BeNil())
				Expect(activityInstance.Status).To(Equal(enums.ActivityStatusPending))
				Expect(activityInstance.ID).To(Equal(id))
			})

			It("Get method", func() {
				activity := &models.ActivityInstance{
					ExecutionID:       100,
					ProcessInstanceID: 100,
				}

				activityInstanceMgr.Create(activity)
				temp := models.ActivityInstance{ExecutionID: activity.ExecutionID}
				temp.ID = activity.ID
				ins, _ := activityInstanceMgr.Get(&temp)
				if ins == nil || ins.ID != activity.ID {
					Fail(fmt.Sprintf("get fail: %v", ins))
				}
			})
		})
	})
})
