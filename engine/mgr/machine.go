package mgr

import "qiniu.io/qbpm/engine/models"

// NewMachineMgr instances a MachineMgr
func NewMachineMgr(base Base) *MachineMgr {
	return &MachineMgr{Base: base}
}

// MachineMgr wraps mgr.Base to handle machine instances
type MachineMgr struct {
	Base
}

// NodeID get node instance id using machine id
func (m *MachineMgr) NodeID(machineID string) (id uint, err error) {
	var machine models.Machine
	if err = m.Where(models.Machine{MachineID: machineID}).FirstOrCreate(&machine).Error; err == nil {
		id = machine.ID
	}
	return
}
