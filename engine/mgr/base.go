package mgr

import (
	"bytes"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
	"qiniu.io/qbpm/engine/models"
)

// Base Base for mgr
type Base struct {
	*gorm.DB
}

// Automigrate Auto migrate tables
func (base *Base) Automigrate() {
	base.AutoMigrate(&models.RunExecution{},
		&models.Machine{},
		&models.ProcessDefinition{},
		&models.ProcessInstance{},
		&models.ActivityInstance{},
		&models.Service{},
		&models.Action{},
		&models.Listener{},
		&models.UrgeTask{},
		&models.SignalInfo{},
		&models.SignalExecutionRelation{},
	)
}

// PerformTransaction Perform transactions
func (base *Base) PerformTransaction(fn func(*Base) error) error {
	db := base.DB.Begin()

	err := fn(&Base{db})
	if err != nil {
		db.Rollback()
		return err
	}

	db.Commit()
	return nil
}

// Fields Get fields of specific table with prefix if provided
func (base *Base) Fields(table interface{}, prefix ...string) (fields string) {
	var (
		buffer    bytes.Buffer
		hasPrefix = len(prefix) > 0
	)

	db := base.Model(table)
	scope := db.NewScope(db.Value)

	for _, f := range scope.Fields() {
		if f.IsIgnored {
			continue
		}

		if hasPrefix {
			buffer.WriteString(fmt.Sprintf("%s.%s,", prefix[0], f.DBName))
		} else {
			buffer.WriteString(fmt.Sprintf("%s,", f.DBName))
		}
	}

	fields = strings.TrimSuffix(buffer.String(), ",")
	return
}

// DefaultPageSize Default page size if not specified
const DefaultPageSize = 200

// MaxPageSize max page size
const MaxPageSize = 1000

// Paging paging for list query
func Paging(db *gorm.DB, page, pageSize int) *gorm.DB {
	if db == nil {
		return nil
	}

	if pageSize <= 0 {
		pageSize = DefaultPageSize
	} else if pageSize > MaxPageSize {
		pageSize = MaxPageSize
	}

	return db.Offset(page * pageSize).Limit(pageSize)
}
