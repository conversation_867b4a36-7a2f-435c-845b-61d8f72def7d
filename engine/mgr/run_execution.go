package mgr

import (
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
)

// NewRunExecutionMgr instances a RunExecutionMgr
func NewRunExecutionMgr(base Base) *RunExecutionMgr {
	return &RunExecutionMgr{Base: base}
}

// RunExecutionMgr wraps mgr.Base to handle RunExecution instances
type RunExecutionMgr struct {
	Base
}

// ListExecutionsByExcodes list executions by given excodes
func (d *RunExecutionMgr) ListExecutionsByExcodes(excodes []string) ([]models.RunExecution, error) {
	result := make([]models.RunExecution, 0)
	err := d.Model(models.RunExecution{}).Where("excode in (?)", excodes).Find(&result).Error
	return result, err
}

// UpdateStatus update execution status according to execution_id
func (d *RunExecutionMgr) UpdateStatus(runExecution *models.RunExecution, status enums.RunExecutionStatus) error {
	return d.Model(runExecution).Update("status", status).Error
}

// UpdateByBatch update executions' values by specified execution ids
func (d *RunExecutionMgr) UpdateByBatch(runExecutions []models.RunExecution, newExecution models.RunExecution) error {
	executionIDs := make([]uint64, 0)
	for _, execution := range runExecutions {
		executionIDs = append(executionIDs, execution.ID)
	}
	return d.Model(&models.RunExecution{}).Where("id in (?)", executionIDs).Updates(newExecution).Error
}

// UpdateParams update execution params according to execution_id
func (d *RunExecutionMgr) UpdateParams(runExecution *models.RunExecution, params string) error {
	return d.Model(runExecution).Update("params", params).Error
}

// DeleteAndCreate create a new execution after delete last nodes
func (d *RunExecutionMgr) DeleteAndCreate(deleteRunExecutions []*models.RunExecution, createRunExecutions []*models.RunExecution) (err error) {

	var deletes []uint64
	for _, exec := range deleteRunExecutions {
		deletes = append(deletes, exec.ID)
	}

	tx := d.Begin()

	if err = tx.Where("id in (?)", deletes).Delete(&models.RunExecution{}).Error; err != nil {
		tx.Rollback()
		return
	}

	for _, runExecution := range createRunExecutions {
		if err = tx.Create(runExecution).Error; err != nil {
			tx.Rollback()
			return
		}
	}

	tx.Commit()
	return
}
