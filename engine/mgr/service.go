package mgr

import (
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/util/snowflake"
)

type ServiceMgr struct {
	Base
}

func (mgr *ServiceMgr) Save(model *models.Service) (id uint64, err error) {
	var isInsert bool
	if model.ID == 0 {
		model.ID = snowflake.Generator.ID()
		isInsert = true
	}

	if isInsert {
		err = mgr.DB.Model(&models.Service{}).Save(model).Error
	} else {
		err = mgr.DB.Model(&models.Service{}).Updates(model).Error
	}

	if err == nil {
		id = model.ID
	}

	return
}

func (mgr *ServiceMgr) ListServices() (records []*models.Service, err error) {
	err = mgr.DB.Find(&records).Order("id desc").Error
	return
}
