package mgr

import (
	"errors"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/util/snowflake"
)

type SignalMgr struct {
	Base
}

func (mgr *SignalMgr) Create(model *models.SignalInfo) (signalId uint64, err error) {
	if model.ID == 0 {
		model.ID = snowflake.Generator.ID()
	}

	err = mgr.DB.Create(model).Error
	if err == nil {
		signalId = model.ID
	}
	return
}

func (mgr *SignalMgr) MgetSignal(signalRef string, business string) (signals []*models.SignalInfo, err error) {
	if signalRef == "" {
		return nil, errors.New("signalRef cant not empty")
	}

	var midSignals []*models.SignalInfo
	if err = mgr.DB.Where("signal_ref = ?", signalRef).Find(&midSignals).Error; err != nil {
		return
	}

	for _, signal := range midSignals {
		if business == "*" || signal.Business == "*" || signal.Business == business {
			signals = append(signals, signal)
		}
	}

	return
}
