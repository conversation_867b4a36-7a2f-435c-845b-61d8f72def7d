package mgr_test

import (
	"time"

	. "github.com/onsi/ginkgo/v2"

	. "github.com/onsi/gomega"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/test"
)

var (
	relationMgr  *mgr.SignalExecutionMgr
	executionMgr *mgr.RunExecutionMgr
)

var _ = Describe("SignalExecutionMgr", func() {
	BeforeEach(func() {
		base = test.Init()
		relationMgr = &mgr.SignalExecutionMgr{*base}
		executionMgr = mgr.NewRunExecutionMgr(*base)
	})

	Context("Create", func() {
		var relation *models.SignalExecutionRelation

		JustBeforeEach(func() {
			relation = &models.SignalExecutionRelation{
				SignalRef:         "signalRef",
				Business:          "test",
				ExecutionId:       1,
				ProcessInstanceID: 1,
				ActivityDefineKey: "activity_key",
				SignalDefineKey:   "signal_define_key",
			}
		})

		It("create, GetWithExecutionId, ConsumeExecution", func() {
			id, err := relationMgr.Create(relation)
			relation.ID = id
			Expect(err).To(BeNil())

			_, err = relationMgr.GetWithExecutionId(relation.ExecutionId)
			Expect(err).To(BeNil())

			err = relationMgr.ConsumeExecution(relation.ExecutionId)
			Expect(err).To(BeNil())

			temp, err := relationMgr.GetWithExecutionId(relation.ExecutionId)
			Expect(err).To(BeNil())
			Expect(temp.Completed).To(Equal(true))
		})
	})

	Context("mget", func() {
		var (
			signalRef, business string
			twoIds              []uint64
			threeIds            []uint64
		)

		JustBeforeEach(func() {
			signalRef = "signalRef"
			business = "test"
			relation := &models.SignalExecutionRelation{
				SignalRef:         signalRef,
				Business:          business,
				ExecutionId:       1,
				ProcessInstanceID: 1,
				ActivityDefineKey: "activity_key",
				SignalDefineKey:   "signal_define_key",
			}

			execution := &models.RunExecution{
				Base:                models.Base{ID: relation.ExecutionId},
				ProcessDefinitionID: 1,
				ProcessInstanceID:   1,
				ActivityDefineKey:   "",
				Name:                "",
				Excode:              "",
				Params:              "",
				StartByID:           "",
				StartAt:             time.Now(),
				Status:              enums.RunExecutionStatusRunning,
			}

			_, err := relationMgr.Create(relation)
			Expect(err).To(BeNil())
			executionMgr.Create(&execution)
			twoIds = append(twoIds, relation.ExecutionId)
			threeIds = append(threeIds, relation.ExecutionId)
			relation.ID = 0

			relation.ExecutionId = 2
			relation.Business = "*"
			_, err = relationMgr.Create(relation)
			Expect(err).To(BeNil())
			twoIds = append(twoIds, relation.ExecutionId)
			threeIds = append(threeIds, relation.ExecutionId)
			execution.ID = relation.ExecutionId
			executionMgr.Create(&execution)
			relation.ID = 0

			relation.ExecutionId = 3
			relation.Completed = true
			_, err = relationMgr.Create(relation)
			Expect(err).To(BeNil())
			execution.ID = relation.ExecutionId
			executionMgr.Create(&execution)
			relation.ID = 0

			relation.ExecutionId = 4
			relation.Completed = false
			relation.Business = "business"
			_, err = relationMgr.Create(relation)
			threeIds = append(threeIds, relation.ExecutionId)
			Expect(err).To(BeNil())
			execution.ID = relation.ExecutionId
			executionMgr.Create(&execution)
		})

		It("mget", func() {
			executions, err := relationMgr.MgetExecution(signalRef, business)
			Expect(err).To(BeNil())
			Expect(len(executions)).To(Equal(len(twoIds)))
			for _, execution := range executions {
				Expect(twoIds).To(ContainElement(execution.ID))
			}

			executions, err = relationMgr.MgetExecution(signalRef, "*")
			Expect(err).To(BeNil())
			Expect(len(executions)).To(Equal(len(threeIds)))
			for _, execution := range executions {
				Expect(threeIds).To(ContainElement(execution.ID))
			}
		})
	})
})
