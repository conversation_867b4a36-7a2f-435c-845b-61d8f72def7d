package mgr_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/test"
	"qiniu.io/qbpm/engine/util/snowflake"
)

var _ = Describe("ProcessInstance", func() {
	var (
		base               *mgr.Base
		processInstanceMgr mgr.ProcessInstanceMgr
	)

	BeforeEach(func() {
		base = test.Init()
		processInstanceMgr = mgr.ProcessInstanceMgr{*base}
	})

	Describe("ProcessInstance", func() {

		Context("with process definition and instance in table", func() {

			var processDef *models.ProcessDefinition
			var instance *models.ProcessInstance

			JustBeforeEach(func() {
				processDef = models.NewProcessDefinition()
				processDef.Name = "testProcessDef1"
				processDef.Key = processDef.Name
				base.Create(processDef)

				instance = &models.ProcessInstance{
					ProcessDefinitionID: processDef.ID,
					Name:                "test",
					Excode:              "test",
				}

				base.Create(instance)
			})

			It("should create 1 ProcessInstance model", func() {
				processInstance := &models.ProcessInstance{
					ProcessDefinitionID: processDef.ID,
					Name:                "test",
					Excode:              "test",
				}

				err := processInstanceMgr.Create(processInstance)
				Expect(err).To(BeNil())
				Expect(processInstance.Status).To(Equal(enums.ProcessStatusPending))
			})

			It("should create 1 ProcessInstance model with specific ID", func() {
				id := snowflake.Generator.ID()
				processInstance := &models.ProcessInstance{
					ProcessDefinitionID: processDef.ID,
					Name:                "test",
					Excode:              "test",
					Base:                models.Base{ID: id},
				}

				err := processInstanceMgr.Create(processInstance)
				Expect(err).To(BeNil())
				Expect(processInstance.Status).To(Equal(enums.ProcessStatusPending))
				Expect(processInstance.ID).To(Equal(id))
			})

			It("should return duplicate record error", func() {
				processInstance := &models.ProcessInstance{
					ProcessDefinitionID: processDef.ID,
					Name:                "test",
					Excode:              "test",
					Base:                models.Base{ID: instance.ID},
				}

				err := processInstanceMgr.Create(processInstance)
				Expect(err).ToNot(BeNil())
			})
		})
	})
})
