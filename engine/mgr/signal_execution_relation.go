package mgr

import (
	"errors"
)

import (
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/util/snowflake"
)

type SignalExecutionMgr struct {
	Base
}

func (mgr *SignalExecutionMgr) Create(model *models.SignalExecutionRelation) (relationId uint64, err error) {
	if model.ID == 0 {
		model.ID = snowflake.Generator.ID()
		relationId = model.ID
	}

	err = mgr.DB.Create(model).Error
	return
}

func (mgr *SignalExecutionMgr) GetWithExecutionId(
	executionId uint64,
) (relation models.SignalExecutionRelation, err error) {
	err = mgr.DB.Where("execution_id = ?", executionId).First(&relation).Error
	return
}

func (mgr *SignalExecutionMgr) MgetExecution(
	signalRef string,
	business string,
) (executions []*models.RunExecution, err error) {
	if signalRef == "" {
		return nil, errors.New("signalRef cant not empty")
	}

	var (
		relations    []*models.SignalExecutionRelation
		executionIds []uint64
	)
	err = mgr.DB.Where("signal_ref = ? and completed = false", signalRef).Find(&relations).Error
	if err != nil {
		return
	}

	for _, relation := range relations {
		if business == "*" || relation.Business == business || relation.Business == "*" {
			executionIds = append(executionIds, relation.ExecutionId)
		}
	}

	if len(executionIds) == 0 {
		return
	}

	err = mgr.DB.Where("id in (?)", executionIds).Find(&executions).Error
	return
}

func (mgr *SignalExecutionMgr) ConsumeExecution(executionId uint64) (err error) {
	err = mgr.DB.Model(&models.SignalExecutionRelation{}).Where("execution_id = ?", executionId).
		Update("completed", true).Error
	return
}

func (mgr *SignalExecutionMgr) MgetSiblingRelation(
	activityKey string,
	processInstanceID uint64,
) (relations []models.SignalExecutionRelation, err error) {
	if activityKey == "" || processInstanceID == 0 {
		err = errors.New("activityKey, processInstanceID can't be empty")
		return
	}

	err = mgr.DB.Where("activity_define_key = ? && process_instance_id = ? && completed = false", activityKey, processInstanceID).Find(&relations).Error
	return
}
