package mgr_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/test"
)

var (
	processDefMgr mgr.ProcessDefinitionMgr
)

var _ = Describe("processDefinition", func() {

	BeforeEach(func() {
		base = test.Init()
		processDefMgr = mgr.ProcessDefinitionMgr{*base}
	})

	Describe("ProcessDefiniton", func() {

		Context("with two record in table", func() {

			var m1 *models.ProcessDefinition
			var m2 *models.ProcessDefinition

			JustBeforeEach(func() {
				m1 = models.NewProcessDefinition()
				m2 = models.NewProcessDefinition()

				m1.Name = "testProcessDef1"
				m1.Key = m1.Name
				m2.Name = "testProcessDef2"
				m2.Key = m2.Name

				base.Create(&m1)
				base.Create(&m2)
			})

			It("should create 2 ProcessDefinition models with diff versions", func() {
				def := models.NewProcessDefinition()
				def.Key = "testCreate"
				def.Name = def.Key
				processDefMgr.Create(def)

				result, err := processDefMgr.GetProcessByID(def.ID)
				Expect(err).To(BeNil())
				Expect(result.Status).To(Equal(enums.ProcessDefStatusNew))
				Expect(result.Version).To(Equal(1))

				def2 := models.NewProcessDefinition()
				def2.Key = def.Key
				def2.Name = def.Key
				processDefMgr.Create(def2)

				result, err = processDefMgr.GetProcessByID(def2.ID)
				Expect(err).To(BeNil())
				Expect(result.Status).To(Equal(enums.ProcessDefStatusNew))
				Expect(result.Version).To(Equal(2))
			})

			It("should suspend 2 ProcessDefinition model", func() {
				err := processDefMgr.SuspendProcessByID(m1.ID)
				Expect(err).To(BeNil())
				result, err := processDefMgr.GetProcessByID(m1.ID)
				Expect(err).To(BeNil())
				Expect(result.Status).To(Equal(enums.ProcessDefStatusSuspended))

				err = processDefMgr.SuspendProcessByKey("testProcessDef2")
				Expect(err).To(BeNil())
				result, err = processDefMgr.GetProcessByID(m2.ID)
				Expect(err).To(BeNil())
				Expect(result.Status).To(Equal(enums.ProcessDefStatusSuspended))
			})

			It("should activate 2 ProcessDefinition model", func() {
				err := processDefMgr.ActivateProcessByID(m1.ID)
				Expect(err).To(BeNil())
				result, err := processDefMgr.GetProcessByID(m1.ID)
				Expect(err).To(BeNil())
				Expect(result.Status).To(Equal(enums.ProcessDefStatusActive))

				err = processDefMgr.ActivateProcessByKey("testProcessDef2")
				Expect(err).To(BeNil())
				result, err = processDefMgr.GetProcessByID(m2.ID)
				Expect(err).To(BeNil())
				Expect(result.Status).To(Equal(enums.ProcessDefStatusActive))
			})

		})

	})

})
