package mgr

import (
	"github.com/jinzhu/gorm"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/util/snowflake"
)

// ProcessDefinitionMgr Model management of process definition
type ProcessDefinitionMgr struct {
	Base
}

// Create Create a process
func (mgr *ProcessDefinitionMgr) Create(model *models.ProcessDefinition) (err error) {
	if model.ID == 0 {
		model.ID = snowflake.Generator.ID()
	}

	exist, err := mgr.GetProcessByKey(model.Key)
	if err != nil && err != gorm.ErrRecordNotFound {
		return
	}

	if err != gorm.ErrRecordNotFound {
		model.Version = exist.Version + 1
	}

	return mgr.DB.Create(model).Error
}

// List List processes
func (mgr *ProcessDefinitionMgr) List() (models []models.ProcessDefinition, err error) {
	err = mgr.Find(&models).Error
	return
}

// GetProcessByKey Get a process by key and select latest one
func (mgr *ProcessDefinitionMgr) GetProcessByKey(key string) (model models.ProcessDefinition, err error) {
	err = mgr.Where(models.ProcessDefinition{Key: key}).Order("version desc").First(&model).Error
	return
}

// GetProcessByID Get process by id
func (mgr *ProcessDefinitionMgr) GetProcessByID(id uint64) (model models.ProcessDefinition, err error) {
	err = mgr.First(&model, id).Error
	return
}

func (mgr *ProcessDefinitionMgr) SearchProcessByName(name string, offset int, limit int) ([]models.ProcessDefinition, error) {
	type nameLatestVersion struct {
		Key           string
		LatestVersion int64
	}
	var nameLatestVersionSet []nameLatestVersion
	// 查找每个 name 对应的最新的 version
	err := mgr.Model(models.ProcessDefinition{}).
		Select("`key`, max(version) as latest_version").
		Group("`key`").
		Where("name like ?", "%"+name+"%").
		Offset(offset).
		Limit(limit).
		Scan(&nameLatestVersionSet).Error
	if err != nil {
		return nil, err
	}
	// 查询每个 name 最新的 version 的记录 process
	result := make([]models.ProcessDefinition, 0)
	if len(nameLatestVersionSet) == 0 {
		return result, nil
	}
	// 转成二维数组
	var nv [][]interface{}
	for _, nlv := range nameLatestVersionSet {
		temp := []interface{}{nlv.Key, nlv.LatestVersion}
		nv = append(nv, temp)
	}
	err = mgr.Model(models.ProcessDefinition{}).
		Where("(`key`, version) IN (?)", nv).
		Order("name").Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (mgr *ProcessDefinitionMgr) CountProcessByName(name string) (int64, error) {
	var count int64
	err := mgr.Model(models.ProcessDefinition{}).
		Where("name LIKE ?", "%"+name+"%").
		Group("`key`").
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// SuspendProcessByID Get process by id
func (mgr *ProcessDefinitionMgr) SuspendProcessByID(id uint64) (err error) {
	err = mgr.Model(models.ProcessDefinition{}).Where("id = ?", id).Update(models.ProcessDefinition{Status: enums.ProcessDefStatusSuspended}).Error
	return
}

// SuspendProcessByKey Get process by key
func (mgr *ProcessDefinitionMgr) SuspendProcessByKey(key string) (err error) {
	err = mgr.Model(models.ProcessDefinition{}).Where(models.ProcessDefinition{Key: key}).Update(models.ProcessDefinition{Status: enums.ProcessDefStatusSuspended}).Error
	return
}

// ActivateProcessByID Get process by id
func (mgr *ProcessDefinitionMgr) ActivateProcessByID(id uint64) (err error) {
	err = mgr.Model(models.ProcessDefinition{}).Where("id = ?", id).Update(models.ProcessDefinition{Status: enums.ProcessDefStatusActive}).Error
	return
}

// ActivateProcessByKey Get process by key
func (mgr *ProcessDefinitionMgr) ActivateProcessByKey(key string) (err error) {
	err = mgr.Model(models.ProcessDefinition{}).
		Where(models.ProcessDefinition{Key: key}).
		Order("version desc").
		Limit(1).
		Update(models.ProcessDefinition{Status: enums.ProcessDefStatusActive}).
		Error
	return
}
