package mgr_test

import (
	"strings"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("Base", func() {
	var (
		base *mgr.Base
	)

	BeforeEach(func() {
		base = test.Init()
	})

	Describe("Base", func() {

		Context("with 1 process definition in table", func() {

			var processDef *models.ProcessDefinition

			JustBeforeEach(func() {
				processDef = models.NewProcessDefinition()
				processDef.Name = "testProcessDef"
				processDef.Key = processDef.Name
				base.Create(processDef)
			})

			It("should create 2 ProcessInstance model in transaction", func() {
				processDef1 := models.NewProcessDefinition()
				processDef1.Name = "testProcessDef1"
				processDef1.Key = processDef1.Name

				processDef2 := models.NewProcessDefinition()
				processDef2.Name = "testProcessDef2"
				processDef2.Key = processDef2.Name

				err := base.PerformTransaction(func(db *mgr.Base) error {
					err := db.Create(processDef1).Error
					if err != nil {
						return err
					}
					return db.Create(processDef2).Error
				})

				Expect(err).To(BeNil())

				var cnt int
				err = base.Model(&models.ProcessDefinition{}).Count(&cnt).Error
				Expect(err).To(BeNil())
				Expect(cnt).To(Equal(3))
			})

			It("should create 0 ProcessInstance model in transaction", func() {
				processDef1 := models.NewProcessDefinition()
				processDef1.Name = "testProcessDef1"
				processDef1.Key = processDef1.Name

				processDef2 := models.NewProcessDefinition()
				processDef2.Name = "testProcessDef2"
				processDef2.Key = processDef2.Name
				processDef2.ID = processDef.ID

				err := base.PerformTransaction(func(db *mgr.Base) error {
					err := db.Create(processDef1).Error
					if err != nil {
						return err
					}
					return db.Create(processDef2).Error
				})

				Expect(err).ToNot(BeNil())

				var cnt int
				err = base.Model(&models.ProcessDefinition{}).Count(&cnt).Error
				Expect(err).To(BeNil())
				Expect(cnt).To(Equal(1))
			})
		})
	})

	Describe("Base", func() {
		It("should list 2 fields ", func() {
			Expect(len(strings.Split(base.Fields(models.Machine{}), ","))).To(Equal(2))
		})

		It("should list fields with prefix", func() {
			Expect(strings.Split(base.Fields(models.Machine{}, "mgr"), ",")[0]).To(Equal("mgr.id"))
		})

		It("should list 12 fields ", func() {
			Expect(len(strings.Split(base.Fields(models.ProcessInstance{}), ","))).To(Equal(13))
		})

		It("should list 10 fields ", func() {
			fields := base.Fields(models.ProcessDefinition{})
			Expect(len(strings.Split(fields, ","))).To(Equal(10))
			Expect(strings.Contains(fields, "id")).To(Equal(true))
		})
	})
})
