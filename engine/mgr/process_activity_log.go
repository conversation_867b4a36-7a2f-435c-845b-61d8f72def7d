package mgr

import (
	"time"

	"qiniu.io/qbpm/engine/models"
)

type ProcessActivityLogMgr struct {
	Base
}

func (mgr *ProcessActivityLogMgr) Create(model *models.ProcessActivityLog) (err error) {
	if model.CreatedAt.IsZero() {
		model.CreatedAt = time.Now()
	}

	return mgr.DB.Create(model).Error
}

// CreateWithMentions 同时创建活动日志及其提及记录
func (mgr *ProcessActivityLogMgr) CreateWithMentions(log *models.ProcessActivityLog, mentions []*models.ProcessActivityLogMention) error {
	// 开始事务
	tx := mgr.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 设置创建时间
	if log.CreatedAt.IsZero() {
		log.CreatedAt = time.Now()
	}

	// 创建主表记录
	if err := tx.Create(log).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 为每个提及记录设置日志ID
	for _, mention := range mentions {
		mention.ActivityID = uint64(log.ID) // 使用数据库自动生成的ID
		if err := tx.Create(mention).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	return tx.Commit().Error
}

type ProcessActivityLogMentionMgr struct {
	Base
}

func (mgr *ProcessActivityLogMentionMgr) Create(model *models.ProcessActivityLogMention) (err error) {
	return mgr.DB.Create(model).Error
}
