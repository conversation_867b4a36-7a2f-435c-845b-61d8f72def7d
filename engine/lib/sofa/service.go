package sofa

import (
	"fmt"
	"net/http"

	"github.com/qbox/pay-sdk/base/client"
)

type SofaService interface {
	GetAccountsByUids(uids []uint64) ([]Account, error)
}

type sofaService struct {
	Host string
	client.Client
}

func NewSofaService(host string, cli *http.Client) SofaService {
	return &sofaService{
		Host:   host,
		Client: client.Client{Client: cli},
	}
}

func (s *sofaService) GetAccountsByUids(uids []uint64) ([]Account, error) {
	url := fmt.Sprintf("%s/api/v1/admin/developers/accounts/query", s.Host)

	ret := sofaResponse[[]Account]{}

	err := s.Client.CallWithJson(&ret, url, map[string][]uint64{"uids": uids})
	if err != nil {
		return nil, err
	}

	if ret.Code != 0 {
		return nil, fmt.Errorf("code: %d, message: %s", ret.Code, ret.Message)
	}

	return ret.Data, nil
}
