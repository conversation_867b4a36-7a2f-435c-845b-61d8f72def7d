package gaea

import "gopkg.in/mgo.v2/bson"

type gaeaResponse[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    T      `json:"data"`
}

type AdminUser struct {
	ID        bson.ObjectId `json:"id,omitempty"`
	Name      string        `json:"name,omitempty"`
	Email     string        `json:"email,omitempty"`
	Mobile    string        `json:"mobile,omitempty"`
	SfSalesId string        `json:"sf_sales_id,omitempty"`
	Status    byte          `json:"status,omitempty"`
	Delete    byte          `json:"delete,omitempty"`
	CnName    string        `json:"cnname,omitempty"`
}

type DeveloperOverview struct {
	UID          uint32 `json:"uid"`
	Email        string `json:"email"`
	FullName     string `json:"fullname"`
	IsEnterprise bool   `json:"is_enterprise"`
	IsCertified  bool   `json:"is_certified"`
	IsInternal   bool   `json:"is_internal"`
}

type UserLevel struct {
	Level string `json:"level"`
	Uid   uint32 `json:"uid"`
}
