package gaea

import (
	"fmt"
	"net/http"

	"github.com/qbox/pay-sdk/base/client"
)

type GaeaAdminService interface {
	ListUsersOfLabel(label string) ([]AdminUser, error)
	GetDeveloperOverview(uid uint64) (*DeveloperOverview, error)
	ListUserByNames(names []string) ([]AdminUser, error)
	GetLevelsByUids(uids []uint64) ([]UserLevel, error)
	ListUserByEmails(emails []string) ([]AdminUser, error)
}

type gaeaAdminService struct {
	Host string
	client.Client
}

func NewGaeaAdminService(host string, cli *http.Client) GaeaAdminService {
	return &gaeaAdminService{
		Host:   host,
		Client: client.Client{Client: cli},
	}
}

func (s *gaeaAdminService) ListUsersOfLabel(label string) ([]AdminUser, error) {
	url := fmt.Sprintf("%s/api/admin/labels/%s/users", s.Host, label)

	ret := gaeaResponse[[]AdminUser]{}

	err := s.Client.GetCall(&ret, url)
	if err != nil {
		return nil, err
	}

	if ret.Code != http.StatusOK {
		return nil, fmt.Errorf("code: %d, message: %s", ret.Code, ret.Message)
	}

	return ret.Data, nil
}

func (s *gaeaAdminService) GetDeveloperOverview(uid uint64) (*DeveloperOverview, error) {
	url := fmt.Sprintf("%s/api/developer/%d/overview", s.Host, uid)

	ret := gaeaResponse[DeveloperOverview]{}

	err := s.Client.GetCall(&ret, url)
	if err != nil {
		return nil, err
	}

	if ret.Code != http.StatusOK {
		return nil, fmt.Errorf("code: %d, message: %s", ret.Code, ret.Message)
	}

	return &ret.Data, nil
}

func (s *gaeaAdminService) ListUserByNames(names []string) ([]AdminUser, error) {
	url := fmt.Sprintf("%s/api/admin/users/search", s.Host)

	ret := gaeaResponse[[]AdminUser]{}

	err := s.Client.CallWithJson(&ret, url, map[string][]string{"names": names})
	if err != nil {
		return nil, err
	}

	if ret.Code != http.StatusOK {
		return nil, fmt.Errorf("code: %d, message: %s", ret.Code, ret.Message)
	}

	return ret.Data, nil
}

func (s *gaeaAdminService) ListUserByEmails(emails []string) ([]AdminUser, error) {
	url := fmt.Sprintf("%s/api/user/admin/list/emails", s.Host)

	ret := gaeaResponse[[]AdminUser]{}

	err := s.Client.CallWithJson(&ret, url, map[string][]string{"emails": emails})
	if err != nil {
		return nil, err
	}

	if ret.Code != http.StatusOK {
		return nil, fmt.Errorf("code: %d, message: %s", ret.Code, ret.Message)
	}

	return ret.Data, nil
}

func (s *gaeaAdminService) GetLevelsByUids(uids []uint64) ([]UserLevel, error) {
	url := fmt.Sprintf("%s/api/developer/levels/by-uids", s.Host)

	ret := gaeaResponse[[]UserLevel]{}

	err := s.Client.CallWithJson(&ret, url, map[string][]uint64{"uids": uids})
	if err != nil {
		return nil, err
	}

	if ret.Code != http.StatusOK {
		return nil, fmt.Errorf("code: %d, message: %s", ret.Code, ret.Message)
	}

	return ret.Data, nil
}
