package test

import (
	"context"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/onsi/ginkgo/v2"
	"github.com/qbox/bo-base/v3/dao"
	"github.com/qbox/pay-sdk/base/account"
	"qiniu.io/qbpm/engine"
	"qiniu.io/qbpm/engine/cache"
	"qiniu.io/qbpm/engine/conf"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/mock"
	serviceMock "qiniu.io/qbpm/engine/mock"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/util/snowflake"

	// used for test
	_ "github.com/jinzhu/gorm/dialects/mysql"
)

// recreateDB recreate test database
func recreateDB(cfg *mgr.DBConfig, dbName string) error {
	base, err := mgr.InitDB(cfg, false)
	if err != nil {
		panic(err)
	}
	defer base.Close()

	err = base.DB.Exec("DROP DATABASE IF EXISTS `" + dbName + "`").Error
	if err != nil {
		return fmt.Errorf("drop test database %s: %v", dbName, err)
	}
	err = base.DB.Exec("CREATE DATABASE `" + dbName + "` DEFAULT CHARSET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci'").Error
	if err != nil {
		return fmt.Errorf("create test database %s: %v", dbName, err)
	}
	err = base.DB.Exec("USE `" + dbName + "`").Error
	if err != nil {
		return fmt.Errorf("use test database %s: %v", dbName, err)
	}
	base.Automigrate()
	return nil
}

// getEnv gets env with default value
func getEnv(key, defaultVal string) string {
	v := os.Getenv(key)
	if v == "" {
		return defaultVal
	}
	return v
}

func getDBCfgAndInit() *mgr.DBConfig {
	log.SetOutput(ginkgo.GinkgoWriter)

	port, err := strconv.Atoi(getEnv("MYSQL_PORT", "3306"))
	if err != nil {
		panic(err)
	}

	cfg := &mgr.DBConfig{
		Driver:      "mysql",
		Host:        getEnv("MYSQL_HOST", "127.0.0.1"),
		Port:        port,
		Username:    getEnv("MYSQL_USERNAME", "root"),
		Password:    getEnv("MYSQL_PASSWORD", ""),
		DB:          "",
		Charset:     "utf8mb4",
		Collation:   "utf8mb4_unicode_ci",
		ParseTime:   true,
		Loc:         "Local",
		MaxOpenConn: 100,
		MaxIdleConn: 100,
	}
	dbName := getEnv("DB_NAME", fmt.Sprintf("qbpm_test_%d", time.Now().UnixNano()))

	err = recreateDB(cfg, dbName)
	if err != nil {
		panic(err)
	}

	cfg.DB = dbName

	return cfg
}

func getRedisConfigAndInit() dao.CacheConfig {
	config := dao.CacheConfig{
		RedisConfig: redis.UniversalOptions{
			Addrs: []string{getEnv("REDIS_ADDR", "127.0.0.1:6379")},
			DB:    6,
		},
	}
	cli, err := cache.Init(&config)
	if err != nil {
		panic(err)
	}
	ctx := context.Background()
	err = cli.FlushDB(ctx).Err()
	if err != nil {
		panic(err)
	}
	return config
}

// Init initialize the base DAO
func Init() *mgr.Base {
	cfg := getDBCfgAndInit()
	base, err := mgr.InitDB(cfg, getEnv("DEBUG", "") == "true")
	if err != nil {
		panic(err)
	}

	snowflake.Init(1)

	// 预置测试用的 service 和 action
	// 因 actionPool 是一个全局的单例，避免并发时从不同的测试库读到不一样的 action 数据，这里统一处理
	prepareActionAndServices(base)

	return base
}

func prepareActionAndServices(base *mgr.Base) {
	m := serviceMock.NewMockServiceAction()
	serviceMgr := mgr.ServiceMgr{Base: *base}
	serviceID, err := serviceMgr.Save(&models.Service{
		Code: "testService",
		Name: "testService",
		Host: m.URL,
	})
	if err != nil {
		panic(err)
	}
	actionMgr := mgr.ActionMgr{Base: *base}
	_, err = actionMgr.Save(&models.Action{
		Code:    "testAction",
		Name:    "testAction",
		Service: serviceID,
		URL:     "/action/test",
		Method:  "POST",
	})
	if err != nil {
		panic(err)
	}

	_, err = actionMgr.Save(&models.Action{
		Code:    "assignNotify",
		Name:    "assignNotify",
		Service: serviceID,
		URL:     "/action/test",
		Method:  "POST",
	})
	if err != nil {
		panic(err)
	}

	_, err = actionMgr.Save(&models.Action{
		Code:    "assignWechatNotify",
		Name:    "assignWechatNotify",
		Service: serviceID,
		URL:     "/action/test",
		Method:  "POST",
	})
	if err != nil {
		panic(err)
	}
}

// InitRedis initialize a redis client
func InitRedis() redis.UniversalClient {
	cfg := getRedisConfigAndInit()
	client, err := cache.Init(&cfg)
	if err != nil {
		panic(err)
	}

	return client
}

// InitEngine initialize engine
func InitEngine() engine.ProcessEngine {
	cfg := &conf.Config{
		Services: conf.ServicesConfig{
			Acc: account.AccConfig{
				Host: mock.NewMockAcc().URL,
			},
		},
		DB:    *getDBCfgAndInit(),
		Redis: getRedisConfigAndInit(),
	}
	engine.Init(cfg, true)

	return engine.GetProcessEngine()
}
