package mock

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"

	"github.com/go-openapi/strfmt"
	"github.com/julienschmidt/httprouter"
	"github.com/qbox/bo-base/v3/rpc"
	sofaSDK "github.com/qbox/pay-sdk/sofa/client/user"
	"github.com/qbox/pay-sdk/sofa/models"
)

type mockSofa struct {
}

func NewMockSofaService() sofaSDK.ClientService {
	s := NewMockSofa()

	sofaTr, err := rpc.NewSwaggerTransport(s.URL, http.DefaultClient)
	if err != nil {
		panic(err)
	}

	return sofaSDK.New(sofaTr, strfmt.Default)
}

func NewMockSofa() *httptest.Server {
	return httptest.NewServer(func() *httprouter.Router {
		r := httprouter.New()
		p := mockSofa{}
		r.GET("/api/v1/admin/user/manager", p.<PERSON>UserManager)
		return r
	}())
}

func (m *mockSofa) GetUserManager(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	email := r.URL.Query().Get("email")

	if email == "<EMAIL>" {
		writeJSONResponse(w, 200, models.UserRet{
			Data: &models.User{
				Email: "<EMAIL>",
			},
		})
	} else if email == "<EMAIL>" {
		writeJSONResponse(w, 200, models.UserRet{
			Data: &models.User{
				Email: "<EMAIL>",
			},
		})
	} else {
		writeJSONResponse(w, 404, models.UserRet{
			Status:  1,
			Message: "未找到资源",
		})
	}

	return
}

func writeJSONResponse(w http.ResponseWriter, status int, src interface{}) error {
	data, err := json.Marshal(src)
	if err != nil {
		return err
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	w.Write(data)
	return nil
}
