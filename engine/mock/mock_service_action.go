package mock

import (
	"net/http"
	"net/http/httptest"

	"github.com/julienschmidt/httprouter"
)

type mockServiceAction struct {
}

func NewMockServiceAction() *httptest.Server {
	return httptest.NewServer(func() *httprouter.Router {
		r := httprouter.New()
		p := mockServiceAction{}
		r.POST("/action/test", p.OK)
		return r
	}())
}

func (m *mockServiceAction) OK(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	writeJSONResponse(w, 200, nil)
}
