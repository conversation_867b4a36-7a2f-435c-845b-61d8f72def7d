package mock

import (
	"net/http"
	"net/http/httptest"

	"github.com/julienschmidt/httprouter"
	"github.com/qbox/pay-sdk/base/oauth"
)

type mockAcc struct {
}

func NewMockAcc() *httptest.Server {
	return httptest.NewServer(func() *httprouter.Router {
		r := httprouter.New()
		p := mockAcc{}
		r.POST("/oauth2/token", p.GetUserToken)
		return r
	}())
}

func (m *mockAcc) GetUserToken(w http.ResponseWriter, r *http.Request, params httprouter.Params) {

	writeJSONResponse(w, 200, oauth.Token{
		AccessToken: "token",
		TokenExpiry: 3600,
	})
}
