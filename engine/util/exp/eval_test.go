package exp_test

import (
	"encoding/json"
	"errors"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	. "qiniu.io/qbpm/engine/util/exp"
	"qiniu.io/qbpm/engine/util/params"
)

var _ = Describe("Eval", func() {
	Describe("Eval", func() {
		It("should return true", func() {
			v, _ := Eval("3==3", "{}")
			value := v.ToBoolean()
			Expect(value).To(Equal(true))
		})

		It("should return true with params", func() {
			v, _ := Eval("params.data.products.length > 0", `{"data":{"products":["pili","kodo"]}}`)
			value := v.ToBoolean()
			Expect(value).To(Equal(true))

			v, _ = Eval("params.data.products.length == 2", `{"data":{"products":["pili","kodo"]}}`)
			value = v.ToBoolean()
			Expect(value).To(Equal(true))
		})

		It("should return specfic user email", func() {
			v, _ := Eval("params.data.email", `{"data":{"email":"<EMAIL>"}}`)
			value := v.String()
			Expect(value).To(Equal("<EMAIL>"))
		})

		It("should return specfic user emails", func() {
			v, _ := Eval("params.data.emails", `{"data":{"emails":["<EMAIL>","<EMAIL>"]}}`)
			value := v.String()
			Expect(value).To(Equal("<EMAIL>,<EMAIL>"))
		})
	})

	DescribeTable("EvalBool",
		func(exp, params string, v bool, err error) {
			val, err1 := EvalBool(exp, params)
			if err != nil {
				Expect(err1).ToNot(BeNil())
				return
			}
			Expect(err1).To(BeNil())
			Expect(val).To(Equal(v))
		},
		Entry("should return true", `params.data.managerProfile == "区域销售总监"`,
			`{"data":{"managerProfile":"区域销售总监","measureResult":false}}`, true, nil),
		Entry("should return error", "x==3", "", false, errors.New("with error")),
		Entry("should return true", "1==1", "{}", true, nil),
		Entry("should return false", "1==2", "{}", false, nil),
		Entry("should return true with param", "params.x==2", `{"x":2}`, true, nil),
		Entry("should return true with param", `params.x=="2a"`, `{"x":"2a"}`, true, nil),
		Entry("should return false with param", "params.x<=0", `{"x":3}`, false, nil),
		Entry("should return true with param",
			`params.data.measurements[0].gpm==0.5`,
			`{
				"data":{
					"measurements":[{"gpm":0.5,"measure_gpm":1}]
				}
			}`,
			true, nil),
		Entry("should return true with param",
			`function fn(data){
				if(!data.measurements){
					return false;
				}
				// TODO: for in 语法此处结果不预期
				for(var i = 0; i < data.measurements.length; i++){
					if (data.measurements[i].measure_gpm && data.measurements[i].gpm) {
						if (data.measurements[i].measure_gpm > data.measurements[i].gpm){
							return true;
						}
					}
				}
				return false;
			}
			fn(params.data);
			`, `{
				"data":{
					"measurements":[{"gpm":0.5,"measure_gpm":1}]
				}
			}`, true, nil),
		Entry("should return true with param",
			`function fn(data){
				if(!data || !data.measurements || data.measurements.length == 0){
					return false;
				}

				var measures = data.measurements;

				measures = _.sortBy(measures, function(m){
					return m.month;
				})

				measures = measures.reverse();

				measures = _.uniq(measures, false, function(m){
					return m.product;
				})

				var skip = true;
				_.each(measures, function(m){
					if (m.measure_gpm < 0.15){
						skip = false;
					}
				})

				return !skip;
			}
			fn(params.data);
			`, `{"data":{
					"measurements":[
						{"product":"fusionov","measure_gpm":0.16,"month":"201806"},
						{"product":"fusionov","measure_gpm":0.14,"month":"201805"}
					]
				}
			}`, false, nil),
		Entry("should return true with param",
			`function fn(data){
				if(!data || !data.measurements || data.measurements.length == 0){
					return false;
				}

				var measures = data.measurements;

				measures = _.sortBy(measures, function(m){
					return m.month;
				})

				measures = measures.reverse();

				measures = _.uniq(measures, false, function(m){
					return m.product;
				})

				var skip = true;
				_.each(measures, function(m){
					if (m.measure_gpm < 0.15){
						skip = false;
					}
				})

				return !skip;
			}
			fn(params.data);
			`, `{"data":{
					"measurements":[
						{"product":"fusionov","measure_gpm":0.16,"month":"201806"},
						{"product":"fusionov","measure_gpm":0.14,"month":"201805"}
					]
				}
			}`, false, nil),
		Entry("should return true with param",
			`function fn(data){
				if(!data || !data.measurements || data.measurements.length == 0){
					return false;
				}

				var measures = data.measurements;

				measures = _.sortBy(measures, function(m){
					return m.month;
				})

				measures = measures.reverse();

				measures = _.uniq(measures, false, function(m){
					return m.product;
				})

				var skip = true;
				_.each(measures, function(m){
					if (m.measure_gpm < 0.15){
						skip = false;
					}
				})

				return !skip;
			}
			fn(params.data);
			`, `{"data":{
					"measurements":[
						{"product":"fusionov","measure_gpm":0.16,"month":"201806"},
						{"product":"fusionov","measure_gpm":0.14,"month":"201807"}
					]
				}
			}`, true, nil),
		Entry("should return false with param",
			`function fn(data){
				if(!data || !data.measurements || data.measurements.length == 0){
					return false;
				}

				var measures = data.measurements;

				measures = _.sortBy(measures, function(m){
					return m.month;
				})

				measures = measures.reverse();

				measures = _.uniq(measures, false, function(m){
					return m.product;
				})

				var skip = true;
				_.each(measures, function(m){
					if (m.measure_gpm < 0.15){
						skip = false;
					}
				})

				return !skip;
			}
			fn(params.data);
			`, `{"data":{
					"measurements":[
						{"product":"fusionov","measure_gpm":0.16,"month":"201806"},
						{"product":"fusionov","measure_gpm":0.14,"month":"201805"}
					]
				}
			}`, false, nil),
	)

	DescribeTable("EvalString",
		func(exp, params, v string, err error) {
			val, err1 := EvalString(exp, params)
			if err != nil {
				Expect(err1).ToNot(BeNil())
				return
			}
			Expect(err1).To(BeNil())
			Expect(val).To(Equal(v))
		},
		Entry("should return error", "x.a", "", "", errors.New("with error")),
		Entry("should return specific string", `"test"`, `{"process":{"status":1}}`, "test", nil),
		Entry("should return specific string with local param", "params.process.instance", `{"process":{"instance":"test"}}`, "test", nil),
		Entry("should return specific string with data param", "params.data.instance", `{"data":{"instance":"test"}}`, "test", nil),
		Entry("should return specific string with other param", "params.instance", `{"instance":"test"}`, "test", nil),
	)

	Describe("EvalSpread", func() {
		It("spread param", func() {
			source := `{"hello":{"hello1":"value1", "hello2":{"hello3":"value2"}}, "array":[{"array1":"value1"}, {"array2":"value2"}]}`
			exp := `{"hello":"params.hello.hello1", "array":"params.array[1].array2"}`
			var source_obj interface{}
			json.Unmarshal([]byte(source), &source_obj)
			result, _ := EvalSpread(exp, source_obj)

			var resultMap map[string]string
			json.Unmarshal([]byte(result), &resultMap)
			if resultMap == nil || resultMap["hello"] != "value1" || resultMap["array"] != "value2" {
				Fail(result)
			}

			exp = "params.hello.hello2"
			result, _ = EvalSpread(exp, source_obj)
			if result != "{\"hello3\":\"value2\"}" {
				Fail(result)
			}

			exp = "params.hello.hello1"
			result, _ = EvalSpread(exp, source_obj)
			if result != "value1" {
				Fail(result)
			}

			exp = `{"body":"'this is ' + params.hello.hello1"}`
			result, _ = EvalSpread(exp, source_obj)
			Expect(result).To(Equal(`{"body":"this is value1"}`))

			param := `{"process":{"status":1}}`
			result, err := EvalSpread(`{"status":"params.process.status"}`, param)
			Expect(err).To(BeNil())
			Expect(result).To(Equal(`{"status":1}`))
		})

		It("spread Fail", func() {
			source := `{"hello":{"hello1":"value1", "hello2":{"hello3":"value2"}}, "array":[{"array1":"value1"}, {"array2":"value2"}]}`
			exp := `{"hello":"hello.hello1", "array":"noExitKey.1.array2"}`
			var source_obj interface{}
			json.Unmarshal([]byte(source), &source_obj)
			result, _ := EvalSpread(exp, source_obj)
			var resultMap map[string]string
			json.Unmarshal([]byte(result), &resultMap)
			if resultMap["array"] != "noExitKey.1.array2" {
				Fail("spread Fail err")
			}
		})

		It("big integer parse", func() {
			srcObj := struct {
				ID uint64 `json:"id,string"`
			}{
				ID: 1590645735613804544,
			}

			jsonStr, err := json.Marshal(srcObj)
			Expect(err).To(BeNil())
			param, err := params.SetParam("", "process", string(jsonStr))
			Expect(err).To(BeNil())
			Expect(param).To(Equal(`{"process":{"id":"1590645735613804544"}}`))
			data, err := EvalSpread("params.process.id", param)
			Expect(err).To(BeNil())
			Expect(data).To(Equal("1590645735613804544"))
		})
	})

})
