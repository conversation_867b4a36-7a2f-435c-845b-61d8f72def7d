package exp

import (
	"encoding/json"
	"errors"
	"reflect"
	"strings"

	"github.com/dop251/goja"
	"github.com/robertkrimen/otto/underscore"
)

// Eval evaluate expression value using specific params
func Eval(exp, params string) (value goja.Value, err error) {
	if params == "" {
		params = "{}"
	}

	vm := goja.New()
	_, err = vm.RunString(underscore.Source())
	if err != nil {
		return
	}

	vm.Set("params", params)
	vm.Set("exp", exp)
	return vm.RunString(`
		var params = JSON.parse(params);
		
		eval(exp);
	`)
}

// EvalBool Evaluate as a bool value
func EvalBool(exp, params string) (isTrue bool, err error) {
	v, err := Eval(exp, params)
	if err != nil {
		return
	}

	return v.ToBoolean(), nil
}

// EvalString Evaluate as a string value
func EvalString(exp, params string) (str string, err error) {
	v, err := EvalExport(exp, params)
	if err != nil {
		return
	}

	str = v.(string)
	return
}

// EvalExport Evaluate as a string value
func EvalExport(exp, params string) (res interface{}, err error) {
	v, err := Eval(exp, params)
	if err != nil {
		return
	}

	result := v.Export()
	if result == nil {
		return "", errors.New("no value")
	}

	switch reflect.TypeOf(result).Kind() {
	case reflect.String:
		return result.(string), nil
	case reflect.Map, reflect.Slice, reflect.Array:
		str, err := json.Marshal(result)
		if err != nil {
			return "", err
		}
		return string(str), nil
	}
	return v.Export(), nil
}

// EvalJSON Evaluate as real values
func EvalJSON(exp string, source interface{}) (res interface{}, err error) {
	if source == nil {
		return exp, nil
	}

	if jsonParam, ok := source.(string); ok {
		return EvalExport(exp, jsonParam)
	}

	jsonParam, err := json.Marshal(source)
	if err != nil {
		return "", err
	}

	return EvalExport(exp, string(jsonParam))
}

func recursionJSON(j interface{}, source interface{}) (interface{}, error) {
	switch reflect.TypeOf(j).Kind() {
	case reflect.Map:
		obj := j.(map[string]interface{})
		for key, value := range obj {
			temp, err := recursionJSON(value, source)
			if err != nil {
				return nil, err
			}
			obj[key] = temp
		}
	case reflect.Slice:
		obj := j.([]interface{})
		for index, value := range obj {
			temp, err := recursionJSON(value, source)
			if err != nil {
				return nil, err
			}
			obj[index] = temp
		}
	case reflect.String:
		str := j.(string)
		if strings.Contains(str, "params.") {
			return EvalJSON(str, source)
		}
		return str, nil
	}
	return j, nil
}

// EvalSpread eval spread
func EvalSpread(exp string, source interface{}) (string, error) {
	if source == nil {
		return exp, nil
	}

	var (
		obj, result interface{}
		err         error
	)
	if err = json.Unmarshal([]byte(exp), &obj); err != nil {
		obj = exp
	}

	if result, err = recursionJSON(obj, source); err != nil {
		return exp, err
	}

	if reflect.TypeOf(result).Kind() == reflect.String {
		return result.(string), nil

	}

	if result, err = json.Marshal(result); err != nil {
		return exp, err
	}
	return string(result.([]byte)), nil
}
