package params_test

import (
	"errors"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	. "qiniu.io/qbpm/engine/util/params"
)

var _ = Describe("Param", func() {
	DescribeTable("SetParam",
		func(val interface{}, param, path, result string, err error) {
			res, err1 := SetParam(param, path, val)
			if err != nil {
				Expect(err1).ToNot(BeNil())
				return
			}
			Expect(err1).To(BeNil())
			Expect(res).To(Equal(result))
		},
		Entry("should return error", "", "test", "", "", errors.New("with error")),
		Entry("should return specific json string with new", "", "", "test", `{"test":""}`, nil),
		Entry("should return specific json string with existed", "test", `{"test":""}`, "data", `{"data":"test","test":""}`, nil),
		Entry("should return specific json string", `{"data":"test"}`, "", "test", `{"test":{"data":"test"}}`, nil),
		Entry("should return specific json string", `{"data","test"}`, "", "test", "", errors.New("with error")),
	)
})
