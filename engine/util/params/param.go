package params

import (
	"strings"

	"github.com/Jeffail/gabs"
)

// GetParamParser Get param as a json parser
func GetParamParser(params string) (parser *gabs.Container, err error) {
	if params == "" {
		parser = gabs.New()
	} else {
		parser, err = gabs.ParseJSON([]byte(params))
	}

	return
}

// SetParam Set param using json format
func SetParam(params string, path string, value interface{}) (result string, err error) {
	//TODO: deal with empty path

	var valObj interface{}
	if str, ok := value.(string); ok && strings.HasPrefix(str, "{") && strings.HasSuffix(str, "}") {
		p, err := GetParamParser(str)
		if err != nil {
			return "", err
		}
		valObj = p.Data()
	}

	jsonParser, err := GetParamParser(params)
	if err != nil {
		return
	}

	if valObj == nil {
		_, err = jsonParser.SetP(value, path)
	} else {
		_, err = jsonParser.SetP(valObj, path)
	}
	if err != nil {
		return
	}

	result = jsonParser.String()

	return
}
