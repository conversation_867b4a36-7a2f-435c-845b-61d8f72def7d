package logger_test

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/sirupsen/logrus"

	. "qiniu.io/qbpm/engine/util/logger"
)

var _ = Describe("Logger", func() {
	Describe("NewLogger", func() {
		It("should get debug and jsonFormatter logger", func() {
			l := NewLogger(nil, true, true)
			log := l.(*logrus.Logger)
			Expect(log.Level).To(Equal(logrus.DebugLevel))
		})

		It("should get info and textFormatter logger", func() {
			l := NewLogger(nil, false, false)
			log := l.(*logrus.Logger)
			Expect(log.Level).To(Equal(logrus.InfoLevel))
		})
	})
})
