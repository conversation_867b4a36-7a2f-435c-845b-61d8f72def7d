package logger_test

import (
	"bytes"
	"encoding/json"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/sirupsen/logrus"

	. "qiniu.io/qbpm/engine/util/logger"
)

var _ = Describe("Formatter", func() {
	Describe("WrapFormatter", func() {
		print := func(data map[string]interface{}, fixedFields ...string) string {
			buf := bytes.NewBuffer(nil)
			l := logrus.New()
			l.Out = buf
			l.Formatter = WrapFormatter(&logrus.JSONFormatter{}, fixedFields...)
			l.WithFields(logrus.Fields(data)).Print()
			return buf.String()
		}

		Context("Without customized fixed fields", func() {
			It("Should print correct output text", func() {
				out := print(map[string]interface{}{
					"name":  "name field",
					"desc":  `should be moved into "payload"`,
					"age":   18,
					"error": "this is a error",
					"reqid": "123456789",
					"nested": map[string]interface{}{
						"time": "2018-02-26",
					},
				})

				var data map[string]interface{}
				err := json.Unmarshal([]byte(out), &data)
				Expect(err).NotTo(HaveOccurred())
				Expect(data).To(HaveKeyWithValue(FieldKeyError, "this is a error"))
				Expect(data).To(HaveKey(FieldKeyLevel))
				Expect(data).To(HaveKey(FieldKeyTime))
				Expect(data).To(HaveKey(FieldKeyMsg))
				Expect(data).To(HaveKeyWithValue(FieldKeyReqid, "123456789"))
				Expect(data).To(HaveKey(FieldKeyPayload))
				Expect(data).NotTo(HaveKey("name"))
				Expect(data).NotTo(HaveKey("desc"))
				Expect(data).NotTo(HaveKey("age"))
				Expect(data).NotTo(HaveKey("nested"))
			})
		})

		Context("With customized fixed fields", func() {
			It("Should print correct output text", func() {
				fixedFields := []string{
					"name",
				}
				out := print(map[string]interface{}{
					"name":  "name field",
					"desc":  `should be moved into "payload"`,
					"age":   18,
					"error": "this is a error",
					"reqid": "123456789",
					"nested": map[string]interface{}{
						"time": "2018-02-26",
					},
				}, fixedFields...)

				var data map[string]interface{}
				err := json.Unmarshal([]byte(out), &data)
				Expect(err).NotTo(HaveOccurred())
				Expect(data).To(HaveKeyWithValue(FieldKeyError, "this is a error"))
				Expect(data).To(HaveKey(FieldKeyLevel))
				Expect(data).To(HaveKey(FieldKeyTime))
				Expect(data).To(HaveKey(FieldKeyMsg))
				Expect(data).To(HaveKeyWithValue(FieldKeyReqid, "123456789"))
				Expect(data).To(HaveKey(FieldKeyPayload))
				Expect(data).To(HaveKey("name"))
				Expect(data).NotTo(HaveKey("desc"))
				Expect(data).NotTo(HaveKey("age"))
				Expect(data).NotTo(HaveKey("nested"))
			})
		})

	})
})
