package kodo

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/qiniu/go-sdk/v7/auth"
	"github.com/qiniu/go-sdk/v7/storage"
)

type Client struct {
	accessKey string
	secretKey string
	domain    string
	bucket    string
}

func NewClient(accessKey, secretKey, domain, bucket string) *Client {
	return &Client{
		accessKey: accessKey,
		secretKey: secretKey,
		domain:    domain,
		bucket:    bucket,
	}
}

type UploadFileReq struct {
	Directory     string
	Prefix        string
	Filename      string
	ContentBase64 string
}

type PutRet struct {
	Hash         string `json:"hash"`
	PersistentId string `json:"persistentId"`
	Key          string `json:"key"`
}

func decodeBase64(x string) ([]byte, error) {
	// 兼容前端传递过来的带形如 "data:application/pdf;base64," 的非标准 base64 格式
	const base64Marker = "base64,"
	var base64Input string
	if strings.HasPrefix(x, "data:") {
		idx := strings.Index(x, base64Marker)
		if idx < 0 {
			return nil, errors.New("unexpected data uri")
		}
		base64Input = x[idx+len(base64Marker):]
	} else {
		base64Input = x
	}

	return base64.StdEncoding.DecodeString(base64Input)
}

// UploadFile 上传文件
func (c *Client) UploadFile(req UploadFileReq) error {
	credentials := auth.New(c.accessKey, c.secretKey)
	putPolicy := storage.PutPolicy{
		Scope: c.bucket,
	}
	token := putPolicy.UploadToken(credentials)

	ext := filepath.Ext(req.Filename)

	if ext == "" {
		return errors.New("file have no specified extensions")
	}

	if strings.HasPrefix(req.Directory, "/") {
		return errors.New("directory should not start with /")
	}

	req.Directory = strings.TrimSuffix(req.Directory, "/")

	baseName := strings.TrimSuffix(req.Filename, ext)
	encodedName := base64.StdEncoding.EncodeToString([]byte(baseName)) + ext

	key := fmt.Sprintf("%s%s", req.Prefix, encodedName)

	if req.Directory != "" {
		key = fmt.Sprintf("%s/%s", req.Directory, key)
	}

	content, err := decodeBase64(req.ContentBase64)
	if err != nil {
		return err
	}

	ret := PutRet{}
	return storage.NewFormUploader(&storage.Config{}).
		Put(context.Background(), &ret, token, key,
			bytes.NewReader(content), int64(len(content)), nil)
}

// GetFile 获取上传的文件
func (c *Client) GetFile(directory string, prefix string, filename string) (string, error) {
	credentials := auth.New(c.accessKey, c.secretKey)

	ext := filepath.Ext(filename)
	baseName := strings.TrimSuffix(filename, ext)
	encodedName := base64.StdEncoding.EncodeToString([]byte(baseName)) + ext

	if strings.HasPrefix(directory, "/") {
		return "", errors.New("directory should not start with /")
	}

	directory = strings.TrimSuffix(directory, "/")

	key := fmt.Sprintf("%s%s", prefix, encodedName)
	if directory != "" {
		key = fmt.Sprintf("%s/%s", directory, key)
	}

	deadline := time.Now().Add(time.Second * 3600).Unix()

	return storage.MakePrivateURLv2(credentials, c.domain, key, deadline), nil
}
