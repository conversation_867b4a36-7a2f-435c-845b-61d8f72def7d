package snowflake

import (
	"sync"

	"github.com/bwmarrin/snowflake"
	"github.com/sirupsen/logrus"
)

// IDGenerator Snowflake generator
type IDGenerator struct {
	*snowflake.Node
}

// Generator Snowflake generator instance
var Generator *IDGenerator
var once sync.Once

// Init init Generator
func Init(nodeID int64) {
	once.Do(func() {
		node, err := snowflake.NewNode(nodeID)
		if err != nil {
			logrus.Fatalf("initialize id generator with error: %s", err)
		}

		Generator = &IDGenerator{node}
	})
}

// ID Get a snowflakes id
func (g *IDGenerator) ID() uint64 {
	return uint64(g.Generate().Int64())
}
