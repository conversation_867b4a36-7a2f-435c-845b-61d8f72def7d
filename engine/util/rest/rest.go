package rest

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"reflect"
	"strings"

	baseHttp "github.com/qbox/bo-base/v3/http"
)

var httpProcessMap = map[string]func(*Rest) (io.Reader, error){
	"application/x-www-form-urlencoded": (*Rest).urlencode,
	"application/json":                  (*Rest).rawJSON,
	"multipart/form-data":               (*Rest).multipartBody,
	"":                                  (*Rest).urlParams,
}

// Rest defines a rest request entity
type Rest struct {
	sourceURL, url, method, header, body string
	formDataContentType                  string
	roundTripper                         http.RoundTripper
	Request                              *http.Request
	Response                             *http.Response
}

// NewRest instances a new rest
func NewRest(url, method, header, body string, roundTripper ...http.RoundTripper) *Rest {
	rest := &Rest{
		url:       url,
		sourceURL: url,
		method:    strings.ToUpper(method),
		header:    header,
		body:      body,
	}

	if len(roundTripper) > 0 && roundTripper[0] != nil {
		rest.roundTripper = roundTripper[0]
	}

	if err := rest.makeRequest(); err != nil {
		return nil
	}
	return rest
}

func (r *Rest) urlParams() (io.Reader, error) {
	parser, err := url.Parse(r.sourceURL)
	if err != nil {
		return nil, err
	}

	params, err := r.jsonToMap(r.body)
	if err != nil {
		return nil, err
	}

	query := parser.RawQuery
	for key, value := range params {
		query = query + "&" + key + "=" + value.(string)
	}
	query = strings.TrimPrefix(query, "&")

	r.url = strings.Split(r.sourceURL, "?")[0]
	if query != "" {
		r.url = r.url + "?" + query
	}
	if parser.Fragment != "" {
		r.url = r.url + "#" + parser.Fragment
	}

	return nil, nil
}

func (r *Rest) urlencode() (io.Reader, error) {
	var req http.Request
	req.ParseForm()

	params, err := r.jsonToMap(r.body)
	if err != nil {
		return nil, err
	}

	if len(params) == 0 {
		return nil, nil
	}

	for key, value := range params {
		switch reflect.TypeOf(value).Kind() {
		case reflect.Slice, reflect.Array:
			for _, v := range value.([]any) {
				formatted := fmt.Sprintf("%v", v)
				marshal, _ := json.Marshal(v)
				// 这样判断是避免把 uint64/float64 用科学计数法表示
				if string(marshal) == formatted {
					req.Form.Add(key, formatted)
				} else {
					// 如果是字符串，去掉双引号
					req.Form.Add(key, strings.Trim(string(marshal), "\""))
				}
			}
		default:
			formatted := fmt.Sprintf("%v", value)
			marshal, _ := json.Marshal(value)
			if string(marshal) == formatted {
				req.Form.Add(key, formatted)
			} else {
				req.Form.Add(key, strings.Trim(string(marshal), "\""))
			}
		}
	}
	body := strings.TrimSpace(req.Form.Encode())

	return strings.NewReader(body), nil
}

func (r *Rest) rawJSON() (io.Reader, error) {
	if r.body == "" {
		return nil, nil
	}

	return strings.NewReader(r.body), nil
}

func (r *Rest) multipartBody() (io.Reader, error) {
	params, err := r.jsonToMap(r.body)
	if err != nil {
		return nil, err
	}

	if len(params) == 0 {
		return nil, nil
	}

	buf := &bytes.Buffer{}
	writer := multipart.NewWriter(buf)
	for key, val := range params {
		_ = writer.WriteField(key, val.(string))
	}
	r.formDataContentType = writer.FormDataContentType()
	writer.Close()
	return buf, nil
}

func (r *Rest) setHeader(req *http.Request) error {
	headers, err := r.jsonToMap(r.header)
	if err != nil {
		return err
	}

	if headers["Content-Type"] == "multipart/form-data" {
		headers["Content-Type"] = r.formDataContentType
	}

	for key, value := range headers {
		if value != nil && value.(string) != "" && key != "" {
			req.Header.Set(key, value.(string))
		}
	}

	return nil
}

func (r *Rest) checkHeader() error {
	headers, err := r.jsonToMap(r.header)
	if err != nil {
		return err
	}

	if contentType, ok := headers["Content-Type"]; !ok || contentType == nil || contentType.(string) == "" {
		if r.method != "GET" {
			headers["Content-Type"] = "application/x-www-form-urlencoded"
		} else {
			headers["Content-Type"] = ""
		}
	}

	header, err := json.Marshal(headers)
	if err != nil {
		return err
	}
	r.header = string(header)

	return nil
}

func (r *Rest) makeRequest() error {
	if err := r.checkHeader(); err != nil {
		return err
	}

	header, err := r.jsonToMap(r.header)
	processor, ok := httpProcessMap[header["Content-Type"].(string)]
	if !ok {
		return errors.New("invalid Content-Type")
	}

	var body io.Reader
	if body, err = processor(r); err != nil {
		return err
	}

	if r.Request, err = http.NewRequest(r.method, r.url, body); err != nil {
		return err
	}
	return nil
}

// Do performs real action to get response
func (r *Rest) Do() (interface{}, error) {
	r.setHeader(r.Request)

	var err error

	client := &http.Client{Transport: baseHttp.DefaultTransport}

	if r.roundTripper != nil {
		client = &http.Client{Transport: r.roundTripper}
	}

	r.Response, err = client.Do(r.Request)
	if err != nil {
		return nil, err
	}

	if r.Response.StatusCode < 200 || r.Response.StatusCode > 300 {
		body, _ := io.ReadAll(r.Response.Body)
		err = errors.New(string(body))
		return nil, err
	}

	if r.Response.StatusCode == 204 {
		return nil, nil
	}

	var result interface{}
	if err = json.NewDecoder(r.Response.Body).Decode(&result); err != nil && err != io.EOF {
		return nil, err
	}

	return result, nil
}

func (r *Rest) jsonToMap(src string) (map[string]interface{}, error) {
	var dest map[string]interface{}
	if src == "" {
		return make(map[string]interface{}), nil
	}

	if err := json.Unmarshal([]byte(src), &dest); err != nil {
		return nil, fmt.Errorf("(%s) to json fail", src)
	}

	return dest, nil
}
