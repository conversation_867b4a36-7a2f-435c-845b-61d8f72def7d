package rest

import (
	"io"
	u "net/url"
	"slices"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Rest", func() {
	var (
		body, url, header, method string
		client                    *Rest
	)

	Describe("GET method", func() {

		BeforeEach(func() {
			body = `{"p1":"v1", "p2":"v2"}`
			url = "http://www.baidu.com"
			method = "get"
			client = NewRest(url, method, header, body)
		})

		It("urlParams", func() {
			Expect(client.method).To(Equal("GET"))
			client.urlParams()
			Expect(len(client.url)).To(Equal(len("http://www.baidu.com?p1=v1&p2=v2")))
			client.sourceURL = "http://www.baidu.com?p1=v1#h"
			client.urlParams()
			Expect(len(client.url)).To(Equal(len("http://www.baidu.com?p1=v1&p2=v2&p1=v1#h")))
		})
	})

	Describe("None Get method", func() {
		BeforeEach(func() {
			body = `{"p1":"v1", "p2":"v2"}`
			url = "http://www.baidu.com"
			method = "post"
			client = NewRest(url, method, header, body)
		})

		It("urlencode", func() {
			client.makeRequest()
			body, _ := io.ReadAll(client.Request.Body)
			if string(body) != "p1=v1&p2=v2" && string(body) != "p2=v2&p1=v1" {
				Fail(string(body))
			}
		})
	})

	Describe("Use www-form-urlencoded", func() {
		BeforeEach(func() {
			body = `{"p1":"v1", "p2":"v2", "p3": ["v3.1", "v3.2"], "p4":1919810114514}`
			url = "http://www.baidu.com"
			method = "post"
			header = `{"Content-Type":"application/x-www-form-urlencoded"}`
			client = NewRest(url, method, header, body)
		})

		It("urlencoded", func() {
			client.makeRequest()
			body, _ := io.ReadAll(client.Request.Body)
			query, err := u.ParseQuery(string(body))
			if err != nil {
				return
			}
			if query.Get("p1") != "v1" {
				Fail(string(body))
			}
			if query.Get("p2") != "v2" {
				Fail(string(body))
			}
			if !slices.Contains(query["p3"], "v3.1") || !slices.Contains(query["p3"], "v3.2") {
				Fail(string(body))
			}
			if query.Get("p4") != "1919810114514" {
				Fail(string(body))
			}
		})
	})
})
