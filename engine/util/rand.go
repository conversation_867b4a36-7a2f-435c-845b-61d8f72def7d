package util

import (
	"crypto/rand"
	"fmt"
	mrand "math/rand"
	"time"
)

var (
	intChars = []byte("0123456789")
	alphanum = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
)

// RandomIntString generates random integer chars of specified length
func RandomIntString(n int) (str string, err error) {
	b, err := randomCreateBytes(n, intChars...)
	if err != nil {
		return
	}
	str = string(b)
	return str, err
}

// RandomIntStringr generates random integer chars robustly of specified length
func RandomIntStringr(n int) (str string) {
	str, err := RandomIntString(n)
	if err != nil {
		str = string(randCreateRobustBytes(n, intChars...))
	}

	return str
}

// RandomString generates random string of specified length
func RandomString(n int) (str string, err error) {
	b, err := randomCreateBytes(n)
	if err != nil {
		return
	}
	str = string(b)
	return str, err
}

// RandomStringr generates random string robustly of specified length
func RandomStringr(n int) (str string) {
	str, err := RandomString(n)
	if err != nil {
		str = string(randCreateRobustBytes(n))
	}

	return str
}

// RandomInt generates integer between min and max value
func RandomInt(min, max int) int {
	if min >= max {
		return min
	}
	src := mrand.NewSource(time.Now().UnixNano())
	return mrand.New(src).Intn(max-min) + min
}

// randomCreateBytes generate random []byte by specify chars.
func randomCreateBytes(n int, alphabets ...byte) ([]byte, error) {
	var bytes = make([]byte, n)
	if num, err := rand.Read(bytes); num != n || err != nil {
		if err == nil {
			err = fmt.Errorf("random string not enough length: need %d but %d", n, num)
		}
		return nil, err
	}
	for i, b := range bytes {
		if len(alphabets) == 0 {
			bytes[i] = alphanum[b%byte(len(alphanum))]
		} else {
			bytes[i] = alphabets[b%byte(len(alphabets))]
		}
	}
	return bytes, nil
}

// randCreateRobustBytes generate random []byte by specify chars robustly.
func randCreateRobustBytes(n int, alphabets ...byte) []byte {
	bytes := make([]byte, n)
	for i := range bytes {
		if len(alphabets) == 0 {
			bytes[i] = alphanum[mrand.Int63()%int64(len(alphanum))]
		} else {
			bytes[i] = alphabets[mrand.Int63()%int64(len(alphabets))]
		}
	}
	return bytes
}
