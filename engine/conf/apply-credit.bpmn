<?xml version="1.0" encoding="UTF-8"?>
<definitions id="definitions"
    xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <auth id="BearerAuth" type="Oauth" tokenKey="params.access_token" prefix="Bearer">
        <restAction
            id="token"
            url="https://acc.qbox.me/oauth2/token"
            method="post"
            body='{"grant_type":"password","username":"", "password":""}'
        />
    </auth>
    <restAction
        id="restToGetStarter"
        url="https://crm.qiniu.io/sf/api/v1/admin/user?email={{params.process.start_by_id}}"
        method="get"
        storeKey="params.starter"
        parseKey="params.data"
        parseFirst="true"
        authRef="BearerAuth"
    />
    <restAction
        id="restToInitAssignee"
        url="https://crm.qiniu.io/sf/api/v1/admin/user?email={{params.process.start_by_id}}"
        method="get"
        storeKey="params.actor.email"
        parseKey="params.data.email"
        parseFirst="true"
        authRef="BearerAuth"
    />
    <restAction
        id="restToGetStarterManager"
        url="https://crm.qiniu.io/sf/api/v1/admin/user/manager?email={{params.process.start_by_id}}"
        method="get"
        storeKey="params.starter_manager.profile"
        parseKey="params.data.profile"
        parseFirst="true"
        authRef="BearerAuth"
    />
    <restAction
        id="restToGetAssigneeManager"
        url="https://crm.qiniu.io/sf/api/v1/admin/user/manager?email={{params.actor.email}}"
        method="get"
        storeKey="params.actor.email"
        parseKey="params.data.email"
        parseFirst="true"
        authRef="BearerAuth"
    />
    <restAction
        id="assignNotify"
        url="https://morse.qiniu.io/api/notification/send/mail"
        header='{"Client-Id":"5b6901ef3f3e62405002352f","Content-Type":"application/json"}'
        body='{
            "uid":{{params.data.uid}},
            "to":["{{params.execute.original_actor}}"],
            "subject":"您有待审批的信用额度申请",
            "content":"您好，&lt;br/&gt;&lt;br/&gt;{{params.starter.name}}为客户 UID：{{params.data.uid}} | 客户名：{{params.data.account_name}} 申请的信用额度需要您审批，请尽快审批，&lt;br/&gt;&lt;a href=&apos;https://portal.qiniu.io/price-biz/approval/credit/{{params.process.excode}}&apos;&gt;https://portal.qiniu.io/price-biz/approval/credit/{{params.process.excode}}&lt;/a&gt;&lt;br/&gt;请单击该链接批准或拒绝该记录。&lt;br/&gt;&lt;br/&gt;十分感谢！&lt;br/&gt;Qiniu Process Management System"
            }'
        method="post"
        authRef="BearerAuth"
    />
    <restAction
        id="assignWechatNotify"
        url="https://ts.qiniu.io/action/internal/wechat/msg/name"
        body='{
            "user_name":"{{params.execute.original_actor.substr(0,params.execute.original_actor.lastIndexOf("@"))}}",
            "agent_id":"1000017",
            "msg":"您好，{{params.starter.name}}为客户 UID：{{params.data.uid}} | 客户名：{{params.data.account_name}} 申请的信用额度需要您审批，请尽快审批，&lt;br/&gt;https://portal.qiniu.io/price-biz/approval/credit/{{params.process.excode}}&lt;br/&gt;（请复制链接后在浏览器内打开）&lt;br/&gt;Qiniu Process Management System"
            }'
        method="post"
        authRef="BearerAuth"
    />
    <restAction 
        id="restToUpdateSofa" 
        url="https://crm.qiniu.io/sf/api/v1/admin/sobject/status" 
        method="put" 
        authRef="BearerAuth" 
        header='{"Content-Type":"application/json"}' 
        body='{
			"sobject":"Credit_Apply__c",
			"key":"{{params.process.excode}}",
			"status":{{params.flow.status}},
			"status_field":"Approval_Status__c"
		}'
    />
    <restAction
        id="restToCreateCredit"
        url="http://pay-credit.bo.internal.qiniu.io/credits"
        method="post"
        header='{"Content-Type":"application/json"}'
        body='{
            "credit_line":{{params.data.credit_amount * 10000}},
            "uid":{{params.data.uid}},
            "status":2,
            "remark":"create from approval flow",
            "credit_period":{{params.data.account_period}},
            "period_unit":{{params.data.account_period_unit}},
            "customer_name":"{{params.data.account_name}}"
        }'
    />

    <process id="apply_credit" name="信用额度申请流程">
        <startEvent id="theStart" name="提交审批"/>
        <sequenceFlow id="toGetStarter" sourceRef="theStart" targetRef="getStarter"/>

        <serviceTask id="getStarter" name="getStarter" actionRef="restToGetStarter"/>
        <sequenceFlow id="toGetStarterManager" sourceRef="getStarter" targetRef="getStarterManager"/>

        <serviceTask id="getStarterManager" name="getStarterManager" actionRef="restToGetStarterManager"/>
        <sequenceFlow id="toInitAssignee" sourceRef="getStarterManager" targetRef="initAssignee"/>

        <serviceTask id="initAssignee" name="initAssignee" actionRef="restToInitAssignee"/>
        <sequenceFlow id="toProfileCheck" sourceRef="initAssignee" targetRef="profileCheck"/>

        <exclusiveGateway id="profileCheck" name="profileCheck" defaultFlow="profileCheckToFinanceManager"/>
        <sequenceFlow id="toSubSalesManager" sourceRef="profileCheck" targetRef="subSalesManager">
            <conditionExpression xsi:type="tFormalExpression">params.starter_manager.profile == "区域销售经理"</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="toSalesManager" sourceRef="profileCheck" targetRef="salesManager">
            <conditionExpression xsi:type="tFormalExpression">params.starter_manager.profile == "区域销售总监"</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="profileCheckToForkManager" sourceRef="profileCheck" targetRef="forkManager"/>

        <userTask id="subSalesManager" name="直属经理审批" assignee="restToGetAssigneeManager" assigneeType="dynamic">
            <listener id="subManagerListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="subManagerWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="subSalesManagerToForkManager" sourceRef="subSalesManager" targetRef="forkManager"/>

        <userTask id="salesManager" name="销售总监审批" assignee="restToGetAssigneeManager" assigneeType="dynamic">
            <listener id="managerListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="managerWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="salesManagerToForkManager" sourceRef="salesManager" targetRef="forkManager"/>
        
        <parallelGateway id="forkManager" />
        <sequenceFlow id="forkToFinanceManager" sourceRef="forkManager" targetRef="financeManager"/>
        <sequenceFlow id="forkToCreditManager" sourceRef="forkManager" targetRef="creditManager"/>

        <userTask id="financeManager" name="财务经理审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="financeListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="financeWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="toForkMoreThan100Thousand" sourceRef="financeManager" targetRef="forkMoreThan100Thousand"/>

        <userTask id="creditManager" name="风控负责人审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="creditManagerListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="creditManagerWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="toForkMoreThan100Thousand" sourceRef="creditManager" targetRef="forkMoreThan100Thousand"/>

        <exclusiveGateway id="forkMoreThan100Thousand" name="forkMoreThan100Thousand" defaultFlow="forkMoreThan100ThousandToEnd"/>
        <sequenceFlow id="moreThan100Thousand" sourceRef="forkMoreThan100Thousand" targetRef="salesVP">
            <conditionExpression xsi:type="tFormalExpression">params.data.credit_amount > 100000</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkMoreThan100ThousandToEnd" sourceRef="forkMoreThan100Thousand" targetRef="theEnd"/>

        <userTask id="salesVP" name="销售负责人审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="vpListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="vpWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="salesVpToJoin" sourceRef="salesVP" targetRef="forkMoreThan1000Thousand"/>

        <exclusiveGateway id="forkMoreThan1000Thousand" name="forkMoreThan1000Thousand" defaultFlow="forkMoreThan1000ThousandToEnd"/>
        <sequenceFlow id="toCFO" sourceRef="forkMoreThan1000Thousand" targetRef="cfo">
            <conditionExpression xsi:type="tFormalExpression">params.data.credit_amount > 1000000</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkMoreThan1000ThousandToEnd" sourceRef="forkMoreThan1000Thousand" targetRef="theEnd"/>

        <userTask id="cfo" name="CFO" assignee="<EMAIL>" assigneeType="static">
            <listener id="cfoListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="cfoWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="cfoToJoin" sourceRef="cfo" targetRef="forkMoreThan3000Thousand"/>

        <exclusiveGateway id="forkMoreThan3000Thousand" name="forkMoreThan3000Thousand" defaultFlow="forkMoreThan3000ThousandToEnd"/>
        <sequenceFlow id="toBO" sourceRef="forkMoreThan3000Thousand" targetRef="bo">
            <conditionExpression xsi:type="tFormalExpression">params.data.credit_amount > 3000000</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkMoreThan3000ThousandToEnd" sourceRef="forkMoreThan3000Thousand" targetRef="theEnd"/>

        <userTask id="bo" name="商业运营审批" multiDecision="any" assignee="<EMAIL>,<EMAIL>" assigneeType="static">
            <listener id="boListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="boWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="boToTheEnd" sourceRef="bo" targetRef="theEnd"/>

        <endEvent id="theEnd" name="审批结束">
            <listener id="completeSofaListener" fire="process.completion" actionRefs="restToUpdateSofa"/>
            <listener id="invokeCreditListener" fire="process.completion" actionRefs="restToCreateCredit"/>
            <listener id="failListener" fire="process.failure" actionRefs="restToUpdateSofa"/>
            <listener id="cancelListener" fire="process.cancellation" actionRefs="restToUpdateSofa"/>
        </endEvent>
    </process>

</definitions>
