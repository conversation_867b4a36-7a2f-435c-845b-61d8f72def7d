<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:qbpm="https://qiniu.com" id="Definitions_Process_1667351337713" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1667351337713" name="trade-change-effect-time">
    <bpmn:startEvent id="Event_0n9trgu" name="销售提交-资源包改期-审批流程">
      <bpmn:outgoing>Flow_072s7v6</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0lea92v">
      <bpmn:incoming>Flow_072s7v6</bpmn:incoming>
      <bpmn:outgoing>Flow_16x1i5e</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m4rh2c</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hvlftv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1c53xtb</bpmn:outgoing>
      <bpmn:outgoing>Flow_1cqqeoz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1swe4l0</bpmn:outgoing>
      <bpmn:outgoing>Flow_1bhaoc7</bpmn:outgoing>
      <bpmn:outgoing>Flow_12z5k7a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_072s7v6" sourceRef="Event_0n9trgu" targetRef="Gateway_0lea92v" />
    <bpmn:endEvent id="Event_10pqsl1" name="审批结束">
      <bpmn:incoming>Flow_16x1i5e</bpmn:incoming>
      <bpmn:incoming>Flow_1m4rh2c</bpmn:incoming>
      <bpmn:incoming>Flow_1hvlftv</bpmn:incoming>
      <bpmn:incoming>Flow_1c53xtb</bpmn:incoming>
      <bpmn:incoming>Flow_1cqqeoz</bpmn:incoming>
      <bpmn:incoming>Flow_1swe4l0</bpmn:incoming>
      <bpmn:incoming>Flow_1bhaoc7</bpmn:incoming>
      <bpmn:incoming>Flow_12z5k7a</bpmn:incoming>
      <qbpm:listener fire="process.completion" actionRefs="tradeUnifyApproved" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_16x1i5e" name="存储 订单" sourceRef="Gateway_0lea92v" targetRef="Event_10pqsl1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==39</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1m4rh2c" name="CDN 订单" sourceRef="Gateway_0lea92v" targetRef="Event_10pqsl1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==45</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1hvlftv" name="内容安全 订单" sourceRef="Gateway_0lea92v" targetRef="Event_10pqsl1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==51</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1c53xtb" name="直播 订单" sourceRef="Gateway_0lea92v" targetRef="Event_10pqsl1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==60</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1cqqeoz" name="云短信 订单" sourceRef="Gateway_0lea92v" targetRef="Event_10pqsl1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==72</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1swe4l0" name="DORA 订单" sourceRef="Gateway_0lea92v" targetRef="Event_10pqsl1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==78</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1bhaoc7" name="RTC 订单" sourceRef="Gateway_0lea92v" targetRef="Event_10pqsl1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==79</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_12z5k7a" name="视频监控 订单" sourceRef="Gateway_0lea92v" targetRef="Event_10pqsl1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==82</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1667351337713">
      <bpmndi:BPMNShape id="Event_0n9trgu_di" bpmnElement="Event_0n9trgu">
        <dc:Bounds x="822" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="812" y="138" width="59" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0lea92v_di" bpmnElement="Gateway_0lea92v" isMarkerVisible="true">
        <dc:Bounds x="815" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_10pqsl1_di" bpmnElement="Event_10pqsl1">
        <dc:Bounds x="822" y="732" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="818" y="775" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_072s7v6_di" bpmnElement="Flow_072s7v6">
        <di:waypoint x="840" y="198" />
        <di:waypoint x="840" y="245" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16x1i5e_di" bpmnElement="Flow_16x1i5e">
        <di:waypoint x="815" y="270" />
        <di:waypoint x="440" y="270" />
        <di:waypoint x="440" y="750" />
        <di:waypoint x="822" y="750" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="416" y="492" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m4rh2c_di" bpmnElement="Flow_1m4rh2c">
        <di:waypoint x="815" y="270" />
        <di:waypoint x="590" y="270" />
        <di:waypoint x="590" y="750" />
        <di:waypoint x="822" y="750" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="565" y="492" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hvlftv_di" bpmnElement="Flow_1hvlftv">
        <di:waypoint x="840" y="295" />
        <di:waypoint x="840" y="732" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="807" y="492" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c53xtb_di" bpmnElement="Flow_1c53xtb">
        <di:waypoint x="815" y="270" />
        <di:waypoint x="740" y="270" />
        <di:waypoint x="740" y="750" />
        <di:waypoint x="822" y="750" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="716" y="491" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cqqeoz_di" bpmnElement="Flow_1cqqeoz">
        <di:waypoint x="865" y="270" />
        <di:waypoint x="940" y="270" />
        <di:waypoint x="940" y="750" />
        <di:waypoint x="858" y="750" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="910" y="492" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1swe4l0_di" bpmnElement="Flow_1swe4l0">
        <di:waypoint x="865" y="270" />
        <di:waypoint x="1040" y="270" />
        <di:waypoint x="1040" y="750" />
        <di:waypoint x="858" y="750" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1011" y="493" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bhaoc7_di" bpmnElement="Flow_1bhaoc7">
        <di:waypoint x="865" y="270" />
        <di:waypoint x="1150" y="270" />
        <di:waypoint x="1150" y="750" />
        <di:waypoint x="858" y="750" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1126" y="492" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12z5k7a_di" bpmnElement="Flow_12z5k7a">
        <di:waypoint x="865" y="270" />
        <di:waypoint x="1270" y="270" />
        <di:waypoint x="1270" y="750" />
        <di:waypoint x="858" y="750" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1235" y="493" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
