<?xml version="1.0" encoding="UTF-8"?>
<definitions id="definitions"
             xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <auth id="BearerAuth" type="Oauth" tokenKey="params.access_token" prefix="Bearer">
        <restAction
                id="token"
                url="http://acc.jfcs.qiniu.io/oauth2/token"
                method="post"
                body='{"grant_type":"password", "username":"root", "password":"root"}'
        />
    </auth>
    <restAction
            id="restToGetApprovers"
            url="http://bo-staging-traded.qa.qiniu.io/order/mixed/new/approvers"
            method="post"
            authRef="BearerAuth"
            header='{"Content-Type":"application/json"}'
            parseKey="params.approvers"
            parseFirst="true"
            body='{{params.data}}'
    />
    <restAction
            id="restToTradeCreateOrder"
            url="http://bo-staging-traded.qa.qiniu.io/order/mixed/new/approve"
            method="post"
            authRef="BearerAuth"
            maxRetry="1"
            header='{"Content-Type":"application/json"}'
            body='{{params.data}}'
    />

    <restAction
            id="assignNotify"
            url="http://bo-staging-morse.qa.qiniu.io/api/notification/send/mail"
            header='{"Client-Id":"59278d3b43c8ce6ad600182e","Content-Type":"application/json"}'
            body='{
            "uid":0,
            "to":["{{params.execute.original_actor}}"],
            "subject":"下单审批请求",
            "content":"您好，&lt;br/&gt;{{params.process.start_by_id}}请求您批准以下项目：&lt;br/&gt;&lt;a href=&apos;https://portal.qiniu.io/price-biz/approval/order/{{params.process.excode}}&apos;&gt;https://portal.qiniu.io/price-biz/approval/order/{{params.process.excode}}&lt;/a&gt;&lt;br/&gt;请单击该链接批准或拒绝该记录。&lt;br/&gt;&lt;br/&gt;十分感谢！&lt;br/&gt;Qiniu Process Management System"
            }'
            method="post"
            authRef="BearerAuth"
    />
    <restAction
            id="assignWechatNotify"
            url="https://ts.qiniu.io/action/internal/wechat/msg/name"
            body='{
            "user_name":"{{params.execute.original_actor.substr(0,params.execute.original_actor.lastIndexOf("@"))}}",
            "agent_id":"1000017",
            "msg":"您好，{{params.process.start_by_id}}请求您批准下单申请：&lt;br/&gt;https://portal.qiniu.io/price-biz/approval/order/{{params.process.excode}}&lt;br/&gt;（请复制链接后在浏览器内打开）&lt;br/&gt;Qiniu Process Management System"
            }'
            method="post"
            authRef="BearerAuth"
    />

    <process id="approve-order" name="预下单申请流程">
        <startEvent id="theStart" name="销售提交审批"/>

        <sequenceFlow id="toApproval" sourceRef="theStart" targetRef="toProductApproval"/>

        <userTask id="toProductApproval" name="产品线审批" multiDecision="all" isSequential="true" assignee="restToGetApprovers"  assigneeType="dynamic">
            <listener id="financeListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="financeWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>

        <sequenceFlow id="toEnd" sourceRef="toProductApproval" targetRef="theEnd"/>

        <endEvent id="theEnd" name="审批结束">
            <listener id="invokeTradeListener" fire="process.completion" actionRefs="restToTradeCreateOrder"/>
            <listener id="failListener" fire="process.failure" actionRefs="assignNotify"/>
            <listener id="failListener" fire="process.failure" actionRefs="assignWechatNotify"/>
            <listener id="cancelListener" fire="process.cancellation" actionRefs="assignNotify"/>
            <listener id="cancelListener" fire="process.cancellation" actionRefs="assignWechatNotify"/>
        </endEvent>
    </process>
</definitions>
