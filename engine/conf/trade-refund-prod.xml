<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:qbpm="https://qiniu.com" id="Definitions_Process_1667351337713" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1667351337713" name="订单-退款">
    <bpmn:startEvent id="Event_0n9trgu" name="销售提交-退款-审批流程">
      <bpmn:outgoing>Flow_072s7v6</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0lea92v">
      <bpmn:incoming>Flow_072s7v6</bpmn:incoming>
      <bpmn:outgoing>Flow_053ukyi</bpmn:outgoing>
      <bpmn:outgoing>Flow_16x1i5e</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m4rh2c</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hvlftv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1c53xtb</bpmn:outgoing>
      <bpmn:outgoing>Flow_1cqqeoz</bpmn:outgoing>
      <bpmn:outgoing>Flow_0l7s7gs</bpmn:outgoing>
      <bpmn:outgoing>Flow_1swe4l0</bpmn:outgoing>
      <bpmn:outgoing>Flow_1bhaoc7</bpmn:outgoing>
      <bpmn:outgoing>Flow_12z5k7a</bpmn:outgoing>
      <bpmn:outgoing>Flow_1jvlsoq</bpmn:outgoing>
      <bpmn:outgoing>Flow_19zwc1n</bpmn:outgoing>
      <bpmn:outgoing>Flow_0beptpg</bpmn:outgoing>
      <bpmn:outgoing>Flow_09h7m2w</bpmn:outgoing>
      <bpmn:outgoing>Flow_0mtnsvb</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rxcicd</bpmn:outgoing>
      <bpmn:outgoing>Flow_0a6p7dp</bpmn:outgoing>
      <bpmn:outgoing>Flow_0arzfy6</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k966ka</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_072s7v6" sourceRef="Event_0n9trgu" targetRef="Gateway_0lea92v" />
    <bpmn:sequenceFlow id="Flow_053ukyi" name="SSL证书订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0zc7ew5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==33</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_0zc7ew5" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_053ukyi</bpmn:incoming>
      <bpmn:outgoing>Flow_0rneqh4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="Event_10pqsl1" name="审批结束">
      <bpmn:incoming>Flow_17iw9av</bpmn:incoming>
      <bpmn:incoming>Flow_01kk6qd</bpmn:incoming>
      <bpmn:incoming>Flow_191k85m</bpmn:incoming>
      <bpmn:incoming>Flow_1mgo2oc</bpmn:incoming>
      <bpmn:incoming>Flow_07s4y0s</bpmn:incoming>
      <bpmn:incoming>Flow_05kkbm7</bpmn:incoming>
      <bpmn:incoming>Flow_0uafxkb</bpmn:incoming>
      <bpmn:incoming>Flow_1wbejg3</bpmn:incoming>
      <bpmn:incoming>Flow_0j9fy99</bpmn:incoming>
      <bpmn:incoming>Flow_0lswbve</bpmn:incoming>
      <bpmn:incoming>Flow_0be446k</bpmn:incoming>
      <bpmn:incoming>Flow_0k5h7p4</bpmn:incoming>
      <bpmn:incoming>Flow_01pwo8n</bpmn:incoming>
      <bpmn:incoming>Flow_1aka80s</bpmn:incoming>
      <bpmn:incoming>Flow_0tn2gex</bpmn:incoming>
      <bpmn:incoming>Flow_1l52pt2</bpmn:incoming>
      <bpmn:incoming>Flow_11v997c</bpmn:incoming>
      <bpmn:incoming>Flow_0e7po8e</bpmn:incoming>
      <bpmn:incoming>Flow_1k92dok</bpmn:incoming>
      <bpmn:incoming>Flow_1xm0am5</bpmn:incoming>
      <qbpm:listener id="listener_1668515455685" fire="process.completion" actionRefs="tradeUnifyApproved" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0rneqh4" sourceRef="Activity_0zc7ew5" targetRef="Activity_0zq567m" />
    <bpmn:userTask id="Activity_1sd194e" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_16x1i5e</bpmn:incoming>
      <bpmn:outgoing>Flow_11aj97c</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_16x1i5e" name="存储 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_1sd194e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==39</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_11aj97c" sourceRef="Activity_1sd194e" targetRef="Activity_0jvfpbl" />
    <bpmn:userTask id="Activity_0jvfpbl" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="false" qbpm:assigneeType="label" qbpm:assignee="kodo-product-approver-2">
      <bpmn:incoming>Flow_11aj97c</bpmn:incoming>
      <bpmn:outgoing>Flow_17iw9av</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_17iw9av" sourceRef="Activity_0jvfpbl" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_0ale989" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1m4rh2c</bpmn:incoming>
      <bpmn:outgoing>Flow_10ab3zp</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_04szbae" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="false" qbpm:assigneeType="label" qbpm:assignee="cdn-product-approver-2">
      <bpmn:incoming>Flow_1aves9y</bpmn:incoming>
      <bpmn:outgoing>Flow_01kk6qd</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_10ab3zp" sourceRef="Activity_0ale989" targetRef="Gateway_1vbf84p" />
    <bpmn:sequenceFlow id="Flow_1m4rh2c" name="CDN 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0ale989">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==45</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_01kk6qd" sourceRef="Activity_04szbae" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_17b4y10" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1hvlftv</bpmn:incoming>
      <bpmn:outgoing>Flow_11xvysl</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_00etjle" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label" qbpm:assignee="ava-product-approver-2">
      <bpmn:incoming>Flow_11xvysl</bpmn:incoming>
      <bpmn:outgoing>Flow_191k85m</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_11xvysl" sourceRef="Activity_17b4y10" targetRef="Activity_00etjle" />
    <bpmn:sequenceFlow id="Flow_1hvlftv" name="内容安全 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_17b4y10">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==51</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_191k85m" sourceRef="Activity_00etjle" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_1iqt7u9" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1cqqeoz</bpmn:incoming>
      <bpmn:outgoing>Flow_1bllmjp</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_01k2ws0" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="false" qbpm:assigneeType="label" qbpm:assignee="sms-product-approver">
      <bpmn:incoming>Flow_1bllmjp</bpmn:incoming>
      <bpmn:outgoing>Flow_1mgo2oc</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1bllmjp" sourceRef="Activity_1iqt7u9" targetRef="Activity_01k2ws0" />
    <bpmn:userTask id="Activity_0k6oejj" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1c53xtb</bpmn:incoming>
      <bpmn:outgoing>Flow_0df3pvf</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1c53xtb" name="直播 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0k6oejj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==60</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0df3pvf" sourceRef="Activity_0k6oejj" targetRef="Activity_1pd6s5j" />
    <bpmn:sequenceFlow id="Flow_1cqqeoz" name="云短信 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_1iqt7u9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==72</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1mgo2oc" sourceRef="Activity_01k2ws0" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_15a5ls1" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1swe4l0</bpmn:incoming>
      <bpmn:outgoing>Flow_0auolkp</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_13sgqxb" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label" qbpm:assignee="dora-product-approver-2">
      <bpmn:incoming>Flow_0auolkp</bpmn:incoming>
      <bpmn:outgoing>Flow_07s4y0s</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0auolkp" sourceRef="Activity_15a5ls1" targetRef="Activity_13sgqxb" />
    <bpmn:userTask id="Activity_0fx5cw4" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_0l7s7gs</bpmn:incoming>
      <bpmn:outgoing>Flow_148jyd6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0l7s7gs" name="短视频SDK  订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0fx5cw4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==75</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_148jyd6" sourceRef="Activity_0fx5cw4" targetRef="Activity_115d271" />
    <bpmn:sequenceFlow id="Flow_1swe4l0" name="DORA 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_15a5ls1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==78</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07s4y0s" sourceRef="Activity_13sgqxb" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_0ell9f7" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1bhaoc7</bpmn:incoming>
      <bpmn:outgoing>Flow_1m6vs59</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1bhaoc7" name="RTC 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0ell9f7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==79</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1m6vs59" sourceRef="Activity_0ell9f7" targetRef="Activity_08q9zh0" />
    <bpmn:userTask id="Activity_1azhzv0" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_12z5k7a</bpmn:incoming>
      <bpmn:outgoing>Flow_1dsji0w</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_12z5k7a" name="视频监控 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_1azhzv0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==82</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1dsji0w" sourceRef="Activity_1azhzv0" targetRef="Activity_1eqhi5o" />
    <bpmn:userTask id="Activity_0fjhimo" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1jvlsoq</bpmn:incoming>
      <bpmn:outgoing>Flow_1lpx39i</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0uzgzz8" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label" qbpm:assignee="apaas-product-approver">
      <bpmn:incoming>Flow_1lpx39i</bpmn:incoming>
      <bpmn:outgoing>Flow_05kkbm7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1lpx39i" sourceRef="Activity_0fjhimo" targetRef="Activity_0uzgzz8" />
    <bpmn:userTask id="Activity_1tmnb3h" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_19zwc1n</bpmn:incoming>
      <bpmn:outgoing>Flow_1lmldnq</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_1fo9myt" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label" qbpm:assignee="apaas-product-approver">
      <bpmn:incoming>Flow_1lmldnq</bpmn:incoming>
      <bpmn:outgoing>Flow_0uafxkb</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1lmldnq" sourceRef="Activity_1tmnb3h" targetRef="Activity_1fo9myt" />
    <bpmn:userTask id="Activity_06jzdf0" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_0beptpg</bpmn:incoming>
      <bpmn:outgoing>Flow_1heybme</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0qrd8lb" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="false" qbpm:assigneeType="label" qbpm:assignee="sdk-product-approver">
      <bpmn:incoming>Flow_1heybme</bpmn:incoming>
      <bpmn:outgoing>Flow_1wbejg3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1heybme" sourceRef="Activity_06jzdf0" targetRef="Activity_0qrd8lb" />
    <bpmn:userTask id="Activity_0xwa9k3" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_09h7m2w</bpmn:incoming>
      <bpmn:outgoing>Flow_0129e03</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0129e03" sourceRef="Activity_0xwa9k3" targetRef="Activity_04h4w36" />
    <bpmn:sequenceFlow id="Flow_1jvlsoq" name="企业直播 aPaaS-01" sourceRef="Gateway_0lea92v" targetRef="Activity_0fjhimo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==94</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_19zwc1n" name="企业直播 aPaaS-02" sourceRef="Gateway_0lea92v" targetRef="Activity_1tmnb3h">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==98</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0beptpg" name="SDK 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_06jzdf0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==99</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_09h7m2w" name="Kodo-Found 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0xwa9k3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==97</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_05kkbm7" sourceRef="Activity_0uzgzz8" targetRef="Event_10pqsl1" />
    <bpmn:sequenceFlow id="Flow_0uafxkb" sourceRef="Activity_1fo9myt" targetRef="Event_10pqsl1" />
    <bpmn:sequenceFlow id="Flow_1wbejg3" sourceRef="Activity_0qrd8lb" targetRef="Event_10pqsl1" />
    <bpmn:sequenceFlow id="Flow_0j9fy99" sourceRef="Activity_1j5ca8n" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_1j5ca8n" name="特定审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="static" qbpm:assignee="<EMAIL>">
      <bpmn:incoming>Flow_1phgddy</bpmn:incoming>
      <bpmn:outgoing>Flow_0j9fy99</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0zq567m" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="false" qbpm:assigneeType="label" qbpm:assignee="ssl-product-approver">
      <bpmn:incoming>Flow_0rneqh4</bpmn:incoming>
      <bpmn:outgoing>Flow_0lswbve</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0lswbve" sourceRef="Activity_0zq567m" targetRef="Event_10pqsl1" />
    <bpmn:exclusiveGateway id="Gateway_1vbf84p" default="Flow_0be446k">
      <bpmn:incoming>Flow_10ab3zp</bpmn:incoming>
      <bpmn:outgoing>Flow_1aves9y</bpmn:outgoing>
      <bpmn:outgoing>Flow_0be446k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1aves9y" name="退款金额&#62;200" sourceRef="Gateway_1vbf84p" targetRef="Activity_04szbae">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.refund_cash&gt;200</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0be446k" sourceRef="Gateway_1vbf84p" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_1pd6s5j" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="false" qbpm:assigneeType="label" qbpm:assignee="pili-product-approver-2">
      <bpmn:incoming>Flow_0df3pvf</bpmn:incoming>
      <bpmn:outgoing>Flow_0k5h7p4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0k5h7p4" sourceRef="Activity_1pd6s5j" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_115d271" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="false" qbpm:assigneeType="label" qbpm:assignee="sdk-product-approver">
      <bpmn:incoming>Flow_148jyd6</bpmn:incoming>
      <bpmn:outgoing>Flow_01pwo8n</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_01pwo8n" sourceRef="Activity_115d271" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_08q9zh0" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label" qbpm:assignee="rtc-product-approver">
      <bpmn:incoming>Flow_1m6vs59</bpmn:incoming>
      <bpmn:outgoing>Flow_1aka80s</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1aka80s" sourceRef="Activity_08q9zh0" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_1eqhi5o" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label" qbpm:assignee="qvs-product-approver">
      <bpmn:incoming>Flow_1dsji0w</bpmn:incoming>
      <bpmn:outgoing>Flow_0tn2gex</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0tn2gex" sourceRef="Activity_1eqhi5o" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_04h4w36" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="static" qbpm:assignee="<EMAIL>">
      <bpmn:incoming>Flow_0129e03</bpmn:incoming>
      <bpmn:outgoing>Flow_1phgddy</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1phgddy" sourceRef="Activity_04h4w36" targetRef="Activity_1j5ca8n" />
    <bpmn:sequenceFlow id="Flow_1l52pt2" sourceRef="Activity_0piquv6" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_0piquv6" name="产线审批" qbpm:multiDecision="any" qbpm:assigneeType="label" qbpm:assignee="bo-billing-test">
      <bpmn:incoming>Flow_0mtnsvb</bpmn:incoming>
      <bpmn:outgoing>Flow_1l52pt2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0mtnsvb" name="63 测试商家" sourceRef="Gateway_0lea92v" targetRef="Activity_0piquv6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==63</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_19v9yzu" name="产线审批" qbpm:multiDecision="any" qbpm:assigneeType="static" qbpm:assignee="<EMAIL>">
      <bpmn:incoming>Flow_0rxcicd</bpmn:incoming>
      <bpmn:outgoing>Flow_11v997c</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_11v997c" sourceRef="Activity_19v9yzu" targetRef="Event_10pqsl1" />
    <bpmn:sequenceFlow id="Flow_0rxcicd" name="存储数据恢复订单" sourceRef="Gateway_0lea92v" targetRef="Activity_19v9yzu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==103</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_1hbaox0" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_0a6p7dp</bpmn:incoming>
      <bpmn:outgoing>Flow_15x6ftf</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_07cryiw" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label" qbpm:assignee="apaas-product-approver">
      <bpmn:incoming>Flow_15x6ftf</bpmn:incoming>
      <bpmn:outgoing>Flow_0e7po8e</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_15x6ftf" sourceRef="Activity_1hbaox0" targetRef="Activity_07cryiw" />
    <bpmn:sequenceFlow id="Flow_0e7po8e" sourceRef="Activity_07cryiw" targetRef="Event_10pqsl1" />
    <bpmn:sequenceFlow id="Flow_0a6p7dp" name="企业直播 aPaaS-03" sourceRef="Gateway_0lea92v" targetRef="Activity_1hbaox0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==104</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_0lt5tj9" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="false" qbpm:assigneeType="label" qbpm:assignee="cloud-security-approver">
      <bpmn:incoming>Flow_198uf7m</bpmn:incoming>
      <bpmn:outgoing>Flow_1k92dok</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0upbwb8" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_0arzfy6</bpmn:incoming>
      <bpmn:outgoing>Flow_198uf7m</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_198uf7m" sourceRef="Activity_0upbwb8" targetRef="Activity_0lt5tj9" />
    <bpmn:sequenceFlow id="Flow_1k92dok" sourceRef="Activity_0lt5tj9" targetRef="Event_10pqsl1" />
    <bpmn:sequenceFlow id="Flow_0arzfy6" name="云安全中心订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0upbwb8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==105</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_0y3rf27" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1k966ka</bpmn:incoming>
      <bpmn:outgoing>Flow_0nciyn5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0ajkj25" name="产线审批" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label" qbpm:assignee="ums-product-approver">
      <bpmn:incoming>Flow_0nciyn5</bpmn:incoming>
      <bpmn:outgoing>Flow_1xm0am5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0nciyn5" sourceRef="Activity_0y3rf27" targetRef="Activity_0ajkj25" />
    <bpmn:sequenceFlow id="Flow_1xm0am5" sourceRef="Activity_0ajkj25" targetRef="Event_10pqsl1" />
    <bpmn:sequenceFlow id="Flow_1k966ka" name="统一消息订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0y3rf27">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==106</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1667351337713">
      <bpmndi:BPMNShape id="Event_0n9trgu_di" bpmnElement="Event_0n9trgu">
        <dc:Bounds x="672" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="653" y="126" width="74" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0lea92v_di" bpmnElement="Gateway_0lea92v" isMarkerVisible="true">
        <dc:Bounds x="665" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ribm44_di" bpmnElement="Activity_0zc7ew5">
        <dc:Bounds x="240" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_10pqsl1_di" bpmnElement="Event_10pqsl1">
        <dc:Bounds x="672" y="862" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="668" y="838" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ehz1yy_di" bpmnElement="Activity_1sd194e">
        <dc:Bounds x="363" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w36ang_di" bpmnElement="Activity_0jvfpbl">
        <dc:Bounds x="363" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1idf6ff" bpmnElement="Activity_0ale989">
        <dc:Bounds x="540" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_098yzrm" bpmnElement="Activity_04szbae">
        <dc:Bounds x="480" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pnah05" bpmnElement="Activity_17b4y10">
        <dc:Bounds x="680" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bj0k2l" bpmnElement="Activity_00etjle">
        <dc:Bounds x="680" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ajmdo0" bpmnElement="Activity_1iqt7u9">
        <dc:Bounds x="970" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_17ge08v" bpmnElement="Activity_01k2ws0">
        <dc:Bounds x="970" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_19yy48f" bpmnElement="Activity_0k6oejj">
        <dc:Bounds x="820" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0llcv1u" bpmnElement="Activity_15a5ls1">
        <dc:Bounds x="1250" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1252zca" bpmnElement="Activity_13sgqxb">
        <dc:Bounds x="1250" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1n1fm6t" bpmnElement="Activity_0fx5cw4">
        <dc:Bounds x="1110" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1pff192" bpmnElement="Activity_0ell9f7">
        <dc:Bounds x="1390" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1d2muxa" bpmnElement="Activity_1azhzv0">
        <dc:Bounds x="1520" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1acvro3" bpmnElement="Activity_0fjhimo">
        <dc:Bounds x="1650" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0gdxzd2" bpmnElement="Activity_0uzgzz8">
        <dc:Bounds x="1650" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0f7u8ar" bpmnElement="Activity_1tmnb3h">
        <dc:Bounds x="1780" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_177kvy8" bpmnElement="Activity_1fo9myt">
        <dc:Bounds x="1780" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0dfil0r" bpmnElement="Activity_06jzdf0">
        <dc:Bounds x="2024" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ugmwxp" bpmnElement="Activity_0qrd8lb">
        <dc:Bounds x="2024" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1p18by8" bpmnElement="Activity_0xwa9k3">
        <dc:Bounds x="2154" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01ot0po_di" bpmnElement="Activity_1j5ca8n">
        <dc:Bounds x="2154" y="710" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0mjpgu2" bpmnElement="Activity_0zq567m">
        <dc:Bounds x="240" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vbf84p_di" bpmnElement="Gateway_1vbf84p" isMarkerVisible="true">
        <dc:Bounds x="565" y="475" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10yoab5" bpmnElement="Activity_1pd6s5j">
        <dc:Bounds x="820" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0b4sgwu" bpmnElement="Activity_115d271">
        <dc:Bounds x="1110" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0v1y7xg" bpmnElement="Activity_08q9zh0">
        <dc:Bounds x="1390" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_01wroia" bpmnElement="Activity_1eqhi5o">
        <dc:Bounds x="1520" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_09krpe4" bpmnElement="Activity_04h4w36">
        <dc:Bounds x="2154" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0rmishu_di" bpmnElement="Activity_0piquv6">
        <dc:Bounds x="-250" y="530" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0llp8w9_di" bpmnElement="Activity_19v9yzu">
        <dc:Bounds x="-70" y="530" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0cnk3n6" bpmnElement="Activity_1hbaox0">
        <dc:Bounds x="1900" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_08d4zet" bpmnElement="Activity_07cryiw">
        <dc:Bounds x="1900" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0hl55si" bpmnElement="Activity_0lt5tj9">
        <dc:Bounds x="80" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0dunjqz" bpmnElement="Activity_0upbwb8">
        <dc:Bounds x="80" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16wkry4" bpmnElement="Activity_0y3rf27">
        <dc:Bounds x="2350" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0spiuz4" bpmnElement="Activity_0ajkj25">
        <dc:Bounds x="2350" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_072s7v6_di" bpmnElement="Flow_072s7v6">
        <di:waypoint x="690" y="198" />
        <di:waypoint x="690" y="245" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_053ukyi_di" bpmnElement="Flow_053ukyi">
        <di:waypoint x="665" y="270" />
        <di:waypoint x="290" y="270" />
        <di:waypoint x="290" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="257" y="313" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rneqh4_di" bpmnElement="Flow_0rneqh4">
        <di:waypoint x="290" y="460" />
        <di:waypoint x="290" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16x1i5e_di" bpmnElement="Flow_16x1i5e">
        <di:waypoint x="665" y="270" />
        <di:waypoint x="413" y="270" />
        <di:waypoint x="413" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="389" y="313" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11aj97c_di" bpmnElement="Flow_11aj97c">
        <di:waypoint x="413" y="460" />
        <di:waypoint x="413" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17iw9av_di" bpmnElement="Flow_17iw9av">
        <di:waypoint x="413" y="620" />
        <di:waypoint x="413" y="880" />
        <di:waypoint x="672" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1fhmxoc" bpmnElement="Flow_10ab3zp">
        <di:waypoint x="590" y="460" />
        <di:waypoint x="590" y="475" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m4rh2c_di" bpmnElement="Flow_1m4rh2c">
        <di:waypoint x="665" y="270" />
        <di:waypoint x="590" y="270" />
        <di:waypoint x="590" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="565" y="313" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01kk6qd_di" bpmnElement="Flow_01kk6qd">
        <di:waypoint x="530" y="620" />
        <di:waypoint x="530" y="880" />
        <di:waypoint x="672" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_01noujn" bpmnElement="Flow_11xvysl">
        <di:waypoint x="730" y="460" />
        <di:waypoint x="730" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hvlftv_di" bpmnElement="Flow_1hvlftv">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="730" y="270" />
        <di:waypoint x="730" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="688" y="313" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_191k85m_di" bpmnElement="Flow_191k85m">
        <di:waypoint x="730" y="620" />
        <di:waypoint x="730" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0gpdht0" bpmnElement="Flow_1bllmjp">
        <di:waypoint x="1020" y="460" />
        <di:waypoint x="1020" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c53xtb_di" bpmnElement="Flow_1c53xtb">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="870" y="270" />
        <di:waypoint x="870" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="846" y="313" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0df3pvf_di" bpmnElement="Flow_0df3pvf">
        <di:waypoint x="870" y="460" />
        <di:waypoint x="870" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cqqeoz_di" bpmnElement="Flow_1cqqeoz">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="1020" y="270" />
        <di:waypoint x="1020" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="985" y="313" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mgo2oc_di" bpmnElement="Flow_1mgo2oc">
        <di:waypoint x="1020" y="620" />
        <di:waypoint x="1020" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_16mifm1" bpmnElement="Flow_0auolkp">
        <di:waypoint x="1300" y="460" />
        <di:waypoint x="1300" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l7s7gs_di" bpmnElement="Flow_0l7s7gs">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="1160" y="270" />
        <di:waypoint x="1160" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1101" y="313" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_148jyd6_di" bpmnElement="Flow_148jyd6">
        <di:waypoint x="1160" y="460" />
        <di:waypoint x="1160" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1swe4l0_di" bpmnElement="Flow_1swe4l0">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="1300" y="270" />
        <di:waypoint x="1300" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1267" y="313" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07s4y0s_di" bpmnElement="Flow_07s4y0s">
        <di:waypoint x="1300" y="620" />
        <di:waypoint x="1300" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bhaoc7_di" bpmnElement="Flow_1bhaoc7">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="1440" y="270" />
        <di:waypoint x="1440" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1416" y="313" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m6vs59_di" bpmnElement="Flow_1m6vs59">
        <di:waypoint x="1440" y="460" />
        <di:waypoint x="1440" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12z5k7a_di" bpmnElement="Flow_12z5k7a">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="1570" y="270" />
        <di:waypoint x="1570" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1525" y="313" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dsji0w_di" bpmnElement="Flow_1dsji0w">
        <di:waypoint x="1570" y="460" />
        <di:waypoint x="1570" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1u0vva9" bpmnElement="Flow_1lpx39i">
        <di:waypoint x="1700" y="460" />
        <di:waypoint x="1700" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1r6bl5a" bpmnElement="Flow_1lmldnq">
        <di:waypoint x="1830" y="460" />
        <di:waypoint x="1830" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1f0jyr4" bpmnElement="Flow_1heybme">
        <di:waypoint x="2074" y="460" />
        <di:waypoint x="2074" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_185gw2q" bpmnElement="Flow_0129e03">
        <di:waypoint x="2204" y="460" />
        <di:waypoint x="2204" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jvlsoq_di" bpmnElement="Flow_1jvlsoq">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="1700" y="270" />
        <di:waypoint x="1700" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1651" y="307" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19zwc1n_di" bpmnElement="Flow_19zwc1n">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="1830" y="270" />
        <di:waypoint x="1830" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1778" y="307" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0beptpg_di" bpmnElement="Flow_0beptpg">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="2074" y="270" />
        <di:waypoint x="2074" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2050" y="313" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09h7m2w_di" bpmnElement="Flow_09h7m2w">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="2204" y="270" />
        <di:waypoint x="2204" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2161" y="313" width="86" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05kkbm7_di" bpmnElement="Flow_05kkbm7">
        <di:waypoint x="1700" y="620" />
        <di:waypoint x="1700" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uafxkb_di" bpmnElement="Flow_0uafxkb">
        <di:waypoint x="1830" y="620" />
        <di:waypoint x="1830" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wbejg3_di" bpmnElement="Flow_1wbejg3">
        <di:waypoint x="2074" y="620" />
        <di:waypoint x="2074" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0j9fy99_di" bpmnElement="Flow_0j9fy99">
        <di:waypoint x="2204" y="790" />
        <di:waypoint x="2204" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lswbve_di" bpmnElement="Flow_0lswbve">
        <di:waypoint x="290" y="620" />
        <di:waypoint x="290" y="880" />
        <di:waypoint x="672" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aves9y_di" bpmnElement="Flow_1aves9y">
        <di:waypoint x="565" y="500" />
        <di:waypoint x="530" y="500" />
        <di:waypoint x="530" y="540" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="485" y="486" width="69" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0be446k_di" bpmnElement="Flow_0be446k">
        <di:waypoint x="590" y="525" />
        <di:waypoint x="590" y="880" />
        <di:waypoint x="672" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k5h7p4_di" bpmnElement="Flow_0k5h7p4">
        <di:waypoint x="870" y="620" />
        <di:waypoint x="870" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01pwo8n_di" bpmnElement="Flow_01pwo8n">
        <di:waypoint x="1160" y="620" />
        <di:waypoint x="1160" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aka80s_di" bpmnElement="Flow_1aka80s">
        <di:waypoint x="1440" y="620" />
        <di:waypoint x="1440" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tn2gex_di" bpmnElement="Flow_0tn2gex">
        <di:waypoint x="1570" y="620" />
        <di:waypoint x="1570" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1phgddy_di" bpmnElement="Flow_1phgddy">
        <di:waypoint x="2204" y="620" />
        <di:waypoint x="2204" y="710" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1l52pt2_di" bpmnElement="Flow_1l52pt2">
        <di:waypoint x="-200" y="610" />
        <di:waypoint x="-200" y="880" />
        <di:waypoint x="672" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mtnsvb_di" bpmnElement="Flow_0mtnsvb">
        <di:waypoint x="665" y="270" />
        <di:waypoint x="-200" y="270" />
        <di:waypoint x="-200" y="530" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-230" y="311" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11v997c_di" bpmnElement="Flow_11v997c">
        <di:waypoint x="-20" y="610" />
        <di:waypoint x="-20" y="880" />
        <di:waypoint x="672" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rxcicd_di" bpmnElement="Flow_0rxcicd">
        <di:waypoint x="665" y="270" />
        <di:waypoint x="-20" y="270" />
        <di:waypoint x="-20" y="530" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-64" y="313" width="88" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_19l3jjq" bpmnElement="Flow_15x6ftf">
        <di:waypoint x="1950" y="460" />
        <di:waypoint x="1950" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e7po8e_di" bpmnElement="Flow_0e7po8e">
        <di:waypoint x="1950" y="620" />
        <di:waypoint x="1950" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a6p7dp_di" bpmnElement="Flow_0a6p7dp">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="1950" y="270" />
        <di:waypoint x="1950" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1918" y="306" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1rptzms" bpmnElement="Flow_198uf7m">
        <di:waypoint x="130" y="460" />
        <di:waypoint x="130" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k92dok_di" bpmnElement="Flow_1k92dok">
        <di:waypoint x="130" y="620" />
        <di:waypoint x="130" y="880" />
        <di:waypoint x="672" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0arzfy6_di" bpmnElement="Flow_0arzfy6">
        <di:waypoint x="665" y="270" />
        <di:waypoint x="130" y="270" />
        <di:waypoint x="130" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="91" y="313" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1fkyfyf" bpmnElement="Flow_0nciyn5">
        <di:waypoint x="2400" y="460" />
        <di:waypoint x="2400" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xm0am5_di" bpmnElement="Flow_1xm0am5">
        <di:waypoint x="2400" y="620" />
        <di:waypoint x="2400" y="880" />
        <di:waypoint x="708" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k966ka_di" bpmnElement="Flow_1k966ka">
        <di:waypoint x="715" y="270" />
        <di:waypoint x="2400" y="270" />
        <di:waypoint x="2400" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2377" y="313" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
