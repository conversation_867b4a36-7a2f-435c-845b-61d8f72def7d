<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:qbpm="https://qiniu.com" id="Definitions_Process_1667351337713" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1667351337713" name="trade-change-end-time">
    <bpmn:startEvent id="Event_0n9trgu" name="销售提交-资源包延期-审批流程">
      <bpmn:outgoing>Flow_072s7v6</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0lea92v">
      <bpmn:incoming>Flow_072s7v6</bpmn:incoming>
      <bpmn:outgoing>Flow_16x1i5e</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m4rh2c</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hvlftv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1c53xtb</bpmn:outgoing>
      <bpmn:outgoing>Flow_1cqqeoz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1swe4l0</bpmn:outgoing>
      <bpmn:outgoing>Flow_1bhaoc7</bpmn:outgoing>
      <bpmn:outgoing>Flow_12z5k7a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_072s7v6" sourceRef="Event_0n9trgu" targetRef="Gateway_0lea92v" />
    <bpmn:endEvent id="Event_10pqsl1" name="审批结束">
      <bpmn:incoming>Flow_17iw9av</bpmn:incoming>
      <bpmn:incoming>Flow_01kk6qd</bpmn:incoming>
      <bpmn:incoming>Flow_191k85m</bpmn:incoming>
      <bpmn:incoming>Flow_1mgo2oc</bpmn:incoming>
      <bpmn:incoming>Flow_07s4y0s</bpmn:incoming>
      <bpmn:incoming>Flow_1rowxkn</bpmn:incoming>
      <bpmn:incoming>Flow_10yskow</bpmn:incoming>
      <bpmn:incoming>Flow_0ko5ija</bpmn:incoming>
      <qbpm:listener fire="process.completion" actionRefs="tradeUnifyApproved" />
    </bpmn:endEvent>
    <bpmn:userTask id="Activity_1sd194e" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_16x1i5e</bpmn:incoming>
      <bpmn:outgoing>Flow_11aj97c</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_16x1i5e" name="存储 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_1sd194e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==39</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_11aj97c" sourceRef="Activity_1sd194e" targetRef="Activity_0jvfpbl" />
    <bpmn:userTask id="Activity_0jvfpbl" name="产线负责人" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label">
      <bpmn:incoming>Flow_11aj97c</bpmn:incoming>
      <bpmn:outgoing>Flow_17iw9av</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_17iw9av" sourceRef="Activity_0jvfpbl" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_0ale989" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1m4rh2c</bpmn:incoming>
      <bpmn:outgoing>Flow_10ab3zp</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_04szbae" name="产线负责人" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label">
      <bpmn:incoming>Flow_10ab3zp</bpmn:incoming>
      <bpmn:outgoing>Flow_01kk6qd</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_10ab3zp" sourceRef="Activity_0ale989" targetRef="Activity_04szbae" />
    <bpmn:sequenceFlow id="Flow_1m4rh2c" name="CDN 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0ale989">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==45</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_01kk6qd" sourceRef="Activity_04szbae" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_17b4y10" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1hvlftv</bpmn:incoming>
      <bpmn:outgoing>Flow_11xvysl</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_00etjle" name="产线负责人" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label">
      <bpmn:incoming>Flow_11xvysl</bpmn:incoming>
      <bpmn:outgoing>Flow_191k85m</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_11xvysl" sourceRef="Activity_17b4y10" targetRef="Activity_00etjle" />
    <bpmn:sequenceFlow id="Flow_1hvlftv" name="内容安全 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_17b4y10">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==51</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_191k85m" sourceRef="Activity_00etjle" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_1iqt7u9" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1cqqeoz</bpmn:incoming>
      <bpmn:outgoing>Flow_1bllmjp</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_01k2ws0" name="产线负责人" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label">
      <bpmn:incoming>Flow_1bllmjp</bpmn:incoming>
      <bpmn:outgoing>Flow_1mgo2oc</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1bllmjp" sourceRef="Activity_1iqt7u9" targetRef="Activity_01k2ws0" />
    <bpmn:userTask id="Activity_0k6oejj" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1c53xtb</bpmn:incoming>
      <bpmn:outgoing>Flow_01qu3ja</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1c53xtb" name="直播 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0k6oejj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==60</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1cqqeoz" name="云短信 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_1iqt7u9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==72</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1mgo2oc" sourceRef="Activity_01k2ws0" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_15a5ls1" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1swe4l0</bpmn:incoming>
      <bpmn:outgoing>Flow_0auolkp</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_13sgqxb" name="产线负责人" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label">
      <bpmn:incoming>Flow_0auolkp</bpmn:incoming>
      <bpmn:outgoing>Flow_07s4y0s</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0auolkp" sourceRef="Activity_15a5ls1" targetRef="Activity_13sgqxb" />
    <bpmn:sequenceFlow id="Flow_1swe4l0" name="DORA 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_15a5ls1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==78</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07s4y0s" sourceRef="Activity_13sgqxb" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_0ell9f7" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_1bhaoc7</bpmn:incoming>
      <bpmn:outgoing>Flow_1m6vs59</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1bhaoc7" name="RTC 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_0ell9f7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==79</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1m6vs59" sourceRef="Activity_0ell9f7" targetRef="Activity_1nn0zdu" />
    <bpmn:userTask id="Activity_1azhzv0" name="销售层级" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="manager">
      <bpmn:incoming>Flow_12z5k7a</bpmn:incoming>
      <bpmn:outgoing>Flow_1dsji0w</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_12z5k7a" name="视频监控 订单" sourceRef="Gateway_0lea92v" targetRef="Activity_1azhzv0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">params.data.extra_param.seller_id==82</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1dsji0w" sourceRef="Activity_1azhzv0" targetRef="Activity_0h0d6al" />
    <bpmn:userTask id="Activity_1fm8e45" name="产线负责人" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label">
      <bpmn:incoming>Flow_01qu3ja</bpmn:incoming>
      <bpmn:outgoing>Flow_1rowxkn</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_01qu3ja" sourceRef="Activity_0k6oejj" targetRef="Activity_1fm8e45" />
    <bpmn:sequenceFlow id="Flow_1rowxkn" sourceRef="Activity_1fm8e45" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_1nn0zdu" name="产线负责人" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label">
      <bpmn:incoming>Flow_1m6vs59</bpmn:incoming>
      <bpmn:outgoing>Flow_10yskow</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_10yskow" sourceRef="Activity_1nn0zdu" targetRef="Event_10pqsl1" />
    <bpmn:userTask id="Activity_0h0d6al" name="产线负责人" qbpm:multiDecision="any" qbpm:isSequential="true" qbpm:assigneeType="label">
      <bpmn:incoming>Flow_1dsji0w</bpmn:incoming>
      <bpmn:outgoing>Flow_0ko5ija</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0ko5ija" sourceRef="Activity_0h0d6al" targetRef="Event_10pqsl1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1667351337713">
      <bpmndi:BPMNShape id="Event_0n9trgu_di" bpmnElement="Event_0n9trgu">
        <dc:Bounds x="982" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="970" y="120" width="59" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0lea92v_di" bpmnElement="Gateway_0lea92v" isMarkerVisible="true">
        <dc:Bounds x="975" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_10pqsl1_di" bpmnElement="Event_10pqsl1">
        <dc:Bounds x="982" y="742" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="978" y="785" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ehz1yy_di" bpmnElement="Activity_1sd194e">
        <dc:Bounds x="390" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w36ang_di" bpmnElement="Activity_0jvfpbl">
        <dc:Bounds x="390" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1idf6ff" bpmnElement="Activity_0ale989">
        <dc:Bounds x="540" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_098yzrm" bpmnElement="Activity_04szbae">
        <dc:Bounds x="540" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pnah05" bpmnElement="Activity_17b4y10">
        <dc:Bounds x="680" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bj0k2l" bpmnElement="Activity_00etjle">
        <dc:Bounds x="680" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ajmdo0" bpmnElement="Activity_1iqt7u9">
        <dc:Bounds x="950" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_17ge08v" bpmnElement="Activity_01k2ws0">
        <dc:Bounds x="950" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_19yy48f" bpmnElement="Activity_0k6oejj">
        <dc:Bounds x="820" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0llcv1u" bpmnElement="Activity_15a5ls1">
        <dc:Bounds x="1120" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1252zca" bpmnElement="Activity_13sgqxb">
        <dc:Bounds x="1120" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1pff192" bpmnElement="Activity_0ell9f7">
        <dc:Bounds x="1300" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1d2muxa" bpmnElement="Activity_1azhzv0">
        <dc:Bounds x="1490" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0hegi3t" bpmnElement="Activity_1fm8e45">
        <dc:Bounds x="820" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0eo0h88" bpmnElement="Activity_1nn0zdu">
        <dc:Bounds x="1300" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_07mw3vy" bpmnElement="Activity_0h0d6al">
        <dc:Bounds x="1490" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_072s7v6_di" bpmnElement="Flow_072s7v6">
        <di:waypoint x="1000" y="198" />
        <di:waypoint x="1000" y="255" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16x1i5e_di" bpmnElement="Flow_16x1i5e">
        <di:waypoint x="975" y="280" />
        <di:waypoint x="440" y="280" />
        <di:waypoint x="440" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="416" y="318" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11aj97c_di" bpmnElement="Flow_11aj97c">
        <di:waypoint x="440" y="460" />
        <di:waypoint x="440" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17iw9av_di" bpmnElement="Flow_17iw9av">
        <di:waypoint x="440" y="620" />
        <di:waypoint x="440" y="760" />
        <di:waypoint x="982" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1fhmxoc" bpmnElement="Flow_10ab3zp">
        <di:waypoint x="590" y="460" />
        <di:waypoint x="590" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m4rh2c_di" bpmnElement="Flow_1m4rh2c">
        <di:waypoint x="975" y="280" />
        <di:waypoint x="590" y="280" />
        <di:waypoint x="590" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="565" y="318" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01kk6qd_di" bpmnElement="Flow_01kk6qd">
        <di:waypoint x="590" y="620" />
        <di:waypoint x="590" y="760" />
        <di:waypoint x="982" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_01noujn" bpmnElement="Flow_11xvysl">
        <di:waypoint x="730" y="460" />
        <di:waypoint x="730" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hvlftv_di" bpmnElement="Flow_1hvlftv">
        <di:waypoint x="975" y="280" />
        <di:waypoint x="730" y="280" />
        <di:waypoint x="730" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="688" y="318" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_191k85m_di" bpmnElement="Flow_191k85m">
        <di:waypoint x="730" y="620" />
        <di:waypoint x="730" y="760" />
        <di:waypoint x="982" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0gpdht0" bpmnElement="Flow_1bllmjp">
        <di:waypoint x="1000" y="460" />
        <di:waypoint x="1000" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c53xtb_di" bpmnElement="Flow_1c53xtb">
        <di:waypoint x="975" y="280" />
        <di:waypoint x="870" y="280" />
        <di:waypoint x="870" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="846" y="318" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cqqeoz_di" bpmnElement="Flow_1cqqeoz">
        <di:waypoint x="1000" y="305" />
        <di:waypoint x="1000" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="965" y="334" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mgo2oc_di" bpmnElement="Flow_1mgo2oc">
        <di:waypoint x="1000" y="620" />
        <di:waypoint x="1000" y="742" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_16mifm1" bpmnElement="Flow_0auolkp">
        <di:waypoint x="1170" y="460" />
        <di:waypoint x="1170" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1swe4l0_di" bpmnElement="Flow_1swe4l0">
        <di:waypoint x="1025" y="280" />
        <di:waypoint x="1170" y="280" />
        <di:waypoint x="1170" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1137" y="318" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07s4y0s_di" bpmnElement="Flow_07s4y0s">
        <di:waypoint x="1170" y="620" />
        <di:waypoint x="1170" y="760" />
        <di:waypoint x="1018" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bhaoc7_di" bpmnElement="Flow_1bhaoc7">
        <di:waypoint x="1025" y="280" />
        <di:waypoint x="1350" y="280" />
        <di:waypoint x="1350" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1326" y="318" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m6vs59_di" bpmnElement="Flow_1m6vs59">
        <di:waypoint x="1350" y="460" />
        <di:waypoint x="1350" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12z5k7a_di" bpmnElement="Flow_12z5k7a">
        <di:waypoint x="1025" y="280" />
        <di:waypoint x="1540" y="280" />
        <di:waypoint x="1540" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1495" y="318" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dsji0w_di" bpmnElement="Flow_1dsji0w">
        <di:waypoint x="1540" y="460" />
        <di:waypoint x="1540" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01qu3ja_di" bpmnElement="Flow_01qu3ja">
        <di:waypoint x="870" y="460" />
        <di:waypoint x="870" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rowxkn_di" bpmnElement="Flow_1rowxkn">
        <di:waypoint x="870" y="620" />
        <di:waypoint x="870" y="760" />
        <di:waypoint x="982" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10yskow_di" bpmnElement="Flow_10yskow">
        <di:waypoint x="1350" y="620" />
        <di:waypoint x="1350" y="760" />
        <di:waypoint x="1018" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ko5ija_di" bpmnElement="Flow_0ko5ija">
        <di:waypoint x="1540" y="620" />
        <di:waypoint x="1540" y="760" />
        <di:waypoint x="1018" y="760" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
