package conf

import (
	"time"

	"github.com/qbox/bo-base/v3/dao"
	"github.com/qbox/pay-sdk/base/account"
	"qiniu.io/qbpm/engine/mgr"
)

// RunMode Run mode of app
type RunMode string

// IsProd Check if running in product mode
func (r RunMode) IsProd() bool {
	return r == ProdMode
}

var (
	// ProdMode Product mode
	ProdMode RunMode = "release"
	// DevMode Develop mode
	DevMode RunMode = "debug"
	// TestMode Test mode
	TestMode RunMode = "test"
)

// Config configuration for process engine
type Config struct {
	DB         mgr.DBConfig      `yaml:"db"`
	Redis      dao.CacheConfig   `yaml:"redis"`
	Name       string            `yaml:"name"`
	Version    string            `yaml:"version"`
	Debug      bool              `yaml:"debug"`
	IsJSONLog  bool              `yaml:"is_json_log"`
	Services   ServicesConfig    `yaml:"services"`
	JobSpecs   map[string]string `yaml:"job_specs"`
	Mode       RunMode
	Statistics StatisticsConfig `yaml:"statistics"`
}

type StatisticsConfig struct {
	DefaultDays  int `yaml:"default_days"`  // 默认统计天数
	OvertimeDays int `yaml:"overtime_days"` // 超时判定天数
}

type ApprovalTypeOrder struct {
	Days int `yaml:"days"`
}

type ServicesConfig struct {
	Morse struct {
		Host string `yaml:"host"`
	} `yaml:"morse"`
	SofaHost      string            `yaml:"sofa_host"`
	GaeaAdminHost string            `yaml:"gaea_admin_host"`
	Acc           account.AccConfig `yaml:"acc"`
	PortalIOHost  string            `yaml:"portal_io_host"`
	Workwx        struct {
		CorpID     string `yaml:"corp_id"`
		CorpSecret string `yaml:"corp_secret"`
		AgentID    int64  `yaml:"agent_id"`
	} `yaml:"workwx"`
	DiscussionAttachment struct {
		AccessKey string `yaml:"access_key"`
		SecretKey string `yaml:"secret_key"`
		Domain    string `yaml:"domain"`
		Bucket    string `yaml:"bucket"`
	} `yaml:"discussion_attachment"`
}

type TracingConfig struct {
	Enable                     bool          `yaml:"enable"`                        // 是否开启 tracing
	ServiceName                string        `yaml:"service_name"`                  // 服务名称，为空则默认取当前应用可执行文件名称
	SamplingType               string        `yaml:"sampling_type"`                 // 采样类型
	SamplingParam              float64       `yaml:"sampling_param"`                // 采样参数
	SamplingRefreshInterval    time.Duration `yaml:"sampling_refresh_interval"`     // 采样刷新间隔
	SamplingServerURL          string        `yaml:"sampling_server_url"`           // 采样策略服务地址
	ReporterLocalAgentEndpoint string        `yaml:"reporter_local_agent_endpoint"` // 采样上报本地 agent 地址
	ReporterCollectorEndpoint  string        `yaml:"reporter_collector_endpoint"`   // 采样数据收集器地址
}
