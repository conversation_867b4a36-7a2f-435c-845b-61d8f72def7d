<?xml version="1.0" encoding="UTF-8"?>
<definitions id="definitions"
    xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <auth id="BearerAuth" type="Oauth" tokenKey="params.access_token" prefix="Bearer">
        <restAction
            id="token"
            url="https://acc.qbox.me/oauth2/token"
            method="post"
            body='{"grant_type":"password", "username":"", "password":""}'
        />
    </auth>
    <restAction
        id="restToGetStarter"
        url="https://crm.qiniu.io/sf/api/v1/admin/user?email={{params.process.start_by_id}}"
        method="get"
        storeKey="params.starter"
        parseKey="params.data"
        parseFirst="true"
        authRef="BearerAuth"
    />
    <restAction
        id="restToGetStarterTopManager"
        url="https://crm.qiniu.io/sf/api/v1/admin/user/manager?email={{params.process.start_by_id}}&amp;level=-1"
        method="get"
        parseKey="params.data.email"
        parseFirst="true"
        authRef="BearerAuth"
    />
    <restAction
        id="restToGetSales"
        url="https://crm.qiniu.io/sf/api/v1/admin/developers/{{params.data.uid}}/saler"
        method="get"
        parseKey="params.data.email"
        parseFirst="true"
        authRef="BearerAuth"
    />
    <restAction
        id="assignNotify"
        url="https://morse.qiniu.io/api/notification/send/mail"
        header='{"Client-Id":"","Content-Type":"application/json"}'
        body='{
            "uid":0,
            "to":["{{params.execute.original_actor}}"],
            "subject":"合同申请审批请求",
            "content":"您好，&lt;br/&gt;{{params.starter.name}}请求您批准以下项目：&lt;br/&gt;&lt;a href=&apos;https://portal.qiniu.io/price-biz/approval/contract/{{params.process.excode}}&apos;&gt;https://portal.qiniu.io/price-biz/approval/contract/{{params.process.excode}}&lt;/a&gt;&lt;br/&gt;请单击该链接批准或拒绝该记录。&lt;br/&gt;&lt;br/&gt;十分感谢！&lt;br/&gt;Qiniu Process Management System"
            }'
        method="post"
        authRef="BearerAuth"
    />
    <restAction
        id="assignWechatNotify"
        url="https://ts.qiniu.io/action/internal/wechat/msg/name"
        body='{
            "user_name":"{{params.execute.original_actor.substr(0,params.execute.original_actor.lastIndexOf("@"))}}",
            "agent_id":"1000017",
            "msg":"您好，{{params.starter.name}}请求您批准修改合同申请：&lt;br/&gt;https://portal.qiniu.io/price-biz/approval/contract/{{params.process.excode}}&lt;br/&gt;（请复制链接后在浏览器内打开）&lt;br/&gt;Qiniu Process Management System"
            }'
        method="post"
        authRef="BearerAuth"
    />
    <restAction
        id="restToUpdateSofa"
        url="https://crm.qiniu.io/sf/api/v1/admin/sobject/status"
        method="put"
        authRef="BearerAuth"
        header='{"Content-Type":"application/json"}'
        body='{
                "sobject":"Contract__c",
                "key":"{{params.process.excode}}",
                "status":{{params.flow.status}},
                "status_field":"Approval_Status__c"
            }'
    />

    <process id="apply-contract" name="合同申请流程">
        <startEvent id="theStart" name="提交审批"/>
        <sequenceFlow id="toGetStarter" sourceRef="theStart" targetRef="getStarter"/>

        <serviceTask id="getStarter" name="getStarter" actionRef="restToGetStarter"/>
        <sequenceFlow id="toGetStarterManager" sourceRef="getStarter" targetRef="subContractCheck"/>
        <sequenceFlow id="toGetStarterManager" sourceRef="getStarter" targetRef="needSalesApvCheck"/>

        <exclusiveGateway id="needSalesApvCheck" name="needSalesApvCheck"/>
        <sequenceFlow id="toSalesApproval" sourceRef="needSalesApvCheck" targetRef="salesApproval" >
            <conditionExpression xsi:type="tFormalExpression">params.data.need_sales_apv</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="toSubContractCheck" sourceRef="needSalesApvCheck" targetRef="subContractCheck">
            <conditionExpression xsi:type="tFormalExpression">!params.data.need_sales_apv</conditionExpression>
        </sequenceFlow>

        <userTask id="salesApproval" name="销售审批" assignee="restToGetSales" assigneeType="dynamic">
            <listener id="salesApprovalListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="salesApprovalWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="salesApprovalToSubContractCheck" sourceRef="salesApproval" targetRef="subContractCheck"/>

        <exclusiveGateway id="subContractCheck" name="subContractCheck"/>
        <sequenceFlow id="toSubContractApproval" sourceRef="subContractCheck" targetRef="subConApvSetp1" >
            <conditionExpression xsi:type="tFormalExpression">params.data.is_sub_contract</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="toSdkCheck" sourceRef="subContractCheck" targetRef="firstCheck">
            <conditionExpression xsi:type="tFormalExpression">!params.data.is_sub_contract</conditionExpression>
        </sequenceFlow>

        <userTask id="subConApvSetp1" name="子合同审批第1步-销售运营" multiDecision="any" assignee="<EMAIL>,<EMAIL>" assigneeType="static">
            <listener id="subConApvSetp1Listener" fire="assign" actionRefs="assignNotify"/>
            <listener id="subConApvSetp1WechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="subConApvSetp1ToSetp2" sourceRef="subConApvSetp1" targetRef="subConApvSetp2"/>

        <userTask id="subConApvSetp2" name="子合同审批第2步-财务审批" multiDecision="any" assignee="<EMAIL>,<EMAIL>" assigneeType="static">
            <listener id="subConApvSetp2Listener" fire="assign" actionRefs="assignNotify"/>
            <listener id="subConApvSetp2WechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="subConApvSetp2ToSetp3" sourceRef="subConApvSetp2" targetRef="subConApvSetp3"/>

        <userTask id="subConApvSetp3" name="子合同审批第3步-总监审批" assigneeType="dynamic"  assignee="restToGetStarterTopManager">
            <listener id="subConApvSetp3Listener" fire="assign" actionRefs="assignNotify"/>
            <listener id="subConApvSetp3WechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="subConApvSetp3ToEnd" sourceRef="subConApvSetp3" targetRef="theEnd"/>

        <exclusiveGateway id="firstCheck" name="firstCheck" defaultFlow="firstCheckToSalesOp"/>
        <sequenceFlow id="firstCheckToPurchase" sourceRef="firstCheck" targetRef="purchase" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'pili-sdk');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="firstCheckToAPaaS" sourceRef="firstCheck" targetRef="apaas" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'apaas');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="firstCheckToSalesOp" sourceRef="firstCheck" targetRef="salesOp">
            <conditionExpression xsi:type="tFormalExpression">1==0</conditionExpression>
        </sequenceFlow>

        <userTask id="purchase" name="生态合作审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="purchaseListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="purchaseWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="purchaseToSalesOp" sourceRef="purchase" targetRef="salesOp" />

        <userTask id="apaas" name="aPaaS审批" multiDecision="any" assignee="<EMAIL>,<EMAIL>" assigneeType="static">
            <listener id="aPaaSListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="aPaaSWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="aPaaSToSalesOp" sourceRef="apaas" targetRef="salesOp"/>

        <userTask id="salesOp" name="销售运营审批" multiDecision="any" assignee="<EMAIL>,<EMAIL>" assigneeType="static" allowCounterSign="true">
            <listener id="salesOpListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="salesOpWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
            <counterSigns>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Fusion|Pili负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Kodo负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="RTC负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="SDK负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Pandora负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Dora负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="AI负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="QVM负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="SMS负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="QVS负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="aPaaS负责人"/>
                <counterSign assigneeType="dynamic" assignee="restToGetStarterTopManager" displayName="总监"/>
            </counterSigns>
            <counterSignTypes>
                <counterSignType>2</counterSignType>
            </counterSignTypes>
        </userTask>
        <sequenceFlow id="salesOpToLegal" sourceRef="salesOp" targetRef="legal" />

        <userTask id="legal" name="法务审批" multiDecision="any" assignee="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>" assigneeType="static" allowCounterSign="true">
            <listener id="legalListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="legalWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
            <counterSigns>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Fusion|Pili负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Kodo负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="RTC负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="SDK负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Pandora负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Dora负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="AI负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="QVM负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="SMS负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="QVS负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="aPaaS负责人"/>
                <counterSign assigneeType="dynamic" assignee="restToGetStarterTopManager" displayName="总监"/>
            </counterSigns>
            <counterSignTypes>
                <counterSignType>2</counterSignType>
            </counterSignTypes>
        </userTask>
        <sequenceFlow id="toContractTypeCheck" sourceRef="legal" targetRef="contractTypeCheck" />

        <exclusiveGateway id="contractTypeCheck" name="contractTypeCheck" defaultFlow="toFinanceVpStd"/>
        <sequenceFlow id="toFinanceVpStd" sourceRef="contractTypeCheck" targetRef="financeVpStd">
            <conditionExpression xsi:type="tFormalExpression">params.data.contract_type == "StandardContract"</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="toFinance" sourceRef="contractTypeCheck" targetRef="finance">
            <conditionExpression xsi:type="tFormalExpression">params.data.contract_type == "CustomContract"</conditionExpression>
        </sequenceFlow>

        <userTask id="financeVpStd" name="财务负责人审批" multiDecision="any" assignee="<EMAIL>,<EMAIL>" assigneeType="static" allowCounterSign="true">
            <listener id="financeVpStdListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="financeVpStdWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
            <counterSigns>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Fusion|Pili负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Kodo负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="RTC负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="SDK负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Pandora负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="Dora负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="AI负责人"/>
                <counterSign assigneeType="static" assignee="" displayName="QVM负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="SMS负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="QVS负责人"/>
                <counterSign assigneeType="static" assignee="<EMAIL>" displayName="aPaaS负责人"/>
                <counterSign assigneeType="dynamic" assignee="restToGetStarterTopManager" displayName="总监"/>
            </counterSigns>
            <counterSignTypes>
                <counterSignType>2</counterSignType>
            </counterSignTypes>
        </userTask>
        <sequenceFlow id="financeVpStdToEnd" sourceRef="financeVpStd" targetRef="theEnd"/>

        <userTask id="finance" name="财务审批" multiDecision="any" assignee="<EMAIL>,<EMAIL>" assigneeType="static">
            <listener id="financeListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="financeWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="financeToFork" sourceRef="finance" targetRef="fork"/>

        <inclusiveGateway id="fork" />
        <sequenceFlow id="forkToManager" sourceRef="fork" targetRef="manager">
            <conditionExpression>1==1</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToFinanceVp" sourceRef="fork" targetRef="financeVp">
            <conditionExpression>1==1</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToFusion" sourceRef="fork" targetRef="fusion" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'fusion', 'fusionov', 'pili') && !fn(params.data, 'apaas');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToKodo" sourceRef="fork" targetRef="kodo" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'kodo', 'kodo-origin');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToSRE" sourceRef="fork" targetRef="sre" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        if(data.contract_type != "CustomContract") {
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'kodo', 'kodo-origin');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToPandora" sourceRef="fork" targetRef="pandora" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'pandora');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToMserver" sourceRef="fork" targetRef="mserver" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'mserver');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToAI" sourceRef="fork" targetRef="ai" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'dora', 'ataraxia');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToKirk" sourceRef="fork" targetRef="kirk" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'kirk');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToQVM" sourceRef="fork" targetRef="qvm" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'qvm');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToQVM" sourceRef="fork" targetRef="qvm" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'sms');
                ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="forkToLinking" sourceRef="fork" targetRef="linking" >
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[
                    function fn(data){
                        if(!data || !data.products || data.products.length == 0){
                            return false;
                        }
                        var products = arguments;
                        items = _.filter(data.products, function(m){
                            for (var i = 1; i < products.length; i++){
                                if (m == products[i]){
                                    return true;
                                }
                            }
                            return false;
                        })
                        return items.length > 0;
                    }
                    fn(params.data, 'linking');
                ]]>
            </conditionExpression>
        </sequenceFlow>

        <userTask id="financeVp" name="财务负责人审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="financeVpListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="financeVpWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="financeVpToJoin" sourceRef="financeVp" targetRef="join"/>

        <userTask id="manager" name="上级审批" assigneeType="manager">
            <listener id="managerListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="managerWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="managerToJoin" sourceRef="manager" targetRef="join"/>

        <userTask id="fusion" name="Fusion|Pili审批" isSequential="true" assignee="<EMAIL>,<EMAIL>" assigneeType="static">
            <listener id="fusionListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="fusionWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="fusionToJoin" sourceRef="fusion" targetRef="join"/>

        <userTask id="kodo" name="Kodo审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="kodoListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="kodoWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="kodoToJoin" sourceRef="kodo" targetRef="join"/>

        <userTask id="sre" name="运维审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="sreListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="sreWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="sreToJoin" sourceRef="sre" targetRef="join"/>

        <userTask id="pandora" name="Pandora审批" multiDecision="any" assignee="<EMAIL>,<EMAIL>" assigneeType="static">
            <listener id="pandoraListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="pandoraWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="pandoraToJoin" sourceRef="pandora" targetRef="join"/>

        <userTask id="mserver" name="产研负责人审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="mserverListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="mserverWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="mserverToJoin" sourceRef="mserver" targetRef="join"/>

        <userTask id="dora" name="Dora审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="doraListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="doraWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="doraToJoin" sourceRef="dora" targetRef="join"/>

        <userTask id="ai" name="AI审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="aiListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="aiWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="aiToJoin" sourceRef="ai" targetRef="join"/>

        <userTask id="kirk" name="Kirk审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="kirkListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="kirkWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="kirkToJoin" sourceRef="kirk" targetRef="join"/>

        <userTask id="qvm" name="QVM审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="qvmListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="qvmWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="qvmToJoin" sourceRef="qvm" targetRef="join"/>

        <userTask id="sms" name="SMS审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="smsListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="smsWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="smsToJoin" sourceRef="sms" targetRef="join"/>

        <userTask id="linking" name="Linking审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="linkingListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="linkingWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="linkingToJoin" sourceRef="linking" targetRef="join"/>

        <inclusiveGateway id="join"/>
        <sequenceFlow id="joinToPriceTypeCheck" sourceRef="join" targetRef="priceTypeCheck"/>

        <exclusiveGateway id="priceTypeCheck"/>
        <sequenceFlow id="priceTypeToBO" sourceRef="priceTypeCheck" targetRef="bo">
            <conditionExpression>params.data.price_type == "PublicCustom"</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="priceTypeToEnd" sourceRef="priceTypeCheck" targetRef="theEnd">
            <conditionExpression>params.data.price_type != "PublicCustom"</conditionExpression>
        </sequenceFlow>

        <userTask id="bo" name="商业运营审批" assignee="<EMAIL>" assigneeType="static">
            <listener id="boListener" fire="assign" actionRefs="assignNotify"/>
            <listener id="boWechatListener" fire="assign" actionRefs="assignWechatNotify"/>
        </userTask>
        <sequenceFlow id="boToEnd" sourceRef="bo" targetRef="theEnd"/>

        <endEvent id="theEnd" name="审批结束">
            <listener id="completeSofaListener" fire="process.completion" actionRefs="restToUpdateSofa"/>
            <listener id="failListener" fire="process.failure" actionRefs="restToUpdateSofa"/>
        </endEvent>
    </process>
</definitions>