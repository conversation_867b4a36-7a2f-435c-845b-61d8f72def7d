package engine

import (
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/denisbrodbeck/machineid"
	"github.com/go-openapi/strfmt"
	"github.com/go-redis/redis/v8"

	"github.com/qbox/bo-base/v3/rpc"
	"github.com/qbox/pay-sdk/base/account"
	sofaSDK "github.com/qbox/pay-sdk/sofa/client/user"
	httpTrace "github.com/qbox/sonic/sdk/trace/http"
	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/cache"
	"qiniu.io/qbpm/engine/conf"
	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/jobs"
	"qiniu.io/qbpm/engine/lib/gaea"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/services/discussion"
	"qiniu.io/qbpm/engine/services/history"
	"qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/services/runtime"
	"qiniu.io/qbpm/engine/services/scheduler"
	"qiniu.io/qbpm/engine/services/system"
	"qiniu.io/qbpm/engine/util/logger"
	"qiniu.io/qbpm/engine/util/snowflake"
)

// ProcessEngine interface to get services handing processes
type ProcessEngine interface {
	GetName() string
	GetVersion() string
	GetRepositoryService() repository.Service
	GetRuntimeService() runtime.Service
	GetHistoryService() history.Service
	GetSchedulerService() scheduler.Service
	GetSystemService() system.Service
	GetDiscussionService() *discussion.Service
}

// processEngine contains services handing processes
type processEngine struct {
	Base              *mgr.Base
	Redis             redis.UniversalClient
	Config            *conf.Config
	AdminTransport    http.RoundTripper
	RepositoryService repository.Service
	RuntimeService    runtime.Service
	HistoryService    history.Service
	SchedulerService  scheduler.Service
	systemService     system.Service
	discussionService *discussion.Service
	sofaService       sofaSDK.ClientService
	gaeaService       gaea.GaeaAdminService
}

var (
	engine *processEngine
)

func (e *processEngine) handlerShutdown() {
	var gracefulStop = make(chan os.Signal)
	signal.Notify(gracefulStop, syscall.SIGTERM)
	signal.Notify(gracefulStop, syscall.SIGINT)
	go func() {
		sig := <-gracefulStop
		logrus.Infof("caught sig: %+v", sig)
		logrus.Info("Wait for schedular to finish processing")
		e.SchedulerService.Shutdown()
		e.Base.Close()
		os.Exit(0)
	}()
}

func (e *processEngine) recoverScheduler() {
	var executions []*models.RunExecution
	if err := e.Base.
		Model(models.RunExecution{}).
		Where("status in (?)", []enums.RunExecutionStatus{
			enums.RunExecutionStatusFinished,
			enums.RunExecutionStatusRunning,
			enums.RunExecutionStatusFailed,
		}).
		Find(&executions).Error; err != nil {
		logrus.Panicf("load scheduler jobs with error: %s", err)
	}
	for _, execution := range executions {
		model, err := e.RepositoryService.GetProcessByID(execution.ProcessDefinitionID)
		if err != nil {
			logrus.WithField("definitionID", execution.ProcessDefinitionID).
				Errorf("get process definition with error: %s", err)
			continue
		}
		switch execution.Status {
		case enums.RunExecutionStatusRunning:
			engine.SchedulerService.Put(jobs.NewExecute(
				ctx.BuildContext(execution, &model, e.Base, e.AdminTransport, e.sofaService, e.gaeaService, nil, false),
			))
		default:
			engine.SchedulerService.Put(jobs.NewFlow(
				ctx.BuildContext(execution, &model, e.Base, e.AdminTransport, e.sofaService, e.gaeaService, nil, false),
			))
		}
	}

	var listeners []*models.Listener
	if err := e.Base.
		Model(models.RunExecution{}).
		Where("status in (?)", []models.ListenerStatus{models.BeginListener}).
		Find(&listeners).Error; err != nil {
		logrus.Panicf("load scheduler jobs with error: %s", err)
	}
	for _, listener := range listeners {
		definition, err := e.GetRepositoryService().GetProcessByID(listener.ProcessDefinitionID)
		if err != nil {
			logrus.Panicf("load listener jobs with error: %s", err)
		}
		node, err := definition.GetNode(listener.NodeKey)
		if err != nil {
			logrus.Panicf("load listener jobs with error: %s", err)
		}
		var activity models.ActivityInstance
		if err := e.Base.
			Model(models.ActivityInstance{}).
			Where("execution_id = ?", listener.ExecutionID).
			First(&activity).Error; err != nil {
			logrus.Panicf("load activity with error: %s", err)
		}
		go jobs.NewListener(
			node,
			&definition,
			listener.ExecutionID,
			e.Base,
			e.AdminTransport,
		).FireAction(listener.Fire)
	}

	return
}

// GetProcessEngine get a process engine
func GetProcessEngine() ProcessEngine {
	return engine
}

// Init Initialize a process engine
func Init(cfg *conf.Config, autoMigrate bool) {
	logger.InitLogger(os.Stdout, cfg.IsJSONLog, cfg.Debug)

	logrus.Infof("start init engine with config: %+v", *cfg)
	defer logrus.Info("init engine succeed")

	base, err := mgr.InitDB(&cfg.DB, cfg.Debug)
	if err != nil {
		logrus.Panicf("init db with error: %s", err)
	}

	if autoMigrate {
		base.Automigrate()
	}

	redis, err := cache.Init(&cfg.Redis)
	if err != nil {
		logrus.Panicf("init redis with error: %s", err)
	}

	initSnowflake(base)

	servBase := &services.Base{Mgr: base, RedisCli: redis}

	repositoryService := repository.NewRepositoryService(servBase)
	schedulerService := scheduler.NewService(redis)

	transport, err := account.NewTransport(&cfg.Services.Acc)
	if err != nil {
		logrus.WithError(err).Fatal("failed to init http client transport")
	}

	client := &http.Client{
		Transport: httpTrace.NewTransport(
			transport,
			httpTrace.OperationNameFunc(
				func(r *http.Request) string {
					return "send_to_http_service"
				},
			),
		),
	}

	sofaTr, err := rpc.NewSwaggerTransport(cfg.Services.SofaHost, client)
	if err != nil {
		logrus.WithError(err).Fatal("failed to init swagger transport")
	}
	sofaService := sofaSDK.New(sofaTr, strfmt.Default)

	gaeaService := gaea.NewGaeaAdminService(cfg.Services.GaeaAdminHost, client)

	historyService := history.NewHistoryService(
		servBase,
		repositoryService,
		transport,
	)

	engine = &processEngine{
		Base:             base,
		Redis:            redis,
		Config:           cfg,
		AdminTransport:   transport,
		gaeaService:      gaeaService,
		sofaService:      sofaService,
		SchedulerService: schedulerService,
		RuntimeService: runtime.NewService(
			servBase,
			repositoryService,
			schedulerService,
			historyService,
			sofaService,
			gaeaService,
			transport,
			cfg,
		),
		RepositoryService: repositoryService,
		HistoryService:    historyService,
		systemService: system.NewSystemService(
			servBase,
			transport,
		),
		discussionService: discussion.NewDiscussionService(servBase, gaeaService, cfg),
	}

	err = repositoryService.Init()
	if err != nil {
		logrus.Fatalf("load existed process definitions with error: %s", err)
	}

	engine.handlerShutdown()
	engine.recoverScheduler()
}

func initSnowflake(base *mgr.Base) {
	machineID, err := machineid.ID()
	if err != nil {
		logrus.Fatalf("get machine id with error: %s", err)
	}

	machineMgr := mgr.NewMachineMgr(*base)
	nodeID, err := machineMgr.NodeID(machineID)
	if err != nil {
		logrus.Fatalf("get node id with error: %s", err)
	}

	snowflake.Init(int64(nodeID))
}

// GetName Get engine name
func (e *processEngine) GetName() string {
	return e.Config.Name
}

// GetVersion Get engine version
func (e *processEngine) GetVersion() string {
	return e.Config.Version
}

// GetRepositoryService Get repository service
func (e *processEngine) GetRepositoryService() repository.Service {
	return e.RepositoryService
}

// GetRuntimeService Get runtime service instance
func (e *processEngine) GetRuntimeService() runtime.Service {
	return e.RuntimeService
}

// GetHistoryService Get history service instance
func (e *processEngine) GetHistoryService() history.Service {
	return e.HistoryService
}

// GetSchedulerService Get scheduler service instance
func (e *processEngine) GetSchedulerService() scheduler.Service {
	return e.SchedulerService
}

func (e *processEngine) GetSystemService() system.Service {
	return e.systemService
}

func (e *processEngine) GetDiscussionService() *discussion.Service {
	return e.discussionService
}
