package jobs

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	sofaSDK "github.com/qbox/pay-sdk/sofa/client/user"
	"github.com/sirupsen/logrus"
	bpmn "qiniu.io/qbpm/engine/bpmn/models"
	bpmnEnums "qiniu.io/qbpm/engine/bpmn/models/enums"
	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/services/repository"
	"qiniu.io/qbpm/engine/services/scheduler"
	"qiniu.io/qbpm/engine/util"
	"qiniu.io/qbpm/engine/util/exp"
	"qiniu.io/qbpm/engine/util/params"
)

// Flow is job to perform node leave
type Flow struct {
	*ctx.Context
}

// NewFlow instances a flow job
func NewFlow(ctx *ctx.Context) *Flow {
	return &Flow{ctx}
}

// Key implements interface to get key of current job
func (j *Flow) Key() string {
	return fmt.Sprintf("process:%d", j.RunExecution.ProcessInstanceID)
}

// Status implements interface to get status of current job
func (j *Flow) Status() string {
	return j.RunExecution.Status.String()
}

// LockTime implements interface to get lock time of current job
func (j *Flow) LockTime() time.Duration {
	return time.Second * 5
}

func (j *Flow) throwEvent(signalRef, business, body string) (nextJobs []scheduler.Job, err error) {
	signalMgr := mgr.SignalMgr{Base: *j.Mgr}
	_, err = signalMgr.Create(&models.SignalInfo{
		SignalRef: signalRef,
		Business:  business,
		Payload:   models.Json(body),
	})
	if err != nil {
		return
	}

	relationMgr := mgr.SignalExecutionMgr{Base: *j.Mgr}
	executions, err := relationMgr.MgetExecution(signalRef, business)
	if err != nil {
		return
	}

	processMgr := mgr.ProcessDefinitionMgr{Base: *j.Mgr}
	executionMgr := mgr.NewRunExecutionMgr(*j.Mgr)

	servBase := &services.Base{Mgr: j.Mgr}
	repositoryService := repository.NewRepositoryService(servBase)

	for _, execution := range executions {
		process, err := processMgr.GetProcessByID(execution.ProcessDefinitionID)
		processDefinition, err := repositoryService.GetProcessByKey(process.Key)

		if err != nil {
			j.Log.Errorf("throwEvent, processMgr.GetProcessByID(%s) error: %s", execution.ProcessDefinitionID, err)
			continue
		}
		executionMgr.UpdateStatus(execution, enums.RunExecutionStatusRunning)
		params, err := params.SetParam(execution.Params, signalRef, body)
		if err != nil {
			j.Log.WithFields(logrus.Fields{
				"execution_id": execution.ID,
				"params":       execution.Params,
				"signalRef":    signalRef,
				"body":         body,
			}).Error(err)
		}
		executionMgr.UpdateParams(execution, params)
		nextJobs = append(nextJobs, NewCatchEvent(
			ctx.BuildContext(
				execution,
				&processDefinition,
				j.Mgr,
				j.AdminTransport,
				j.SofaService,
				j.GaeaService,
				j.Log,
				false,
			),
		))
	}
	return
}

// 节点运行结束回调
func (j *Flow) nodeEndCallback(status ctx.NodeStatus) (nextJobs []scheduler.Job, err error) {
	node, err := j.Context.CurrentNode()
	if err != nil {
		j.Log.WithField("NodeStatus", status).
			Errorf("nodeEndCallback: j.Context.CurrentNode() err with %s", err)
	}

	if node.GetType() == "IntermediateCatchEvent" {
		relationMgr := mgr.SignalExecutionMgr{Base: *j.Mgr}
		relations, err := relationMgr.MgetSiblingRelation(j.Context.RunExecution.ActivityDefineKey, j.Context.RunExecution.ProcessInstanceID)
		for _, relation := range relations {
			relationMgr.ConsumeExecution(relation.ExecutionId)
		}

		return nextJobs, err
	}

	var (
		validate bool
	)
	throwEvents := node.GetThrowEvents()
	for _, event := range throwEvents {
		signal := event.SignalEvent
		validate = true
		if signal.Condition != nil && signal.Condition.Expression != "" {
			validate, err = exp.EvalBool(signal.Condition.Expression, j.Context.RunExecution.Params)
			if err != nil {
				j.Log.WithField("signalEventDefinition_id", event.ID).
					Errorf("exp.EvalBool() err: %s", err)
				continue
			}
		}
		if validate {
			payload := signal.Payload
			if payload == "" {
				payload = "{}"
			}
			if payload, err = exp.EvalSpread(payload, j.RunExecution.Params); err != nil {
				j.Log.WithFields(logrus.Fields{
					"payload": payload,
					"params":  j.RunExecution.Params,
				}).Errorf("exp.EvalSpread payload err: %s", err)
				continue
			}
			if status == ctx.NodeStatusFailed {
				if payload, err = params.SetParam(payload, "node_status", "failure"); err != nil {
					j.Log.WithFields(logrus.Fields{
						"payload":     payload,
						"node_status": "failure",
					}).Errorf("payload set failure status err: %s", err)
				}
			}
			if status == ctx.NodeStatusCompleted {
				if payload, err = params.SetParam(payload, "node_status", "success"); err != nil {
					j.Log.WithFields(logrus.Fields{
						"payload":     payload,
						"node_status": "success",
					}).Errorf("payload set success status err: %s", err)
				}
			}

			signalKey := signal.SignalRef
			if j.RunExecution.Params != "" {
				signalKey, err = exp.EvalSpread(signal.SignalRef, j.RunExecution.Params)
				if err != nil {
					j.Log.Errorf("throwEvent, exp.EvalSpread(%s, %s) error: %s", signal.SignalRef, j.RunExecution.Params, err)
					continue
				}
			}

			jobs, err := j.throwEvent(signalKey, signal.Business, payload)
			if err != nil {
				j.Log.WithFields(logrus.Fields{
					"signalRef": signal.SignalRef,
					"business":  signal.Business,
					"payload":   signal.Payload,
				}).Errorf("throwEvent error:%s", err)
			}
			nextJobs = append(nextJobs, jobs...)
		}
	}
	return
}

// Run implements interface to run current job
func (j *Flow) Run() (nextJobs []scheduler.Job, err error) {

	if !j.RunExecution.Status.IsEnd() {
		j.Log.WithField("status", j.RunExecution.Status.String()).
			Warn("execution node is not in end status, skipped.")
		return
	}

	err = j.Mgr.First(&models.RunExecution{}, j.RunExecution.ID).Error
	if err != nil {
		return
	}

	availableOutgoings, err := j.Context.GetInstanceOutgoings()
	if err != nil {
		j.Log.WithError(err).Error("get outgoings with error")
		return
	}

	curNodeStatus, err := j.currentNodeStatus()
	if err != nil {
		j.Log.WithError(err).Error("get current node status with error")
		return
	}

	isCurExecPending := curNodeStatus == ctx.NodeStatusPending
	// 当前节点执行结束触发 callback
	if !isCurExecPending {
		jobs, _ := j.nodeEndCallback(curNodeStatus)
		nextJobs = append(nextJobs, jobs...)
	}

	var siblingExecutions []*models.RunExecution

	if curNodeStatus == ctx.NodeStatusCompleted {
		// 当前节点执行完成，获取兄弟节点看是否都执行完
		siblingExecutions, err = j.getSiblings(availableOutgoings)
	} else if curNodeStatus == ctx.NodeStatusFailed {
		// 当前节点执行失败，获取当前流程所有执行节点看是否都执行完
		siblingExecutions, err = j.currentProcessExecutions()
	} else {
		siblingExecutions = []*models.RunExecution{j.RunExecution}
	}

	if err != nil {
		j.Log.WithField("outgoings", availableOutgoings).
			WithError(err).
			Error("get siblings with error")
		return
	}

	nodeSiblingExecs := groupExecsByKey(siblingExecutions)
	status := ctx.NodeStatusPending
	if !isCurExecPending {
		status, err = j.nextEnabled(nodeSiblingExecs)
		if err != nil {
			j.Log.WithError(err).Error("check current node status with error")
			return
		}
	}

	var runExecutions []*models.RunExecution
	if status == ctx.NodeStatusFailed { // 扭转end节点
		runExecutions, err = j.getFailRunExecutions(availableOutgoings)
	} else if status == ctx.NodeStatusCompleted { // 正常扭转，会处理多实例节点
		runExecutions, err = j.getNextRunExecutions(availableOutgoings)
	} else {
		// 先判断是否是层级审批节点
		// 层级审批节点只流转到下一级经理审批，兄弟节点不发生变化
		// 非层级节点只更新状态
		runExecutions, err = j.getNextRunExecutions(availableOutgoings)
		if err != nil {
			return nil, err
		}

		if !j.isHierarchyApproval(runExecutions) {
			err = j.pendingUpdate(nodeSiblingExecs, isCurExecPending)
			if err != nil {
				j.Log.WithError(err).Error("pending update with error")
				return
			}
			return
		}
	}
	if err != nil {
		j.Log.WithError(err).Error("get next executions with error")
		return
	}

	if err = j.performNodeLeave(siblingExecutions, runExecutions); err != nil {
		j.Log.WithField("siblings", siblingExecutions).
			WithField("nextExecutions", runExecutions).
			WithError(err).
			Error("perform node leave with error")
		return
	}

	for _, runExecution := range runExecutions {
		n, _ := j.ProcessDefinition.GetExecutionNode(runExecution)

		if n.GetType() == "UserTask" || n.GetType() == "SuggestTask" {
			NewListener(n, j.ProcessDefinition, runExecution.ID, j.Mgr, j.AdminTransport).FireAction("assign")
		}

		if n.GetType() == "IntermediateCatchEvent" {
			nextJobs = append(nextJobs, NewCatchEvent(
				ctx.BuildContext(
					runExecution,
					j.ProcessDefinition,
					j.Mgr,
					j.AdminTransport,
					j.SofaService,
					j.GaeaService,
					j.Log,
					false,
				),
			))
		} else {
			nextJobs = append(nextJobs, NewExecute(
				ctx.BuildContext(
					runExecution,
					j.ProcessDefinition,
					j.Mgr,
					j.AdminTransport,
					j.SofaService,
					j.GaeaService,
					j.Log,
					false,
				),
			))
		}
	}

	err = j.tryFireProcessEndAction()
	if err != nil {
		return nil, err
	}

	return
}

func (j *Flow) tryFireProcessEndAction() error {

	node, err := j.Context.CurrentNode()
	if err != nil {
		return err
	}

	if node.GetType() != "EndEvent" {
		return nil
	}

	jsonParser, err := params.GetParamParser(j.RunExecution.Params)
	if err != nil {
		return err
	}

	data := jsonParser.Path(ParamFlowStatus).Data()
	if data == nil {
		return nil
	}

	listener := NewListener(node, j.ProcessDefinition, j.RunExecution.ID, j.Mgr, j.AdminTransport)

	go listener.FireAction("process.end")
	status := enums.ProcessInstanceStatus(data.(float64))

	switch status {
	case enums.ProcessStatusCompleted:
		go listener.FireAction("process.completion")
	case enums.ProcessStatusFailed:
		go listener.FireAction("process.failure")
	case enums.ProcessStatusRemoved:
		go listener.FireAction("process.cancellation")
	}

	return nil
}

// pendingUpdate updates activity and status of sub-tasks if exist
func (j *Flow) pendingUpdate(execMap map[string][]*models.RunExecution, isCurExecPending bool) (err error) {
	// record current execution's activity history, fail or success
	err = updateActivity(j.RunExecution, j.Mgr)
	if err != nil {
		return
	}
	if isCurExecPending {
		return
	}

	// if current node is not in pending status, update executions and activities which not ended
	// it usually happens when task completed if only partially completed
	executions, ok := execMap[j.RunExecution.ActivityDefineKey]
	if !ok {
		return
	}
	updateExecs := make([]*models.RunExecution, 0)
	for _, exec := range executions {
		if !exec.Status.IsEnd() {
			exec.Status = enums.RunExecutionStatusNoResponse
			updateExecs = append(updateExecs, exec)
		}
	}

	// 前加签节点执行完需要将之前 suspend 的当前节点恢复成 pending 状态
	// 后加签节点执行完需要将前置的第一个 suspend 的节点恢复成 pending 状态
	for _, exec := range executions {
		if exec.CounterSignType == bpmnEnums.CounterSignTypeBefore && !j.Context.IsCounterSignTypeAfter {
			// 检查兄弟加签节点是否都执行完
			var isSiblingPending bool
			for _, execs := range execMap {
				for _, e := range execs {
					// 先保证判断的都是加签节点
					if e.ParentKey == exec.ParentKey && e.CounterSignType.Valid() && !e.Status.IsEnd() {
						isSiblingPending = true
						break
					}
				}
			}

			if !isSiblingPending {
				parentExecs := execMap[exec.ParentKey]
				for _, parentExec := range parentExecs {
					if parentExec.ActivityDefineKey == exec.ParentKey && parentExec.Status == enums.RunExecutionStatusSuspend {
						parentExec.Status = enums.RunExecutionStatusPending
						updateExecs = append(updateExecs, parentExec)
					}
				}
			}
		}
		if exec.CounterSignType == bpmnEnums.CounterSignTypeAfter {
			rootNode, err := j.ProcessDefinition.GetExecutionNode(exec)
			if err != nil {
				return err
			}
			currentExecution := exec
			for currentExecution.ActivityDefineKey != rootNode.GetKey() {
				// 只恢复第一个遇到的挂起的 activityInstance
				hasSuspend := false
				parentExecutions := execMap[currentExecution.ParentKey]
				for _, parentExec := range parentExecutions {
					if parentExec.Status == enums.RunExecutionStatusSuspend {
						hasSuspend = true
						parentExec.Status = enums.RunExecutionStatusPending
						updateExecs = append(updateExecs, parentExec)
					}
				}
				if hasSuspend {
					break
				}
				currentExecution = parentExecutions[0]
			}

		}
	}

	if len(updateExecs) > 0 {
		return j.Mgr.PerformTransaction(func(base *mgr.Base) (err error) {
			for _, exec := range updateExecs {
				if err = base.Model(&models.RunExecution{}).
					Where("id = ?", exec.ID).
					Updates(exec).Error; err != nil {
					return
				}

				if err = updateActivity(exec, base); err != nil {
					return
				}
			}
			return nil
		})
	}
	return
}

// groupExecsByKey groups executions by activity define key
func groupExecsByKey(executions []*models.RunExecution) map[string][]*models.RunExecution {
	execMap := make(map[string][]*models.RunExecution, 0)
	for _, exec := range executions {
		if _, ok := execMap[exec.ActivityDefineKey]; !ok {
			execMap[exec.ActivityDefineKey] = make([]*models.RunExecution, 0)
		}
		execMap[exec.ActivityDefineKey] = append(execMap[exec.ActivityDefineKey], exec)
	}

	return execMap
}

// nextEnabled 检查是否有条件转到下一个节点
func (j *Flow) nextEnabled(execMap map[string][]*models.RunExecution) (status ctx.NodeStatus, err error) {

	// 收集所有execution ID，一次性批量查询ActivityInstance
	var allExecutions []*models.RunExecution
	executionIds := make([]uint64, 0)
	for _, execs := range execMap {
		allExecutions = append(allExecutions, execs...)
		for _, exec := range execs {
			executionIds = append(executionIds, exec.ID)
		}
	}

	// 批量查询所有ActivityInstance
	var activityInstances []*models.ActivityInstance
	if len(executionIds) > 0 {
		err = j.Mgr.Where("execution_id in (?)", executionIds).Find(&activityInstances).Error
		if err != nil {
			return 0, err
		}
	}

	// 建立execution_id到ActivityInstance的映射
	activityMap := make(map[uint64]*models.ActivityInstance)
	for _, activity := range activityInstances {
		activityMap[activity.ExecutionID] = activity
	}

	// 检查每个节点组的状态
	for _, execs := range execMap {
		nodeStatus, e := j.checkNodeStatusWithActivityMap(execs, activityMap)
		if e != nil {
			return 0, e
		}

		switch nodeStatus {
		case ctx.NodeStatusPending:
			return ctx.NodeStatusPending, nil

		case ctx.NodeStatusFailed:
			return ctx.NodeStatusFailed, nil
		}
	}

	return ctx.NodeStatusCompleted, nil
}

func updateActivity(execution *models.RunExecution, db *mgr.Base) (err error) {
	activity := models.ActivityInstance{
		Status: execution.Status.ActivityStatus(),
	}

	if execution.Status.IsEnd() {
		activity.EndAt = time.Now()
	}

	if execution.Params != "" {
		activity.Params = execution.Params
	}
	if execution.Memo != "" {
		activity.Memo = execution.Memo
	}
	if execution.Assignee != "" {
		activity.Actor = execution.Assignee
	}

	return db.Model(&models.ActivityInstance{}).
		Where("execution_id=?", execution.ID).
		Updates(activity).Error
}

func (j *Flow) isHierarchyApproval(runExecutions []*models.RunExecution) bool {
	return len(runExecutions) > 0 &&
		runExecutions[0].ActivityDefineKey == j.RunExecution.ActivityDefineKey
}

func (j *Flow) performNodeLeave(siblings []*models.RunExecution, runExecutions []*models.RunExecution) (err error) {
	// check if end node
	processStatus := enums.ProcessStatusPending
	if node, _ := j.CurrentNode(); node.GetType() == "EndEvent" {
		processStatus, err = j.getProcessStatus()
		if err != nil {
			return
		}
	}

	return j.Mgr.PerformTransaction(func(db *mgr.Base) (err error) {
		// update process
		if processStatus != enums.ProcessStatusPending {
			err = db.Model(&models.ProcessInstance{}).
				Where("id=?", j.RunExecution.ProcessInstanceID).
				Updates(models.ProcessInstance{
					Status: processStatus,
					EndAt:  time.Now(),
				}).Error
			if err != nil {
				return
			}
		}

		// update activity
		if err = updateActivity(j.RunExecution, db); err != nil {
			return
		}

		// delete runnings
		var deletes []uint64

		// 层级审批节点只扭转到下一级审批
		// 非层级审批需要将兄弟节点状态一起变更
		if !j.isHierarchyApproval(runExecutions) {
			for _, sibling := range siblings {
				if !sibling.Status.IsEnd() {
					sibling.Status = enums.RunExecutionStatusNoResponse
					if err = updateActivity(sibling, db); err != nil {
						return
					}
				}
				deletes = append(deletes, sibling.ID)
			}
		} else {
			deletes = append(deletes, j.RunExecution.ID)
		}

		if err = db.Where("id in (?)", deletes).Delete(&models.RunExecution{}).Error; err != nil {
			return
		}

		// create running
		for _, exec := range runExecutions {
			if err = db.Create(exec).Error; err != nil {
				return
			}
		}

		// create activity
		var node bpmn.INode
		for _, exec := range runExecutions {
			node, err = ctx.GetExecutionNode(exec, j.ProcessDefinition)
			if err != nil {
				return
			}
			if err = db.Create(models.NewActivityInstance(exec, node.GetType())).Error; err != nil {
				return
			}
		}

		return
	})
}

// 一：网关，获取兄弟节点, 二：多实例，获取所有实例的execution
// gw1 - node11 - node12 - node13 - gw2
//    |							     |
// 	  ------ node21 - node22 ---------
// 对于上述流程，node11, node12, node13, node21, node22 都是网关内的节点
// node22 执行完后能否直接扭转到 gw2 节点，取决于 node11, node12, node13, node21, node22 是否都执行完
// 即当节点的下一个节点是汇聚节点时，需要判 断汇聚节点的所有输入都已经执行完成，需要从汇聚节点往前递归找到网关的起始发散节点
// 每一个汇聚节点一定对应有一个发散节点
// 为了实现方便，这里直接查找除当前节点分支的所有的前置节点
// 如 node22 执行完会查找 node11, node12, node13, node22 的执行记录，而不会查找 node21 的执行记录，因为 node21 执行完才会到 node22

func (j *Flow) getSiblings(availableOutgoings []*bpmn.SequenceFlow) (runExecutions []*models.RunExecution, err error) {

	if len(availableOutgoings) == 0 {
		runExecutions = []*models.RunExecution{j.RunExecution}
		return
	}

	curNode, err := j.CurrentNode()
	if err != nil {
		return nil, err
	}

	var (
		nodeKeys []string
	)

	for _, sequenceFlow := range availableOutgoings {
		nodeKeys = append(nodeKeys, j.getPreNodes(curNode.GetKey(), sequenceFlow.TargetRef, j.ProcessDefinition.ProcessModel.Process.SequenceFlows)...)
	}

	util.RemoveDuplicate(nodeKeys)

	err = j.Mgr.
		Where("process_instance_id = ?", j.RunExecution.ProcessInstanceID).
		Where("activity_define_key in (?)", nodeKeys).
		Find(&runExecutions).Error

	var otherRunExecutions []*models.RunExecution
	var filteredRunExecutions []*models.RunExecution

	err = j.Mgr.
		Where("process_instance_id=?", j.RunExecution.ProcessInstanceID).
		Find(&otherRunExecutions).Error

	for _, execution := range otherRunExecutions {
		if !execution.CounterSignType.Valid() {
			continue
		}
		for _, key := range nodeKeys {
			if strings.Contains(execution.ActivityDefineKey, key) {
				filteredRunExecutions = append(filteredRunExecutions, execution)
			}
		}
	}

	result := append(runExecutions, filteredRunExecutions...)

	return result, err
}

func (j *Flow) currentProcessExecutions() (runExecutions []*models.RunExecution, err error) {
	err = j.Mgr.
		Where("process_instance_id = ?", j.RunExecution.ProcessInstanceID).
		Find(&runExecutions).Error
	return
}

func (j *Flow) currentNodeStatus() (ctx.NodeStatus, error) {
	var executions []*models.RunExecution
	err := j.Mgr.
		Where("process_instance_id = ?", j.RunExecution.ProcessInstanceID).
		Where("activity_define_key = ?", j.RunExecution.ActivityDefineKey).
		Find(&executions).Error
	if err != nil {
		return 0, err
	}

	return j.CheckNodeStatus(executions)
}

// getPreNodes 返回指定目标节点除当前节点分支外的所有前置节点
// 如 getSiblings 示例所示
// 如果当前节点是 node22，则返回 node 22、node13、node12、node11、gw1
// 如果当前节点是 node12，则返回 node12
func (j *Flow) getPreNodes(currentNode, targetNode string, flows []*bpmn.SequenceFlow) []string {
	incomingMap := map[string][]string{}
	for _, flow := range flows {
		incomingMap[flow.TargetRef] = append(incomingMap[flow.TargetRef], flow.SourceRef)
	}

	visited := make(map[string]bool)
	var results []string
	var queue []string

	queue = append(queue, targetNode)
	visited[targetNode] = true

	for len(queue) > 0 {
		node := queue[0]
		queue = queue[1:]

		for _, neighbor := range incomingMap[node] {
			if !visited[neighbor] {
				visited[neighbor] = true
				results = append(results, neighbor)

				// 如果是当前节点，则不再继续往前找
				if neighbor != currentNode {
					queue = append(queue, neighbor)
				}
			}
		}
	}

	return results
}

func (j *Flow) getProcessStatus() (status enums.ProcessInstanceStatus, err error) {
	status = enums.ProcessStatusCompleted

	jsonParser, err := params.GetParamParser(j.RunExecution.Params)
	if err != nil {
		return
	}

	data := jsonParser.Path(ParamFlowStatus).Data()
	if data == nil {
		return
	}

	status = enums.ProcessInstanceStatus(data.(float64))
	return
}

func (j *Flow) getUserManager(email string) (manager string, err error) {
	if j.SofaService == nil {
		return "", errors.New("sofa service not initialized")
	}

	result, err := j.SofaService.GetUserManager(sofaSDK.NewGetUserManagerParamsWithContext(context.Background()).WithEmail(&email))
	if err != nil {
		if _, ok := err.(*sofaSDK.GetUserManagerNotFound); ok {
			return "", nil
		}

		return "", err
	}

	if result.Payload == nil || result.Payload.Data == nil {
		return "", errors.New("unexpected error")
	}

	return result.Payload.Data.Email, nil
}

func (j *Flow) getNextRunExecutions(availableOutgoings []*bpmn.SequenceFlow) (runExecutions []*models.RunExecution, err error) {
	node, err := j.CurrentNode()
	if err != nil {
		return nil, err
	}

	isEndNode := false

	defer func() {
		if len(runExecutions) > 0 && isEndNode {
			err = setStatusParams(enums.ProcessStatusCompleted, runExecutions...)
		}
	}()

	if task, ok := node.(bpmn.Task); ok {
		// 当前节点是层级审批节点（也有可能是加签层级审批），需要看是否还有上级
		isCurrentNodeAssigneeTypeManager := task.GetAssigneeNode().AssigneeType == bpmnEnums.AssigneeTypeManager && j.RunExecution.OriginalAssignee != ""
		isCurrentExecutionAssigneeTypeManager := j.RunExecution.CounterSigner != "" && j.RunExecution.AssigneeType == bpmnEnums.AssigneeTypeManager
		if isCurrentNodeAssigneeTypeManager || isCurrentExecutionAssigneeTypeManager {
			// 先取原分配人，后面看需求
			manager, err := j.getUserManager(j.RunExecution.OriginalAssignee)
			if err != nil {
				j.Log.WithError(err).WithField("email", j.RunExecution.OriginalAssignee).Error("get user manager failed")
				return nil, err
			}

			if manager != "" {
				r := models.NextRunExecution(j.RunExecution, node)
				r.Status = enums.RunExecutionStatusPending
				r.OriginalAssignee = manager
				r.Assignee = manager
				runExecutions = append(runExecutions, r)

				return runExecutions, nil
			}
		}

		// 如果是串行多实例节点且没有执行完，则继续后面的实例
		if task.GetIsSequential() && j.RunExecution.LeftAssignees != "" {

			current, left := j.getCurrentAndLeftAssignees(j.RunExecution.LeftAssignees, true)

			r := models.NextRunExecution(j.RunExecution, node)
			r.Status = enums.RunExecutionStatusPending
			r.OriginalAssignee = current[0]
			r.Assignee = current[0]
			r.LeftAssignees = left
			runExecutions = append(runExecutions, r)

			return runExecutions, nil
		}
	}

	for _, flow := range availableOutgoings {
		n, err := j.ProcessDefinition.GetNode(flow.TargetRef)
		if err != nil {
			return nil, err
		}

		switch n.GetType() {
		case "UserTask", "SuggestTask":
			task := n.(bpmn.Task)
			assigneeNode := task.GetAssigneeNode()
			if assigneeNode.AssigneeType == bpmnEnums.AssigneeTypeManager {
				// 层级审批，找申请人上级，未找到跳过当前节点
				manager, err := j.getUserManager(j.RunExecution.StartByID)
				if err != nil {
					j.Log.WithError(err).WithField("email", j.RunExecution.StartByID).Error("get user manager failed")
					return nil, err
				}

				r := models.NextRunExecution(j.RunExecution, n)
				r.Status = enums.RunExecutionStatusPending
				r.OriginalAssignee = manager
				r.Assignee = manager

				if manager == "" {
					r.Status = enums.RunExecutionStatusRunning
					r.Memo = "系统自动通过"
				}

				runExecutions = append(runExecutions, r)
			} else if assigneeNode.Assignee != "" {
				assignees, err := j.GetAssignee(assigneeNode.AssigneeType, assigneeNode.Assignee)
				if err != nil {
					return nil, err
				}

				currentAssignees, left := j.getCurrentAndLeftAssignees(assignees, task.GetIsSequential())

				for _, assignee := range currentAssignees {
					r := models.NextRunExecution(j.RunExecution, n)
					r.Status = enums.RunExecutionStatusPending
					if assignee == "" {
						r.Status = enums.RunExecutionStatusRunning
						r.Memo = "系统自动通过"
					}
					r.OriginalAssignee = assignee
					r.Assignee = assignee
					r.LeftAssignees = left
					runExecutions = append(runExecutions, r)
				}
			} else {
				r := models.NextRunExecution(j.RunExecution, n)
				r.Status = enums.RunExecutionStatusPending
				runExecutions = append(runExecutions, r)
			}
		case "IntermediateCatchEvent":
			catchEvent := n.(*bpmn.IntermediateCatchEvent)
			relationMgr := mgr.SignalExecutionMgr{Base: *j.Mgr}
			// 分裂多个execution, 并创建execution和signal关联记录
			for _, signal := range catchEvent.SignalEvents {
				execution := models.NextRunExecution(j.RunExecution, n)
				execution.Status = enums.RunExecutionStatusRunning
				runExecutions = append(runExecutions, execution)

				signalKey := signal.SignalRef
				if j.RunExecution.Params != "" {
					signalKey, err = exp.EvalSpread(signal.SignalRef, j.RunExecution.Params)
					if err != nil {
						j.Log.Errorf("relationMgr.Create, exp.EvalSpread(%s, %s) error: %s", signal.SignalRef, j.RunExecution.Params, err)
						continue
					}
				}

				if _, err := relationMgr.Create(&models.SignalExecutionRelation{
					SignalRef:         signalKey,
					Business:          signal.Business,
					ExecutionId:       execution.ID,
					Completed:         false,
					ProcessInstanceID: execution.ProcessInstanceID,
					ActivityDefineKey: catchEvent.GetKey(),
					SignalDefineKey:   signal.ID,
				}); err != nil {
					return nil, err
				}
			}

			if len(catchEvent.SignalEvents) == 0 {
				execution := models.NextRunExecution(j.RunExecution, n)
				execution.Status = enums.RunExecutionStatusRunning
				runExecutions = append(runExecutions, execution)
			}
		case "EndEvent":
			isEndNode = true
			fallthrough
		default:
			runExecutions = append(runExecutions, models.NextRunExecution(j.RunExecution, n))
		}
	}

	return
}

func (j *Flow) getCurrentAndLeftAssignees(assignees string, isSequential bool) ([]string, string) {
	assigneeList := strings.Split(assignees, ",")
	if !isSequential || len(assigneeList) <= 1 {
		return assigneeList, ""
	}

	return assigneeList[:1], strings.Join(assigneeList[1:], ",")

}

// 有一个节点走不下去，直接跳到end节点
func (j *Flow) getFailRunExecutions(availableOutgoings []*bpmn.SequenceFlow) (runExecutions []*models.RunExecution, err error) {

	//todo fail action
	if len(j.ProcessDefinition.ProcessModel.Process.EndEvents) == 0 {
		return nil, fmt.Errorf("empty end events")
	}

	endEvent := j.ProcessDefinition.ProcessModel.Process.EndEvents[0]
	runExecutions = []*models.RunExecution{models.NextRunExecution(j.RunExecution, endEvent)}

	executionMgr := mgr.RunExecutionMgr{Base: *j.Context.Mgr}
	// 获取最新一个 failed execution
	var re models.RunExecution
	err = executionMgr.Model(&re).
		Where("process_instance_id=? and status=?",
			j.RunExecution.ProcessInstanceID, enums.RunExecutionStatusFailed).
		Order("updated_at desc").
		First(&re).Error
	if err != nil {
		return
	}

	err = setStatusParams(enums.ProcessStatusFailed, runExecutions...)
	// 添加驳回备注信息
	p, err := params.SetParam(runExecutions[0].Params, ParamFlowMemo, re.Memo)
	if err != nil {
		return
	}
	for _, execution := range runExecutions {
		execution.Params = p
	}

	return
}

func setStatusParams(status enums.ProcessInstanceStatus, executions ...*models.RunExecution) (err error) {
	if len(executions) == 0 {
		return
	}

	var p string
	if p, err = params.SetParam(executions[0].Params, ParamFlowStatus, status); err != nil {
		return
	}

	if p, err = params.SetParam(p, ParamFlowStatusText, getStatusText(status)); err != nil {
		return
	}

	for _, exec := range executions {
		exec.Params = p
	}

	return
}

func getStatusText(status enums.ProcessInstanceStatus) string {
	statusText := "已通过"
	switch status {
	case enums.ProcessStatusFailed:
		statusText = "未通过"
	case enums.ProcessStatusRemoved:
		statusText = "已撤回"
	case enums.ProcessStatusPending:
		statusText = "执行中"
	case enums.ProcessStatusSuspended:
		statusText = "已挂起"
	}

	return statusText
}

// checkNodeStatusWithActivityMap 使用预先查询的ActivityInstance映射检查节点状态
func (j *Flow) checkNodeStatusWithActivityMap(executions []*models.RunExecution, activityMap map[uint64]*models.ActivityInstance) (status ctx.NodeStatus, err error) {
	if len(executions) == 0 {
		return
	}

	var nodeFinishedCnt, nodeFailedCnt, total int
	for _, exec := range executions {
		var isFinished, isFailed bool

		if exec.ID == j.RunExecution.ID {
			// 当前execution：使用RunExecutionStatus判断
			isFinished = exec.Status == enums.RunExecutionStatusFinished
			isFailed = exec.Status == enums.RunExecutionStatusFailed
		} else {
			// 其他execution：使用ActivityStatus判断
			if activity, exists := activityMap[exec.ID]; exists {
				isFinished = activity.Status == enums.ActivityStatusCompleted
				isFailed = activity.Status == enums.ActivityStatusFailed
			} else {
				// 如果没有找到ActivityInstance，跳过
				total++
				continue
			}
		}

		if isFinished {
			nodeFinishedCnt++
		} else if isFailed {
			node, e := j.ProcessDefinition.GetExecutionNode(exec)
			if e != nil {
				return 0, e
			}

			if _, ok := node.(*bpmn.SuggestTask); ok {
				nodeFinishedCnt++
			} else {
				nodeFailedCnt++
			}
		}

		total++
	}

	// 调用Context中拆分出来的状态计算函数
	return j.Context.CalculateNodeStatusFromCounts(executions, nodeFinishedCnt, nodeFailedCnt, total)
}
