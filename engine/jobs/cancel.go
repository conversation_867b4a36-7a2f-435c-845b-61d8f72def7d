package jobs

import (
	"errors"
	"fmt"
	"time"

	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services/scheduler"
)

// Cancel is job to perform cancel action
type Cancel struct {
	*ctx.Context
	operator string
	memo     string
}

// NewCancel instances a cancel job
func NewCancel(ctx *ctx.Context, operator string, memo string) *Cancel {
	return &Cancel{
		Context:  ctx,
		operator: operator,
		memo:     memo,
	}
}

// Key implements interface to get key of current job
func (j *Cancel) Key() string {
	return fmt.Sprintf("process:%d", j.RunExecution.ProcessInstanceID)
}

// Status implements interface to get status of current job
func (j *Cancel) Status() string {
	return j.RunExecution.Status.String()
}

// LockTime implements interface to get lock time of current job
func (j *Cancel) LockTime() time.Duration {
	return time.Second * 5
}

// Run implements interface to run current job
func (j *Cancel) Run() (nextJobs []scheduler.Job, err error) {

	if node, _ := j.CurrentNode(); node.GetType() == "EndEvent" {
		err = errors.New("process is end")
		return
	}

	err = j.Mgr.First(&models.RunExecution{}, j.RunExecution.ID).Error
	if err != nil {
		return
	}

	pendingExecutions, err := j.getPendings()
	if err != nil {
		return
	}

	runExecution, err := j.getCancelRunExecution()
	if err != nil {
		return
	}

	if err = j.performNodeLeave(pendingExecutions, runExecution); err != nil {
		return
	}

	nextJobs = append(nextJobs, NewExecute(ctx.BuildContext(runExecution, j.ProcessDefinition, j.Mgr, j.AdminTransport, j.SofaService, j.GaeaService, j.Log, false)))

	return
}

func (j *Cancel) updateActivities(db *mgr.Base, executionIds []uint64) (err error) {
	activity := models.ActivityInstance{
		Status: enums.ActivityStatusRemoved,
		Actor:  j.operator,
		EndAt:  time.Now(),
		Memo:   j.memo,
	}

	return db.Model(&models.ActivityInstance{}).
		Where("execution_id in (?)", executionIds).
		Updates(activity).Error
}

func (j *Cancel) performNodeLeave(pendings []*models.RunExecution, runExecution *models.RunExecution) (err error) {

	// check if end node
	return j.Mgr.PerformTransaction(func(db *mgr.Base) (err error) {

		// update activity
		executionIds := make([]uint64, len(pendings))
		for i, exe := range pendings {
			executionIds[i] = exe.ID
		}

		j.updateActivities(db, executionIds)

		// delete runnings
		var deletes []uint64
		for _, pending := range pendings {
			deletes = append(deletes, pending.ID)
		}

		if err = db.Where("id in (?)", deletes).Delete(&models.RunExecution{}).Error; err != nil {
			return
		}

		// create running
		if err = db.Create(runExecution).Error; err != nil {
			return
		}

		// create activity
		node, err := ctx.GetExecutionNode(runExecution, j.ProcessDefinition)
		if err != nil {
			return
		}
		if err = db.Create(models.NewActivityInstance(runExecution, node.GetType())).Error; err != nil {
			return
		}

		return
	})
}

func (j *Cancel) getPendings() (runExecutions []*models.RunExecution, err error) {

	err = j.Mgr.
		Model(&models.RunExecution{}).
		Where("process_instance_id = ?", j.RunExecution.ProcessInstanceID).
		Find(&runExecutions).Error

	return
}

func (j *Cancel) getCancelRunExecution() (runExecution *models.RunExecution, err error) {

	//todo fail action
	if len(j.ProcessDefinition.ProcessModel.Process.EndEvents) == 0 {
		return nil, fmt.Errorf("empty end events")
	}

	endEvent := j.ProcessDefinition.ProcessModel.Process.EndEvents[0]
	runExecution = models.NextRunExecution(j.RunExecution, endEvent)

	err = setStatusParams(enums.ProcessStatusRemoved, runExecution)
	return
}
