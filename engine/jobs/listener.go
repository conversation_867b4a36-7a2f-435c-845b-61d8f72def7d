package jobs

import (
	"encoding/json"
	"errors"
	"net/http"

	"qiniu.io/qbpm/engine/services/system"
	"qiniu.io/qbpm/engine/util/logger"

	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/mgr"
	ormModel "qiniu.io/qbpm/engine/models"
)

// ParamFlowStatus param key of flow status
const ParamFlowStatus string = "flow.status"

// ParamFlowStatusText param key for flow status humanize text
const ParamFlowStatusText string = "flow.status_text"

// ParamFlowMemo param key of execution memo
const ParamFlowMemo string = "flow.memo"

// Listener is job to perform listener action
type Listener struct {
	*ctx.Context
	BpmnModel   *models.BpmnModel
	Node        models.INode
	ExecutionID uint64

	ListenerFireMap  map[string][]*models.Listener
	ListenerMgr      *mgr.ListenerMgr
	activityInstance *ormModel.ActivityInstance
	log              logrus.FieldLogger
}

// NewListener instances a listener action job
func NewListener(node models.INode, processDef *ormModel.ProcessDefinition, executionID uint64, base *mgr.Base, adminTransport http.RoundTripper) *Listener {
	listenerMgr := mgr.ListenerMgr{Base: *base}
	listener := &Listener{BpmnModel: processDef.ProcessModel, Node: node, ExecutionID: executionID, ListenerMgr: &listenerMgr}

	activityMgr := mgr.ActivityInstanceMgr{Base: *base}
	listener.activityInstance, _ = activityMgr.Get(&ormModel.ActivityInstance{ExecutionID: executionID})
	listener.Context = ctx.BuildContext(getExecutionByActivity(listener.activityInstance, executionID, processDef), processDef, base, adminTransport, nil, nil, nil, false)
	listener.log = listener.Context.Log.WithFields(logrus.Fields{
		logger.FieldKeyPrefix: "Listener",
		"nodeKey":             node.GetKey(),
	})

	return listener
}

func getExecutionByActivity(activity *ormModel.ActivityInstance, executionID uint64, processDef *ormModel.ProcessDefinition) *ormModel.RunExecution {
	if activity == nil {
		return &ormModel.RunExecution{
			Base: ormModel.Base{
				ID: executionID,
			},
			ProcessDefinitionID: processDef.ID,
		}
	}

	return &ormModel.RunExecution{
		Base: ormModel.Base{
			ID: activity.ExecutionID,
		},
		ProcessInstanceID:   activity.ProcessInstanceID,
		ActivityDefineKey:   activity.ActivityDefineKey,
		Name:                activity.Name,
		Params:              activity.Params,
		Status:              activity.Status.ExecutionStatus(),
		OriginalAssignee:    activity.OriginalActor,
		Assignee:            activity.Actor,
		ProcessDefinitionID: processDef.ID,
	}
}

func (job *Listener) createListenerModel(listenerKey, actionKey, fireType string) *ormModel.Listener {
	listenerMode := ormModel.Listener{
		ExecutionID:         job.ExecutionID,
		ActionKey:           actionKey,
		ListenerKey:         listenerKey,
		NodeKey:             job.Node.GetKey(),
		Fire:                fireType,
		Status:              ormModel.BeginListener,
		ProcessInstanceID:   job.activityInstance.ProcessInstanceID,
		ProcessDefinitionID: job.ProcessDefinition.ID,
	}
	return &listenerMode
}

// RunListener run listener action job
func (job *Listener) RunListener(fireType string, listener *models.Listener, actionID string) error {
	var errMap = make(map[string]string)

	actions, err := system.GetActions(listener.ActionRefs...)
	if err != nil {
		return err
	}

	runActions := actions
	if actionID != "" {
		for _, action := range actions {
			if action.Code == actionID {
				runActions = []*system.Action{action}
				break
			}
		}
	}

	for _, action := range runActions {
		l := job.log.WithFields(logrus.Fields{
			"listenerType": listener.Fire,
			"actionKey":    action.Code,
		})

		listenerModel := job.createListenerModel(listener.ID, action.Code, fireType)
		if err := job.ListenerMgr.Upsert(listenerModel); err != nil {
			l.WithError(err).Error("listener model insert err")
		}

		listenerModel.Status = ormModel.CompleteListener
		listenerModel.Errors = "ok"

		_, err = job.DoAction(action.Code, job.activityInstance, false)
		if err != nil {
			l.WithError(err).Error("do action with error")
			errMap[action.Code] = err.Error()
			listenerModel.Status = ormModel.FailListener
			listenerModel.Errors = err.Error()
		}

		if err := job.ListenerMgr.Upsert(listenerModel); err != nil {
			l.WithError(err).Error("listener model update err")
		}
	}

	if len(errMap) != 0 {
		errInfo, _ := json.Marshal(errMap)
		return errors.New(string(errInfo))
	}
	return nil
}

func (job *Listener) initListenerFireMap() {
	job.ListenerFireMap = job.Node.GetListenerMap()
}

// FireAction fire listener action
// len(aboutIds)=2, listenerId = aboutIds[0], actionId = aboutIds[1]
func (job *Listener) FireAction(fireType string, aboutIds ...string) error {
	if job.ListenerFireMap == nil {
		job.initListenerFireMap()
	}

	var errMap = make(map[string]string)
	var runListeners = job.ListenerFireMap[fireType]
	var runAction string

	if len(aboutIds) == 2 {
		for _, listener := range runListeners {
			if listener.ID == aboutIds[0] {
				runListeners = []*models.Listener{listener}
				runAction = aboutIds[1]
				break
			}
		}
	}

	for _, listener := range runListeners {
		err := job.RunListener(fireType, listener, runAction)
		if err != nil {
			errMap[listener.ID] = err.Error()
		}
	}

	if len(errMap) != 0 {
		errInfo, _ := json.Marshal(errMap)
		err := errors.New(string(errInfo))
		job.log.WithError(err).Error("run listener action with error")
		return err
	}

	return nil
}
