package jobs_test

import (
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/bpmn/parser"
	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/jobs"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/services/scheduler"
	"qiniu.io/qbpm/engine/services/system"
	"qiniu.io/qbpm/engine/test"
	"qiniu.io/qbpm/engine/util/snowflake"
)

var _ = Describe("execute job", func() {
	var (
		processDefinition *models.ProcessDefinition
		processInstance   *models.ProcessInstance
	)
	xml := `<?xml version="1.0" encoding="UTF-8"?>
		<definitions id="definitions"
	  	xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
	  		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	 
			<process id="flow-test" name="flow-test">
				<startEvent id="theStart"/>
				<sequenceFlow id="startStatic1" sourceRef="theStart" targetRef="catchEvent1"/>
				<intermediateCatchEvent id="catchEvent1" multiDecision="any">
					<signalEventDefinition id="signal1" signalRef="params.signalKey">
						<conditionExpression xsi:type="tFormalExpression">
							<![CDATA[
								function fn(data){
									var key = data.signalKey
									return data[key]["flow_status"] == 3
								}
								fn(params);
							]]>
						</conditionExpression>
					</signalEventDefinition>
					<signalEventDefinition id="signal2" signalRef="signalref2" business="compact"/>
				</intermediateCatchEvent>

				<sequenceFlow id="startStatic2" sourceRef="catchEvent1" targetRef="emptyThrowEvent"/>

				<userTask id="emptyThrowEvent">
					<intermediateThrowEvent id="throwEvent">
					<signalEventDefinition id="" signalRef="params.signalKey"  business="" payload='{"key":"params.key", "flow_status":"params.flow_status"}'/>
					</intermediateThrowEvent>
				</userTask>

				<sequenceFlow id="startStatic3" sourceRef="emptyThrowEvent" targetRef="static1"/>

				<userTask id="static1" assigneeType="static" assignee="<EMAIL>"/>
				<sequenceFlow id="toExclusive" sourceRef="static1" targetRef="exclusive"/>

				<exclusiveGateway id="exclusive" defaultFlow="exclusive2"/>
				<sequenceFlow id="exclusive1" sourceRef="exclusive" targetRef="static2">
					<conditionExpression xsi:type="tFormalExpression">params.exclusive_more</conditionExpression>
				</sequenceFlow>
				<sequenceFlow id="exclusive2" sourceRef="exclusive" targetRef="theEnd">
				</sequenceFlow>

				<userTask id="static2" assigneeType="static" assignee="<EMAIL>"/>
				<sequenceFlow id="toInclusive" sourceRef="static2" targetRef="inclusive"/>

				<inclusiveGateway id="inclusive" defaultFlow="inclusiveParallel"/>
				<sequenceFlow id="inclusive1" sourceRef="inclusive" targetRef="static3">
					<conditionExpression xsi:type="tFormalExpression">params.inclusives > 0</conditionExpression>
				</sequenceFlow>
				<sequenceFlow id="inclusive2" sourceRef="inclusive" targetRef="static4">
					<conditionExpression xsi:type="tFormalExpression">params.inclusives > 1</conditionExpression>
				</sequenceFlow>
				<sequenceFlow id="inclusiveParallel" sourceRef="inclusive" targetRef="parallel">
					<conditionExpression xsi:type="tFormalExpression">1 != 1</conditionExpression>
				</sequenceFlow>
				
				<userTask id="static3" assigneeType="static" assignee="<EMAIL>"/>
				<sequenceFlow id="static3Parallel" sourceRef="static3" targetRef="parallel"/>
				<userTask id="static4" assigneeType="static" assignee="<EMAIL>"/>
				<sequenceFlow id="static4Parallel" sourceRef="static4" targetRef="parallel"/>


				<parallelGateway id="parallel" sourceRef="theStart" targetRef="theEnd"/>
				<sequenceFlow id="parallel1" sourceRef="parallel" targetRef="static5"/>
				<sequenceFlow id="parallel2" sourceRef="parallel" targetRef="static6"/>
				<sequenceFlow id="parallel3" sourceRef="parallel" targetRef="static7"/>
				<sequenceFlow id="parallel4" sourceRef="parallel" targetRef="static8"/>

				<userTask id="static5" multiDecision="any" assigneeType="static" assignee="<EMAIL>,<EMAIL>"/>
				<sequenceFlow id="static5ParallelEnd" sourceRef="static5" targetRef="parallelEnd"/>
				
				<userTask id="static6" multiDecision="all" assigneeType="static" assignee="<EMAIL>,<EMAIL>"/>
				<sequenceFlow id="static6ParallelEnd" sourceRef="static6" targetRef="parallelEnd"/>
				
				<userTask id="static7" multiDecision="rate" multiDecisionRate="0.5" assigneeType="static" assignee="<EMAIL>,<EMAIL>"/>
				<sequenceFlow id="static7ParallelEnd" sourceRef="static7" targetRef="parallelEnd"/>
				
				<userTask id="static8" multiDecision="threshold" multiDecisionThreshold="1" assigneeType="static" assignee="<EMAIL>,<EMAIL>"/>
				<sequenceFlow id="static8ParallelEnd" sourceRef="static8" targetRef="parallelEnd"/>

				<parallelGateway id="parallelEnd"/>
				<sequenceFlow id="parallelEndEnd" sourceRef="parallelEnd" targetRef="theEnd"/>

				<endEvent id="theEnd"/>
			</process>

		</definitions>`

	BeforeEach(func() {
		base = test.Init()
		runExecutionMgr = mgr.NewRunExecutionMgr(*base)
		data := []byte(xml)
		model, err := parser.ParseBpmn(data)
		Expect(err).To(BeNil())
		processDefinition = models.NewProcessDefinition()
		processDefinition.XMLData = []byte(xml)
		processDefinition.ProcessModel = model

		processInstance = &models.ProcessInstance{
			ProcessDefinitionID: processDefinition.ID,
			Status:              enums.ProcessStatusPending,
		}
		processInstance.ID = snowflake.Generator.ID()
		base.Create(processInstance)
		base.Create(processDefinition)
		_ = system.NewSystemService(&services.Base{Mgr: base}, nil)
	})

	AfterEach(func() {
		base.Close()
	})

	It("test simple flow job", func() {
		execution, _ := models.NewRunExecution(*processDefinition, "", "", "", 0, 0)
		execution.Status = enums.RunExecutionStatusFinished
		runExecutionMgr.Create(execution)
		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		Expect(job.Status()).To(Equal(enums.RunExecutionStatusFinished.String()))
		Expect(job.Key()).NotTo(Equal(""))
		Expect(job.LockTime()).To(Equal(time.Second * 5))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecution models.RunExecution
		Expect(base.Where("process_instance_id = ?", execution.ProcessInstanceID).
			First(&findExecution).Error).NotTo(HaveOccurred())
		Expect(findExecution.ActivityDefineKey).To(Equal("catchEvent1"))
	})

	It("test exlusive gateway flow job", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "exclusive",
			Status:              enums.RunExecutionStatusFinished,
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)
		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		Expect(job.Status()).To(Equal(enums.RunExecutionStatusFinished.String()))
		Expect(job.Key()).NotTo(Equal(""))
		Expect(job.LockTime()).To(Equal(time.Second * 5))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecution models.RunExecution
		Expect(base.Where("process_instance_id = ?", execution.ProcessInstanceID).
			First(&findExecution).Error).NotTo(HaveOccurred())
		Expect(findExecution.ActivityDefineKey).To(Equal("theEnd"))
	})

	It("test exlusive gateway flow job with params", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "exclusive",
			Status:              enums.RunExecutionStatusFinished,
			Params:              `{"exclusive_more":true}`,
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)
		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		Expect(job.Status()).To(Equal(enums.RunExecutionStatusFinished.String()))
		Expect(job.Key()).NotTo(Equal(""))
		Expect(job.LockTime()).To(Equal(time.Second * 5))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecution models.RunExecution
		Expect(base.Where("process_instance_id = ?", execution.ProcessInstanceID).
			First(&findExecution).Error).NotTo(HaveOccurred())
		Expect(findExecution.ActivityDefineKey).To(Equal("static2"))
	})

	It("test inclusive gateway flow job", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "inclusive",
			Status:              enums.RunExecutionStatusFinished,
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)
		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		Expect(job.Status()).To(Equal(enums.RunExecutionStatusFinished.String()))
		Expect(job.Key()).NotTo(Equal(""))
		Expect(job.LockTime()).To(Equal(time.Second * 5))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecution models.RunExecution
		Expect(base.Where("process_instance_id = ?", execution.ProcessInstanceID).
			First(&findExecution).Error).NotTo(HaveOccurred())
		Expect(findExecution.ActivityDefineKey).To(Equal("parallel"))
	})

	It("test inclusive gateway flow job with 1 outgoing", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "inclusive",
			Status:              enums.RunExecutionStatusFinished,
			Params:              `{"inclusives":1}`,
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)
		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		Expect(job.Status()).To(Equal(enums.RunExecutionStatusFinished.String()))
		Expect(job.Key()).NotTo(Equal(""))
		Expect(job.LockTime()).To(Equal(time.Second * 5))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecution models.RunExecution
		Expect(base.Where("process_instance_id = ?", execution.ProcessInstanceID).
			First(&findExecution).Error).NotTo(HaveOccurred())
		Expect(findExecution.ActivityDefineKey).To(Equal("static3"))

		var cnt int
		Expect(base.Table("run_execution").Where("process_instance_id = ?", execution.ProcessInstanceID).
			Count(&cnt).Error).NotTo(HaveOccurred())
		Expect(cnt).To(Equal(1))
	})

	It("test inclusive gateway flow job with 2 outgoings", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "inclusive",
			Status:              enums.RunExecutionStatusFinished,
			Params:              `{"inclusives":2}`,
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)
		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		Expect(job.Status()).To(Equal(enums.RunExecutionStatusFinished.String()))
		Expect(job.Key()).NotTo(Equal(""))
		Expect(job.LockTime()).To(Equal(time.Second * 5))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var cnt int
		Expect(base.Table("run_execution").Where("process_instance_id = ?", execution.ProcessInstanceID).
			Count(&cnt).Error).NotTo(HaveOccurred())
		Expect(cnt).To(Equal(2))
	})

	It("test parallel gateway flow job", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "parallel",
			Status:              enums.RunExecutionStatusFinished,
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)
		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		Expect(job.Status()).To(Equal(enums.RunExecutionStatusFinished.String()))
		Expect(job.Key()).NotTo(Equal(""))
		Expect(job.LockTime()).To(Equal(time.Second * 5))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var cnt int
		Expect(base.Table("run_execution").Where("process_instance_id = ?", execution.ProcessInstanceID).
			Count(&cnt).Error).NotTo(HaveOccurred())
		Expect(cnt).To(Equal(8))
	})

	It("test multi instance flow job", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "parallel",
			Status:              enums.RunExecutionStatusFinished,
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)
		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecutions []models.RunExecution
		Expect(base.Where("process_instance_id = ?", processInstance.ID).
			Find(&findExecutions).Error).NotTo(HaveOccurred())
		Expect(len(findExecutions)).To(Equal(8))

		// test all passed if any one passed
		var execAnys []models.RunExecution
		Expect(base.Where("process_instance_id = ?", processInstance.ID).
			Where("activity_define_key = ?", "static5").
			Find(&execAnys).Error).NotTo(HaveOccurred())
		execAnys[0].Status = enums.RunExecutionStatusFinished
		base.Model(&execAnys[0]).Update("status", enums.RunExecutionStatusFinished)
		job = jobs.NewFlow(ctx.BuildContext(&execAnys[0], processDefinition, base, nil, nil, nil, logrus.New(), false))
		job.Run()

		var execAny models.RunExecution
		Expect(base.Where("id = ?", execAnys[1].ID).First(&execAny).Error).NotTo(HaveOccurred())
		Expect(execAny.Status).To(Equal(enums.RunExecutionStatusNoResponse))

		// test all passed only if all passed
		var execAlls []models.RunExecution
		Expect(base.Where("process_instance_id = ?", processInstance.ID).
			Where("activity_define_key = ?", "static6").
			Find(&execAlls).Error).NotTo(HaveOccurred())
		execAlls[0].Status = enums.RunExecutionStatusFinished
		base.Model(&execAlls[0]).Update("status", enums.RunExecutionStatusFinished)
		job = jobs.NewFlow(ctx.BuildContext(&execAlls[0], processDefinition, base, nil, nil, nil, logrus.New(), false))
		job.Run()

		var execAll models.RunExecution
		Expect(base.Model(&models.RunExecution{}).Where("id = ?", execAlls[0].ID).
			First(&execAll).Error).NotTo(HaveOccurred())
		Expect(execAll.Status).To(Equal(enums.RunExecutionStatusFinished))
		var execAll2 models.RunExecution
		Expect(base.Model(&models.RunExecution{}).Where("id = ?", execAlls[1].ID).
			First(&execAll2).Error).NotTo(HaveOccurred())
		Expect(execAll2.Status).To(Equal(enums.RunExecutionStatusPending))

		execAlls[1].Status = enums.RunExecutionStatusFinished
		base.Model(&execAlls[1]).Update("status", enums.RunExecutionStatusFinished)
		job = jobs.NewFlow(ctx.BuildContext(&execAlls[1], processDefinition, base, nil, nil, nil, logrus.New(), false))
		job.Run()

		var cnt int
		Expect(base.Model(&models.RunExecution{}).
			Where("process_instance_id = ?", processInstance.ID).
			Where("activity_define_key = ?", "static6").
			Where("status = ?", enums.RunExecutionStatusFinished).
			Count(&cnt).Error).NotTo(HaveOccurred())
		Expect(cnt).To(Equal(2))

		// test all passed only if rate passed
		var execRates []models.RunExecution
		Expect(base.Where("process_instance_id = ?", processInstance.ID).
			Where("activity_define_key = ?", "static7").
			Find(&execRates).Error).NotTo(HaveOccurred())
		execRates[0].Status = enums.RunExecutionStatusFinished
		base.Model(&execRates[0]).Update("status", enums.RunExecutionStatusFinished)
		job = jobs.NewFlow(ctx.BuildContext(&execRates[0], processDefinition, base, nil, nil, nil, logrus.New(), false))
		job.Run()

		var execRate models.RunExecution
		Expect(base.Where("id = ?", execRates[1].ID).First(&execRate).Error).NotTo(HaveOccurred())
		Expect(execRate.Status).To(Equal(enums.RunExecutionStatusNoResponse))

		// test all passed only if threshold passed
		var execThresholds []models.RunExecution
		Expect(base.Where("process_instance_id = ?", processInstance.ID).
			Where("activity_define_key = ?", "static8").
			Find(&execThresholds).Error).NotTo(HaveOccurred())
		execThresholds[0].Status = enums.RunExecutionStatusFinished
		base.Model(&execThresholds[0]).Update("status", enums.RunExecutionStatusFinished)
		job = jobs.NewFlow(ctx.BuildContext(&execThresholds[0], processDefinition, base, nil, nil, nil, logrus.New(), false))
		job.Run()

		Expect(base.Model(&models.RunExecution{}).
			Where("process_instance_id = ?", processInstance.ID).
			Where("activity_define_key = ?", "static8").
			Count(&cnt).Error).NotTo(HaveOccurred())
		Expect(cnt).To(Equal(0))

		var finalExecution models.RunExecution
		Expect(base.Where("process_instance_id = ?", processInstance.ID).First(&finalExecution).Error).NotTo(HaveOccurred())
		Expect(finalExecution.ActivityDefineKey).To(Equal("parallelEnd"))
	})

	It("test wait multi signal", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "theStart",
			Status:              enums.RunExecutionStatusFinished,
			Params:              "{\"signalKey\":\"signalref\"}",
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)

		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		nextJobs, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecutions []models.RunExecution
		Expect(base.Where("process_instance_id = ?", processInstance.ID).
			Find(&findExecutions).Error).NotTo(HaveOccurred())
		Expect(len(findExecutions)).To(Equal(2))

		relationMgr := mgr.SignalExecutionMgr{runExecutionMgr.Base}
		signals, err := relationMgr.MgetExecution("signalref2", "compact")
		Expect(err).To(BeNil())
		Expect(len(signals)).To(Equal(1))

		signals, err = relationMgr.MgetExecution("signalref", "")
		Expect(err).To(BeNil())
		Expect(len(signals)).To(Equal(1))

		for _, job := range nextJobs {
			_, err = job.Run()
			Expect(err).To(BeNil())
		}
		base.Where("process_instance_id = ?", processInstance.ID).Find(&findExecutions)
		for _, execution := range findExecutions {
			Expect(execution.Status).To(Equal(enums.RunExecutionStatusPending))
		}
	})

	It("test catch signal(signal throw before, success)", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "theStart",
			Status:              enums.RunExecutionStatusFinished,
			Params:              "{\"signalKey\":\"signalref\"}",
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)

		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		nextJobs, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		// 抛消息
		signalInfo := models.SignalInfo{
			SignalRef: "signalref",
			Business:  "",
			Payload:   models.Json("{\"node_status\":\"success\", \"flow_status\":3}"),
		}
		signalMgr := mgr.SignalMgr{runExecutionMgr.Base}
		_, err = signalMgr.Create(&signalInfo)
		Expect(err).To(BeNil())

		for _, job := range nextJobs {
			// signalref会被触发，signalref2挂起
			_, err := job.Run()
			Expect(err).To(BeNil())
		}

		relationMgr := mgr.SignalExecutionMgr{runExecutionMgr.Base}
		var relations []*models.SignalExecutionRelation
		err = relationMgr.DB.Where("signal_ref = ?", "signalref").Find(&relations).Error
		Expect(err).To(BeNil())
		Expect(len(relations)).To(Equal(1))
		var executions []*models.RunExecution
		err = runExecutionMgr.DB.Where("id=?", relations[0].ExecutionId).Find(&executions).Error
		Expect(err).To(BeNil())
		Expect(len(executions)).To(Equal(1))
		Expect(executions[0].Status).To(Equal(enums.RunExecutionStatusFinished))

		err = relationMgr.DB.Where("signal_ref = ?", "signalref2").Find(&relations).Error
		Expect(err).To(BeNil())
		Expect(len(relations)).To(Equal(1))
		err = runExecutionMgr.DB.Where("id=?", relations[0].ExecutionId).Find(&executions).Error
		Expect(err).To(BeNil())
		Expect(len(executions)).To(Equal(1))
		Expect(executions[0].Status).To(Equal(enums.RunExecutionStatusPending))
	})

	It("test catch signal(signal throw before, failure)", func() {
		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "theStart",
			Status:              enums.RunExecutionStatusFinished,
			Params:              "{\"signalKey\":\"signalref\"}",
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)

		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		// theStart结束，准备扭转catchEvent，并进行多实例化
		nextJobs, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		// 抛消息
		signalInfo := models.SignalInfo{
			SignalRef: "signalref2",
			Business:  "compact",
			Payload:   models.Json("{\"node_status\":\"failure\"}"),
		}
		signalMgr := mgr.SignalMgr{runExecutionMgr.Base}
		_, err = signalMgr.Create(&signalInfo)
		Expect(err).To(BeNil())
		signalInfo = models.SignalInfo{
			SignalRef: "signalref",
			Business:  "",
			Payload:   models.Json("{\"node_status\":\"success\"}"),
		}
		_, err = signalMgr.Create(&signalInfo)
		Expect(err).To(BeNil())

		for _, job := range nextJobs {
			_, err := job.Run()
			Expect(err).To(BeNil())
		}

		var relations []*models.SignalExecutionRelation
		var executions []*models.RunExecution
		relationMgr := mgr.SignalExecutionMgr{runExecutionMgr.Base}

		err = relationMgr.DB.Where("signal_ref = ?", "signalref2").Find(&relations).Error
		Expect(err).To(BeNil())
		Expect(len(relations)).To(Equal(1))
		err = runExecutionMgr.DB.Where("id=?", relations[0].ExecutionId).Find(&executions).Error
		Expect(err).To(BeNil())
		Expect(len(executions)).To(Equal(1))
		Expect(executions[0].Status).To(Equal(enums.RunExecutionStatusFailed))

		err = relationMgr.DB.Where("signal_ref = ?", "signalref").Find(&relations).Error
		Expect(err).To(BeNil())
		Expect(len(relations)).To(Equal(1))
		err = runExecutionMgr.DB.Where("id=?", relations[0].ExecutionId).Find(&executions).Error
		Expect(err).To(BeNil())
		Expect(len(executions)).To(Equal(1))
		Expect(executions[0].Status).To(Equal(enums.RunExecutionStatusFailed))

	})

	It("test throw signal", func() {
		waitExecution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "theStart",
			Status:              enums.RunExecutionStatusFinished,
			Params:              "{\"signalKey\":\"signalref\", \"key\":\"asd\", \"flow_status\":3}",
		}
		waitExecution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(waitExecution)
		waitJob := jobs.NewFlow(ctx.BuildContext(waitExecution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		// 创建多实例,等待不同消息
		nexts, err := waitJob.Run()
		Expect(err).NotTo(HaveOccurred())
		Expect(len(nexts)).To(Equal(2))

		execution := &models.RunExecution{
			ProcessDefinitionID: processDefinition.ID,
			ProcessInstanceID:   processInstance.ID,
			ActivityDefineKey:   "emptyThrowEvent",
			Status:              enums.RunExecutionStatusRunning,
			Params:              "{\"signalKey\":\"signalref\", \"key\":\"asd\", \"flow_status\":3}",
		}
		execution.ID = snowflake.Generator.ID()
		runExecutionMgr.Create(execution)

		executionJob := jobs.NewExecute(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		// 执行节点，获取flow，执行flow，会重启pending消息的execution
		flows, err := executionJob.Run()
		Expect(err).NotTo(HaveOccurred())
		var waitExecutions []scheduler.Job
		// len(flows) = 1
		for _, flow := range flows {
			// 获取pending消息的execution
			executions, err := flow.Run()
			Expect(err).NotTo(HaveOccurred())
			waitExecutions = append(waitExecutions, executions...)
		}

		relationMgr := mgr.SignalExecutionMgr{runExecutionMgr.Base}
		// 验证触发的消息
		midExecutions, err := relationMgr.MgetExecution("signalref", "")
		Expect(midExecutions[0].Status).To(Equal(enums.RunExecutionStatusRunning))
		Expect(midExecutions[0].Params).To(ContainSubstring("\"node_status\":\"success\""))
		var midSignals []models.SignalInfo
		signalMgr := mgr.SignalMgr{*base}
		signalMgr.Where("signal_ref = ?", "signalref").Find(&midSignals)
		Expect(string(midSignals[0].Payload)).To(ContainSubstring("\"key\":\"asd\""))

		// flow控制了节点执行状态相关信息
		for _, execution := range waitExecutions {
			flows, _ := execution.Run()
			for _, flow := range flows {
				flow.Run()
			}
		}

		// 验证等待多个消息，any，未触发消息
		var midRelation []models.SignalExecutionRelation
		relationMgr.Where("signal_ref = ?", "signalref2").Find(&midRelation)
		Expect(midRelation[0].Completed).To(Equal(true))
	})
})
