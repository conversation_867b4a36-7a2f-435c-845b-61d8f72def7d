package jobs_test

import (
	"time"

	gomock "github.com/golang/mock/gomock"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	bpmn_models "qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/bpmn/models/mock"
	"qiniu.io/qbpm/engine/services"
	"qiniu.io/qbpm/engine/services/system"

	"github.com/sirupsen/logrus"
	"qiniu.io/qbpm/engine/bpmn/parser"
	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/jobs"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/test"
)

var _ = Describe("execute job", func() {

	BeforeEach(func() {
		base = test.Init()
		runExecutionMgr = mgr.NewRunExecutionMgr(*base)
		_ = system.NewSystemService(&services.Base{Mgr: base}, nil)
		processDefinition = models.NewProcessDefinition()
		data := []byte(xml)
		model, _ := parser.ParseBpmn(data)
		processDefinition.ProcessModel = model
	})

	AfterEach(func() {
		base.Close()
	})

	It("test execute job", func() {
		execution, _ := models.NewRunExecution(*processDefinition, "", "", "", 0, 0)
		runExecutionMgr.Create(execution)
		job := jobs.NewExecute(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecution models.RunExecution
		Expect(base.First(&findExecution, execution.ID).Error).NotTo(HaveOccurred())
		Expect(findExecution.Status).To(Equal(enums.RunExecutionStatusFinished))
	})

	It("test flow job", func() {
		execution, _ := models.NewRunExecution(*processDefinition, "", "", "", 0, 0)
		execution.Status = enums.RunExecutionStatusFinished
		runExecutionMgr.Create(execution)
		job := jobs.NewFlow(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false))
		Expect(job.Status()).To(Equal(enums.RunExecutionStatusFinished.String()))
		Expect(job.Key()).NotTo(Equal(""))
		Expect(job.LockTime()).To(Equal(time.Second * 5))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecution models.RunExecution
		Expect(base.Where("process_instance_id = ?", execution.ProcessInstanceID).First(&findExecution).Error).NotTo(HaveOccurred())
		Expect(findExecution.ActivityDefineKey).To(Equal("theEnd"))
	})

	It("test cancel job", func() {
		execution, _ := models.NewRunExecution(*processDefinition, "", "", "", 0, 0)
		execution.Status = enums.RunExecutionStatusFinished
		runExecutionMgr.Create(execution)
		job := jobs.NewCancel(ctx.BuildContext(execution, processDefinition, base, nil, nil, nil, logrus.New(), false), "<EMAIL>", "")
		Expect(job.Status()).To(Equal(enums.RunExecutionStatusFinished.String()))
		Expect(job.Key()).NotTo(Equal(""))
		Expect(job.LockTime()).To(Equal(time.Second * 5))
		_, err := job.Run()
		Expect(err).NotTo(HaveOccurred())

		var findExecution models.RunExecution
		Expect(base.Where("process_instance_id = ?", execution.ProcessInstanceID).First(&findExecution).Error).NotTo(HaveOccurred())
		Expect(findExecution.ActivityDefineKey).To(Equal("theEnd"))
	})

	It("test listener job", func() {
		execution, _ := models.NewRunExecution(*processDefinition, "", "", "", 0, 0)
		runExecutionMgr.Create(execution)

		processInstance := &models.ProcessInstance{
			Base: models.Base{
				ID: execution.ID,
			},
			ProcessDefinitionID: execution.ProcessDefinitionID,
		}
		processMgr := mgr.ProcessInstanceMgr{Base: *base}
		processMgr.Create(processInstance)

		activityMgr := mgr.ActivityInstanceMgr{Base: *base}
		activity := models.NewActivityInstance(execution, "UserTask")
		activityMgr.Create(activity)

		var t mock.GinkgoTestReporter
		mockCtl := gomock.NewController(t)
		actioner := mock.NewMockActioner(mockCtl)
		actioner.EXPECT().GetAuthRef().Return("").MinTimes(0)
		actioner.EXPECT().Do(gomock.Any(), gomock.Any()).Return(nil, nil).MinTimes(0)
		actioner.EXPECT().GetID().Return("testAction").MinTimes(0)
		actioner.EXPECT().GetParseKey().Return("").MinTimes(0)
		actioner.EXPECT().GetStoreKey().Return("").MinTimes(0)
		actioner.EXPECT().GetParseFirst().Return(false).MinTimes(0)

		processDefinition.ProcessModel.ActionMap = map[string]bpmn_models.Actioner{
			"testAction": actioner,
		}
		model_listener := &bpmn_models.Listener{
			BaseElement: bpmn_models.BaseElement{
				ID: "testListenerAction",
			},
			Fire:       "begin",
			ActionRefs: []string{"testAction"},
		}

		var t2 mock.GinkgoTestReporter
		mockCtl2 := gomock.NewController(t2)
		node := mock.NewMockINode(mockCtl2)
		node.EXPECT().GetKey().Return("GetKeyL").MinTimes(0)
		node.EXPECT().GetListenerMap().Return(nil).MinTimes(0)
		node.EXPECT().GetListeners().Return(nil).MinTimes(0)
		job_listener := jobs.NewListener(node, processDefinition, execution.ID, base, nil)

		err := job_listener.RunListener("begin", model_listener, "")
		Expect(err).NotTo(HaveOccurred())
	})
})
