package jobs_test

import (
	"testing"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
)

var (
	base              *mgr.Base
	processDefinition *models.ProcessDefinition
	runExecutionMgr   *mgr.RunExecutionMgr
	xml               = `<?xml version="1.0" encoding="UTF-8"?>
				<definitions id="definitions"
				  xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
				  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
				 
					<process id="modify_user_price" name="用户改价流程">
						<startEvent id="theStart"/>
				 
						<sequenceFlow id="toProfileCheck" sourceRef="theStart" targetRef="theEnd"/>
				 
						<endEvent id="theEnd"/>
					</process>
					 
				</definitions>
				`
)

func TestJobs(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Jobs Suite")
}
