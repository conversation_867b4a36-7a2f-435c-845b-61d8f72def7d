package jobs

import (
	"errors"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"qiniu.io/qbpm/engine/bpmn/behaviors"
	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services/scheduler"
)

// ErrExecutionNotRunning indicates execution is not in running status
var ErrExecutionNotRunning = errors.New("execution not running")

// Execute is job to perform node action
type Execute struct {
	*ctx.Context
}

// NewExecute instances a execute job
func NewExecute(ctx *ctx.Context) *Execute {
	return &Execute{ctx}
}

// Key implements interface to get key of current job
func (j *Execute) Key() string {
	return fmt.Sprintf("execution:%d:%s", j.RunExecution.ID, j.RunExecution.ActivityDefineKey)
}

// Status implements interface to get status of current job
func (j *Execute) Status() string {
	return j.RunExecution.Status.String()
}

// LockTime implements interface to get lock time of current job
func (j *Execute) LockTime() time.Duration {
	node, err := j.CurrentNode()
	if err == nil {
		switch node.GetType() {
		case "ServiceTask":
			return time.Minute
		}
	}
	return time.Second * 5
}

func (j *Execute) callback(err error) (err1 error) {
	var status = enums.RunExecutionStatusFinished

	if err != nil {
		status = enums.RunExecutionStatusFailed
		err1 = mgr.NewRunExecutionMgr(*j.Mgr).UpdateStatus(j.RunExecution, status)
	} else {
		err1 = mgr.NewRunExecutionMgr(*j.Mgr).UpdateStatus(j.RunExecution, status)
	}

	if err1 != nil {
		j.Log.WithField("name", "Execute").
			WithField("key", j.Key()).
			WithField("status", status.String()).
			Errorf("update runExecution status with error: %s", err1)
	}
	return
}

// Run implements interface to run current job
func (j *Execute) Run() (nextJobs []scheduler.Job, err error) {

	if j.RunExecution.Status != enums.RunExecutionStatusRunning {
		j.Log.WithField("status", j.RunExecution.Status.String()).
			Warn("execution node is not in running status, skipped.")
		return
	}

	err = j.Mgr.First(&models.RunExecution{}, j.RunExecution.ID).Error
	if err != nil {
		return
	}

	defer func() {
		err1 := j.callback(err)
		if err1 == nil {
			nextJobs = []scheduler.Job{NewFlow(j.Context)}
		}
	}()

	node, err := j.CurrentNode()
	if err != nil {
		j.Log.WithError(err).Error("get current node with error")
		return
	}

	listener := NewListener(node, j.ProcessDefinition, j.RunExecution.ID, j.Mgr, j.AdminTransport)

	listener.FireAction("begin")

	// assert behavior and execute
	if exe, ok := node.GetBehavior().(behaviors.BaseBehavior); ok {
		if err = exe.Execute(j.Context); err != nil {
			j.Log.WithFields(logrus.Fields{
				"currentNode": node.GetName(),
				"processDef":  j.ProcessDefinition.Name,
			}).WithError(err).Error("execute node behavior with error")
			go listener.FireAction("end")
			go listener.FireAction("failure")
			return nextJobs, err
		}
	}

	go listener.FireAction("end")
	go listener.FireAction("completion")

	return
}
