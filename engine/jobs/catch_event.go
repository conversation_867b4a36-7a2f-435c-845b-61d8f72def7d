package jobs

import (
	"errors"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	qbpmModel "qiniu.io/qbpm/engine/bpmn/models"
	"qiniu.io/qbpm/engine/ctx"
	"qiniu.io/qbpm/engine/mgr"
	"qiniu.io/qbpm/engine/models"
	"qiniu.io/qbpm/engine/models/enums"
	"qiniu.io/qbpm/engine/services/scheduler"
	"qiniu.io/qbpm/engine/util/exp"

	paramsUtil "qiniu.io/qbpm/engine/util/params"
)

type CatchEvent struct {
	*ctx.Context
}

func NewCatchEvent(ctx *ctx.Context) *CatchEvent {
	return &CatchEvent{ctx}
}

func (j *CatchEvent) LockTime() time.Duration {
	return time.Second * 5
}

func (j *CatchEvent) Key() string {
	return fmt.Sprintf("catchEvent:%d:%s", j.RunExecution.ID, j.RunExecution.ActivityDefineKey)
}

// Status implements interface to get status of current job
func (j *CatchEvent) Status() string {
	return j.RunExecution.Status.String()
}

func (j *CatchEvent) Run() (nextJobs []scheduler.Job, err error) {
	if j.RunExecution.Status != enums.RunExecutionStatusRunning {
		j.Log.WithField("status", j.RunExecution.Status.String()).
			Warn("CatchEvent node is not in running status, skipped.")
		return
	}

	relationMgr := mgr.SignalExecutionMgr{Base: *j.Context.Mgr}
	executionMgr := mgr.RunExecutionMgr{Base: *j.Context.Mgr}
	node, err := j.Context.CurrentNode()
	if err != nil {
		j.Log.Errorf("j.Context.CurrentNode() err with %s", err)
		return
	}
	listener := NewListener(node, j.ProcessDefinition, j.RunExecution.ID, j.Mgr, j.AdminTransport)

	defer func() {
		if err != nil {
			err = executionMgr.UpdateStatus(j.RunExecution, enums.RunExecutionStatusFailed)
			go listener.FireAction("failure")
			go listener.FireAction("end")
		}
		if j.RunExecution.Status == enums.RunExecutionStatusFinished {
			go listener.FireAction("completion")
			go listener.FireAction("end")
		}
		nextJobs = []scheduler.Job{NewFlow(j.Context)}
	}()

	err = j.Mgr.First(&models.RunExecution{}, j.RunExecution.ID).Error
	if err != nil {
		return
	}

	relation, err := relationMgr.GetWithExecutionId(j.RunExecution.ID)
	if err != nil {
		j.Log.WithField("status", j.RunExecution.Status.String()).
			Errorf("relationMgr.GetWithExecutionId err: %s", err)
		return
	}

	signalMgr := mgr.SignalMgr{*j.Context.Mgr}
	signals, err := signalMgr.MgetSignal(relation.SignalRef, relation.Business)
	if err != nil {
		j.Log.WithField("status", j.RunExecution.Status.String()).
			Errorf("signalMgr.MgetSignal(%s, %s) err: %s", relation.SignalRef, relation.Business, err)
		return
	}

	// 保存signal的数据
	params := j.RunExecution.Params
	for _, signal := range signals {
		if signal.Payload != "" {
			if params, err = paramsUtil.SetParam(params, signal.SignalRef, string(signal.Payload)); err != nil {
				j.Log.Errorf("util.SetParam(%s, %s, %s) error: %s", params, signal.SignalRef, signal.Payload, err)
				return
			}
		}
	}

	go listener.FireAction("begin")

	if err = executionMgr.UpdateParams(j.RunExecution, params); err != nil {
		return
	}

	if len(signals) != 0 {
		if err = relationMgr.ConsumeExecution(j.RunExecution.ID); err != nil {
			j.Log.WithField("status", j.RunExecution.Status.String()).
				Errorf("relationMgr.ConsumeExecution err: %s", err)
		}

		// 执行signalEventDefinition的expression
		catchEvent, ok := (node).(*qbpmModel.IntermediateCatchEvent)
		if !ok {
			err = errors.New("node convert to qbpmModel.IntermediateCatchEvent fail")
			return
		}
		var matchedEventDefinition bool
		for _, signal := range catchEvent.SignalEvents {
			if signal.ID == relation.SignalDefineKey {
				matchedEventDefinition = true
				if signal.Condition != nil && signal.Condition.Expression != "" {
					validate, err := exp.EvalBool(signal.Condition.Expression, params)
					if err != nil {
						j.Log.Errorf("catchEvent, exp.EvalBool with err: %s", err)
						return nextJobs, err
					}

					// signalEventDefinition的expression没过，返回失败
					if !validate {
						err = fmt.Errorf("exp.EvalBool(%s, %s)", signal.Condition.Expression, params)
						return nextJobs, err
					}
				} else {
					// 默认1️⃣依赖的节点状态作为扭转依据
					signal := signals[len(signals)-1]
					jsonPaser, err := paramsUtil.GetParamParser(string(signal.Payload))
					if err != nil {
						return nextJobs, err
					}

					data := jsonPaser.Path("node_status").Data()
					if data == nil {
						err = errors.New("the payload of signalInfo do not contain node_status")
						return nextJobs, err
					}
					if data.(string) == "failure" {
						err = errors.New("deley node failure")
						return nextJobs, err
					}
				}
				break
			}
		}
		// 没有找到对应的signalEventDefinition，报错
		if !matchedEventDefinition {
			j.Log.WithFields(logrus.Fields{
				"SignalDefineKey":           relation.SignalDefineKey,
				"SignalExecutionRelationId": relation.ID,
				"ActivityDefineKey":         relation.ActivityDefineKey,
			}).Error("can not find signalEventDefinition")
			err = errors.New("can not find signalEventDefinition")
			return
		}

		err = executionMgr.UpdateStatus(j.RunExecution, enums.RunExecutionStatusFinished)
	} else {
		err = executionMgr.UpdateStatus(j.RunExecution, enums.RunExecutionStatusPending)
	}
	return
}
