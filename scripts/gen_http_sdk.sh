#!/usr/bin/env bash

TOP="$(cd "$(dirname "${BASH_SOURCE[0]}")"/.. && pwd)"
DOCKER_WORK_DIR="/local/src/qiniu.io/qbpm/sdk"
DOCKER_GOPATH="/local"

# swagger: https://github.com/go-swagger
# macos:
# brew tap go-swagger/go-swagger && brew install go-swagger
gen-http-sdk() {
    service=$1

    docker run --rm -v "${TOP}/sdk:$DOCKER_WORK_DIR" -e GOPATH=$DOCKER_GOPATH quay.io/goswagger/swagger generate client -t $DOCKER_WORK_DIR -f "$DOCKER_WORK_DIR/qbpm.yml" -A $service
}

pushd $TOP
    gen-http-sdk qbpm
popd
